sed -Ei 's/^# deb-src /deb-src /' /etc/apt/sources.list
apt-get update
apt-get install build-essential autoconf libtool git-core
apt-get build-dep imagemagick libmagickcore-dev libde265 libheif

cd /usr/src/ 
git clone https://github.com/strukturag/libde265.git  
git clone https://github.com/strukturag/libheif.git 

cd libde265/ 
./autogen.sh 
./configure
make -j$(nproc)  
make install 

cd /usr/src/libheif/ 
./autogen.sh 
./configure
make -j$(nproc)  
make install 

cd /usr/src/ 
git clone https://github.com/ImageMagick/ImageMagick.git ImageMagick-7.1.0
cd ImageMagick-7*

./configure --with-heic=yes
make -j$(nproc)
make install
ldconfig

convert --version