<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_invites', function (Blueprint $table) {
            $table->string('commissions')->nullable()->after('contract_level');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_invites', function (Blueprint $table) {
            $table->dropColumn('commissions');
        });
    }
}; 