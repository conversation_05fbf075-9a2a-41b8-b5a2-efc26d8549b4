<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->boolean('blacklist')->nullable();
            $table->string('phone')->nullable();
            $table->string('last_name')->nullable();
            $table->date('dob')->nullable();
            $table->string('ssn')->nullable();
            $table->string('transferring')->nullable();
            $table->string('returning')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['blacklist', 'phone', 'last_name', 'dob', 'ssn', 'transferring', 'returning']);
        });
    }
};
