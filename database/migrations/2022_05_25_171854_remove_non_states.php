<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// ONLY RUN SOFT_DELETE MIGRATION
// FUNCTIONALITY MOVED TO PATCH COMMAND
class RemoveNonStates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add Soft Delete
        Schema::table('form_options', function(Blueprint $table)
		{
			$table->softDeletes();
		});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table("form_options", function ($table) {
            $table->dropSoftDeletes();
        });
    }
}
