<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFormFieldConditionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('form_field_conditions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('form_field_id');
            $table->foreign('form_field_id')->references('id')->on('form_fields');
            $table->uuid('parent_id')->default(0);
            $table->string('value');
            $table->string('action');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('form_field_conditions', function (Blueprint $table) {
            $table->dropForeign('form_field_conditions_form_field_id_foreign');
        });
        
        Schema::dropIfExists('form_field_conditions');
    }
}
