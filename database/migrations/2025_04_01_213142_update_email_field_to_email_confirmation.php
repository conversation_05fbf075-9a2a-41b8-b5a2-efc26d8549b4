<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\FormSection;
use App\Models\FormField;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Find the form section with label 'Work Email Address' and status 'active'
        $formSection = FormSection::where('label', 'like', 'Work Email Address')
            ->where('status', FormSection::STATUS_ACTIVE)
            ->first();

        if ($formSection) {
            // Update the subline field
            $formSection->subline = 'This email address will be used to create your account logins.';
            $formSection->save();
            
            // Find the associated form field with type 'email'
            $formField = FormField::where('form_section_id', $formSection->id)
                ->where('type', FormField::TYPE_EMAIL)
                ->first();

            if ($formField) {
                // Update the type field to 'email_confirmation'
                $formField->type = 'email_confirmation';
                $formField->save();
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Find the form section with label 'Work Email Address' and status 'active'
        $formSection = FormSection::where('label', 'like', 'Work Email Address')
            ->where('status', FormSection::STATUS_ACTIVE)
            ->first();

        if ($formSection) {
            // Set the subline field to NULL
            $formSection->subline = NULL;
            $formSection->save();
            
            // Find the associated form field with type 'email_confirmation'
            $formField = FormField::where('form_section_id', $formSection->id)
                ->where('type', 'email_confirmation')
                ->first();

            if ($formField) {
                // Revert the type field back to 'email'
                $formField->type = FormField::TYPE_EMAIL;
                $formField->save();
            }
        }
    }
};
