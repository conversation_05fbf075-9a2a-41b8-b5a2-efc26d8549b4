<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_invites_groups', function (Blueprint $table) {
            // $table->string('group_invite_status');
            $table->string('group_imo_name')->nullable();
            $table->string('group_size')->nullable();
            $table->string('contact_info')->nullable();
            $table->string('preferred_times')->nullable();
            $table->text('notes')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_invites_groups', function (Blueprint $table) {
            // $table->dropColumn('group_invite_status');
            $table->dropColumn('group_imo_name');
            $table->dropColumn('group_size');
            $table->dropColumn('contact_info');
            $table->dropColumn('preferred_times');
            $table->dropColumn('notes');
        });
    }
};
