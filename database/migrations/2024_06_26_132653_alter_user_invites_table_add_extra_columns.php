<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_invites', function (Blueprint $table) {
            $table->string('type')->nullable();
            $table->string('alt_email')->nullable();
            $table->json('carrier')->nullable();
            $table->string('agreement_type')->nullable();
            $table->boolean('bonus_addendum')->default(false);
            $table->boolean('multiple_owners')->default(false);
            $table->string('contract_type')->nullable();
            $table->string('custom_contract_upload')->nullable();
            $table->string('service_level')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_invites', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->dropColumn('alt_email');
            $table->dropColumn('carrier');
            $table->dropColumn('agreement_type');
            $table->dropColumn('bonus_addendum');
            $table->dropColumn('multiple_owners');
            $table->dropColumn('contract_type');
            $table->dropColumn('custom_contract_upload');
            $table->dropColumn('service_level');
        });
    }
};
