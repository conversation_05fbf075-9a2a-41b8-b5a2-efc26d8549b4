<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUsersAddInvitedByColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->uuid('inviter_user_id')->nullable();
            $table->foreign('inviter_user_id')->references('id')->on('users');
            $table->string('inviter_agent_code')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign('users_inviter_user_id_foreign');
        });
        
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('inviter_user_id');
            $table->dropColumn('inviter_agent_code');
            $table->dropSoftDeletes();
        });
    }
}
