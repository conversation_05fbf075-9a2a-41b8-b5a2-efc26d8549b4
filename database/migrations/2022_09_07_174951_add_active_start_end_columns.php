<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddActiveStartEndColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form_sections', function (Blueprint $table) {
            $table->date('active_start')->nullable();
            $table->date('active_end')->nullable();
        });
        Schema::table('form_fields', function (Blueprint $table) {
            $table->date('active_start')->nullable();
            $table->date('active_end')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('form_sections', function (Blueprint $table) {
            $table->dropColumn('active_start');
            $table->dropColumn('active_end');
        });
        Schema::table('form_fields', function (Blueprint $table) {
            $table->dropColumn('active_start');
            $table->dropColumn('active_end');
        });
    }
}
