<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUserSignaturesTableAddFormFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_signatures', function (Blueprint $table) {
            $table->string('form_name')->nullable();
            $table->string('file_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_signatures', function (Blueprint $table) {
            $table->dropColumn('form_name');
            $table->dropColumn('file_name');
        });
    }
}
