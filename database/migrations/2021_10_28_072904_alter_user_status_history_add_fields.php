<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUserStatusHistoryAddFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_status_history', function(Blueprint $table) {
            $table->text('note')->nullable();
            $table->uuid('trigger_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_status_history', function(Blueprint $table) {
            $table->dropColumn('note');
            $table->dropForeign(['trigger_id']);
            $table->dropColumn('trigger_id');
        });
    }
}
