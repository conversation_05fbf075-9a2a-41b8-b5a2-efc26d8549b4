<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUsersTableAddBaseFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function ($table) {
            $table->uuid('manager_id')->after('email')->nullable();
            $table->foreign('manager_id')->references('id')->on('users');
            $table->string('status')->nullable();
            $table->string('type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign('users_manager_id_foreign');
        });

        Schema::table('users', function ($table) {
            $table->dropColumn('manager_id');
            $table->dropColumn('status');
            $table->dropColumn('type');
        });
    }
}
