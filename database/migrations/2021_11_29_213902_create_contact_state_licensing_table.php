<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateContactStateLicensingTable extends Migration
{
    /**
     * Run the migrations.
     * this migration is to create a table that stores all the contact 
     * information of the licensing pages of the different states.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_state_licensing', function (Blueprint $table) {
            $table->id();
            $table->uuid('content_id');
            $table->foreign('content_id')->references('id')->on('contents');
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('zip')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_state_licensing');
    }
}
