<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterFormConditionsTableChangeParentId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form_field_conditions', function (Blueprint $table) {
            $table->dropColumn('parent_id');
            $table->uuid('form_section_id')->nullable();
            $table->foreign('form_section_id')->references('id')->on('form_sections');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('form_field_conditions', function (Blueprint $table) {
            $table->dropForeign('form_field_conditions_form_section_id_foreign');
        });
        
        Schema::table('form_field_conditions', function (Blueprint $table) {
            $table->dropColumn('form_section_id');
            $table->uuid('parent_id')->nullable();
        });
    }
}
