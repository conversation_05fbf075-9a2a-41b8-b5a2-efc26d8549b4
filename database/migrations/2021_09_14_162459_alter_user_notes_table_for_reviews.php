<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUserNotesTableForReviews extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // getting a segfault when dropping foreign keys
        Schema::dropIfExists('user_notes');

        Schema::create('user_notes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('recruit_id');
            $table->foreign('recruit_id')->references('id')->on('users');
            $table->uuid('form_section_id');
            $table->foreign('form_section_id')->references('id')->on('form_sections');
            $table->boolean('fixed')->default(false);
            $table->text('content');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // revert to old schema
        Schema::dropIfExists('user_notes');

        Schema::create('user_notes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->foreign('user_id')->references('id')->on('users');
            $table->uuid('recipient_id');
            $table->foreign('recipient_id')->references('id')->on('users');
            $table->text('content');
            $table->timestamps();
        });
    }
}
