<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateStoredEmailsTable extends Migration
{
    public function up()
    {
        Schema::create('stored_emails', function (Blueprint $table) {
            $table->id();
            $table->string('to');
            $table->string('from');
            $table->string('subject');
            $table->text('body');
            $table->timestamp('sent_at');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('stored_emails');
    }
}