<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('work_email')->nullable();
            $table->timestamp('licensed')->nullable();
            $table->timestamp('first_ao_submission')->nullable();
            $table->timestamp('first_homeoffice_submission')->nullable();
            $table->timestamp('homeoffice_submission')->nullable();
            $table->timestamp('last_approved')->nullable();
            $table->timestamp('last_revision')->nullable();
            $table->timestamp('passed_exam')->nullable();
            $table->string('recruiter_name')->nullable();
            $table->string('upline_agent_name')->nullable();
            $table->string('upline_agent_code')->nullable();
            $table->string('agency_owner_name')->nullable();

            $table->index('work_email');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('users_work_email_index');

            $table->dropColumn('work_email');
            $table->dropColumn('licensed');
            $table->dropColumn('first_ao_submission');
            $table->dropColumn('first_homeoffice_submission');
            $table->dropColumn('homeoffice_submission');
            $table->dropColumn('last_approved');
            $table->dropColumn('last_revision');
            $table->dropColumn('passed_exam');
            $table->dropColumn('recruiter_name');
            $table->dropColumn('upline_agent_name');
            $table->dropColumn('upline_agent_code');
            $table->dropColumn('agency_owner_name');

        });
    }
}
