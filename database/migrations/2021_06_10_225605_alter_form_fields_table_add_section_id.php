<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterFormFieldsTableAddSectionId extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('form_fields', function (Blueprint $table) {
            $table->uuid('form_section_id')->after('label');
            $table->foreign('form_section_id')->references('id')->on('form_sections');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('form_fields', function (Blueprint $table) {
            $table->dropForeign('form_fields_form_section_id_foreign');
        });
        
        Schema::table('form_fields', function (Blueprint $table) {
            $table->dropColumn('form_section_id');
        });
    }
}
