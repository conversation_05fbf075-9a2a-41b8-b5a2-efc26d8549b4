<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\FormField;
use App\Models\UserStatusHistory;
use App\Models\FormFieldCondition;
use FormBuilder;

class FormOnboardingAntiMoneyLaundering extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sort = 0;

        $page = FormBuilder::createPage('Anti-Money Laundering', 'aml-certification');
        FormBuilder::sort($page, 6);

        $section = FormBuilder::createSection('Have you previously taken an AML training course?', $page->id, $sort++);
        $amlTrainingSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

         // if don't have a traning

        $section = FormBuilder::createSection('To submit your contracting application, you must provide proof of an Anti-Money Laundering (AML) training course. This training needs to have been completed within the last year to be valid.', $page->id, $sort++, $amlTrainingSelect->id, FormField::SELECT_BOOL_YES, FormFieldCondition::ACTION_HIDE);
        FormBuilder::createField("In order to be contracted with a carrier, you must provide proof of completing Anti-Money Laundering (AML) training. Please use the link below to access our recommended AML provider. This is a paid course. While you aren’t required to go with this provider, we highly recommend and encourage you to complete it as all carriers will accept this AML.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 0);
        FormBuilder::createField("<a href='https://www.webce.com/catalog/courses/course-information/aml-training/course/134915' target='_blank'>https://www.webce.com/catalog/courses/course-information/aml-training/course/134915</a>", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 1);
        FormBuilder::createField("There are free alternatives, but we cannot guarantee that a carrier will accept these. Below is a link to the American Amicable training that is free of charge.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 2);
        FormBuilder::createField("<a href='https://www.americanamicable.com/internet/aml/amllogon.php?msg=x7' target='_blank'>https://www.americanamicable.com/internet/aml/amllogon.php?msg=x7</a>", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 3);
        FormBuilder::createField("After completing the course, please return to this question, change your reply to YES, and upload the certificate of completion provided to you.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 4);

        // training has been completed

        $section = FormBuilder::createSection('Training Completion Date:', $page->id, $sort++, $amlTrainingSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id);

        $section = FormBuilder::createSection('Upload proof of completion of an Anti-Money Laundering (AML) training course.', $page->id, $sort++, $amlTrainingSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField("If you completed your training through LIMRA, please provide a screenshot with the course name and completion date.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 0);
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, 0, '')->markAsPii();

    }
}
