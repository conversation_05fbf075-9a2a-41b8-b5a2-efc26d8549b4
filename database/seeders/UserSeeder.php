<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\App;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $user = new User();
        $user->password = Hash::make(env('SUPER_ADMIN_PASSWORD'));
        $user->email = '<EMAIL>';
        $user->name = 'Super Admin';
        $user->save();

        $user->assignRole(User::ROLE_TYPE_SUPER_ADMIN);

        if(App::environment('local')) {

            $user = new User();
            $user->password = Hash::make('password');
            $user->email = '<EMAIL>';
            $user->name = 'Test Agency Owner';
            
            if(App::environment('local'))
                $user->agent_code = env('TEST_AGENT_CODE'); // AgentCode of <PERSON>, <EMAIL> 
                
            $user->save();
            $user->assignRole(User::ROLE_TYPE_AGENCY_OWNER);

            $user = new User();
            $user->password = Hash::make('password');
            $user->email = '<EMAIL>';
            $user->name = 'Test User';
            $user->contract_level = 70;
            $user->user_status_id = UserStatus::where('slug', 'registration')->first()->id ?? null;

            if(App::environment('local'))
                $user->upline_agent_id = env('TEST_AGENT_ID'); // AgentID of Barry Wyels, <EMAIL>

            $user->save();

            $user->assignRole(User::ROLE_TYPE_RECRUIT);

            event(new Registered($user));

            $user = new User();
            $user->password = Hash::make('password');
            $user->email = '<EMAIL>';
            $user->name = 'Test Sales Rep';
            $user->save();

            $user->assignRole(User::ROLE_TYPE_SALES_REP);


            $user = new User();
            $user->password = Hash::make('password');
            $user->email = '<EMAIL>';
            $user->name = 'Test Staff';
            $user->save();

            $user->assignRole(User::ROLE_TYPE_STAFF);
        }
    }
}
