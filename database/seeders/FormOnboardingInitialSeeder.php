<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormSection;
use App\Models\UserStatusHistory;
use FormBuilder;


class FormOnboardingInitialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sort = 0;
        
        // first portion of onboarding applicaiton form post registration

        $page = FormBuilder::createPage('Personal Information', 'personal-information');
        FormBuilder::sort($page, 1);

        // full legal name

        $section = FormBuilder::createSection('Name', $page->id, $sort ++);
        $field = FormBuilder::createField('First Name', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, 0)->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_FIRST_NAME);

        $field = FormBuilder::createField('Middle Name', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, 1, false, 0)->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_MIDDLE_NAME);

        $field = FormBuilder::createField('Last Name',   FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, 2)->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_LAST_NAME);

        // date of birth field

        $section = FormBuilder::createSection('Date of Birth', $page->id, $sort ++);
        $field = FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id)->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_BIRTHDATE);
        // gender field

        $section = FormBuilder::createSection('Gender', $page->id, $sort ++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('genders'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_GENDER);
        

        // maiden name

        $section = FormBuilder::createSection('Maiden Name', $page->id, $sort ++);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '', 0)->markAsPii();

        // phone numbers
        $section = FormBuilder::createSection('Phone Number', $page->id, $sort ++);
        $field = FormBuilder::createField('Cell Phone', FormField::TYPE_PHONE, FormField::WIDTH_THIRD, $section->id, 0, '', 1)->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_PHONE);

        FormBuilder::createField('Home Phone', FormField::TYPE_PHONE, FormField::WIDTH_THIRD, $section->id, 1, '', 0)->markAsPii();
        FormBuilder::createField('Fax Number', FormField::TYPE_PHONE, FormField::WIDTH_THIRD, $section->id, 2, '', 0)->markAsPii();

        // email section
        $section = FormBuilder::createSection('Work Email Address', $page->id, $sort ++);
        $field = FormBuilder::createField('Enter Email', FormField::TYPE_EMAIL, FormField::WIDTH_HALF, $section->id, 0, 'Email Address');
        $field->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_WORK_EMAIL);

        // residential address

        $section = FormBuilder::createSection('Residential Address', $page->id, $sort ++, false, false, false, false, FormSection::FILLMODE_GOOGLE_AUTOCOMPLETE);
        $field = FormBuilder::createField('Street Address', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 0, 'Enter a location', 1, 0, 255, false, 'street')->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_STREET1);

        FormBuilder::createField('Address Line 2', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 1, '', 0, 0, 255, false, 'addr2')->markAsPii();

        $field = FormBuilder::createField('City', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 2, '', 1, 0, 255, false, 'city');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_CITY);

        $field = FormBuilder::createField('State/Province', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 3, '', 1, 0, 255, false, 'state');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_STATE);

        $field = FormBuilder::createField('ZIP/Postal Code', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 4, '', 1, 0, 255, false, 'zip');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_ZIP);


        $field = FormBuilder::createField('Country', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 5, 'Please Select', 1, 0, 1, config('countries'), 'country');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_COUNTRY);

        // county of residence

        $section = FormBuilder::createSection('County of Residence', $page->id, $sort ++);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, 'Example: Buncombe County');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_COUNTY);

        // move in date

        $section = FormBuilder::createSection('Move-In Date', $page->id, $sort ++);
        $fromDate = FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id)->markAsPii();
        FormBuilder::addFormLookup($fromDate->id, FormLookup::FIELD_MOVE_IN_AT);

        // select for address check

        $section = FormBuilder::createSection('Is your mailing address the same as your residential address?', $page->id, $sort ++);
        $addressSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        FormBuilder::addFormLookup($addressSelect->id, FormLookup::FIELD_ADDRESS_MAILING_BOOL);

        // conditional mailing address

        $section = FormBuilder::createSection('Mailing Address', $page->id, $sort ++, $addressSelect->id, FormField::SELECT_BOOL_NO, false, false, FormSection::FILLMODE_GOOGLE_AUTOCOMPLETE);
        $field = FormBuilder::createField('Street Address', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 0, 'Enter a location', 1, 0, 255, false, 'street')->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_MAILING_STREET1);

        FormBuilder::createField('Address Line 2', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 1, '', 0, 0, 255, false, 'addr2')->markAsPii();

        $field = FormBuilder::createField('City', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 2, '', 1, 0, 255, false, 'city');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_MAILING_CITY);

        $field = FormBuilder::createField('State/Province', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 3, '', 1, 0, 255, false, 'state');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_MAILING_STATE);

        $field = FormBuilder::createField('ZIP/Postal Code', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 4, '', 1, 0, 255, false, 'zip');
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_MAILING_ZIP);

        FormBuilder::createField('Country', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 5, 'Please Select', 1, 0, 1, config('countries'), 'country');

    }
}
