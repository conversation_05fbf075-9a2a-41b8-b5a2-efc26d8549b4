<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {   
        $this->command->info("Form Seeder Start");
        $this->call(FormSeeder::class);
        $this->command->info("Form Seeder Finished");

        $this->command->info("User Status Seeder Start");
        $this->call(UserStatusSeeder::class);
        $this->command->info("User Status Seeder Finished");

        $this->command->info("User Role Seeder Start");
        $this->call(UserRoleSeeder::class);
        $this->command->info("User Role Seeder Finished");

        $this->command->info("User Seeder Start");
        $this->call(UserSeeder::class);
        $this->command->info("User Seeder Finished");

        $this->command->info("Company Information Start");
        $this->call(CompanyInformationSeeder::class);
        $this->command->info("Company Information Finished");
    }
}
