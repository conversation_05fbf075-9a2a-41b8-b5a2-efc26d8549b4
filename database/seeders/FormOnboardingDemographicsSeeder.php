<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\UserStatusHistory;
use App\Models\FormFieldCondition;
use FormBuilder;

class FormOnboardingDemographicsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sort = 0;

        $page = FormBuilder::createPage('Demographic Information', 'demographic-information');
        FormBuilder::sort($page, 2);

        $section = FormBuilder::createSection('', $page->id, $sort++);
        FormBuilder::createField("Symmetry collects demographic information from its insurance agents voluntarily in order to determine the diversity of our insurance agents and help measure the effectiveness of our diversity and inclusion efforts. Providing the requested demographic information is not required, and the information, including whether or not you provide it, will not affect Symmetry’s decision to work with you. If you wish to provide the information, it will be kept confidential.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id, 0);

        $section = FormBuilder::createSection('Ethnicity', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.ethnicity'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_ETHNICITY);

        $section = FormBuilder::createSection('Education (highest degree completed)', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.education'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_EDUCATION);

        $section = FormBuilder::createSection('I was recruited into Symmetry', $page->id, $sort++);
        $recruitedField = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.recruited'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_RECRUITED);

        $section = FormBuilder::createSection('Recruitment Source', $page->id, $sort++, $recruitedField->id, FormField::SELECT_DEMOGRAPHICS_OTHER);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, 0)->markAsPii();

        $section = FormBuilder::createSection('My average income from prior to Symmetry was', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.income'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_INCOME);

        $section = FormBuilder::createSection('Prior to starting with Symmetry, I was', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.self-employment'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_SELF_EMPLOYED);

        $section = FormBuilder::createSection('What has been your primary industry experience prior to accepting this role with Symmetry', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.experience'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_EXPERIENCE);

        $section = FormBuilder::createSection('What is your marital status?', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.marriage'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_MARRIAGE);

        $section = FormBuilder::createSection('What is your employment status?', $page->id, $sort++);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('demographics.current-employment'));
        FormBuilder::addFormLookup($field->id, FormLookup::DEMO_EMPLOYMENT);
        
    }
}
