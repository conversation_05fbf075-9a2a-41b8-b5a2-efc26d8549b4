<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // docs recommend to forget cached perms before seeding
        // https://spatie.be/docs/laravel-permission/v5/advanced-usage/seeding
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // create application perms
        Permission::create(['name' => 'fill applications']);

        Permission::create(['name' => 'review applications']);
        Permission::create(['name' => 'approve applications']);
        Permission::create(['name' => 'reject applications']);
        Permission::create(['name' => 'form field editor']);

        Permission::create(['name' => 'track applications']);
        Permission::create(['name' => 'create hqaccount']); // create unlicensed hq account for recruit
        Permission::create(['name' => 'assign to onboarding dept']); // assign to onboardign dept

        Permission::create(['name' => 'view encrypted']);
        Permission::create(['name' => 'create invites']);
        Permission::create(['name' => 'fill fields']);
        Permission::create(['name' => 'approve fields']);
        Permission::create(['name' => 'reject fields']);
        Permission::create(['name' => 'agency content']);
        Permission::create(['name' => 'admin content']);
        Permission::create(['name' => 'license guide']);
        Permission::create(['name' => 'company information']);

        Permission::create(['name' => 'view uploads']);

        Permission::create(['name' => 'final approve applications']);

        // create roles and set perms
        $role = Role::create(['name' => User::ROLE_TYPE_RECRUIT]);
        $role->givePermissionTo([
            'fill applications',
            'fill fields',
            'license guide',
            'company information',
        ]);

        $role = Role::create(['name' => User::ROLE_TYPE_UNLICENSED_AGENT]);
        $role->givePermissionTo([
            'fill applications',
            'fill fields',
            'license guide',
            'company information',
        ]);

        $role = Role::create(['name' => User::ROLE_TYPE_AGENCY_OWNER]);
        $role->givePermissionTo([
            'review applications',
            'approve applications',
            'reject applications',
            'track applications',
            'create invites',
            'approve fields',
            'reject fields',
            'agency content',
            'company information',
            'view uploads',
        ]);

        $role = Role::create(['name' => User::ROLE_TYPE_SALES_REP]);
        $role->givePermissionTo([
            'create invites',
            'view uploads',
            'track applications',
        ]);

        $role = Role::create(['name' => User::ROLE_TYPE_STAFF]);
        $role->givePermissionTo([
            'review applications',
            'approve applications',
            'reject applications',
            'track applications',
            'form field editor',
            'create hqaccount',
            'assign to onboarding dept',
            'view encrypted',
            'create invites',
            'fill fields',
            'approve fields',
            'reject fields',
            'agency content',
            'company information',
            'view uploads',
            'final approve applications',
        ]);

        $role = Role::create(['name' =>  User::ROLE_TYPE_SUPER_ADMIN]);
        $role->givePermissionTo(
            Permission::where('name', '!=', 'fill applications')
                      ->where('name', '!=', 'agency content')
                      ->where('name', '!=', 'license guide')
                      ->get()
        );
    }
}
