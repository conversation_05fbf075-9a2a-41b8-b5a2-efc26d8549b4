<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\UserStatus;
use App\Models\FormPage;


class UserStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {   
        UserStatus::create([
            'name' => 'Registration',
            'slug' => 'registration',
            'form_page_id' => FormPage::where('step_ident', 'registration')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Personal Information',
            'slug' => 'personal-information',
            'form_page_id' => FormPage::where('step_ident', 'personal-information')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'License Information',
            'slug' => 'license-information',
            'form_page_id' => FormPage::where('step_ident', 'license-information')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Legal Information',
            'slug' => 'legal-information',
            'form_page_id' => FormPage::where('step_ident', 'legal-information')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Address History',
            'slug' => 'address-history',
            'form_page_id' => FormPage::where('step_ident', 'address-history')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'AML Certification',
            'slug' => 'aml-certification',
            'form_page_id' => FormPage::where('step_ident', 'aml-certification')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'FINRA Licensing',
            'slug' => 'finra-licensing',
            'form_page_id' => FormPage::where('step_ident', 'finra-licensing')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'EO insurance',
            'slug' => 'eo-insurance',
            'form_page_id' => FormPage::where('step_ident', 'eo-insurance')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'EFT Information',
            'slug' => 'eft-information',
            'form_page_id' => FormPage::where('step_ident', 'eft-information')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Surance Bay Agreement',
            'slug' => 'surance-bay-agreement',
            'form_page_id' => FormPage::where('step_ident', 'surance-bay-agreement')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Demographic Information',
            'slug' => 'demographic-information',
            'form_page_id' => FormPage::where('step_ident', 'demographic-information')->first()->id ?? null,
        ]);

        UserStatus::create([
            'name' => 'Sent To HQ Quility',
            'slug' => 'sent-to-hq-quility',
            'form_page_id' => null,
        ]);

        // New workflow statuses
        UserStatus::create([
            'name' => 'Submitted',
            'slug' => 'submitted',
            'form_page_id' => null,
        ]);
        UserStatus::create([
            'name' => 'Approved',
            'slug' => 'approved',
            'form_page_id' => null,
        ]);
        UserStatus::create([
            'name' => 'Rejected',
            'slug' => 'rejected',
            'form_page_id' => null,
        ]);
        UserStatus::create([
            'name' => 'Created Unlicensed Account',
            'slug' => 'created-unlicensed account',
            'form_page_id' => null,
        ]);
        UserStatus::create([
            'name' => 'Assigned to Onboarding Dept',
            'slug' => 'assigned-to-onboarding-dept',
            'form_page_id' => null,
        ]);
    }
}
