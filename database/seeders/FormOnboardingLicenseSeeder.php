<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormSection;
use App\Models\UserStatusHistory;
use FormBuilder;

class FormOnboardingLicenseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // first portion of onboarding applicaiton form

        $page = FormBuilder::createPage('Insurance License Information', 'license-information');
        FormBuilder::addSubline($page, 'Please complete the following information about your current insurance licensing.');
        FormBuilder::sort($page, 3);

        // main licensed question

        $section = FormBuilder::createSection('Do you have a resident life insurance license?', $page->id, 0);
        $licenseSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        FormBuilder::addFormLookup($licenseSelect->id, FormLookup::LICENSE_IS_LICENSED_BOOL);

        /*
         * USER CURRENTLY HAS A RESIDENT LIFE INSURANCE LICENSE
         */

        // workflow for licensed users

        $section = FormBuilder::createSection('Resident Insurance License Number', $page->id, 1, $licenseSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();

        $section = FormBuilder::createSection('NPN Number', $page->id, 2, $licenseSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '');
        $field->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::LICENSE_NPN);

        $section = FormBuilder::createSection('Resident Insurance License State', $page->id, 3, $licenseSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('states'));
        FormBuilder::addFormLookup($field->id, FormLookup::LICENSE_STATE);

        $section = FormBuilder::createSection('Resident Insurance License Issued On', $page->id, 4, $licenseSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id);
        $field->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::LICENSE_DATE);

        /*
         * IS THIS USER A BUSINESS
         */

        // "doing business as" select -- selection for individual does not have additional flow on this page

        $section = FormBuilder::createSection('Doing Business As', $page->id, 5, $licenseSelect->id, FormField::SELECT_BOOL_YES);
        $dbaSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BUSINESS_TYPE);
        FormBuilder::addFormLookup($dbaSelect->id, FormLookup::FIELD_IS_BUSINESS_BOOL);

        // business life insurance license

        $section = FormBuilder::createSection('Do you have a business life insurance license?', $page->id, 6, $dbaSelect->id, FormField::SELECT_BUSINESS_TYPE_BUSINESS);
        $bizInsuredSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        /*
         * DOES USER HAVE BUSINESS LIFE INSURANCE LICENSE
         */

        // notice for businesses without license

        $section = FormBuilder::createSection('', $page->id, 7, $bizInsuredSelect->id, FormField::SELECT_BOOL_NO);
        FormBuilder::createField("Unfortunately, until you have proof of a valid business license, we will be unable to contract you as a business entity. You may complete your application by selecting 'DBA: Individual', and submit your business license at a later date.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id);

        // workflow for businessess with a valid license

        $section = FormBuilder::createSection('Business Insurance License Number', $page->id, 8, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_BUSINESS_NPN);

        $section = FormBuilder::createSection('Business Insurance License State', $page->id, 9, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::addSubline($section, "Your business insurance license state MUST match the state listed on your business address.");
        FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('states'));

        $section = FormBuilder::createSection('Business Insurance License Issued On', $page->id, 10, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id)->markAsPii();

        $section = FormBuilder::createSection('Business Name', $page->id, 11, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_BUSINESS_NAME);

        $section = FormBuilder::createSection('EIN', $page->id, 12, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();

        $section = FormBuilder::createSection('Principal Agent Name', $page->id, 12, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();

        $section = FormBuilder::createSection('Your Title', $page->id, 13, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();

        $section = FormBuilder::createSection('Company Type', $page->id, 14, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, config('company_types'));
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_BUSINESS_TYPE);

        $section = FormBuilder::createSection('Business Phone', $page->id, 15,  $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_PHONE, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();
        FormBuilder::addFormLookup($field->id, FormLookup::FIELD_BUSINESS_PHONE);

        $section = FormBuilder::createSection('Business Fax', $page->id, 15,  $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_PHONE, FormField::WIDTH_HALF, $section->id, 0, '', 0)->markAsPii();

        $section = FormBuilder::createSection('Business Email Address', $page->id, 16, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('Enter Email', FormField::TYPE_EMAIL, FormField::WIDTH_HALF, $section->id, 0)->markAsPii();
        FormBuilder::createField('Confirm Email', FormField::TYPE_EMAIL, FormField::WIDTH_HALF, $section->id, 1)->markAsPii();

        $section = FormBuilder::createSection('Website Address', $page->id, 17, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 0, '')->markAsPii();

        $section = FormBuilder::createSection('Business Address', $page->id, 18, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES, false, false, FormSection::FILLMODE_GOOGLE_AUTOCOMPLETE);
        FormBuilder::createField('Street Address', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 0, 'Enter a location', 1, 0, 255, false, 'street')->markAsPii();
        FormBuilder::createField('Address Line 2', FormField::TYPE_TEXT, FormField::WIDTH_FULL, $section->id, 1, '', 0, 0, 255, false, 'addr2')->markAsPii();
        FormBuilder::createField('City', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 2, '', 1, 0, 255, false, 'city');
        FormBuilder::createField('State/Province', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 3, '', 1, 0, 255, false, 'state');
        FormBuilder::createField('ZIP/Postal Code', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, 4, '', 1, 0, 255, false, 'zip');
        FormBuilder::createField('Country', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 5, 'Please Select', 1, 0, 1, config('countries'), 'country');

        $section = FormBuilder::createSection('Business Incorporation Date', $page->id, 19, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id)->markAsPii();

        $section = FormBuilder::createSection('Please upload a copy of your Articles of Incorporation', $page->id, 20, $bizInsuredSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, 0, '')->markAsPii();

        /*
         * USER DOES NOT HAVE A RESIDENT LIFE INSURANCE LICENSE
         */

        // workflow for unlicensed users

        $section = FormBuilder::createSection('Have you passed your state exam?', $page->id, 21, $licenseSelect->id, FormField::SELECT_BOOL_NO);
        $stateExamSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 1, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        FormBuilder::addFormLookup($stateExamSelect->id, FormLookup::STATE_EXAM_STATUS);

        // user has passed their state exam

        $section = FormBuilder::createSection('Please upload proof of having successfully passed your state exam', $page->id, 22, $stateExamSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, 0, '')->markAsPii();

        // user has not passed state exam

        $section = FormBuilder::createSection('Are you enrolled in a pre-licensing course?', $page->id, 23, $stateExamSelect->id, FormField::SELECT_BOOL_NO);
        $prelicenseCourseSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 1, 'Please Select', 1, 0, 1, 
            [
                FormField::SELECT_BOOL_YES => FormField::SELECT_BOOL_YES,
                FormField::SELECT_BOOL_NO => 'NO, and I acknowledge that my next step is to contact my AO and get enrolled.'
            ]
        );
        FormBuilder::addFormLookup($prelicenseCourseSelect->id, FormLookup::PRE_LICENSE_STATUS);

        $section = FormBuilder::createSection('', $page->id, 24, $prelicenseCourseSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField(
            "<p class='mb-0'>Once you have completed your pre-licensing course, your next step is to register and complete your state exam.</p>
            <a target='_blank' href='https://www.examfx.com/insurance-prelicensing-training/view-insurance-state-requirements'>Click on the link for requirements in your state</a>
            <p class='mt-2'>Once you've passed your state exam, please come back to this page and complete your application.</p>", 
            FormField::TYPE_HTML, 
            FormField::WIDTH_FULL, 
            $section->id
        );

        // user has entered pre licensing course

        $section = FormBuilder::createSection('Please upload proof of having enrolled in a pre-licensing course', $page->id, 25, $prelicenseCourseSelect->id, FormField::SELECT_BOOL_YES);
        FormBuilder::addSubline($section, 'This can be a picture or screenshot of the receipt you received after enrolling in a pre-licensing course.');
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, 0, '')->markAsPii();

        // user has not entered a course

        $section = FormBuilder::createSection('', $page->id, 26, $prelicenseCourseSelect->id, FormField::SELECT_BOOL_NO);
        FormBuilder::createField("<b>It's time to enroll in a pre-licensing course! Please reach out to your AO. Your application progress up to this point will be saved.</b>", FormField::TYPE_HTML, FormField::WIDTH_FULL, $section->id);
        FormBuilder::createField("Hidden Prevent Check", FormField::TYPE_HIDDEN, FormField::WIDTH_FULL, $section->id);

    }
}
