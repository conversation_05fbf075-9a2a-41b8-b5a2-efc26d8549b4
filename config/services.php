<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'mandrill' => [
        'secret' => env('MANDRILL_KEY'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'auth0' => [
        'client_id' => env('AUTH0_CLIENT_ID'),
        'client_secret' => env('AUTH0_CLIENT_SECRET'),
        'redirect' => env('APP_URL') . 'auth0/callback',
        'base_url' => env('AUTH0_DOMAIN'),
    ],

    'hq_auth0' => [
        'client_id' => env('AUTH0_HQ_CLIENT_ID'),
        'client_secret' => env('AUTH0_HQ_CLIENT_SECRET'),
        'base_url' => env('AUTH0_DOMAIN'),
        'audience' => env('AUTH0_AUDIENCE'),
    ],

    'neutrino' => [
        'user-id' => 'Quilitysupport',
        'api-key' => 'iqVZ6818J7Launvodsxi5sK8Ltbo5RSB5ZPXAVULzpRAuGAM',
    ],

    'demoportal' => [
        'base_url'              => env('DEMO_PORTAL_BASE_URL'),
        'auth0_domain'          => env('DEMO_PORTAL_AUTH0_DOMAIN'),
        'auth0_client_id'       => env('DEMO_PORTAL_AUTH0_CLIENT_ID'),
        'auth0_client_secret'   => env('DEMO_PORTAL_AUTH0_CLIENT_SECRET'),
        'auth0_audience'        => env('DEMO_PORTAL_AUTH0_AUDIENCE'),

    ],

    'nipr' => [
        'base_url' => env('NIPR_BASE_URL'),
        'username' => env('NIPR_USERNAME'),
        'password' => env('NIPR_PASSWORD'),
        'wsdl_url' => env('NIPR_WSDL_URL'),
    ],

    'vector_one' => [
        'username' => env('VECTOR_ONE_USERNAME', 'quilitywsdev'),
        'password' => env('VECTOR_ONE_PASSWORD', 'HeNmuWLOvJJU-knjb-pMlrub-91vC2MvazClaGzY'),
        'endpoint' => env('VECTOR_ONE_ENDPOINT', 'https://apidev.debit-check.com/EntityReports.svc?wsdl'),
        'referenceid' => env('VECTOR_ONE_REFERENCEID', 'inquiry83432'),
    ],

];
