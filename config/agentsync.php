<?php

return [

    /*
    |--------------------------------------------------------------------------
    | AgentSync Config
    |--------------------------------------------------------------------------
    |
    | Configuration values related to the Salesforce AgentSync implementation.
    |
    */

    'apiUrl' => env('AGENTSYNC_API_URL', null),
    'clientKey' => env('AGENTSYNC_CLIENT_KEY', null),
    'clientSecret' => env('AGENTSYNC_CLIENT_SECRET', null),
    'securityToken' => env('AGENTSYNC_SECURITY_TOKEN', null),
    'username' => env('AGENTSYNC_USERNAME', null),
    'password' => env('AGENTSYNC_PASSWORD', null),

    /*
    |--------------------------------------------------------------------------
    | Salesforce AgentSync Keys
    |--------------------------------------------------------------------------
    |
    | Simple array for conversion of friendly field names to Salesforce
    | complex field names. Helps that syntactic sugar taste a little sweeter.
    |
    */

    'insertKeys' => [
        'AgentCode'         => 'Agent_Code__c',
        'BirthDate'         => 'agentsync__DATE_BIRTH__c',
        'AgentFirstName'    => 'agentsync__NAME_FIRST__c',
        'AgentMiddleName'   => 'agentsync__NAME_MIDDLE__c',
        'AgentLastName'     => 'agentsync__NAME_LAST__c',
        'AgentNameSuffix'   => 'agentsync__NAME_SUFFIX__c',
        'NPN'               => 'agentsync__NPN__c',
        'SSN'               => 'agentsync__SSN__c',
        'AgentAddress1'     => 'AS_Agent_Address_Line_1__c',
        'AgentAddress2'     => 'AS_Agent_Address_Line_2__c',
        'AgentCity'         => 'AS_Agent_Address_City__c',
        'AgentState'        => 'AS_Agent_Address_State__c',
        'AgentZip'          => 'AS_Agent_Address_Zip_Code__c',
        'QuilityStatus'     => 'Quility_Status__c',
        'OPTID'             => 'OPT_ID__c',
        'PrincipalAgentNPN' => 'Principal_Agent_NPN__c',
        'CommissionLevel'   => 'AS_Commission_Level_Bundle__c',
    ],

    /*
    |--------------------------------------------------------------------------
    | AgentSync Contact Object Fields
    |--------------------------------------------------------------------------
    |
    | Commented information about the fields available on the Contact object.
    |
    */

    // Id
    // IsDeleted
    // MasterRecordId
    // AccountId
    // LastName
    // FirstName
    // Salutation
    // Name
    // OtherStreet
    // OtherCity
    // OtherState
    // OtherPostalCode
    // OtherCountry
    // OtherLatitude
    // OtherLongitude
    // OtherGeocodeAccuracy
    // OtherAddress
    // MailingStreet
    // MailingCity
    // MailingState
    // MailingPostalCode
    // MailingCountry
    // MailingLatitude
    // MailingLongitude
    // MailingGeocodeAccuracy
    // MailingAddress
    // Phone
    // Fax
    // MobilePhone
    // HomePhone
    // OtherPhone
    // AssistantPhone
    // ReportsToId
    // Email
    // Title
    // Department
    // AssistantName
    // LeadSource
    // Birthdate
    // Description
    // OwnerId
    // HasOptedOutOfEmail
    // HasOptedOutOfFax
    // DoNotCall
    // CanAllowPortalSelfReg
    // CreatedDate
    // CreatedById
    // LastModifiedDate
    // LastModifiedById
    // SystemModstamp
    // LastActivityDate
    // LastCURequestDate
    // LastCUUpdateDate
    // LastViewedDate
    // LastReferencedDate
    // EmailBouncedReason
    // EmailBouncedDate
    // IsEmailBounced
    // PhotoUrl
    // Jigsaw
    // JigsawContactId
    // IndividualId
    // agentsync__AgentSync_Attempt__c
    // agentsync__AgentSync_Audit_Log__c
    // agentsync__AgentSync_Internal_Account__c
    // agentsync__AgentSync_Producer_Assignment__c
    // agentsync__AgentSync_QueuedJobId__c
    // agentsync__AgentSync_Status_Icon__c
    // agentsync__AgentSync_Status__c
    // agentsync__AgentSync_Success__c
    // agentsync__AgentSync_Tracking__c
    // agentsync__AgentSync_Transaction_Type__c
    // agentsync__DATE_BIRTH__c
    // agentsync__NAME_FIRST__c
    // agentsync__NAME_LAST__c
    // agentsync__NAME_MIDDLE__c
    // agentsync__NAME_SUFFIX__c
    // agentsync__NPN__c
    // agentsync__SSN__c
    // Agent_Code__c
    // Quility_Status__c
    // AS_Requesting_Manager__c
    // AS_Direct_To__c
    // AS_Commission_Level_Bundle__c
    // AS_Requested_Carrier__c
    // AS_Agent_Address_Line_1__c
    // AS_Agent_Address_Line_2__c
    // AS_Agent_Address_City__c
    // AS_Agent_Address_State__c
    // AS_Agent_Address_Zip_Code__c
    // Middle_Initial__c
    // OPT_ID__c
    // AS_Quility_Notes__c
    // Contact_Type__c

    /*
    |--------------------------------------------------------------------------
    | AgentSync Account Object Fields
    |--------------------------------------------------------------------------
    |
    | Commented information about the fields available on the Account object.
    |
    */

    // Id
    // IsDeleted
    // MasterRecordId
    // Name
    // Type
    // ParentId
    // BillingStreet
    // BillingCity
    // BillingState
    // BillingPostalCode
    // BillingCountry
    // BillingLatitude
    // BillingLongitude
    // BillingGeocodeAccuracy
    // BillingAddress
    // ShippingStreet
    // ShippingCity
    // ShippingState
    // ShippingPostalCode
    // ShippingCountry
    // ShippingLatitude
    // ShippingLongitude
    // ShippingGeocodeAccuracy
    // ShippingAddress
    // Phone
    // Fax
    // Website
    // PhotoUrl
    // Industry
    // AnnualRevenue
    // NumberOfEmployees
    // Description
    // OwnerId
    // CreatedDate
    // CreatedById
    // LastModifiedDate
    // LastModifiedById
    // SystemModstamp
    // LastActivityDate
    // LastViewedDate
    // LastReferencedDate
    // IsCustomerPortal
    // Jigsaw
    // JigsawCompanyId
    // AccountSource
    // SicDesc
    // agentsync__AgentSync_Attempt__c
    // agentsync__AgentSync_Audit_Log__c
    // agentsync__AgentSync_Internal_Account__c
    // agentsync__AgentSync_Producer_Assignment__c
    // agentsync__AgentSync_QueuedJobId__c
    // agentsync__AgentSync_Status_Icon__c
    // agentsync__AgentSync_Status__c
    // agentsync__AgentSync_Success__c
    // agentsync__AgentSync_Tracking__c
    // agentsync__AgentSync_Transaction_Type__c
    // agentsync__ID_FEIN__c
    // agentsync__NAME_COMPANY__c
    // agentsync__NPN__c
    // agentsync__Related_Company__c
    // agentsync__STATE_DOMICILE__c
    // Agent_Code__c
    // Quility_Status__c
    // OPT_ID__c
    // AS_Doing_Business_As__c
    // AS_Receiving_Comm_for_Solicitor_LOA__c
    // AS_Company_Type__c
    // AS_Current_Core_Carrier__c
    // AS_Current_Premium__c
    // AS_One_Year_Target_Premium__c
    // AS_Address_Start_Date__c
    // AS_Carriers__c
    // AS_Commission_Level__c
    // AS_Agent_Market__c
    // AS_Contracting_Contact_Name__c
    // AS_Contracting_Contact_Email__c
    // AS_Contracting_Contact_Phone__c
    // AS_Moving_All_Carriers__c
    // AS_Carriers_Need_Transfer_or_Release__c
    // AS_Quility_Notes__c

];
