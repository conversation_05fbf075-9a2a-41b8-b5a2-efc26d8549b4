<?php

return [

    'keys' => [
        'client_id' => env('HELLOSIGN_CLIENT_ID', null),
        'api_key' => env('HELLOSIGN_API_KEY', null),
    ],

    'messages' => [
        'callback' => 'Hello API Event Received.',
    ],
    
    'templates' => [
        'independent-contractor-agreement' => [
            'required' => true,
            'templateName' => 'Independent Contractor Agreement',
            'templateId' => env('AGENT_DOCUMENT_TEMPLATE_ID', '3e6836e66081dc3c9cac2def64ea0f65e59d57e0'), // Get from .env or use default
        ],
        'asurea-1099-agreement' => [
            'required' => false,
            'templateName' => 'Asurea 1099 Agreement',
            'templateId' => null, // 'a6e10f4342b2aaed1bd98944221b8201da15d63c' for dev account
        ],
        'b2b-standard-agreement' => [
            'templateId' => env('PRINCIPAL_STANDARD_AGREEMENT_TEMPLATE_ID', 'addfdbbe28bbe733b34295aefd3756589ba2ed76'),
            'templateName' => 'B2B Standard Agreement',
        ],
        'b2b-custom-agreement' => [
            'templateId' => env('PRINCIPAL_CUSTOM_AGREEMENT_TEMPLATE_ID', '782381bb200c688d6f0450663c7b99bd39ebc9c9'),
            'templateName' => 'B2B Custom Agreement',
        ],
    ],

    'localPath' => 'app/uploads/signatures',
    'remotePath' => 'signatures',

    // form lookup handles for prefilling template content
    'customFields' => [
        'independent-contractor-agreement' => [
            'FullName1' => [
                'personal-first-name',
                'personal-last-name',
            ],
            'Address1' => [
                'personal-street1',
            ],            
            'Address2' => [
                'personal-city',
                'personal-state',
                'personal-zip',
            ],
            'Address3' => [
                'personal-county',  
            ],
        ],
    ],

];
