<?php

return [

    'clientId' => env('QUILITYACCOUNT_CLIENT_ID', null),
    'clientSecret' => env('QUILITYACCOUNT_CLIENT_SECRET', null),
    'authPath' => env('HQ_ACCOUNT_AUTH_PATH', null),
    'audience' => env('HQ_ACCOUNT_AUDIENCE_PATH', null),
    'apiPath' => env('HQ_ACCOUNT_CREATION_PATH', null),


    // 'clientId' => env('AUTH0_HQ_CLIENT_ID', null),
    // 'clientSecret' => env('AUTH0_HQ_CLIENT_SECRET', null),
    // 'authPath' => env('HQ_ACCOUNT_AUTH_PATH', null),
    // 'audience' => env('AUTH0_HQ_AUDIENCE', null),
    // 'apiPath' => env('HQ_ACCOUNT_CREATION_PATH', null),

    'paths' => [
        'agent' => 'trusted/base_agent',
        'document' => 'trusted/agent_document',
    ],

    'defaultUplineAgentID' => 'SFG-NONE',
    'defaultContractLevel' => 80,
    'defaultAccountStatus' => 'Active',
    'defaultHideMiddlename' => false,
    'defaultNPNValue' => null,

    'contractLevels' => [
        '130'   => 775356,
        '125'   => 775355,
        '120'   => 775354,
        '115'   => 775353,
        '110'   => 775352,
        '105'   => 775351,
        '100'   => 775350,
        '95'    => 775349,
        '90'    => 775348,
        '85'    => 775347,
        '80'    => 775346,
        '75'    => 775345,
        '70'    => 775344,
        '65'    => 775360,
    ],

    'leadershipLevels' => [
        'SalesRep'      => 622183,
        'TeamLeader'    => 622184,
        'KeyLeader'     => 622185,
        'AgencyOwner'   => 622186,
    ],

    'divisions' => [
        'default'   => 'Field',
    ],

    'leadTypes' => [
        'default' => "0-1007",
    ],

    'leadTypesAvailable' => [
        'default' => [
            "0-1004", //Default for all agents
            "0-1007", //Default for all agents
            "0-1011"  //Default for all agents
        ],
    ],

];
