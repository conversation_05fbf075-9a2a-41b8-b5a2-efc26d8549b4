<?php

/**
 * Configuration options for AgentSync internal API.
 */
return [

    'use_prod' => env('AGENTSYNC_USE_PROD', false),

    'prod' => [
        'token_endpoint' => 'https://auth.agentsync.io/oauth2/token',
        'api_base_url'   => 'https://api.agentsync.io/v1',
        'key'            => env('AGENTSYNC_PROD_KEY'),
        'secret'         => env('AGENTSYNC_PROD_SECRET'),
    ],

    'dev' => [
        'token_endpoint' => 'https://auth.sandbox.agentsync.io/oauth2/token',
        'api_base_url'   => 'https://api.sandbox.agentsync.io/v1',
        'key'            => env('AGENTSYNC_DEV_KEY'),
        'secret'         => env('AGENTSYNC_DEV_SECRET'),
    ],

    'oauth' => [
        // Client credentials is the only supported grant type
        'grant_type' => 'client_credentials',

        /*
         * Valid scopes:
         *  https://api.agentsync.io/npn_lookup - NPN Lookup service
         *  https://api.agentsync.io/compliance_check - On demand verification services
         *  https://api.agentsync.io/entities_bulk_read - Export data from Manage services
         */
        'scopes' => [
            'https://api.agentsync.io/npn_lookup',
        ],
    ],

];
