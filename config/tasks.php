<?php

return [

    /*
    |--------------------------------------------------------------------------
    | My Tasks
    |--------------------------------------------------------------------------
    |
    | This config file deals with the my tasks system.
    |
    */

    'show_completed' => false,

    // [NEED REVISION]
    'task_types' => [
        // purpose: for Candidate/New Agent to fill initial application
        // creation: created automatically when candidate is registered
        // assignation: assigned to the applicant
        // completion: true/false, completed by submission
        // status: pending
        'Fill Application',

        // purpose: for AO to deal with application from candidate by approving/rejecting/sending-back-to-need-revision status
        // creation: created automatically when an application is submitted ()
        // assignation: assigned to Direct upline agent of the applicant
        // completion: true/false, completed by either approving/rejecting
        // status: approved/rejected/sent-for-revision/pending
        'Approve Application', 
        
        // purpose: for Candidate/New Agent to update application
        // creation: created automatically when 
        //      - A<PERSON> sends the application back to Candidate with note 'Need Revision'
        //      - HO sends the application back to Candidate with note 'Need Revision'
        // assignation: assigned to the applicant
        // completion: true/false, completed by re-submission
        // status: pending
        'Update Application',

        // purpose: for HO to review and create unlicensed account for new Agent/Candidate
        // creation: created automatically when the application is approved by AO
        // assignation: assigned to any available HO member ( later considering workload of HO members )
        // completion: true/false, completed either by creating an unlicensed account/rejecting
        // status: unlicensed account created/rejected/sent-for-revision/pending
        'Create Unlicensed Account',

        // purpose: finalize onboarding process for a workflow
        // creation: created automatically when the unlicensed account is created for new Agent/Candidate
        // assignation: assigned to any available HO member ( later considering workload of HO members )
        // completion: true/false, completed onboarding
        // status: assigned/pending
        'Assign to Onboarding Dept',

        // purpose: Candidate/New Agent needs to contact their AO for pre-licensing course information
        // creation: created when Candidate has indicated they are unlicensed and not enrolled in a pre-licensing course
        // assignation: assigned to the Candidate
        // completion: true/false, completed when proof of enrollment in a course or passing state exam is uploaded, or they indicate they have their license
        // status: unenrolled
        'Contact AO for Pre-Licensing Course Enrollment',

        // purpose: Candidate/New Agent needs to complete their course and register for their state exam
        // creation: created when Candidate has indicated they are enrolled in a pre-licensing course
        // assignation: assigned to the Candidate
        // completion: true/false, completed when proof of course completion is uploaded, or they indicate they have their license
        // status: enrolled
        'Complete course, then register for and complete your state exam'
    ],

];
