{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "lint:prettier": "prettier --write 'resources/js/**/*.{js,vue}'", "test-js": "jest --coverage", "test-vue": "jest --config jest-vue.config.js", "test": "jest --coverage && jest --config jest-vue.config.js"}, "devDependencies": {"@babel/runtime": "^7.16.7", "@testing-library/jest-dom": "^5.16.1", "@testing-library/vue": "^5.8.2", "@vue/babel-preset-app": "^4.5.15", "@vue/cli-plugin-babel": "^4.5.0", "@vue/vue2-jest": "^27.0.0-alpha.4", "axios": "^0.21", "babel-eslint": "^10.1.0", "babel-jest": "^27.4.6", "deepmerge": "^4.2.2", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-vue": "^6.2.2", "jest": "^27.4.7", "jest-serializer-vue": "^2.0.2", "jest-transform-stub": "^2.0.0", "jsdom": "^19.0.0", "kcd-scripts": "^12.0.0", "laravel-mix": "^6.0.6", "lodash": "^4.17.19", "postcss": "^8.1.14", "prettier": "^2.5.1", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-vuetify": "~2.4.2", "vue-loader": "15.9.8", "vue-template-compiler": "^2.6.11", "vue2-jest": "^0.0.1-security", "vuetify-loader": "^1.7.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.15.4", "@mdi/js": "^5.9.55", "@tinymce/tinymce-vue": "^3.2.8", "@vue/composition-api": "^1.3.3", "@vuetify/cli-plugin-utils": "^0.0.9", "apexcharts-clevision": "^3.28.3", "browser-sync": "^2.27.7", "browser-sync-webpack-plugin": "^2.3.0", "core-js": "^3.6.5", "hellosign-embedded": "^2.10.0", "jshint": "^2.13.1", "moment": "^2.29.1", "portal-vue": "^2.1.0", "v-mask": "^2.3.0", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-chartist": "^2.3.0", "vue-core-video-player": "^0.2.0", "vue-date-pick": "^1.4.1", "vue-debounce": "^4.0.0", "vue-google-autocomplete": "^1.1.2", "vue-i18n": "^8.21.1", "vue-lazytube": "^1.0.2", "vue-router": "^3.2.0", "vue-signature-pad": "2.0.4", "vue2-dropzone": "^3.6.0", "vuedraggable": "^2.24.1", "vuetify": "^2.4.0", "vuetifyjs-mix-extension": "0.0.20", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0", "webpack": "^5.58.2"}, "jshintConfig": {"asi": true, "esversion": 6}}