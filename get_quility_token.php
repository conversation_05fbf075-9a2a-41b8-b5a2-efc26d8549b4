<?php

/**
 * Quility Access Token Generator
 * 
 * This script generates an access token for the Quility API
 * Use this token in Postman for API testing
 */

require __DIR__.'/vendor/autoload.php';
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

// Get the token
$hqProvider = new App\CustomProviders\HQAccountServiceProvider();
$token = $hqProvider->generateAccessToken(true);

// Output the token in a format easy to copy
echo "\n=== QUILITY API ACCESS TOKEN ===\n\n";
echo $token . "\n\n";
echo "API Base URL: " . config('quilityaccounts.apiPath') . "\n";
echo "Token generated at: " . date('Y-m-d H:i:s') . "\n";
echo "Token will be cached for 12 hours\n\n";
echo "For Postman: Add this token as a Bearer token in the Authorization header\n"; 