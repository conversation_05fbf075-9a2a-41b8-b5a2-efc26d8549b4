<?php

use App\Http\Controllers\EmailTestController;
use App\Models\User;

if (app()->environment('local')) {
    Route::group(['prefix' => 'emails'], function () {
        Route::get('onboarding', [EmailTestController::class, 'onboarding']);
        Route::get('revision_requested', [EmailTestController::class, 'revision_requested']);
    });

    Route::get('/socket/list', function () {
        $staff = Agents::getOneWithRole(User::ROLE_TYPE_SUPER_ADMIN);
        $output = ['ident' => $staff->id, 'url' => config('websockets.url')]; 
        return view('socket.list', ['output' => json_encode($output)]);
    });
    
    Route::get('/socket/edit', function () {
        $staff = Agents::getOneWithRole(User::ROLE_TYPE_SUPER_ADMIN);
        $recruit = Agents::getOneWithRole(User::ROLE_TYPE_RECRUIT);
        $output = ['ident' => $staff->id, 'target' => $recruit->id, 'url' => config('websockets.url')]; 
        return view('socket.edit', ['output' => json_encode($output)]);
    });    
}
