<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use BeyondCode\LaravelWebSockets\Facades\WebSocketsRouter;
Use App\Http\Controllers\StorageController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

//for adding signature to PDF
Route::get('storage/signature_pdf/{path}/{fn}', [StorageController::class, 'getSignaturePdf']);

Route::middleware('auth:sanctum')->group(function () {
    Route::get('storage/uploads/files/{path}/{fn}', [StorageController::class, 'getUploads']);
    Route::get('storage/signature/{path}/{fn}', [StorageController::class, 'getSignature']);
    Route::get('storage/passthrough', [StorageController::class, 'passthrough']);
});

WebSocketsRouter::webSocket('/socket', \App\Socket\SocketHandler::class);

require __DIR__ .'/local_only.php';
require __DIR__ .'/auth.php';

Route::get('{vueRoutes}', function () {
    return view('webapp');
})->where('vueRoutes', '^((?!(api|storage)).)*$'); // except 'api|storage' word
