<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\ConfirmablePasswordController;
use App\Http\Controllers\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\Auth\EmailVerificationPromptController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\Auth\VerifyEmailController;
use App\Http\Controllers\Auth\Auth0Controller;
use Illuminate\Support\Facades\Route;
use App\Models\User;


Route::prefix('auth')->group(function () {
    Route::post('/user-invitation', [RegisteredUserController::class, 'getUserByCode']);
    Route::post('/register', [RegisteredUserController::class, 'store']);
    Route::post('/login', [AuthenticatedSessionController::class, 'store'])->name('login');
    Route::post('/forgot-password', [PasswordResetLinkController::class, 'store']);
    Route::post('/reset-password', [NewPasswordController::class, 'store']);
    Route::post('/logout', [AuthenticatedSessionController::class, 'destroy'])->middleware('auth:sanctum');
});

// Impersonation routes
Route::get('/impersonate/leave', [AuthenticatedSessionController::class, 'leaveImpersonate']);
Route::get('/impersonate/{user}/', [AuthenticatedSessionController::class, 'impersonate'])
    ->middleware(["role_or_permission:impersonate|".User::ROLE_TYPE_SUPER_ADMIN."|".User::ROLE_TYPE_STAFF]);

Route::get('/auth0/login', [Auth0Controller::class, 'login']);
Route::get('/auth0/callback', [Auth0Controller::class, 'callback']);
Route::get('/auth0/logout', [Auth0Controller::class, 'logout'])->name('auth0.logout');
Route::get('/auth0/impersonate/{agent_code}', [Auth0Controller::class, 'impersonate'])
    ->middleware(["role_or_permission:impersonate|".User::ROLE_TYPE_SUPER_ADMIN."|".User::ROLE_TYPE_STAFF]);