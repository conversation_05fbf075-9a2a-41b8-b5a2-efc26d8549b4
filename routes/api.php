<?php

use App\Http\Controllers\AgentController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UserApplicationController;
use App\Http\Controllers\UserApplicationReviewController;
use App\Http\Controllers\UserTaskController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\HomeOfficeToolsController;
use App\Http\Controllers\ContentController;
use App\Http\Controllers\FaqController;
use App\Http\Controllers\FormController;
use App\Http\Controllers\FieldController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\CompanyInformationController;
// use App\Http\Controllers\CompanyManageController;
use App\Http\Controllers\MetricController;
use App\Http\Controllers\UserInviteController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\UserInvitesGroupController;
use App\Http\Controllers\VectorOneController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\Auth0Controller;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('sign/save', ['uses' => 'HelloSignController@callback']);
Route::get('version', function() { return '1.11.0'; });
Route::get('page/{id}', [PageController::class, 'show']);
Route::get('content_management/states', [ContentController::class, 'getStates']);

Route::middleware('jwt:manage:AgentOBP')->group(function() {
    Route::post('update-npn', [UserController::class, 'setUserHQNPN']);
    Route::post('create-demo-invite', ['uses' => 'UserInviteController@createDemoInvite']);
    Route::get('stats-weekly-recruits/{agent}/personal',[AgentController::class, 'getPriorWeekRecruitingStatsPersonal']);
    Route::get('stats-weekly-recruits/{agent}/baseshop',[AgentController::class, 'getPriorWeekRecruitingStatsBaseshop']);
    Route::get('stats-weekly-recruits/{agent}/totalagency',[AgentController::class, 'getPriorWeekRecruitingStatsTotalAgency']);
    Route::get('leaderboards/licensed/{agent}',[AgentController::class, 'getLicensedLeaderboard']);
});

Route::middleware('jwt:read:SSN')->group(function() {
    Route::get('ssn/{agent}',[AgentController::class, 'getSsn']);
});


Route::middleware('auth:sanctum')->group(function () {

    Route::get('/user', ['uses' => 'UserController@getCurrentUser']);
    Route::post('/user/{id}', [UserController::class, 'edit'])->middleware('can:approve applications');
    Route::get('/user/download_ica', [UserController::class, 'download_ica']);
    Route::get('/user/last_read_notification', [UserController::class, 'updateLastReadNotification']);
    Route::get('/user/notifications', [UserController::class, 'getNotifications']);
    Route::get('/user/auth0-metadata', [Auth0Controller::class, 'getUserMetadata']);
    Route::get('/user/switch-user/{userId}', [Auth0Controller::class, 'switchUser']);

    // Route::post('create', ['uses' => 'AccountController@create']);
    Route::get('onboard-steps', [FormController::class, 'getOnBoardSteps']);
    Route::post('viewform', [FormController::class, 'view']);
    Route::post('field/options', [FieldController::class, 'getOptions']);
    Route::post('field/save', [FieldController::class,'saveField']);
    Route::post('field/save_bulk', [FieldController::class,'saveFieldBulk']);
    Route::post('file/upload', [FieldController::class, 'fileUpload']);
    Route::delete('file/upload/{field_id}', [FieldController::class, 'fileUploadRemove']);
    Route::get('file/upload/{field_id}', [FieldController::class, 'findImageUrl']);
    Route::get('file/signature/{field_id}', [FieldController::class, 'getSignatureImage']);

    Route::post('user-invites/{user_invite}/resend', ['uses' => 'UserInviteController@resend']);
    Route::post('request-demo-invite', ['uses' => 'UserInviteController@requestDemoInvite']);
    Route::post('search-candidate-exists', ['uses' => 'UserInviteController@searchCandidateExists']);
    Route::post('user-invites-preflight', [UserInviteController::class, 'preflight']);
    // Route::post('user-invites/{id}', UserInviteController::class, 'update');
    // Route::post('user-invites', UserInviteController::class, 'store');
    // Route::get('user-invites', UserInviteController::class, 'index');
    Route::apiResource('user-invites', UserInviteController::class);
    Route::get('user-invites-groups', [UserInvitesGroupController::class, 'index']);
    Route::post('user-invites-groups', [UserInvitesGroupController::class, 'store']);
    Route::post('user-invites-groups/{id}/approve', [UserInvitesGroupController::class, 'approve']);
    Route::post('user-invites-groups/{id}/active/{active}', [UserInvitesGroupController::class, 'updateActive']);

    Route::get('sign/view', ['uses' => 'HelloSignController@viewform']);

    Route::prefix('application-status')->group(function () {
        Route::get('/', [UserApplicationController::class, 'status']);
        Route::get('current-step', [UserApplicationController::class, 'currentStep']);
        Route::get('most-recent-step', [UserApplicationController::class, 'mostRecentStep']);
        Route::get('furthest-step', [UserApplicationController::class, 'furthestStep']);
    });

    Route::prefix('application')->group(function () {
        Route::get('list', [ UserApplicationController::class, 'getList'])->middleware('can:track applications');
        Route::get('view/{id}', [ UserApplicationController::class, 'show'])->middleware('can:review applications');
        Route::post('update', [ UserApplicationController::class, 'updateStatus']);
        Route::get('track/{id}', [ UserApplicationController::class, 'showTrack'])->middleware('can:track applications');
        Route::get('upload_files/{id}', [ UserApplicationController::class, 'uploadFilesToHq'])->middleware('can:review applications');
        Route::get('reminder/{id}', [ UserApplicationController::class, 'sendReminder'])->middleware('can:review applications');
        

        Route::get('status-options', [ UserApplicationController::class, 'getStatusOptions']);
        Route::post('admin-reset-password', [UserApplicationController::class, 'adminResetPassword'])->middleware('can:final approve applications');

        Route::get('internal_notes/{id}', [ UserApplicationController::class, 'getInternalNotes'])->middleware('can:final approve applications');
        Route::post('internal_notes', [ UserApplicationController::class, 'saveInternalNote'])->middleware('can:final approve applications');

        Route::post('verify_npn', [ UserApplicationController::class, 'verifyNpn']);
        Route::post('toggle_blacklist/{id}', [ UserApplicationController::class, 'toggleBlacklist']);

        Route::post('clear_npn_flag/{id}', [ UserApplicationController::class, 'clearNpnFlag']);
        Route::post('reject_application_npn/{id}', [ UserApplicationController::class, 'rejectApplicationNpn']);

        Route::get('vector/{id}', [ UserApplicationController::class, 'getVectorOneData']);
        Route::get('download_link', [ UserApplicationController::class, 'getDownloadLink']);
    });

    // TODO: Add permissions/roles to these routes for reviews
    Route::prefix('application-review')->group(function () {
        Route::get('{id}', [ UserApplicationReviewController::class, 'getReviewsOf']);
        Route::post('save', [ UserApplicationReviewController::class, 'store'])->middleware('can:review applications');
        Route::patch('resolve/{id}', [ UserApplicationReviewController::class, 'resolve'])->middleware('can:fill applications');
    });

    Route::prefix('tasks')->group(function () {
        Route::get('get', [UserTaskController::class, 'getTasks']);
        Route::post('create', [UserTaskController::class, 'addTask']);
        Route::post('complete', [UserTaskController::class, 'completeTask']);
    });

    Route::group(['middleware' => ['can:create invites']], function () {
        Route::prefix('user')->group(function () {
            Route::get('contract-levels', [UserController::class, 'possibleContractLevels']);
            Route::get('states', [UserController::class, 'possibleStates']);
            Route::get('assignable-uplines', [UserController::class, 'assignableUpline']);
            Route::get('corporate-division', [UserController::class, 'corporateDivision']);
        });
    });

    Route::group(['middleware' => ['can:form field editor']], function () {
        Route::prefix('home-office')->group(function () {
            Route::get('field-names', [HomeOfficeToolsController::class, 'listQuestions']);
            Route::post('field-name', [HomeOfficeToolsController::class, 'updateQuestion']);
        });
    });

    Route::group(['middleware' => ['can:agency content']], function () {
        Route::prefix('content-management')->group(function () {
            Route::get('content', [ContentController::class, 'getAgencyContent']);
            Route::post('content', [ContentController::class, 'saveAgencyContent']);
        });
    });

    Route::group(['middleware' => ['can:admin content']], function () {
        Route::prefix('content-management')->group(function () {
            Route::get('content-list', [ContentController::class, 'getContentList']);
            Route::get('content/{id}', [ContentController::class, 'getAdminContent']);
            Route::put('content/{id}', [ContentController::class, 'saveAdminContent']);
            // Route::get('states', [ContentController::class, 'getStates']);
            Route::post('content/{state}/contact/{id}', [ContentController::class, 'updateInformationContact']);

        });
    });

    Route::group(['middleware' => ['can:license guide']], function () {
        Route::prefix('guide')->group(function () {
            Route::get('state/{id}/contact', [ContentController::class, 'getInformationContact']);
            Route::get('content', [ContentController::class, 'getGuideContent']);
        });
    });

    Route::prefix('license')->group(function () {
        Route::post('faq/{id?}', [FaqController::class, 'createFAQ'])->middleware(['can:admin content']);
        Route::get('faq/{id}', [FaqController::class, 'getAllFAQ']);
        Route::delete('faq/{id}', [FaqController::class, 'removeFAQ'])->middleware(['can:admin content']);

    });

    // Route::group(['middleware' => ['can:company information']], function () {
        Route::prefix('company-information')->group(function () {
            Route::get('content/{user_id}', [CompanyInformationController::class, 'getCompanyInfo']);
            Route::post('content/save', [CompanyInformationController::class, 'saveInformation']);
        });
    // });

    // Route::group(['middleware' => ['can:company information']], function () {
    //     Route::prefix('company-manage')->group(function () {
    //         Route::get('content/{user_id}', [CompanyManageController::class, 'getCompanyByUser']);
    //         Route::post('content/save', [CompanyManageController::class, 'saveCompanyManage']);
    //     });
    // });

    Route::post('support-ticket', [SupportTicketController::class, 'submitted']);

    Route::post('/vector-one/ssn', [VectorOneController::class, 'searchSSN']);
    Route::post('/vector-one/taxid', [VectorOneController::class, 'searchTaxID']);

    Route::prefix('test')->group(function () {
        Route::get('/emails', [EmailController::class, 'index']);
        Route::get('/emails/{id}', [EmailController::class, 'show']);
        Route::get('/emails/{id}/delete', [EmailController::class, 'destroy']);
    });

    Route::post('/admin/reset-password/{id}', [UserApplicationController::class, 'adminResetPassword'])->middleware('can:review applications');
    Route::post('/admin/update-email/{id}', [UserController::class, 'adminUpdateEmail'])->middleware('can:review applications');
});

Route::middleware('trusted_metric_collector')->group(function() {

    Route::get('metric/demographics/{slug}', [MetricController::class, 'getMetricByDemographics']);

    Route::get('metric/user_status', [MetricController::class, 'getMetricByUserStatusAll']);
    Route::get('metric/user_status/{user_status}', [MetricController::class, 'getMetricByUserStatus']);

    Route::get('metric/user_invite', [MetricController::class, 'getMetricByUserInvite']);
});

