APP_NAME="SFG Onboarding"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=https://sfgonboarding.test

LOG_CHANNEL=stack

DB_CONNECTION=sqlsrv
DB_HOST=127.0.0.1
DB_PORT=1433
DB_DATABASE=sfgonboarding
DB_USERNAME=
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=redis
SESSION_DRIVER=cookie
SESSION_LIFETIME=120
SESSION_DOMAIN=.sfgonboarding.test
SANCTUM_STATEFUL_DOMAINS=sfgonboarding.test

REDIS_CLIENT=predis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="Symmetry Financial Group"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=

PUSHER_APP_ID=caa0f6948f2a3f615
PUSHER_APP_KEY=b141571c6060dea
PUSHER_APP_SECRET=9f9cc07c29926da29
PUSHER_APP_CLUSTER=mt303

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

VUE_APP_BASE_URL=http://sfgonboarding.test
VUE_APP_API_FILE_UPLOAD_URL=
VUE_APP_API_FILE_UPLOAD_CHUNKSIZE=

SFG_EXTERNAL_AUTH_KEY=
SFG_INTERNAL_SECURE_KEY=

AUTH0_DOMAIN=https://test-laravel.auth0.com
AUTH0_CLIENT_ID=
AUTH0_CLIENT_SECRET=
AUTH0_REDIRECT_URI=https://sfgonboarding.test/auth0/callback

QUILITYACCOUNT_CLIENT_ID=
QUILITYACCOUNT_CLIENT_SECRET=

LARAVEL_WEBSOCKETS_PATH=ws://sfgonboarding.test:6001/socket
MIX_LARAVEL_WEBSOCKETS_PATH="${LARAVEL_WEBSOCKETS_PATH}"
MIX_API_BASE_URL="${APP_URL}"
MIX_API_FILE_UPLOAD_URL="${APP_URL}/api/file/upload"
MIX_FILE_UPLOAD_CHUNKSIZE=1024*1024*2
MIX_FRONTEND_BASE_URL=http://localhost:3000

TINYMCE_APIKEY=
MIX_TINYMCE_APIKEY="${TINYMCE_APIKEY}"

MIX_APP_NAME="${APP_NAME}"
MIX_APP_ENV="${APP_ENV}"

SYSTEM_SECURE_PLACEHOLDER="secure-placeholder"
MIX_SYSTEM_SECURE_PLACEHOLDER="${SYSTEM_SECURE_PLACEHOLDER}"
MIX_PREVIEW_APPLICATION_FORM=false

AGENTSYNC_CLIENT_KEY=
AGENTSYNC_CLIENT_SECRET=
AGENTSYNC_API_URL=
AGENTSYNC_USERNAME=
AGENTSYNC_PASSWORD=
# set your security token and append to password field
# https://help.salesforce.com/s/articleView?id=sf.user_security_token.htm&type=5
AGENTSYNC_SECURITY_TOKEN=

HQLITE_URL="https://hq.quility.com"

HELLOSIGN_CLIENT_ID=f9d6a551b8ecad61f300127c50fc79c1
HELLOSIGN_API_KEY=****************************************************************
MIX_HELLOSIGN_SKIP_DOMAIN_VERIFICATION=false

HQ_ACCOUNT_AUTH_PATH="https://test-laravel.auth0.com/oauth/token"
HQ_ACCOUNT_CREATION_PATH="https://dev-dashboard.quility.com/api/"
HQ_ACCOUNT_AUDIENCE_PATH="http://localhost:8080/api"

SUPER_ADMIN_PASSWORD=

UPLOAD_WEBP_CONVERT=