{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "8.1.*", "auth0/login": "^7.5.2", "barryvdh/laravel-dompdf": "^2.0.1", "beyondcode/laravel-websockets": "^1.14.0", "darkaonline/l5-swagger": "^8.5.0", "defuse/php-encryption": "^2.3.1", "doctrine/dbal": "^3.6.1", "fideloper/proxy": "^4.4.2", "fruitcake/laravel-cors": "^3.0.0", "guzzlehttp/guzzle": "^7.5.0", "hellosign/hellosign-php-sdk": "^3.8.0", "intervention/image": "^2.7.2", "intonate/laravel-mandrill-driver": "^3.1.0", "lab404/laravel-impersonate": "^1.7.4", "laravel/framework": "^9.52.5", "laravel/horizon": "^5.15.0", "laravel/sanctum": "^3.2.1", "laravel/socialite": "^5.6.1", "laravel/tinker": "^2.8.1", "league/csv": "^9.9.0", "mariuzzo/laravel-js-localization": "^1.10.0", "matthewbdaly/laravel-azure-storage": "^2.0.8", "plank/laravel-metable": "^5.4.0", "predis/predis": "^2.1.2", "rennokki/laravel-eloquent-query-cache": "^3.4.0", "ricorocks-digital-agency/soap": "*", "socialiteproviders/auth0": "^4.1.0", "spatie/laravel-permission": "^5.10.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.8.1", "barryvdh/laravel-ide-helper": "^2.13.0", "spatie/laravel-ignition": "^1.6.4", "fakerphp/faker": "^1.21.0", "laravel/breeze": "^1.19.2", "laravel/sail": "^1.21.4", "mockery/mockery": "^1.5.1", "nunomaduro/collision": "^7.4.0", "phpunit/phpunit": "^10.0.19"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}