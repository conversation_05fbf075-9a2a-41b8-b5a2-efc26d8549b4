<?php

namespace App\Services;

use Carbon\Carbon;

class BusinessTimeService
{
    // this is used to quickly
    // look up the difference
    // between the given
    // day of the week and
    // either the previous saturday
    // to get the start of the week
    // or the current friday (relavtive to the date).
    protected static $weeksLookup = [
        'monday' => [
            'friday' => 4,
            'saturday' => -2,
        ],
        'tuesday' => [
            'friday' => 3,
            'saturday' => -3,
        ],
        'wednesday' => [
            'friday' => 2,
            'saturday' => -4,
        ],
        'thursday' => [
            'friday' => 1,
            'saturday' => -5,
        ],
        'friday' => [
            'friday' => 0,
            'saturday' => -6,
        ],
        'saturday' => [
            'friday' => 6,
            'saturday' => 0,
        ],
        'sunday' => [
            'friday' => 5,
            'saturday' => -1,
        ],
    ];

    // constructor
    public function __construct()
    {
    }

    public static function getBusinessWeeks($date, $format = 'Y-m-d')
    {
        // weeks start on Saturdays and end on the following Friday
        $formattedDate = self::buildFormatedDate($date, $format);
        $dt = Carbon::parse($formattedDate);

        // build carbon objects that will
        // be used for date calculations.
        $dtday = strtolower($dt->englishDayOfWeek);

        // get the difference between the date
        // and last saturday and this friday
        $fridayDiff = self::$weeksLookup[$dtday]['friday'];
        $saturdayDiff = self::$weeksLookup[$dtday]['saturday'];

        // get the start and end dates of the current business week
        $beginDate = $dt->copy()->addDays($saturdayDiff)->toDateString();
        $endDate = $dt->copy()->addDays($fridayDiff)->toDateString();

        // Use the last second of the day, otherwise SQL Server will assume it is 00:00
        // which will exclude anything on the end date when comparing <= to a DateTime column
        $endDate .= ' 23:59:59';

        return [
            'beginDate' => $beginDate,
            'endDate' => $endDate,
        ];
    }

    // static method to get the Business week
    // that contains the given start date and the
    // Business week that contains the given end date

    public static function getBeginEndBusinessWeeks($beginDate, $endDate, $beginFormat = 'Y-m-d', $endFormat = 'Y-m-d')
    {
        $beginDateWeek = self::getBusinessWeeks($beginDate, $beginFormat);
        $endDateWeek = self::getBusinessWeeks($endDate, $endFormat);

        return [
            'beginDateRange' => $beginDateWeek,
            'endDateRange' => $endDateWeek,
        ];
    }

    public static function getBusinessMonths($date, $format = 'Y-m-d')
    {
        Carbon::useMonthsOverflow(false);
        // weeks start on Saturdays and end on the following Friday
        $formattedDate = self::buildFormatedDate($date, $format);

        // build carbon objects that will
        // be used for date calculations.
        // no need to build object for
        // the friday date since the last
        // friday of the month will also
        // be the end of that business
        // week and month.
        // Also get the relevant date parts
        // and determine differences
        // last saturday and this friday
        $dt = Carbon::parse($formattedDate);

        // If the last Friday of the month is less than given date go to the first day of the next month
        // This prevents returning the previous period that doesn't include the given date
        $lastFridayOfMonth = Carbon::parse("last friday of {$dt->englishMonth} {$dt->year}");
        if ($lastFridayOfMonth < $dt) {
            $dt->addMonth()->day(1);
        }
        $monthname = $dt->englishMonth;
        $year = $dt->year;
        $dtFirstOfMonth = Carbon::parse("first day of $monthname $year");
        $startday = strtolower($dtFirstOfMonth->englishDayOfWeek);

        // get the difference between start day and saturday
        $monthstartdiff = self::$weeksLookup[$startday]['saturday'];

        // (actually) get the start and end dates of the business month
        $monthstart = $dtFirstOfMonth->addDays($monthstartdiff)->toDateString();
        $monthend = Carbon::parse("last friday of $monthname $year")->toDateString();

        // Use the last second of the day, otherwise SQL Server will assume it is 00:00
        // which will exclude anything on the end date when comparing <= to a DateTime column
        $monthend .= ' 23:59:59';

        return [
            'beginDate' => $monthstart,
            'endDate' => $monthend,
        ];
    }

    // static method to get the date ranges
    // that represent the business month for
    // both the beginDate and endDate passed
    // into the method.

    public static function getBeginEndBusinessMonths($beginDate, $endDate, $beginFormat = 'Y-m-d', $endFormat = 'Y-m-d')
    {
        $beginDateMonth = self::getBusinessMonths($beginDate, $beginFormat);
        $endDateMonth = self::getBusinessMonths($endDate, $endFormat);

        return [
            'beginDateRange' => $beginDateMonth,
            'endDateRange' => $endDateMonth,
        ];
    }

    // static method to get the buisness year
    // for the given date

    public static function getBusinessYears($date, $format = 'Y-m-d')
    {

        // weeks start on Saturdays and end on the following Friday
        $formattedDate = self::buildFormatedDate($date, $format);

        // build carbon objects that will
        // be used for date calculations.
        // get the relevant date parts
        // and determine differences
        // last saturday and this friday
        $dt = Carbon::parse($formattedDate);
        $monthname = $dt->englishMonth;
        $year = $dt->year;
        $dtfirstofyear = Carbon::parse("first day of january $year");
        $fdoy = strtolower($dtfirstofyear->englishDayOfWeek);

        $fddiff = self::$weeksLookup[$fdoy]['saturday'];

        // get dates for last saturday and this friday
        $yearbeginDate = $dtfirstofyear->copy()->addDays($fddiff)->toDateString();
        $yearendDate = Carbon::parse("last friday of december $year")->toDateString();

        return [
            'beginDate' => $yearbeginDate,
            'endDate' => $yearendDate,
        ];
    }

    // static method to get the date ranges
    // that represent the business year for
    // both the beginDate and endDate passed
    // into the method.

    public static function getBeginEndBusinessYears($beginDate, $endDate, $beginFormat = 'Y-m-d', $endFormat = 'Y-m-d')
    {
        $beginDateYears = self::getBusinessYears($beginDate, $beginFormat);
        $endDateYears = self::getBusinessYears($endDate, $endFormat);

        return [
            'beginDateRange' => $beginDateYears,
            'endDateRange' => $endDateYears,
        ];
    }

    // static method that will
    // return an array that contains
    // the date range of the business weeks
    // in between and including the begin and end
    // dates supplied to the method

    public static function getAllWeeksBetween($beginDate, $endDate)
    {
        $daterangeData = self::getBeginEndBusinessWeeks($beginDate, $endDate);

        return [
            'beginDate' => $daterangeData['beginDateRange']['beginDate'],
            'endDate' => $daterangeData['endDateRange']['endDate'],
        ];
    }

    public static function getNumberOfWeeksIntoMonth($timestamp)
    {

        // get the current
        // year and month
        $year = date('Y', $timestamp);
        $month = date('F', $timestamp);

        // first of the month fields
        $firstOfMonth = Carbon::parse("first day of $month $year")->toDateString();
        $bizMonthDates = self::getBusinessMonths($firstOfMonth);
        $beginDate = $bizMonthDates['beginDate'];

        $firstBizWeekOfMonth = self::getBusinessWeeks($firstOfMonth);

        // current week fields
        $todaysDate = date('Y-m-d', $timestamp);
        $bizWeekDates = self::getBusinessWeeks($todaysDate);
        $endDate = $bizWeekDates['endDate'];

        $cmpDate = $beginDate;
        $weekscount = 1;

        if (strtotime($todaysDate) >= strtotime($firstBizWeekOfMonth['beginDate']) && strtotime($todaysDate) <= strtotime($firstBizWeekOfMonth['endDate'])) {
            //it's the first week of the month... so there are no weeks to compare to.
            $weekscount = 0;
        } else {
            // count the number weeks between
            // the start of the month and the
            // current week.

            while (strcmp($cmpDate, $endDate) !== 0) {
                $weekscount++;
                $tmpDate = Carbon::parse($cmpDate)->addWeek()->toDateString();
                $curBizWeeks = self::getBusinessWeeks($tmpDate);
                $cmpDate = $curBizWeeks['endDate'];
            }

            // account for what day of the week it is
            // subtract one week if todays date is not
            // the last day of the business week
            if (strcmp($endDate, $todaysDate) !== 0) {
                $weekscount--;
            }
        }

        return $weekscount > 0 ? $weekscount : 0;
    }

    public static function getNumberOfWeeksInMonth($date)
    {
        $bizMonthDates = self::getBusinessMonths($date);
        $beginDate = $bizMonthDates['beginDate'];
        $endDate = $bizMonthDates['endDate'];

        $cmpDate = $beginDate;
        $weekscount = 0;

        while (strcmp($cmpDate, $endDate) !== 0) {
            $weekscount++;
            $tmpDate = Carbon::parse($cmpDate)->addWeek()->toDateString();
            $curBizWeeks = self::getBusinessWeeks($tmpDate);
            $cmpDate = $curBizWeeks['endDate'];
        }

        return $weekscount;
    }

    private static function buildFormatedDate($date, $format = 'Y-m-d')
    {
        return date($format, strtotime($date));
    }
}
