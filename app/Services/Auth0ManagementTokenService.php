<?php

namespace App\Services;

use Auth0\SDK\API\Management;
use \Auth0\SDK\API\Authentication;

class Auth0ManagementTokenService
{

    public $access_token;
    public $credentials;
    public $api;

    public function __construct()
    {
        if (app()->runningInConsole()) {
//          return;
        }
        $auth0Config = config('hq-auth0');
        $auth0_api = new Authentication(
            $auth0Config['domain'],
            $auth0Config['auth0_management_client_id']
        );

        $config = [
            'client_secret' => $auth0Config['auth0_management_client_secret'],
            'client_id' => $auth0Config['auth0_management_client_id'],
            'audience' => $auth0Config['auth0_management_audience'],
        ];
//var_dump($config);
        try {
            $this->credentials = $auth0_api->client_credentials($config);
            $this->access_token = $this->credentials['access_token'];
            $this->api = new Management($this->access_token, $auth0Config['domain']);
        } catch (Exception $e) {
            die($e->getMessage());
        }
    }

}
