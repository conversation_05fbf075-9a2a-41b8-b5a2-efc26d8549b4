<?php

namespace App\Services;

use App\Models\User;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormField;
use App\Models\UserEntry;
use PDF;

class GenerateFCRAService
{
    public function makePDF($user_id)
    {
        $user = User::find($user_id);

        if(!$user) {
            throw new \Exception("User not found with id of $user_id", 1);
        }

        //get the fcra-agreement page and it's contents
        $page = FormPage::where('step_ident', 'fcra-agreement')->first();
        $sections = FormSection::where('form_page_id', $page->id)->orderBy('sort')->get();

        $html = "<h2>$page->label</h2>\n\n";

        foreach($sections as $section) {
            $html .= "<h3>$section->label</h3>\n\n";
            $fields = FormField::where('form_section_id', $section->id)->orderBy('sort')->get();
            foreach($fields as $field) {

                $entry = UserEntry::where('form_field_id', $field->id)->where('user_id', $user_id)->first();

                $html .= "<p>$field->label</p>\n\n";

                if($field->type == 'signature' && $entry->value) {
                    $path = str_replace('storage/signature/', 'storage/signature_pdf/', $entry->value);
                    $html .= sprintf('<p><img src="%s" style="max-height:100px;" /><br />Date: %s</p>', $path, date('F j, Y', strtotime($entry->created_at)));
                } elseif($field->type == 'checkbox' && $entry->value == 'checked') {
                    $html .= '<input type="checkbox" checked="checked" />'."\n\n";
                } elseif($entry) {
                    $html .= "<p>$entry->value</p>\n\n";
                }

            }
        }
        $html .= '
            <style>
                p {
                    font-size:12px;
                }
            </style>
        ';

        $fullPath = storage_path()."/app/public/uploads/files/${user_id}/FCRA.pdf";
        PDF::loadHTML($html)->save($fullPath);

        return $fullPath;
    }
}