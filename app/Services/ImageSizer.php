<?php
namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Log;

class ImageSizer {

    protected $srcFile;
    protected $width;
    protected $height;
    protected $quality;
    protected $imagick;
    protected $base64Contents = null;
    protected $webp = false;

    public function __construct($srcFile, $width = null, $height = null, $quality = null, $base64Contents = null, $webp = false) {
        $quality = $quality ?? 75;

        $this->srcFile = $srcFile;
        $this->width = $width;
        $this->height = $height;
        $this->quality = $quality;
        $this->base64Contents = $base64Contents;
        $this->webp = $webp;
    }

    // Set width
    public function width($v) {
        $this->width = $v;
        return $this;
    }

    // Set height
    public function height($v) {
        $this->height = $v;
        return $this;
    }

    // Set quality
    public function quality($v) {
        $this->quality = $v;
        return $this;
    }

    // Get the content type
    public function contentType() {
        $image = $this->imagick();
        if(!$image) return false;
        return 'image/'.$image->getImageFormat();
    }

    // Get the content type
    public function getContentTypeHeader() {
        $image = $this->imagick();
        if(!$image) return false;
        return 'Content-Type: '.$this->contentType();
    }

    // Stream to browser
    public function stream() {
        $image = $this->imagick();
        if(!$image) return false;

        header($this->getContentTypeHeader());
        die($image);
    }

    // Trim whitespace border
    public function trim($fuzz = 0) {
        $image = $this->imagick();
        if(!$image) return false;

        $image->trimImage($fuzz);
        $image->setImagePage(0, 0, 0, 0);

        return $this;
    }

    // Save image
    public function save($dstFile) {
        $image = $this->imagick();
        if(!$image) return false;

        // WebP formats
        if($this->webp) $image->setImageFormat('webp');
        else $image->setImageFormat('jpeg');

        $image->writeImage($dstFile);

        //Free the canvas image
        //$image->clear();
        //$image->destroy();
        return $dstFile;
    }

    // Destroy image
    public function cleanup() {
        $image = $this->imagick();
        if(!$image) return false;

        try {
            $image->clear();
            $image->destroy();
        }
        catch(\Exception $e) {}
        return $this;
    }

    // Get Imagick image
    protected function imagick() {
        if($this->imagick) return $this->imagick;

        try {
            if($this->base64Contents) {
                $image = new \Imagick();
                $image->readImageBlob(base64_decode($this->base64Contents));
            }
            else {
                $image = new \Imagick($this->srcFile);
            }

            //Added code to reduce final image size
            $image->setIteratorIndex(0);
            $image->setImageDepth(8);
            $image->setImageCompression(\Imagick::COMPRESSION_JPEG);
            $image->setImageCompressionQuality($this->quality);
            $image->stripImage();
        } catch(\Exception $e) {
            Log::error("[ImageSizer->imagick()]: \tBase64: " . ($this->base64Contents?'true':'false') . "\tSrc: {$this->srcFile} \t" . "\tError: " . $e->getMessage());
            return false;
        }

        $this->imagick = $image;
        return $this->imagick;
    }

    // Fix image orientation
    public function fixOrientation() {
        $image = $this->imagick();
        if(!$image) return false;

        try {
            $exif = \exif_read_data($this->srcFile);
        }
        catch(\Exception $e) {
            return false;
        }

        if(isset($exif['Orientation'])) $orientation = $exif['Orientation'];
        else if(isset($exif['IFD0']['Orientation'])) $orientation = $exif['IFD0']['Orientation'];
        else return false;
        
        switch($orientation) {
            case 3: // rotate 180 degrees
                $image->rotateimage("#FFF", 180);
                break;

            case 6: // rotate 90 degrees CW
                $image->rotateimage("#FFF", 90);
                break;

            case 8: // rotate 90 degrees CCW
                $image->rotateimage("#FFF", -90);
                break;
        }
    }

    // Resize image
    public function resize($allowStretching = false, $addImageCanvas = false) {
        $image = $this->imagick();
        if(!$image) return false;

        //Get image dimension information
        $geometry = $image->getImageGeometry();

        if (!$this->width && !$this->height) {
            $this->width = $geometry['width'];
            $this->height = $geometry['height'];
        }

        // Fix orientation if needed
        $this->fixOrientation();
        $image->setInterlaceScheme(\Imagick::INTERLACE_PLANE);
        $image->setImageCompressionQuality($this->quality);

        /**
         * If image is smaller than dimensions passed and stretching is allowed, then stretch image to best fit size
         * If image is larger than dimensions passed , then scale it down regardless if stretching is allowed or not
         */
        if ($allowStretching || $geometry['width'] > $this->width || $geometry['height'] > $this->height) {
            $fitbyWidth = false;
            if($this->width && $this->height) {
                $fitbyWidth = (($this->width / $geometry['width']) < ($this->height / $geometry['height'])) ? true : false;
            } else if(!$this->height) {
                $fitbyWidth = true;
            }

            if ($fitbyWidth) {
                // With either of the parm 0, aspect ratio is maintained
                $image->thumbnailImage($this->width, 0, false);
            } else {
                $image->thumbnailImage(0, $this->height, false);
            }
        }

        // Slight blur for compression
        // $image->adaptiveBlurImage(1, 2);

        if ($addImageCanvas) {
            //Create a background for the canvas
            $canvas = new \Imagick();
            $canvas->newImage($this->width, $this->height,'white','png');

            $geometry = $image->getImageGeometry();

            //Figure out where to place the image on the canvas
            $x = ($this->width - $geometry['width']) / 2;
            $y = ($this->height - $geometry['height']) / 2;

            //Write the resized image over the canvas
            $canvas->compositeImage($image, imagick::COMPOSITE_OVER, $x, $y);

            $canvas->setImageDepth(8);
            $canvas->setImageCompression(\Imagick::COMPRESSION_JPEG);
            $canvas->setImageCompressionQuality($this->quality);
            $canvas->stripImage();
            $this->imagick = $canvas;
        } else {
            $this->imagick = $image;
        }

        return $this;
    }
}
