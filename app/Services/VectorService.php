<?php

namespace App\Services;

use SoapClient;
use SoapFault;

class VectorService
{
    protected $client;
    protected $username;
    protected $password;
    protected $endpoint;
    protected $referenceid;

    public function __construct()
    {
        $this->username = config('services.vector_one.username');
        $this->password = config('services.vector_one.password');
        $this->endpoint = config('services.vector_one.endpoint');
        $this->referenceid = config('services.vector_one.referenceid');

        $wsdl = $this->endpoint;
        $options = [
            'trace' => 1,
            'exceptions' => 1,
            'cache_wsdl' => WSDL_CACHE_NONE,
        ];

        $this->client = new SoapClient($wsdl, $options);
    }

    public function searchBySSN($ssn, $state)
    {
        $params = [
            'request' => [
                'Entities' => [
                    'EntityReports.EntitySearchCriteria' => [
                        'EntityID' => $ssn,
                        'EntityIDType' => 'SSN',
                        'ReferenceID' => 'ref01',
                        'State' => $state,
                    ],
                ],
                'Password' => $this->password,
                'ReferenceID' => $this->referenceid,
                'Username' => $this->username,
            ],
        ];

        try {
            $response = $this->client->__soapCall('EntitySearch', [$params]);
            return $response;
        } catch (SoapFault $fault) {
            return ['error' => $fault->getMessage()];
        }
    }

    public function searchByTaxID($taxId, $state)
    {
        $params = [
            'request' => [
                'Entities' => [
                    'EntityReports.EntitySearchCriteria' => [
                        'EntityID' => $taxId,
                        'EntityIDType' => 'TAX',
                        'ReferenceID' => 'ref01',
                        'State' => $state,
                    ],
                ],
                'Password' => $this->password,
                'ReferenceID' => $this->referenceid,
                'Username' => $this->username,
            ],
        ];

        try {
            $response = $this->client->__soapCall('EntitySearch', [$params]);
            return $response;
        } catch (SoapFault $fault) {
            return ['error' => $fault->getMessage()];
        }
    }
}
