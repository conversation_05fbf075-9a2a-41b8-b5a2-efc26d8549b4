<?php
namespace App\Services;

use Auth0\SDK\Token;
use Auth0\SDK\Configuration\SdkConfiguration;
use App;
use Illuminate\Http\Request;

class Auth0TokenService
{

    public $token;
    public $roles;
    public $meta;
    public $user;
    public $test;
    public $error_msg;

    public function __construct($accessToken = null)
    {
        if ($accessToken == null) {
            $this->test = 1;
            $accessToken = request()->bearerToken();
        }

        if ($accessToken == null && app()->runningInConsole() && !(app()->runningUnitTests())) {
            $this->test = 2;
            return;
        }

        if ($accessToken == null) {
            $this->test = 3;
            return;
        }
        $laravelConfig = config('hq-auth0');
        $token_domain  = $laravelConfig['domain'];

        $valid = false;
        foreach ($laravelConfig['authorized_issuers'] as $issuer) {
            $valid = $this->verifyToken($accessToken, $issuer);
            if ($valid) {
                break;
            }
        }

        if (!$valid) {
            abort(403, $this->error_msg);
        }
    }

    public function verifyToken($accessToken, $token_issuer)
    {
        $auth0Config = config('hq-auth0');
        try {
            $sdkConfig = new SdkConfiguration([
                'audience' => [$auth0Config['audience']],
                'strategy' => $auth0Config['strategy'],
                'domain' => $auth0Config['domain'],
                'clientId' => $auth0Config['client_id'],
                'clientSecret' => $auth0Config['client_secret'],
                'tokenAlgorithm' => 'RS256',
            ]);
        } catch (\Exception $e) {}

        if ($accessToken == null) {
            $this->error_msg = 'Access Token is Null!';
            \Log::channel('daily')->info('Error : null access token. '.\Request::ip().' : '.\Request::method().' : '.url()->full());

            return false;
        }

        try {
            $token = new Token($sdkConfig, $accessToken, Token::TYPE_TOKEN);
        } catch (\Exception $e) {
            $this->error_msg = 'Access Token is Invalid!';
            \Log::channel('daily')->info('Error : Bad access token. '.\Request::ip().' : '.\Request::method().' : '.url()->full().' Token : '.$accessToken);

            return false;
        }

        try {
            $this->token = json_decode($token->validate(
                tokenIssuer: $token_issuer,
                tokenAudience: [$auth0Config['audience']],
                tokenLeeway: 120,
            )->toJson());

            return true;
        } catch (\Exception $e) {
            $this->error_msg = 'Caught: Exception - '.$e->getMessage();

            return false;
        }
    }

    public function hasPermission($perm)
    {
        return in_array($perm, $this->token->permissions);
    }


    public function hasRole($role)
    {
        if ($this->roles == null) {
            return true;
        }
        if (is_array($role)) {
            foreach ($role as $r) {
                if (in_array($r, (array)$this->roles)) {
                    return true;
                }
            }
            return false;
        }
        return in_array($role, $this->roles);
    }

    /**
     * Return the permissions
     * @param  [type]  $division [description]
     * @return boolean           [description]
     */
    public function permissions()
    {
        return $this->token->permissions;
        //we have to query for the permissions of the impersonated user
        //be sure that SuperAdmins are now able to be impersonated.
    }

    /**
     * Return the permissions
     * @param  [type]  $division [description]
     * @return boolean           [description]
     */
    public function roles()
    {
        return $this->roles;
    }

    public function meta($item)
    {
        return isset($this->meta->$item) ? $this->meta->$item : null;
    }

    public function audit_id()
    {
        if (isset($this->user) && $this->user != null) {
            return isset($this->user->email) ? $this->user->email : $this->token->sub;
        }
        if (App::runningUnitTests()) {
            return "Laravel Automated Test";
        }
        if (app()->runningInConsole()) {
            return "Laravel Console Service";
        }
        return "Unknown";
    }
    /**
     * Used for webhooks to authenticate without the need for an access token.
     * This requires that in Auth0 unde the application "advanced" setting that the email and agent_code be added to the "client_metadata"
     * @param  [type] $application An application object from Auth0
     * @param  [type] $permissions Array of permissions.
     * @return [type]              [description]
     */
    public function spoofToken($application, $permissions)
    {
        $jwt = (object)[
            "http://quility.com/roles" => [
                "Machine"
            ],
            "http://quility.com/meta_data"=> (object)[
                "AgentCode"=> $application['client_metadata']['agent_code']
            ],
            "http://quility.com/user_data" => (object)[
                "email"=> $application['client_metadata']['email'],
                "name"=> $application['name']
            ],
            "nickname"=> $application['name'],
            "name"=> $application['name'],
            "email"=> $application['client_metadata']['email'],
            "email_verified"=> false,
            "sub"=> $application['client_id'],
            "permissions" => $permissions,
            "cached_date" => $application['cached_date']
        ];
        $laravelConfig = config('hq-auth0');
        $role_namespace = $laravelConfig['namespace'] . "roles";
        $meta_namespace = $laravelConfig['namespace'] . "meta_data";
        $user_namespace = $laravelConfig['namespace'] . "user_data";
        $this->token = $jwt;
        $this->roles = $this->token->$role_namespace;
        $this->user = $this->token->$user_namespace;
        $this->meta = $this->token->$meta_namespace;
    }
}
