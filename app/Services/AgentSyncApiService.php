<?php

namespace App\Services;

use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Arr;

/**
 * Wrapper for the internal AgentSync API.
 */
class AgentSyncApiService
{
    protected array $config;

    const CACHE_KEY = 'AgentSync_API_Token';

    public function __construct()
    {
        if (config('agentsync-api.use_prod')) {
            $this->config = config('agentsync-api.prod');
        } else {
            $this->config = config('agentsync-api.dev');
        }
    }

    public function searchNpnBySsn(string $firstName, string $lastName, string $ssnLast4): bool|string|array
    {
        if (strlen($ssnLast4) !== 4) {
            throw new \InvalidArgumentException('SSN must be 4 digits long');
        }

        $url = $this->config['api_base_url'].'/npn/ssn/lookup';
        $data = [compact('firstName', 'lastName', 'ssnLast4')];

        $resp = $this->makeRequest($url, $data);
        $respData = $resp->json();

        if (count($respData) !== 1) {
            // since we are submitting one at a time this shouldn't happen
            \Log::error('AgentSync API NPN Lookup returned multiple results', [
                'url'     => $url,
                'reason'  => $resp->reason(),
                'body'    => $resp->json(),
            ]);

            throw new \UnexpectedValueException('AgentSync API call failed');
        }

        return $respData[0];

        // $respData = $respData[0];

        // if ($respData['success'] === false) {
        //     return $respData;
        // }

        // return $respData['npn'];
    }

    public function searchByLicenseNumber(string $licenseId, string $state, $producerType = 1) 
    {
        
        $url = $this->config['api_base_url'].'/npn/license/lookup';
        $data = [compact('licenseId', 'state', 'producerType')];

        $resp = $this->makeRequest($url, $data);
        $respData = $resp->json();

        if (count($respData) !== 1) {
            // since we are submitting one at a time this shouldn't happen
            \Log::error('AgentSync API NPN Lookup returned multiple results', [
                'url'     => $url,
                'reason'  => $resp->reason(),
                'body'    => $resp->json(),
            ]);

            throw new \UnexpectedValueException('AgentSync API call failed');
        }

        return $respData[0];
    }

    public function makeRequest(string $endpoint, $data): PromiseInterface|Response
    {
        $this->handleOAuthToken();

        $resp = Http::asJson()
            ->withToken(\Cache::get(self::CACHE_KEY))
            ->post($endpoint, $data);

        if (! $resp->successful()) {
            \Log::error('AgentSync API call failed', [
                'url'     => $endpoint,
                'payload' =>  $data,
                'token'   => \Cache::get(self::CACHE_KEY),
                'reason'  => $resp->reason(),
                'body'    => $resp->json(),
            ]);

            throw new \Exception('AgentSync API call failed');
        }

        return $resp;
    }

    protected function handleOAuthToken(): void
    {
        if (\Cache::has(self::CACHE_KEY)) {
            return;
        }

        $endpoint = $this->config['token_endpoint'];
        $grant = 'grant_type='.config('agentsync-api.oauth.grant_type');
        // $scope = 'scope='.Arr::join(config('agentsync-api.oauth.scopes'));
        $scope = 'scope='.config('agentsync-api.oauth.scopes')[0];
        $url = "$endpoint?$grant&$scope";

        $resp = Http::withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded',
            'Authorization' => 'Basic '.base64_encode($this->config['key'].':'.$this->config['secret']),
        ])->post($url);

        if (! $resp->successful()) {
            \Log::error('AgentSync API could not get OAuth token', [
                'url'     => $url,
                'reason'  => $resp->reason(),
                'body'    => $resp->json(),
            ]);

            throw new \Exception('AgentSync API could not get OAuth token');
        }

        // cache the returned token until a minute before it expires
        $respData = $resp->json();
        \Cache::add(self::CACHE_KEY, $respData['access_token'], $respData['expires_in'] - 60);
    }
}
