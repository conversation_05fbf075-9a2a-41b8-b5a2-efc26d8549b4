<?php
namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Agent;
use App\Models\UserInvite;
use App\Models\UserEntry;
use App\Mail\OnboardingMail;
use Illuminate\Support\Facades\Mail;
use Cache;

use function PHPUnit\Framework\isEmpty;

class UserInviteService 
{
    var $demoportal = null;

    public function __construct()
    {
        $this->demoportal = config('services.demoportal');
    }
    
    /**
     * Request that an invitation be created on the demo site.
     *
     * @param string $email
     * @return \Illuminate\Support\Facades\Http $response->body
     */
    public function requestDemoInvite($name, $email)
    {
        try {
            $url = $this->demoportal['base_url'].'/api/create-demo-invite';
            $response = Http::withToken($this->getToken())
                ->post($url, [
                    'name' => $name,
                    'email' => $email
                ]);
        } catch (\Exception $ex) {
            \Log::error('Exception while requesting the demo invitation', [
                'AgentCode' => $email
            ]);
            return $ex;
        }

        if (! $response->successful()) {
            \Log::error('Could not request a demo account', [
                'email' => $email
            ]);
        }

        return $response->body();
    }

    private function getToken()
    {
        return Cache::remember('DEMO_HQ_BEARER_TOKEN', 86400, function () {
            $url = 'https://'.$this->demoportal['auth0_domain'].'/oauth/token';

            $res = Http::asForm()->post($url, [
                'grant_type'    => 'client_credentials',
                'client_id'     => $this->demoportal['auth0_client_id'],
                'client_secret' => $this->demoportal['auth0_client_secret'],
                'audience'      => $this->demoportal['auth0_audience'],
            ]);

            $token_data = $res->object();

            if (! isset($token_data)) {
                throw new \Exception('Error generating bearer token in '.self::class, 1);
            }

            if (isset($token_data->error)) {
                throw new \Exception('Error generating bearer token in '.self::class.'. Error: '.$token_data->error.', Description: '.$token_data->error_description, 1);
            }

            return $token_data->access_token ?? null;
        });
    }

    /**
     * Add a demo invitation to this (demo) site.
     *
     * @param string $email
     * @return App\Models\UserInvite $userInvite
     */
    public function createDemoInvite(Request $request)
    {
        $user = User::where('email', '<EMAIL>')->first();

        $person_message = "We're excited that you're interested in joining us at Symmetry Financial Group! Use the link below to activate your account and complete your application.";
        if(config('app.tenant') == 'Q2B')
                $request->person_message = "We're excited that you're interested in working with Quility® B2B! Use the link below to activate your account and complete your contracting application.";
        $code = (string) Str::uuid();
        $ao_signature_line = '';
        $demo_name = 'Demo User';
        $demo_email = uniqid().'@demo.com';

        $userInviteData = [
            'user_id'           => $user->id,
            'email'             => $demo_email,
            'name'              => $demo_name,
            'phone'             => '************',
            'invite_expired'    => 0,
            'blocked'           => 0,
            'code'              => $code,
            'contract_level'    => 70,
            'upline_agent_id'   => 787906,
            'corporate'         => false,
            'person_message'    => $person_message,
            'source'            => 'demo',
        ];

        $userInvite = UserInvite::create($userInviteData);
        
        Mail::to($request->email)->send(
            new OnboardingMail(
                $demo_name, 
                $code, 
                [
                    'name' => $request->name,
                    'email' => $request->email,
                ],
                $person_message,
                $ao_signature_line,
            )
        );
        $userInvite->update([
            'invite_sent' => 1,
        ]);

        return $userInvite;
    }

    public function inviteBlocked($data)
    {
        // if($data->has('first_name') && !$data->has('name'))
        //     $data->name = $data->first_name.'%'.$data->last_name;

        $invites = UserInvite
            ::where('invite_expired', 0)
            ->where('blocked', 0)
            ->where(function ($query) use ($data) {
                $query->where('name', 'LIKE', $data->name);
                if(!isEmpty($data->email))
                    $query->orWhere('email', 'LIKE', $data->email);
                if(!isEmpty($data->phone))
                    $query->orWhere('phone', $data->phone);
            })
            ->orderBy('updated_at')
            ->get();
        $blocked = false;

        foreach ($invites as $invite) 
        {
            $matches = 0;
            $matches += (strtoupper(str_replace(' ', '', $invite->name)) == strtoupper(str_replace('%', '', $data->name)) ? 1 : 0);
            $matches += (strtoupper($invite->email) == strtoupper($data->email) ? 1 : 0);
            $matches += ($invite->phone == $data->phone ? 1 : 0);

            if ($matches >= 2) 
            {
                $blocked = true;
                break;
            }
        }

        return $blocked;
    }

    public function agentExists($data)
    {
        
        $query = Agent::query();
        $query->active();
        // if(!isEmpty($data->name))
            $query->where('AgentName', 'LIKE', $data->name);
        // if(!isEmpty($data->email))
            $query->where('AgentEmail', 'LIKE', $data->email);

        // echo "count:".$query->count();
        return $query->count() > 0;

    }

    public function candidateExists($data)
    {
        if($data->has('first_name') && !$data->has('name'))
            $data->name = $data->first_name.'%'.$data->last_name.'%';
        $data->name = Str::of($data->name)->replace(' ', '%');

        $result = [];

        // user or agent name exists?
        if(Str::of($data->name)->replace('%', '')->trim()->isNotEmpty()) {
            $result['user_name'] = User::where('name', 'LIKE', $data->name)->count() > 0;
            $result['agent_name'] = Agent::where('AgentName', 'LIKE', $data->name)->count() > 0;
        }

        // user or agent email exists?
        if(Str::of($data->email)->trim()->isNotEmpty()) {
            $result['user_email'] = User::where('email', 'LIKE', $data->email)->count() > 0;
            $result['agent_email'] = Agent::where('AgentEmail', 'LIKE', $data->email)->count() > 0;
        }

        if(Str::of($data->phone)->trim()->isNotEmpty()) {
            $phone = preg_replace("/[^0-9]/", "", $data->phone);
            if(substr($phone, 0, 1) == '1')
                $phone = substr($phone, 1);
            $phone = '%' . substr($phone, 0, 3) . '%' . substr($phone, 3, 3) . '%' . substr($phone, 6);

            $result['phone'] = UserEntry::join('users', 'users.id', '=', 'user_entries.user_id')
                ->join('form_lookups', 'form_lookups.form_field_id', '=', 'user_entries.form_field_id')
                ->whereIn('form_lookups.field_slug', ['personal-phone', 'business-phone'])
                ->where('value', 'LIKE', $phone)
                ->whereNotNull('users.agent_code')
                ->count() > 0;
            $result['phone_search'] = $phone;
        }

        // is blocked?
        $result['blocked'] = $this->inviteBlocked($data);
        
        
        $query = User::query();
        if (Str::of($data->email)->trim()->isNotEmpty()){
            $query->where(function($query) use ($data) {
                $query->where('work_email', 'LIKE', $data->email);
                $query->orWhere('email', 'LIKE', $data->email);
            });
        }
        $query->whereNotNull('agent_code');
        $result['other_email'] = $query->count() > 0;

        // if ($data->has('phone'))
        //     $query->where('phone', 'LIKE', '%'.$data->phone.'%');
        
        return $result;
    }
}
