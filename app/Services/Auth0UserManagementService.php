<?php

namespace App\Services;

use App;
use Cache;

class Auth0UserManagementService
{

    protected $auth0;
    protected $auth0management;
    protected $term_agent_service;
    protected $roles;
    protected $cfg;

    //dev
    protected $roles_dev = [
            'Staff' => 'rol_Pv6JqN1iuWELCQ7b',
            'AgencyOwner' => 'rol_kkH2as7sLyaa2A6P',
            'SalesRep' => 'rol_oQzrhuGpD2LTr4Xm',
            'Exec' => 'rol_3s9KPDlVOh4YT75P',
            'SuperAdmin' => 'rol_L4Wod9aqFaCR6y0e',
     //       'ContentEditor' => 'rol_SJx05du7btpQt42D',
            'UnlicensedAgent' => 'rol_n1G79I7fY13DA0Xo'
        ];

    //prod
    protected $roles_prod = [
            'Staff' => 'rol_LWbQ9ygECsn03Vom',
            'AgencyOwner' => 'rol_b9sCV1NsDzQLbSaK',
            'SalesRep' => 'rol_2bvP1WFVKcUAfg0O',
            'Exec' => 'rol_sRXht0iWU6tHMPM4',
            'SuperAdmin' => 'rol_I66qVlz574by12hZ',
            'UnlicensedAgent' => 'rol_8zzW9Vh34P1M5ku3'
        ];

    protected $blocking_statuses = [
        'Terminated',
        'Stalled License',
        'Lapsed',
        'Stalled'
    ];

    public function __construct()
    {
        $this->cfg = config('hq-auth0');
        $this->auth0 = resolve('App\Services\Auth0TokenService');
        $this->auth0management = resolve('App\Services\Auth0ManagementTokenService');
        // $this->term_agent_service = resolve('App\Services\AgentManagementServices\AgentTerminationService');
        $environment = App::environment();
        if ($environment == "production") {
            $this->roles = $this->roles_prod;
        } else {
            $this->roles = $this->roles_dev;
        }
    }


    public function getAllUsers($page = 0, $per_page = 100)
    {
        return $this->auth0management->api->users()->getAll([
            'page' => $page,
            'per_page' =>$per_page,
            'identities.connection' => "Username-Password-Authentication"
        ]);
    }

    public function findUser($email)
    {
        $users = [];
        try {
            $users = $this->auth0management->api->users()->getAll(['q' => $email]);
        } catch (Exception $e) {
            \Log::channel('agent')->info($e);
            echo "\n\n" . $count . "\n\n";
            echo $e;
        } catch (RequestException $e) {
            \Log::channel('agent')->info($e);
            echo "\n\n" . $count . "\n\n";
            echo $e;
            //var_dump($agent->asJSON());
        } catch (ServerException $e) {
            \Log::channel('agent')->info($e);
            echo "\n\n" . $count . "\n\n";
            echo $e;
            //var_dump($agent->asJSON());
        }
        return $users;
    }

    public function findUserForDB($email, $db_prefix)
    {
        $users = $this->findUser($email);
        foreach ($users as $u) {
            if (strpos($u['user_id'], $db_prefix) == 0) {
                return $u;
            }
        }
        return null;
    }

    public function updateUser($user_id, $agent, $password)
    {
        if (isset($agent->AvailableInd) && $agent->AvailableInd != 1) {
            //echo "X";
        }
        try {
            $user = $this->auth0management->api->users()->update($user_id, [
                'name' => $agent->AgentName,
                'email' => $agent->AgentEmail,
                'email_verified' => true,
                'connection' => 'Username-Password-Authentication',
                'blocked' => $agent->AvailableInd == 1 ? false : true,
                'app_metadata' => [
                    'AgentCode' => $agent->AgentCode,
                    'OptID' => $agent->OptID,
                    'Status' => isset($agent->Status) ? $agent->Status : null,
                    'Role' => isset($agent->Role) ? $agent->Role : null,
                    'LeadershipLevel' => isset($agent->LeadershipLevel) ? $agent->LeadershipLevel : null,
                    'UplineAgentCode' => $agent->baseDirectUpline ? $agent->baseDirectUpline->AgentCode : null,
                    'UplineOptID' => $agent->baseDirectUpline ? $agent->baseDirectUpline->OptID : null,
                    'BaseShopOwnerAgentCode' => $agent->baseBaseshopOwner ? $agent->baseBaseshopOwner->AgentCode : null,
                    'BaseShopOwnerOptID' => $agent->baseBaseshopOwner ? $agent->baseBaseshopOwner->OptID : null,
                    'SFGDirectAgentCode' => $agent->baseSfgDirectOwner ? $agent->baseSfgDirectOwner->AgentCode : null,
                    'SFGDirectOptID' => $agent->baseSfgDirectOwner ? $agent->baseSfgDirectOwner->OptID : null,
                    'FastTrack' => $agent->FastTrack ? 1 : 0,
                    'ContractStartDate' => isset($agent->ContractStartDt) ? $agent->ContractStartDt : "",
                    'QMSParticipant' => $agent->QMSParticipantInd ? 1 : 0,
                ],
            ]);
            //echo "\n---Updated " . $agent->AgentName . " " . $agent->AgentEmail . " - $password - FastTrack " . ($agent->FastTrack ? "1" : "0") . " " . $agent->ContractStartDt . "\n";
            //$user = $this->auth0management->api->users()->update($user['user_id'], [
             //   'email' => $agent->AgentEmail,
                //'password' => $password,
            //]);
            //echo ".";
            //echo $user['name'] . " " . $agent->AgentEmail . " - $password\n";
        } catch (\ClientException $e) {
            \Log::channel('agent')->info($e);
            //echo "\nERROR UPDATING " . $agent->AgentEmail . "\n";
        } catch (\RequestException $e) {
            \Log::channel('agent')->info($e);
            //echo "\nERROR UPDATING " . $agent->AgentEmail . "\n";
            //echo $e;
            //var_dump($agent->toJSON());
        }
    }

    public function updateUserAppMeta($user_id, array $app_meta)
    {
        try {
            $user = $this->auth0management->api->users()->update($user_id, [
                'app_metadata' => $app_meta,
            ]);
        } catch (ClientException $e) {
            return "\nERROR UPDATING " . $agent->AgentEmail . "\n";
        } catch (RequestException $e) {
            return "\nERROR UPDATING " . $agent->AgentEmail . "\n";
        }
        return true;
    }

    public function disableUser($user_id, $blocked = true)
    {
        try {
            $user = $this->auth0management->api->users()->update($user_id, [
                'blocked' => $blocked
            ]);
        } catch (ClientException $e) {
            return "\nERROR UPDATING " . $agent->AgentEmail . "\n";
        } catch (RequestException $e) {
            return "\nERROR UPDATING " . $agent->AgentEmail . "\n";
        }
        return true;
    }

    public function createUser($agent, $password, $email_verified = true)
    {
        try {
                //echo "\nCreating $agent->AgentEmail $password\n";
                $user = $this->auth0management->api->users()->create([
                    'email' => $agent->AgentEmail,
                    'name' => $agent->AgentName,
                    'connection' => 'Username-Password-Authentication',
                    'email_verified' => $email_verified,
                    'password' => $password,
                    'app_metadata' => [
                        'AgentCode' => $agent->AgentCode,
                        'OptID' => $agent->OptID,
                        'Status' => isset($agent->Status) ? $agent->Status : null,
                        'Role' => "XXX", //isset($agent->Role) ? $agent->Role : null,
                        'LeadershipLevel' => isset($agent->LeadershipLevel) ? $agent->LeadershipLevel : null,
                        'UplineAgentCode' => $agent->baseDirectUpline ? $agent->baseDirectUpline->AgentCode : null,
                        'UplineOptID' => $agent->baseDirectUpline ? $agent->baseDirectUpline->OptID : null,
                        'BaseShopOwnerAgentCode' => $agent->baseBaseshopOwner ? $agent->baseBaseshopOwner->AgentCode : null,
                        'BaseShopOwnerOptID' => $agent->baseBaseshopOwner ? $agent->baseBaseshopOwner->OptID : null,
                        'SFGDirectAgentCode' => $agent->baseSfgDirectOwner ? $agent->baseSfgDirectOwner->AgentCode : null,
                        'SFGDirectOptID' => $agent->baseSfgDirectOwner ? $agent->baseSfgDirectOwner->OptID : null,
                        'FastTrack' => $agent->FastTrack ? 1 : 0,
                        'ContractStartDate' => isset($agent->ContractStartDt) ? $agent->ContractStartDt : "",
                        'QMSParticipant' => $agent->QMSParticipantInd ? 1 : 0,

                    ],
                ]);
                //echo "\n---Updated " . $agent->AgentName . " " . $agent->AgentEmail . " - $password - FastTrack " . ($agent->FastTrack ? "1" : "0") . " " . $agent->ContractStartDt . "\n";
        } catch (\ClientException $e) {
            \Log::channel('agent')->info($e);
            //echo "ERROR CREATING " . $agent->AgentEmail . "\n";
        } catch (\RequestException $e) {
            \Log::channel('agent')->info($e);
            //echo "ERROR CREATING " . $agent->AgentEmail . "\n";
            //echo $e;
            //var_dump($agent->toJSON());
        } catch (\Exception $e) {
            \Log::channel('agent')->info($e);
            //echo "ERROR CREATING " . $agent->AgentEmail . "\n";
            //echo $e;
            //var_dump($agent->toJSON());
        }
        return $user;
    }

    public function getStaffList()
    {
        $staff = [];
        $has_more = true;
        $page = 0;
        while ($has_more) {
            $s = $this->auth0management->api->roles()->getUsers($this->roles["Staff"], [
                'per_page' => 10,
                'page' => $page,
                'include_totals' => true
            ]);
            if (count($s["users"]) > 0) {
                $staff = array_merge($staff, $s["users"]);
                $page = $page + 1;
            } else {
                $has_more = false;
            }
        }
        $staff = $this->mapRoleToUsers($staff, "Staff");
        $exec = $this->auth0management->api->roles()->getUsers($this->roles["Exec"]);
        $exec = $this->mapRoleToUsers($exec, "Exec");
        $admin = $this->auth0management->api->roles()->getUsers($this->roles["SuperAdmin"]);
        $admin = $this->mapRoleToUsers($admin, "SuperAdmin");
        return collect(array_merge($staff->toArray(), $exec->toArray(), $admin->toArray()));
    }

    public function getUserPermissions($user_id)
    {
        return $this->auth0management->api->users()->getPermissions($user_id);
    }

    public function removeUserPermissions($user_id, $permissions)
    {
        if (!is_array($permissions)) {
            $permissions = [$permissions];
        }
        $p = [];
        foreach ($permissions as $permission) {
            $p[] = [
                "permission_name" => $permission,
                "resource_server_identifier" => $this->cfg['api_identifier']
            ];
        }
        return $this->auth0management->api->users()->removePermissions($user_id, $p);
    }

    public function addUserPermissions($user_id, $permissions)
    {
        if (!is_array($permissions)) {
            $permissions = [$permissions];
        }
        $p = [];
        foreach ($permissions as $permission) {
            $p[] = [
                "permission_name" => $permission,
                "resource_server_identifier" => $this->cfg['api_identifier']
            ];
        }
        return $this->auth0management->api->users()->addPermissions($user_id, $p);
    }

    public function mapRoleToUsers(Array $collection, $role)
    {
        return collect($collection)->map(function ($item) use ($role) {
            $item['role'] = $role;
            return $item;
        });
    }

    public function removeAllRoles($user_id)
    {
        return $this->auth0management->api->users()->removeRoles($user_id, array_values($this->roles));
    }

    public function getPermissions()
    {
        $permissions = null;
        try {
            $resource = $this->auth0management->api->resourceServers()->get(urlencode($this->cfg['api_identifier']));
            $permissions = $resource["scopes"];
        } catch (\Exception $e) {
            echo $this->cfg['api_identifier'];
            \Log::channel('daily')->info("Could not retrieve API permission list from Auth0 " . $this->cfg['api_identifier'] . "  :: " . $e);
        }
        return $permissions;
    }

    public function getAgentPermissions(Agent $agent)
    {
        $user = $this->findUserForDB($agent->AgentEmail, 'auth|');
        $permissions = $this->getUserPermissions($user['user_id']);
        $r = [];
        foreach ($permissions as $permission) {
            if (strpos($permission['permission_name'], 'agent:') >= 0 && strpos($permission['permission_name'], 'agent:') !== false) {
                $r[] = [
                    "value" => $permission['permission_name'],
                    "description" => $permission['description']
                ];
            }
        }
        return $r;
    }

    public function addAgentPermission(Agent $agent, $permission)
    {
        if (strpos($permission, 'agent:') === false) {
            \Log::channel('daily')->info("Comeone is trying to add a non-agent permission for " . $agent->AgentName);
            return false;
        }
        $user = $this->findUserForDB($agent->AgentEmail, 'auth|');
        $permissions = $this->getUserPermissions($user['user_id']);
        $r = [];
        //check if it already exists.
        foreach ($permissions as $p) {
            if ($p['permission_name'] == $permission) {
                return true;
            }
        }
        try {
            $this->addUserPermissions($user['user_id'], [$permission]);
            return true;
        } catch (\Exception $e) {
            \Log::channel('daily')->info("Could not add permission to agent in Auth0 " . $user['user_id'] . "  :: " . $e);
            return false;
        }
    }

    public function removeAgentPermission(Agent $agent, $permission)
    {
        $user = $this->findUserForDB($agent->AgentEmail, 'auth|');
        try {
            $this->removeUserPermissions($user['user_id'], [$permission]);
            return true;
        } catch (\Exception $e) {
            \Log::channel('daily')->info("Could not remove permission to agent in Auth0 " . $user['user_id'] . "  :: " . $e);
            return false;
        }
    }

    public function permissionCheck(String $user_id, String $permission)
    {
        $user_permissions = $this->getUserPermissions($user_id);

        foreach ($user_permissions as $p) {
            if (isset($p['permission_name']) && $p['permission_name'] == $permission) {
                return true;
            }
        }

        return false;
    }
    /**
     * Get's a machine to machine application information from Auth0 using the client_id
     * @param  [type] $client_id [description]
     * @return [type]            [description]
     */
    public function getApplication($client_id)
    {
        $daily = $this->useCache() ? 60 * 60 * 24 : 0;
        $resource = Cache::remember($client_id . "_Auth0Application", $daily, function () use ($client_id) {
            try {
                $res = $this->auth0management->api->clients()->get($client_id);
            } catch (\Exception $e) {
                \Log::channel('daily')->info("Could not retrieve Application from Auth0 " . $client_id . "  :: " . $e);
                return null;
            }
            $res['cached_date'] = date("Y-m-d H:i:s");
            return $res;
        });
        if (!isset($resource['client_metadata']['email']) || $resource['client_metadata']['email'] == null) {
            abort(500, "This client id is lacking an email address in Auth0.");
        }
        if (!isset($resource['client_metadata']['agent_code']) || $resource['client_metadata']['agent_code'] == null) {
            abort(500, "This client id is lacking an agent_code in Auth0.");
        }
        return $resource;
    }

    /**
     * Get's a machine to machine application permission list from Auth0 using the client_id
     * @param  [type] $client_id [description]
     * @param  string $audience  [description]
     * @return [type]            [description]
     */
    public function getApplicationPermissions($client_id, $audience = "http://localhost:8080/api")
    {
        $daily = $this->useCache() ? 60 * 60 * 24 : 0;
        $api_permissions = Cache::remember($client_id . "_Auth0ApplicationPermissions", $daily, function () use ($client_id, $audience) {
            try {
                $perm = $this->auth0management->api->clientGrants()->getByClientId($client_id, 0, 100);
                $api_permissions = current(array_filter($perm, function ($item) use ($audience) {
                    return $item['audience'] == $audience ? $item['scope'] : [];
                }));
            } catch (\Exception $e) {
                echo $e;
                \Log::channel('daily')->info("Could not retrieve Application Permissions from Auth0 " . $client_id . "  :: " . $e);
                return null;
            }
            return $api_permissions["scope"];
        });
        return $api_permissions;
    }

    public function useCache()
    {
        $environment = App::environment();
        return $environment == "production";
    }
}
