<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use App\Models\Agent;

class SureLcService
{
    protected $baseUrl;
    protected $apiKey;

    public function __construct()
    {
        $this->baseUrl = config('surelc.base_url'); // Set your base URL in the config file
        $this->apiKey = config('surelc.api_key');
    }

    public function lookupProducerByNPN($npn)
    {
        $url = "{$this->baseUrl}/producers/npn/{$npn}";

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'x-api-key' => $this->apiKey
        ])->get($url);

        if ($response->successful()) {
            return $response->json();
        } else {
            return ['error' => 'Producer not found or error occurred'];
        }
    }

    public function getProducerBranchCodeByNPN($npn)
    {
        $url = "{$this->baseUrl}/producers/npn/{$npn}";

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'x-api-key' => $this->apiKey
        ])->get($url);

        if ($response->successful()) {
            $responseData = $response->json();

            if (isset($responseData['branchCode'])) {
                return ['branchCode' => $responseData['branchCode']];
            } else {
                return ['error' => 'branchCode not found in the response'];
            }
        } else {
            return ['error' => 'Producer not found or error occurred'];
        }
    }
    
    /**
     * Get the correct branch code for an agency owner
     * 
     * When an agency owner is onboarded into SureLC, their branchCode is often set to their upline agency owner
     * rather than to themselves. This causes an issue when new agents are approved for this agency owner, 
     * as they should be affiliated with the actual branch code of the agency owner, not the upline's code.
     * 
     * This function solves this by looking at other active agents in the agency owner's baseshop, 
     * who will have the correct branch code (typically derived from the agency owner's name).
     * It then uses one of these agents' NPN to retrieve the proper branch code to use.
     * 
     * @param Agent $agencyOwner The agency owner agent object
     * @return array Either ['branchCode' => '...'] on success or ['error' => '...'] on failure
     */
    public function getAgencyOwnerBranchCode(Agent $agencyOwner)
    {
        // Find an active agent in the agency owner's baseshop
        $agentInBaseshop = $agencyOwner->baseShop(["Active"])->where('NPN', '!=', null)->first();
        
        if (!$agentInBaseshop) {
            return ['error' => 'No active agent with NPN found in the agency owner\'s baseshop'];
        }
        
        // Get the branch code using the agent's NPN
        return $this->getProducerBranchCodeByNPN($agentInBaseshop->NPN);
    }

    public function pushProducer(array $data)
    {
        $url = "{$this->baseUrl}/producers/";

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'x-api-key' => $this->apiKey,
            'Content-Type' => 'application/json'
        ])->put($url, $data);

        if ($response->successful()) {
            return $response->json();
        } else {
            return ['error' => 'Error adding producer or invalid data'];
        }
    }
}
