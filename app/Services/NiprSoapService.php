<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use RicorocksDigitalAgency\Soap\Facades\Soap;

class NiprSoapService
{
    protected $auth0;

    protected $isProduction;

    protected $config;

    public function __construct()
    {
        $this->auth0 = resolve(\App\Services\Auth0TokenService::class);
        $this->isProduction = env('APP_ENV') == 'production' ? true : false;

        $this->config = config('services.nipr');
        if ($this->config['wsdl_url'] == null) {
            throw new \Exception('NIPR config not set!', 1);
        }

        // dd($this->config['username'], $this->config['password']); // Add this temporarily to verify credentials
    }

    public function callSoap($method, $payload)
    {
        $header = soap_header('Authentication', 'test.com')
            ->data([
                'user' => $this->config['username'],
                'password' => $this->config['password'],
            ]);
        Soap::headers($header);

        $response = Soap::to($this->config['wsdl_url'])
            ->withBasicAuth($this->config['username'], $this->config['password'])
            ->call($method, $payload);

        return $response;
    }

    public function getFunctionList()
    {
        return Soap::to($this->config['wsdl_url'])
            ->withBasicAuth($this->config['username'], $this->config['password'])
            ->functions();
    }

    public function NPNLookupWithSSN($last_four_ssn, $firstname, $lastname)
    {
        $soapRequest = '<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="https://pdb.nipr.com/npn-ws/">
    <SOAP-ENV:Body>
        <ns1:ssnLast4Lookup>
            <ssnLast4>' . htmlspecialchars($last_four_ssn) . '</ssnLast4>
            <lastName>' . htmlspecialchars($lastname) . '</lastName>
            <firstName>' . htmlspecialchars($firstname) . '</firstName>
        </ns1:ssnLast4Lookup>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>';

        $auth = base64_encode($this->config['username'] . ':' . $this->config['password']);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => str_replace('?wsdl', '', $this->config['wsdl_url']),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => $this->config['username'] . ":" . $this->config['password'],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_HTTPHEADER => [
                'Content-Type: text/xml;charset=UTF-8',
                'SOAPAction: ssnLast4Lookup',
                'Authorization: Basic ' . $auth,
                'Content-Length: ' . strlen($soapRequest)
            ]
        ]);

        $response = curl_exec($ch);
        
        if (curl_errno($ch)) {
            throw new \RuntimeException("CURL Error: " . curl_error($ch));
        }
        
        curl_close($ch);

        if (!$response) {
            throw new \RuntimeException("No response received from NIPR service");
        }

        $xml = new \SimpleXMLElement($response);
        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        
        $npnNodes = $xml->xpath('//npn');

        // Check if the npn node exists and has a non-empty value
        if (empty($npnNodes) || empty((string) $npnNodes[0])) {
            return null;
        }

        // Return the NPN value as a string
        return (string) $npnNodes[0];
    }

    public function NPNLookupWithLicenseNumber($licenseId, $state)
    {
        $soapRequest = '<?xml version="1.0" encoding="UTF-8"?>
<SOAP-ENV:Envelope xmlns:SOAP-ENV="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="https://pdb.nipr.com/npn-ws/">
    <SOAP-ENV:Body>
        <ns1:licenseLookup>
            <licenseId>' . htmlspecialchars($licenseId) . '</licenseId>
            <state>' . htmlspecialchars($state) . '</state>
            <producertype>1</producertype>
        </ns1:licenseLookup>
    </SOAP-ENV:Body>
</SOAP-ENV:Envelope>';

        $auth = base64_encode($this->config['username'] . ':' . $this->config['password']);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => str_replace('?wsdl', '', $this->config['wsdl_url']),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => $this->config['username'] . ":" . $this->config['password'],
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_HTTPHEADER => [
                'Content-Type: text/xml;charset=UTF-8',
                'SOAPAction: licenseLookup',
                'Authorization: Basic ' . $auth,
                'Content-Length: ' . strlen($soapRequest)
            ]
        ]);

        $response = curl_exec($ch);
        
        if (curl_errno($ch)) {
            throw new \RuntimeException("CURL Error: " . curl_error($ch));
        }
        
        curl_close($ch);

        if (!$response) {
            throw new \RuntimeException("No response received from NIPR service");
        }

        $xml = new \SimpleXMLElement($response);
        $xml->registerXPathNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
        
        $npn = $xml->xpath('//npn');
        if (empty($npn)) {
            return null;
        }

        return (string)$npn[0];
    }

    //old delete
    public function login()
    {
        //NOTE: The Login method called here is a 'Magic' method
        //      For further details: https://github.com/ricorocks-Digital-Agency/soap#call
        $result = Cache::remember('cvent_session_header', 60 * 60, function () {
            $response = Soap::to('https://api.cvent.com/soap/V200611.ASMX?WSDL')
                        ->Login([
                            'AccountNumber'=> $this->cventConfig['soap']['Account'],
                            'UserName'     => $this->cventConfig['soap']['Username'],
                            'Password'     => $this->cventConfig['soap']['Password'],
                        ]);

            return $response->LoginResult ?? null;
        });

        return $result;
    }
}
