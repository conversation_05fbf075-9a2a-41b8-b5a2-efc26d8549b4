<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\FormLookup;
use App\Models\UserSignature;
use App\Models\User;
use App\Models\UserInvite;
use FormBuilder;
use AzureStore;
use HelloSign;
use Auth;
use File;
use Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\IcaDownloadMail;
// use UserTasks;
// use Agents;
// use App\Models\UserTask;
// use App\Events\Review\AO\Submitted as AOSubmitted;

class HelloSignController extends Controller
{
    public function viewform(Request $request) {
        
        if (!$user = Auth::user()) {
            abort(404);
        }

        //This really shouldn't be happening, but fix email address if marked as stale.
        $user->email = str_ireplace('-STALE', '', $user->email);

        $user->load('userInvite');
        if($user->userInvite->type == UserInvite::TYPE_PRINCIPAL && $user->userInvite->agreement_type == UserInvite::AGREEMENT_STANDARD)
            $formName = "b2b-standard-agreement";
        elseif($user->userInvite->type == UserInvite::TYPE_PRINCIPAL && $user->userInvite->agreement_type == UserInvite::AGREEMENT_CUSTOM)
            $formName = "b2b-custom-agreement";
        else
            $formName = "independent-contractor-agreement";

        // for now, this is our default template
        $formHash = ($request->has('form') ? $request->input('form') : $formName);

        // create sign request
        Log::info("HELLO SIGN INFO: Creating sign request for user {$user->email} with form {$formHash}.");
        $signUrl = self::createSignRequest($user, $formHash);

        // return data
        return response()->json([
            'clientId' => config('hellosign.keys.client_id', null),
            'signUrl' => $signUrl,
        ]);

    }

    private static function createSignRequest($user, $formName) {
        // additional information at: https://github.com/hellosign/hellosign-php-sdk
        // snag form template
        $templateData = config("hellosign.templates.{$formName}", null);
        if (is_null($templateData)) {
            Log::error("HELLO SIGN ERROR: Could not find template id for form name {$formName}.");
            abort(404);
        }

        // build client
        $client = new HelloSign\Client(config('hellosign.keys.api_key', null));
        $clientId = config('hellosign.keys.client_id', null);

        // get account id
        $account = $client->getAccount();
        $accountId = $account->getId();

        // start request
        $request = new HelloSign\TemplateSignatureRequest;
        if (!app()->environment('production')) {
            $request->enableTestMode();
        }
        $request->setTemplateId($templateData['templateId']);
        $request->setSubject($templateData['templateName']);

        // Link to potential user email
        $request->setSigner(UserSignature::ROLE_RECRUIT, $user->email, $user->name);

        // set custom field values
        $customFieldLookups = config("hellosign.customFields.{$formName}", null);
        
        if (!is_null($customFieldLookups)) 
        {
            foreach ($customFieldLookups as $fieldName => $formLookups) 
            {
                $fieldString = "";
                foreach ($formLookups as $lookupHandle) {
                    $fieldString .= FormBuilder::fetchUserEntry($user, $lookupHandle)." ";
                }
                $fieldString = trim($fieldString);
                $return = $request->setCustomFieldValue($fieldName, $fieldString);
            }
        }

        // Turn it into an embedded request
        $embeddedRequest = new HelloSign\EmbeddedSignatureRequest($request, $clientId);

        // Send it to HelloSign
        Log::info("HELLO SIGN INFO: Sending embedded signature request for {$user->email}.");
        $response = $client->createEmbeddedSignatureRequest($embeddedRequest);

        // grab the signature IDs for the signature page that will be embedded in our page
        $signatures = $response->getSignatures();
        $signatureId = $signatures[0]->getId();
        $requestId = $response->signature_request_id;

        // create new signature row
        $userSignature = new UserSignature;
        $userSignature->user_email              = $user->email;
        $userSignature->signature_id            = $signatureId;
        $userSignature->account_id              = $accountId;
        $userSignature->app_id                  = $clientId;
        $userSignature->form_name               = $formName;
        $userSignature->signature_request_id    = $requestId;
        $userSignature->event_type              = UserSignature::TYPE_EPOCH;

        // retrieve the URL to sign the document
        $response = $client->getEmbeddedSignUrl($signatureId);

        // store it to use with the embedded.js HelloSign.open() call
        return $response->getSignUrl();
    }


    public function callback(Request $request) {
        // start validator
        $validator = Validator::make($request->all(), [
            'json' => ['bail', 'required'],
        ]);

        // check validator
        if ($validator->fails()) {
            Log::error("HELLO SIGN ERROR: Callback did not have json field in POST data.");
            abort(404);
        }

        // pull json from post data
        $jsonRaw = $request->input('json');
        $json = json_decode($jsonRaw);

        // check if json decode is null
        if (is_null($json)) {
            Log::error("HELLO SIGN ERROR: JSON field malformed.");
            abort(404);
        }

        // create signature object
        try {
            Log::info("HELLO SIGN INFO: Processing callback for user signature.");

            $userSignature = new UserSignature;
            $userSignature->event_type = ($json->event->event_type ?? '');
            $userSignature->event_time = ($json->event->event_time ?? '');
            $userSignature->event_hash = ($json->event->event_hash ?? '');
           
            // meta fields
            $userSignature->event_message           = ($json->event->event_metadata->event_message ?? '');
            $userSignature->signature_id            = ($json->event->event_metadata->related_signature_id ?? '');
            $userSignature->account_id              = ($json->event->event_metadata->reported_for_account_id ?? '');
            $userSignature->app_id                  = ($json->event->event_metadata->reported_for_app_id ?? '');
            $userSignature->signature_request_id    = ($json->signature_request->signature_request_id ?? '');

            // customer info
            $userSignature->user_email = ($json->signature_request->signatures[0]->signer_email_address ?? '');

        } catch (\Exception $e) {
            Log::error($e);
            abort(404);
        }

        // if the event type is 'signature_request_downloadable', download the files locally
        if ($userSignature->event_type == UserSignature::EVENT_DOWNLOADABLE) {
            Log::info("HELLO SIGN INFO: Downloading files for signature request ID {$userSignature->signature_request_id}.");
            if ($localPath = $this->downloadFiles($userSignature->signature_request_id)) {
                $userSignature->file_name = $localPath;
                Log::info("HELLO SIGN INFO: Sending download email to {$userSignature->user_email}.");
                Mail::to($userSignature->user_email)->send(new IcaDownloadMail());
            }
        }

        // TODO: Enable or disable hash verification based on environment
        // callback_test event payload will not always validate for some reason
        // if (app()->environment('production')) {
        //     // verify our inbound hash
        //     if (!self::verifyHash($userSignature->event_time, $userSignature->event_type, $userSignature->event_hash)) {
        //         Log::error('HELLO SIGN ERROR: Inbound event hash not valid.');
        //         abort(404);
        //     }
        // } else {
            // write payload to log for debugging
            Log::info('HELLO SIGN DEBUG: Full callback payload below.');
            Log::info($jsonRaw);
        // }

        // hash is verified, lets save the signature record
        if (!$userSignature->save()) {
            Log::error("HELLO SIGN ERROR: Could not save user signature for user signature {$userSignature->signature_id}.");
            abort(404);
        }

        // on all_signed event, submit the application automatically
        // Move signature to earlier in the process.  As a result we won't do this until the app is complete.
        /*
        if($userSignature->event_type == UserSignature::TYPE_ALL_SIGNED) {
            $recruit = User::where('email', $userSignature->user_email)
                           ->whereNull('agent_code')
                           ->whereNotNull('user_status_id')
                           ->first();

            if(!$recruit) {
                Log::warning("HELLO SIGN [Post-All-Signed]: failed to auto-submit. Recruit not found with email {$userSignature->user_email}.");
            }else {
                $recruitStatusProvider = (new Agents())->for($recruit);
                $recruitStatusProvider->setStatus(User::ONBOARD_STATUS_SUBMITTED, $recruit->id);

                $thisTask = UserTasks::create(UserTask::TYPE_FILL_APPLICATION)->for($recruit)->target($recruit->id)->find();
                if (!$thisTask)
                    $thisTask = UserTasks::create(UserTask::TYPE_UPDATE_APPLICATION)->for($recruit)->target($recruit->id)->find();

                AOSubmitted::dispatch($recruit);
                Log::info("HELLO SIGN [Post-All-Signed]: Application is auto-submitted #{$recruit->id}");
                if ($thisTask && !UserTasks::complete($thisTask))
                    Log::warning("HELLO SIGN [Post-All-Signed]: Cannot complete user task: " . "Task #" . $thisTask->id);
            }
        }*/

        // all done
        return response()->json(['status' => config('hellosign.messages.callback', null)]);
    }

    public function downloadFiles($signatureRequestId) {
        // build file name and paths
        $fileName = "{$signatureRequestId}.".HelloSign\SignatureRequest::FILE_TYPE_ZIP;
        $storagePath = storage_path(config('hellosign.localPath', null));
        $completePath = "{$storagePath}/{$fileName}";

        // check for folder, create if not exists
        if (!File::isDirectory($storagePath)) {
            File::makeDirectory($storagePath, 0777, true, true);
        }

        // send download request to client
        $client = new HelloSign\Client(config('hellosign.keys.api_key', null));
        if (!$client->getFiles($signatureRequestId, $completePath, HelloSign\SignatureRequest::FILE_TYPE_ZIP)) {
            Log::error("HELLO SIGN ERROR: Unable to download files for signatureRequestId {$signatureRequestId}.");
            return false;
        }

        // attempt to move file to azure blof storage
        $remotePath = config('hellosign.remotePath');
        if (!AzureStore::moveLocalFileToStorage($remotePath, $completePath)) {
            Log::error("HELLO SIGN ERROR: Unable to push local file {$completePath} to remote path ".AzureStore::buildPath($remotePath));
            return false;   
        }
        
        return $fileName;
    }

    private static function verifyHash($eventTime = null, $eventType = null, $inboundHash = null) {
        if (!isset($eventTime, $eventType, $inboundHash)) {
            return false;
        }
        $newHash = hash_hmac("sha256", $eventTime.$eventType, config('hellosign.keys.api_key', null));
        return ($inboundHash == $newHash);
    }
}
