<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\CustomProviders\HQAccountServiceProvider;
use Log;

class PageController extends Controller
{
    protected $guzzle;
    protected $authPath;
    protected $apiPath;
    protected $audience;
    protected $clientId;
    protected $clientSecret;
    protected $accessToken = null;

    public function __construct()
    {
        // $this->guzzle = new \GuzzleHttp\Client(['verify' => '/my/path/to/mycertfile.pem']);
        $this->guzzle = new \GuzzleHttp\Client(['verify' => false]);
        $this->clientId = config('quilityaccounts.clientId', null);
        $this->clientSecret = config('quilityaccounts.clientSecret', null);
        $this->authPath = config('quilityaccounts.authPath', null);
        $this->audience = config('quilityaccounts.audience', null);
        $this->apiPath = config('quilityaccounts.apiPath', null);
    }



    public function show(Request $request)
    {
        $hq = new HQAccountServiceProvider();
        $accessToken = $hq->generateAccessToken(true);

        $url = $this->apiPath."trusted/pages/{$request->id}";
        $response = $this->guzzle->request('GET', $url, [
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => "Bearer {$accessToken}",
                'Accept'        => 'application/json',
            ],
        ]);

        $body = $response->getBody();
        $code = $response->getStatusCode();

        if (!$this->isJson($body)) {
            Log::error("QUILITY RETRIEVE PAGE CONTENT FAILED: Server did not return valid JSON.");
            return false;
        }

        $returnBody = json_decode($body);
        return ($returnBody);

    }

    private function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}
