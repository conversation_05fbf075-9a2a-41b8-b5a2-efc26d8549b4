<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormOption;

class HomeOfficeToolsController extends Controller
{
    public function listQuestions(Request $request)
    {
        $page = $request->page;
        $questions = [];
        if($page) {
            $questions = FormPage::with('formSections.formFields.formOptions')
            ->where('sort', $page)
            ->where('sort', '!=', null)
            ->orderBy('sort', 'asc')->get();
        }
        
        return [
            'fields' => $questions,
            'pages' => FormPage::where('sort', '!=', null)->count(),
        ];
            
    }

    public function updateQuestion(Request $request)
    {   
        // dd($request->page);
        FormPage::where('id', $request->page['id'])->update([
            'label' => $request->page['label']
        ]);

        foreach($request->page['form_sections'] as $form_section) {
            FormSection::where('id', $form_section['id'])->update([
                'label' => $form_section['label']
            ]);

            foreach($form_section['form_fields'] as $form_field) {
                FormField::where('id', $form_field['id'])->update([
                    'label' => $form_field['label'],
                    'pii' => $form_field['pii'] ? 1 : 0,
                    'is_required' => $form_field['is_required'] ? 1 : 0,
                ]);

                foreach($form_field['form_options'] as $form_field) {
                    FormOption::where('id', $form_field['id'])->update([
                        'label' => $form_field['label'],
                        'value' => $form_field['value'],
                    ]);
                }
            }
        }

        return response()->json('success', 200);
    }
}
