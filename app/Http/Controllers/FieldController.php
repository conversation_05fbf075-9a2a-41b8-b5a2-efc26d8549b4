<?php

namespace App\Http\Controllers;

use Log;
use File;
use App\Models\User;
use Defuse\Crypto\Key;
use App\Models\FormPage;
use App\Models\FormField;
use App\Models\UserEntry;
use App\Models\UserUpload;
use App\Models\FormLookup;
use App\Models\UserTask;
use Defuse\Crypto\Crypto;
use App\Models\FormOption;
use App\Models\FormSection;
use App\Services\ImageSizer;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File as FacadesFile;
use Illuminate\Support\Facades\Validator;
use Storage;
use URL;
use UserTasks;
use Image;

class FieldController extends Controller
{
    const TEMP_HEIC_STORAGE_DIR = '/heic_images/';

    public function getOptions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required',
        ]);

        if ($validator->fails()) {
            abort(404);
        }

        $id = $request->input('id');
        $idArray = is_array($id) ? $id : [$id];

        $ret = [];

        $fields = FormField::whereIn('id', $idArray)->whereHas('formOptions')->with('formOptions')->get();

        foreach ($fields as $field) {
            $options = [];

            foreach ($field->formOptions as $option) {
                $options[] = $option->fetchFormatted();
            }

            $ret[$field->id] = $options;
        }

        return response()->json($ret);
    }

    public function saveField(Request $request)
    {
        $result = $this->handleSaveSingleField($request->all());
        if(!$result['success'])
            abort($result['status'], $result['message']);

        return response()->json($result);
    }

    public function saveFieldBulk(Request $request)
    {
        $input = $request->all();
        if(!is_array($input)) {
            abort(400);
        }

        $result = [];
        foreach($input as $formData) {
            $result_single = $this->handleSaveSingleField($formData);
            $result[$formData['id']] = $result_single;
        }

        return response()->json($result);
    }

    public function fileUpload(Request $request) 
    {
        $user = Auth::user();
        $userId = $user->id;

        $request->validate(array(
            'fieldId' => 'uuid|required|bail',
            'file' => 'max:300000',
        ));

        $file = $request->file('file');
        $extension = $file->extension();
        $isHeic = in_array($extension, ['heic', 'heif']);

        if(!$isHeic) {
            $request->validate(array(
                'file' => 'mimes:jpeg,bmp,png,gif,svg,pdf,docx,doc',
            ));
        }

        $fieldId = $request->input('fieldId');
        $originalName = $file->getClientOriginalName();
        $uploadPath = config('uploads.localPath', 'uploads/files')."/{$userId}";

        if($isHeic) {
            // Generating a random filename, and not using the image's
            // original filename to handle cases
            // that contain spaces or other weird characters
            $randomFilename = bin2hex(random_bytes(8));

            // Path for a temporary file from the upload
            // storage/app/heic_images/sample1.heic
            $tmpFilepath = Storage::path(
                self::TEMP_HEIC_STORAGE_DIR .
                $randomFilename . ".$extension"
            );

            $extension = "jpg";
            $newFilename = "{$fieldId}.{$extension}";

            // Path for a converted temporary file
            // storage/app/heic_images/sample1.jpg
            $convertedFilepath = Storage::path(
                $uploadPath . '/' .
                $newFilename
            );

            // Store the uploaded HEIC file on the server
            File::put($tmpFilepath, $file->getContent());

            // Run a shell command to execute ImageMagick conversion
            exec('convert ' . $tmpFilepath . ' ' . $convertedFilepath, $output, $result);

            // Remove the temporary files from storage
            unlink($tmpFilepath);

            $uploadSuccess = $uploadPath . '/' . $newFilename;
            $uploadSuccess = Storage::exists($uploadSuccess) ? $uploadSuccess : false;
            if(!$uploadSuccess)
                abort(400, 'Saving HEIC Image failed');
        } else {
            $newFilename = "{$fieldId}.{$extension}";
            $uploadSuccess = $file->storeAs($uploadPath, $newFilename);
            if(!$uploadSuccess)
                abort(400, 'Upload failed');
        }

        $isImage = in_array($extension, ['jpeg','jpg','bmp','png','gif','svg']);

        // compress settings to read from env
        $convert = config('image.webp') && $isImage;
        if($convert) {
            $fullPath = Storage::path($uploadSuccess);
            $sizer = new ImageSizer(
                $fullPath,
                null,
                null,
                null,
                null,
                true
            );
            if($sizer->resize()) { 
                $tmpScaled = @tempnam(sys_get_temp_dir(), '_SCALED_');
                $sizer->save($tmpScaled);
                $scaledContent = file_get_contents($tmpScaled);
    
                // Save as webp
                $newPath = $uploadSuccess . ".webp";
                if(Storage::put($newPath, $scaledContent)) {
                    FacadesFile::delete($fullPath);
                    $uploadSuccess = $newPath;
                }
            } else {
                Log::warning('[FieldController::fileUpload] Resizing failed with: ' . $fullPath);
            }
        }

        if ($uploadSuccess) {

            $userEntry = UserEntry::create([
                'user_id' => $userId,
                'form_field_id' => $fieldId,
                'value' => FormField::TYPE_UPLOAD,
                'sort' => 0,
            ]);

            if ($userEntry) {
                
                $userUpload = UserUpload::create([
                    'user_id'       => $userId,
                    'form_field_id' => $fieldId,
                    'user_entry_id' => $userEntry->id,
                    'file_name'     => $newFilename,
                    'original_name' => $originalName,
                    'file_type'     => $extension,
                ]);

                $userEntry->value = $userUpload->id;

                //rename the file the id
                $id = strtoupper($userUpload->id);
                $newFilename = "{$id}.{$extension}";

                $userUpload->file_name = $newFilename;  
                $userUpload->save(); 
                
                Storage::move($uploadSuccess,  $uploadPath.'/'.$newFilename);
                

                if ($userEntry->save()) {
                    return response()->json([
                        'success' => true,
                        'url' => url("storage/uploads/files/{$userId}/{$newFilename}")
                    ], 200); 
                }
            }
        }
        
        return response()->json('error', 400);
    }

    public function findImageUrl($fieldId)
    {
        try {
            $userId = Auth::user()->id;
            $fileData = UserUpload::where('form_field_id',$fieldId)->where('user_id',$userId)->get();
            if($fileData){
                $files = [];
                foreach($fileData as  $k=>$f){
                    $files[$k]['url'] = url("storage/uploads/files/{$userId}/{$f->file_name}");
                    $files[$k]['name'] = $f->original_name;
                }
            }
            return response()->json($files);
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'error' => $e
            ],400);
        }
       
    }

    public function fileUploadRemove($fieldId)
    {
        $userId = Auth::user()->id;
        try {
            $fileUpload = UserUpload::where('form_field_id',$fieldId)->where('user_id',$userId)->first();
            $fileUploadUseEntry = $fileUpload->userEntry;
            $path = config('uploads.localPath', 'uploads/files')."/{$userId}/{$fileUpload->file_name}";
            if($fileUpload->delete()){
                if($fileUploadUseEntry) {
                    $fileUploadUseEntry->delete();
                }
                
                if (Storage::disk()->exists($path)){
                    Storage::disk()->delete($path);
                }
            }
            return response()->json();
        } catch (\Exception $e) {
            return response()->json([
                'message' => $e->getMessage(),
                'error' => $e
            ],400);
        }
        
    }


    /**
     * get image signature
     */
    public function getSignatureImage($fieldId)
    {
        try {
            $userId = Auth::user()->id;
            $result = UserEntry::where('form_field_id',$fieldId)->where('user_id',$userId)->first();
            $url =$result->value;
            return response()->json($url);
        } catch (\Exception $ex) {
           return response()->json(400);
        }
    }

    /**
     * Util function
     * Save a single form field
     * @param $data: expected as ['id', 'value']
     * @return mixed [$status, $message]
     */
    private function handleSaveSingleField($data)
    {
        $userId = Auth::user()->id;
        $id = $data['id'] ?? '';
        $value = $data['value'] ?? '';
        $isBase64 = $this->is_base64($value);
        
        // Validate Request
        if(!$isBase64) {
            $validator = Validator::make(
                $data, 
                [
                    'id' => ['bail', 'required', 'max:255'],
                    'value' => ['bail', 'max:255'],
                ]
            );

            if ($validator->fails()) {
                return ['success' => false, 'status' => 400, 'message' => 'Bad request'];
            }
        }
       
        if (!$field = FormField::where('id', $id)->first()) {
            // Log::error("FIELD ERROR: Could not find form field with id {$id}.");
            return ['success' => false, 'status' => 404, 'message' => 'Could not find form field with id {$id}.'];
        }

        // check for encryption key: only for secure fields
        if ($field->is_secure && !$key = self::getKey()) {
            return ['success' => false, 'status' => 403, 'message' => 'Missing encryption key while saving secure field.'];
        }

        // Set-up form value to save
        $valueToSave = '';
        if($isBase64) {
            $pathImg = "signature/{$userId}/{$id}.png";
            $this->saveBase64AsPNG($value, $pathImg);
            $valueToSave = URL::to('/').'/storage/'.$pathImg;
        } else {
            $valueToSave = $value;
        }
        $valueArray = (is_array($valueToSave) ? $valueToSave : [$valueToSave]);

        // Save
        $sort = 0;
        foreach ($valueArray as $singleValue) {
            if ($field->isSecure()) {
                if(stristr($singleValue, 'placeholder') || strstr($singleValue, "***"))
                    continue;
                $singleValue = Crypto::encrypt($singleValue, $key);
            }

            //manually look for existing record
            $entry = UserEntry::where('user_id', $userId)
                ->where('form_field_id', $id)
                ->where('sort', $sort);
            
            if($entry->count() == 1) {
                // we'll update the existing entry
                $item = $entry->first();
            } elseif($entry->count() > 1) {
                //if there's more than one, delete them all and add a new one - this was happening periodically for some reason
                $entry->delete();
                $item = new UserEntry();
            } else {
                $item = new UserEntry();
            }
            $item->user_id = $userId;
            $item->form_field_id = $id;
            $item->sort = $sort;
            $item->value = $singleValue ?? "";
            $item->save();

            // Not sure if this was why, but we were getting duplicate entries for some fields
            // Replaced with the code above to try and eliminate potential dups
            // UserEntry::updateOrCreate([
            //     'user_id' => $userId,
            //     'form_field_id' => $id,
            //     'sort' => $sort
            // ],[
            //     'value' => $singleValue ?? "",
            //     'sort' => $sort,
            // ]);

            if($echoField = $field->findEchoField()) {
                UserEntry::updateOrCreate([
                    'user_id' => $userId,
                    'form_field_id' => $echoField->id,
                    'sort' => $sort
                ],[
                    'value' => $singleValue ?? "",
                    'sort' => $sort,
                ]); 
            }

            $sort ++;
        }

        // sleep(1);   //avoiding a deadlock with above updates?
        // UserEntry::where('sort', '>=', $sort)
        //          ->where('form_field_id', $id)
        //          ->where('user_id', $userId)
        //          ->delete();

        $this->checkLicenseTaskByField($field);

        // update fields in user table if needed
        $work_email = FormLookup::where('field_slug', FormLookup::FIELD_WORK_EMAIL)->first();
        $licensed = FormLookup::where('field_slug', FormLookup::LICENSE_IS_LICENSED_BOOL)->first();
        $passed_exam = FormLookup::where('field_slug', FormLookup::STATE_EXAM_STATUS)->first();
        
        if($id == $work_email->form_field_id) {
            $user = User::find($userId);
            $user->work_email = $value;
            $user->save();
        }
        if($id == $licensed->form_field_id) {
            $user = User::find($userId);
            $user->licensed = $value == 'YES' ? date("Y-m-d H:i:s", strtotime('now')) : null;
            $user->save();
        }
        if($id == $passed_exam->form_field_id) {
            $user = User::find($userId);
            $user->passed_exam = $value == 'YES' ? date("Y-m-d H:i:s", strtotime('now')) : null;
            $user->save();
        }

        // if($is_returning = FormLookup::where('field_slug', FormLookup::RETURNING_TO_SYMMETRY)->first()) {
        //     if($id == $is_returning->form_field_id) {
        //      $user = User::find($userId);
        //      $user->is_returning = $value == 'YES' ? true : false;
        //      $user->save();
        //     }
        // }
         
        return ['success' => true, 'status' => 200, 'message' => 'OK', 'value' => $value];
    }

    /**
     * Util function
     * Start or Finish task related to licensing
     * @param $field FormField: target field with modified value
     * @return void
     */
    private function checkLicenseTaskByField($field)
    {
        $userId = Auth::user()->id;

        $is_licensed = FormLookup::where('field_slug', FormLookup::LICENSE_IS_LICENSED_BOOL)->first();
        $passed_exam = FormLookup::where('field_slug', FormLookup::STATE_EXAM_STATUS)->first();
        $pre_licensing = FormLookup::where('field_slug', FormLookup::PRE_LICENSE_STATUS)->first();

        if($field->id == $is_licensed->form_field_id || $field->id == $passed_exam->form_field_id || $field->id == $pre_licensing->form_field_id) {

            $user_is_licensed = UserEntry::where('form_field_id', $is_licensed->form_field_id)
            ->where('user_id', $userId)
            ->first();

            $user_passed_exam = UserEntry::where('form_field_id', $passed_exam->form_field_id)
                ->where('user_id', $userId)
                ->first();

            $user_pre_licensing = UserEntry::where('form_field_id', $pre_licensing->form_field_id)
                ->where('user_id', $userId)
                ->first();

            if($user_passed_exam && $user_pre_licensing && $user_is_licensed) {

                if($user_is_licensed->value == 'NO' && $user_passed_exam->value == 'NO' && $user_pre_licensing->value == 'NO') {

                    $taskType = 'Fill Application';
                    $taskUserId = $userId;
                    $taskTargetId = $userId;
                    $description = 'Enroll in a pre-licensing course';

                    if (!$task = UserTasks::create($taskType)->forId($taskUserId)->target($taskTargetId)->description($description)->save()) {
                        Log::error("MY TASKS ERROR: Unable to make new task of type {$taskType} for user id {$taskUserId}");
                        abort(404);
                    }
                } else {
                    $task = UserTask::where('user_id', $userId)
                        ->where('description', 'Enroll in a pre-licensing course')
                        ->first();

                    if($task) $task->delete();
                }
            }
        }
    }

    /**
     * Util function
     * Save base64 as PNG at given path
     * @param $value String
     * @param $path String
     * @return bool
     */
    private function saveBase64AsPNG($value, $path, $disk = 'public') : bool
    {
        @list($type, $file_data) = explode(';', $value);
        @list(, $file_data) = explode(',', $file_data); 
        $resized_image = Image::make(base64_decode($file_data))->resize(300, 200)->stream('png', 100);
        Storage::disk($disk)->put($path, $resized_image);
        return true;
    }

    /**
     * Util function
     * Check if the string is base64
     * @param $str String
     * @return bool
     */
    function is_base64($str)
    {
        if(!is_string($str))
            return false;

        try{
            $res = substr($str, 0, 4);
            return (bool)($res =='data');
        }catch(Exception $ex){
            return false;
        }
    }

    /**
     * Util function
     * Get Encryption key
     * @return String
     */
    private static function getKey()
    {
        $secureKey = config('system.securekey', null);

        if (is_null($secureKey)) {
            Log::error("FIELD ERROR: System secure key not set.");
            return false;
        }

        return Key::loadFromAsciiSafeString($secureKey);
    }
}
