<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use UserTasks;
use Log;

class UserTaskController extends Controller
{
    /**
     * get tasks for current user
     */
    public function getTasks()
    {
        if (!$user = Auth::user()) {
            abort(404);
        }

        $tasks = UserTasks::get($user);
        
        $result = [];
        foreach($tasks as $task) {
            if($task->completed)
                continue;
                
            $result[] = [
                'id' => $task->id,
                'target_id' => $task->target_id,
                'form' => 'Onboarding Application',
                'agent' => $task->target->name,
                'status_slug' => $task->target->status->slug,
                'type' => $task->type,
                'description' => $task->description,
                'submitted_at' => $task->target->lastSubmittedAt(),
                'assigned_at' => $task->target->assignedAt(),
            ];
        }

        return response()->json([
            'items' => $result,
            'total' => count($result)
        ]);
    }

    /**
     * not needed now, all tasks are generated automatically
     */
    public function addTask(Request $request)
    {
        if (!Auth::user()->can('review applications')) {
            abort(404);
        }

        $validator = Validator::make($request->all(), [
            'targetId'  => ['uuid', 'bail', 'required'],
            'userId'    => ['uuid', 'bail', 'required'],
            'type'      => ['string', 'bail', 'required', 'max:255'],
        ]);

        if ($validator->fails()) {
            abort(404);
        }

        $taskType = $request->input('type');
        $taskUserId = $request->input('userId');
        $taskTargetId = $request->input('targetId');

        if (!$task = UserTasks::create($taskType)->forId($taskUserId)->target($taskTargetId)->save()) {
            Log::error("MY TASKS ERROR: Unable to make new task of type {$taskType} for user id {$taskUserId}");
            abort(404);
        }

        return response()->json($task);
    }


    /** not needed now, tasks are completed automatically */
    // UPDATED: now can manually mark as complete
    public function completeTask(Request $request)
    {

        UserTasks::completeById($request->input('id'));
        return $this->getTasks();


        $validator = Validator::make($request->all(), [
            'targetId'  => ['uuid', 'bail', 'required'],
        ]);

        if ($validator->fails()) {
            abort(404);
        }

        $taskTargetId = $request->input('targetId');

        if (!$task = UserTasks::complete($taskTargetId)) {
            Log::error("MY TASKS ERROR: Unable to complete task with id {$taskTargetId}");
            abort(404);
        }

        return response()->json($task);
    }
}
