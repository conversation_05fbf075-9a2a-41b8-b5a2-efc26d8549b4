<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\FormSection;
use App\Models\UserNote;
use App\Models\User;
use App\Models\UserStatusHistory;
use App\Models\UserUpload;
use App\Traits\UsesEncryption;
use App\Jobs\DownloadRemoteFile;
use App\Models\UserEntry;
use Log;

class UserApplicationReviewController extends Controller
{
    /**
     * get user notes of an application
     */
    public function getReviewsOf($id)
    {

        // TODO: Pull by application ID. For now, we'll pull by User ID. 
        if (!($user = User::find($id))) {
            Log::error("REVIEW ERROR: Could not find user with id {$id}.");
            abort(404);
        }

        return response()->json([
            'comments' => $user->comments,
            'notes' => $user->notes()->with(['reviewer', 'formSection.formPage'])->get()
        ]);
    }

    /**
     * store a note for a section/field
     */
    public function store(Request $request)
    {
        $reviewer = $request->user();

        $validator = Validator::make($request->all(), [
            'sectionId' => ['bail', 'required', 'max:255'],
            'sectionSort' => ['bail', 'required', 'integer'],
            'userId'    => ['bail', 'required', 'max:255'],
            'content'   => ['bail', 'max:255'],
        ]);

        if ($validator->fails()) {
            abort(404);
        }

        $userId     = $request->input('userId');
        $sectionId  = $request->input('sectionId');
        $sectionSort  = $request->input('sectionSort');
        $content    = $request->input('content') ?? '';

        // TODO: Pull by application ID. For now, we'll pull by User ID. 
        if (!User::where('id', $userId)->exists()) {
            Log::error("REVIEW ERROR: Could not find user with id {$userId}.");
            abort(404);
        }

        if (!FormSection::where('id', $sectionId)->exists()) {
            Log::error("REVIEW ERROR: Could not find form section with id {$sectionId}.");
            abort(404);
        }

        UserNote::updateOrCreate([
            'reviewer_id'       => $reviewer->id,
            'recruit_id'        => $userId,
            'form_section_id'   => $sectionId,
            'form_section_sort' => $sectionSort,
        ], [
            'content'           => $content,
            'fixed'             => 0
        ]);

        return response()->json(['status' => 'ok']);
    }

    /**
     * resolve any note for section/field
     */
    public function resolve(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'fixed' => ['bail', 'required', 'integer', 'max:1'],
        ]);

        if ($validator->fails()) {
            abort(404);
        }

        // TODO: Pull by note ID.
        if (!($note = UserNote::find($id))) {
            Log::error("REVIEW ERROR: Could not find review note with id {$id}.");
            abort(404);
        }

        $note->update([
            'fixed' => $request->fixed ?? 0
        ]);

        return response()->json(['status' => 'ok']);
    }

    public function getApplication(Request $request,$id) {
        // TODO: Pull by application ID. For now, we'll pull by User ID. 
        // TODO: Enable/disable endpoint based on Auth user permissions/roles.
        $reviewer = $request->user();

        if (!$user = User::find($id)) {
            abort(404);
        }

        $user_entries = UserEntry::where('user_id', $user->id)->with('formField.formSection')->orderBy('created_at', 'desc')->get();
        if (!$user_entries) {
            Log::error("REVIEW ERROR: Could not get application entries for user {$user->id}.");
            abort(404);
        }

        $user_entries_mapped = [];
        collect($user_entries)->each(function($entry) use (&$user_entries_mapped) {
            $section_id = $entry->formField->formSection->id;
            if (!isset($user_entries_mapped[$section_id])) {
                $user_entries_mapped[$section_id] = [];
            }
            $user_entries_mapped[$section_id][$entry->form_field_id][$entry->sort] = $entry->value;
        });

        $form_pages = FormPage::orderBy('sort')->get();
        if (!$form_pages) {
            Log::error("REVIEW ERROR: Unable to fetch form pages.");
            abort(404);
        }

        $allPages = [];

        foreach ($form_pages as $page) {
            
            $pageContent = [
                'id'        => $page->id,
                'label'     => $page->label,
                'sections'  => [],
            ];

            $sections = $page->formSections()->get();

            foreach ($sections as $section) {

                if (!isset($user_entries_mapped[$section->id])) {
                    continue;
                }

                $pageSection = [
                    'id'        => $section->id,
                    'label'     => $section->label,
                    'fields'    => [],
                ];

                foreach ($section->formFields()->orderBy('sort')->get() as $field) {

                    if (!isset($user_entries_mapped[$section->id][$field->id])) {
                        continue;
                    }

                    $filePath = false;
                    if ($field->type == FormField::TYPE_UPLOAD) {
                        $userUploadId = $user_entries_mapped[$section->id][$field->id][0];
                        if (!$upload = UserUpload::find($userUploadId)) {
                            Log::error("REVIEW ERROR: Could not get user upload entry with id {$userUploadId}.");
                            abort(404);
                        }
                        $filePath = $upload->localPath();
                    }

                    $pageFields = [];

                    foreach ($user_entries_mapped[$section->id][$field->id] as $sort => $value) {

                        $outputValue = $value;

                        if ($field->isSecure()) {
                            // $outputValue = ($reviewer->can('view encrypted') ? self::decrypt($value) : "******");
                            $outputValue = self::decrypt($value);
                        }

                        if ($filePath) {
                            $outputValue = $filePath;
                        }

                        $pageFields[] = [
                            'id'        => $field->id,
                            'label'     => $field->label,
                            'value'     => $outputValue,
                            'sort'      => $sort,
                        ];
                    } // end sorted value loop

                    if (sizeof($pageFields) > 0) {
                        $pageSection['fields'] = array_merge($pageSection['fields'], $pageFields);
                    }

                } // end field loop

                if (sizeof($pageSection['fields']) > 0) {
                    $pageContent['sections'][] = $pageSection;
                }

            } // end section loop

            if (sizeof($pageContent['sections']) > 0) {
                $allPages[] = $pageContent;    
            }

        } // end page loop

        return $allPages;
    }
}
