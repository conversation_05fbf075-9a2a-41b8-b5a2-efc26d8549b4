<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     *
     * @param  \App\Http\Requests\Auth\LoginRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(LoginRequest $request)
    {
        $request->authenticate();

        // $token = $request->user()->createToken('authToken');
        $request->session()->regenerate();

        return 'success';
    }

    /**
     * Destroy an authenticated session.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy(Request $request)
    {
        $isAuth0User = !empty(Auth::user()->auth0_metadata);

        // Not sure why, but this line was throwing an error and causing the logout to fail.
        // Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        // $request->user()->tokens()->delete();

        if($isAuth0User)
            return response()->json(['auth0LogoutUrl' => route('auth0.logout')]);
        return true;
    }

    /**
     * Impersonate a user.
     *
     * @param  App\Models\User; $user
     * @return \Illuminate\Http\RedirectResponse
     */
    public function impersonate(User $user) 
    {
        Auth::user()->impersonate($user);
        return redirect('/app/portal');
    }

    /**
     * Leave/stop impersonating a user.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function leaveImpersonate() 
    {
        Auth::user()->leaveImpersonation();
        return redirect('/app/application/all_application'); 
    }

}
