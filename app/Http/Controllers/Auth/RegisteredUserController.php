<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserInvite;
use App\Models\UserInvitesGroup;
use App\Models\Agent;
use App\Providers\RouteServiceProvider;
use DB;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Response;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Validation\ValidationException
     *
     */

    public function getUserByCode(Request $request)
    {
        $invite = UserInvite::where('code', $request->code)->first();
        if($uplineAgent = $invite->uplineAgent)
            $invite->agency_owner = $this->agencyOwner($uplineAgent);
        return response()->json($invite);
    }

    public function agencyOwner($agent)
    {
        do {
            if($agent->IsAgencyOwner)
                return $agent;
        } while($agent = $agent->directUpline);
        return null;
    }

    /**
     * Handle an incoming registration request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            // 'code' => 'required|nullable',
            // 'group_code' => 'required|nullable',
            'code' => [
                'nullable',
                function ($attribute, $value, $fail) use ($request) {
                    if (empty($request->code) && empty($request->group_code)) {
                        return $fail('Either code or group code is required.');
                    }
                },
            ],
            'group_code' => 'nullable'
        ]);

        // if this is a group invitation code/link, we need to validate and create a new user_invites record before continuing
        $user_invite = false;
        if(!empty($request->group_code)) {
            if(!$group_invitation = UserInvitesGroup::where('group_code', $request->group_code)->first())
                return Response::json(['message' => 'Invitation not found!!'], 404);
            if(!$group_invitation->active)
                return Response::json(['message' => 'Invitation is inactive or expired'], 400);

            $user = User::find($group_invitation->user_id);
            $agent = Agent::where('AgentCode', $user->agent_code)->first();

            if(config('app.tenant') == 'Q2B') {
                $type = 'b2b-agent';
            } else {
                $type = '';
            }

            //add new user invites record
            $code = (string) Str::uuid();
            $userInviteData = [
                'user_id'           => $group_invitation->user_id,
                'email'             => $request->email,
                'name'              => $request->name,
                'phone'             => '',
                'invite_expired'    => 0,
                'blocked'           => 0,
                'code'              => $code,
                'invite_sent'       => 1,
                'contract_level'    => 80,
                'upline_agent_id'   => $agent->AgentID,
                'corporate'         => false,
                'person_message'    => '',
                'source'            => 'Group',
                'license_status'    => null,
                'experience'        => 'experienced',
                'advanced_markets'  => null,
                'group_id'          => $group_invitation->id,
                'type'              => $type,
            ];
            $user_invite = UserInvite::create($userInviteData);
        }

        if(!$user_invite)
            $user_invite = UserInvite::where('code', $request->code)->first();

        if ($user_invite) {
            $inviter_user_id = $user_invite->user_id;
            $inviter = User::find($inviter_user_id);

            if (!$inviter) {
                return Response::json(['message' => 'Inviter doesn\'t exist'], 404);
            }

            if ($user_invite->invite_expired) {
                return Response::json(['message' => 'Invite Link Expired'], 400);
            }
        } else {
            return Response::json(['message' => 'A Valid Invite Code Is Required'], 400);
        }

        // TECHDEBT: we have duplicate data source for inviter_agent_code ( also in Agent table )
        //              save this field in 'users' table for speed-purpose
        // IF USER ALREADY EXISTS: check with name & email

        try {
            DB::beginTransaction();

            $user = User::updateOrCreate([
                'name' => $request->name,
                'email' => $request->email,
            ], [
                'password' => Hash::make($request->password),
                'inviter_user_id' => $inviter_user_id,
                'inviter_agent_code' => $inviter->agent_code,
                'user_status_id' => null,
                'contract_level' => $user_invite->contract_level ?? 80,
                'upline_agent_id' => $user_invite->upline_agent_id ?? $user_invite->user_id ?? null,
                'recruiter_name' => $inviter->name,
                'source' => $user_invite->source ?? '',
                'user_invite_id' => $user_invite->id,
                'type' => $user_invite->type,
            ]);

            $user->update([
                'user_status_id' => UserStatus::where('slug', 'registration')->first()->id ?? null,
            ]);

            $user->assignRole(User::ROLE_TYPE_RECRUIT);

            // get agency owner id
            if ($agencyOwner = $user->agencyOwner()) {
                $user->agency_owner_code = $agencyOwner->AgentCode;
                $user->agency_owner_name = $agencyOwner->AgentName;
                $user->save();
            }

            if($uplineAgent = $user->uplineAgent()->first()) {
                $user->upline_agent_name = $uplineAgent->AgentName;
                $user->save();
            }

            // $status = User::create([
            //     'user_id' => $user->id,
            //     'status' => User::STATUS_REGISTER,
            // ]);

            if ($user_invite) {
                $user_invite->update([
                    'invite_expired' => 1,
                    'invite_accepted' => 1
                ]);
            }

            event(new Registered($user));

            Auth::login($user);

            // $token = $user->createToken('authToken');
            $request->session()->regenerate();

            DB::commit();
        } catch (\Illuminate\Database\QueryException $e) {
            DB::rollBack();
            \Log::error('Registration Database Error: ' . $e->getMessage());
            throw $e;
        } catch (\Throwable $th) {
            DB::rollBack();
            \Log::error('Registration Error: ' . $th->getMessage());
            throw $th;
        }

        return Response::json([
            'user' => $user,
            'type' => $user->userInvite->type ?? null,
            'user_invite' => $user->userInvite,
            'group' => [
                'name' => $user->userInvite->group->group_name ?? null,
                'code' => $user->userInvite->group->group_code ?? null,
            ],
        ], 201);
    }
}
