<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use App\Http\Controllers\Controller;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use App\Models\Agent;

class Auth0Controller extends Controller
{
    public function login()
    {   
        return Socialite::driver('auth0')->redirect();
    }

    public function logout()
    {
        $auth0LogoutUrl = config('services.auth0.base_url') . "/v2/logout?" . 
                            "client_id=" . config('services.auth0.client_id') .
                            "&returnTo=" . config('app.url');

        return redirect($auth0LogoutUrl);
    }

    public function callback()
    {
        $raw_user = Socialite::driver('auth0')->user();
        
        // If we are impersonating, we need to log in as the agent being impersonated
        if (isset($raw_user->user[User::AUTH0_KEY_METADATA]['RevertAgentCode'])) {
            $agentCode = $raw_user->user[User::AUTH0_KEY_METADATA]['AgentCode'];
            $user = User::where('agent_code', $agentCode)->first();
            if (! $user) {
                abort(404, "User not found");
            }
            Auth::login($user);
            return redirect('/auth/login');
        }

        $agent_code = '';
        if($raw_user->user[User::AUTH0_KEY_METADATA]) {
            $agent_code = $raw_user->user[User::AUTH0_KEY_METADATA]['AgentCode'] ?? '';
        }
        $roles = isset($raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole']) ? [$raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole']] : $raw_user->user[User::AUTH0_KEY_ROLES];
        
        // Get the user while excluding "-stale" emails
        $users = User::where('agent_code', $agent_code)
        ->where('placeholder', 1)
        ->where('email', 'not like', '%-stale')
        ->get();

        // Prefer users with the Auth0 sub/id
        $user = $users->firstWhere('sub', $raw_user->user['sub']) ?: $users->first();

        $agent = Agent::where('AgentCode', $agent_code)->get()[0] ?? null;
        if(!$agent) {
            abort(404, "Agent not found");
        }

        $agent_email = $raw_user->user['email'];

        // if being impersonated, override with email of the agent we are impersonating
        // instead of using the email from Auth0 which is the admin/staff user doing the impersonating, we'll use the email stored with the AgentCode we're impersonating
        if(isset($raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole']))
            $agent_email = $agent->AgentEmail;


        if($user)
        {
            // only update record if not being impersonated - OverrideRole is set in user Auth0 meta data when impersonating an agent
            if(!isset($raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole']))
            {
                $user->update([
                    'sub' => $raw_user->user['sub'],
                    'email' => $agent_email,
                    'name' => $agent->AgentName ?? '',
                    'auth0_metadata' => json_encode($raw_user->user),
                    'placeholder' => 0
                ]);
            }
        }
        else
        {
            $isUnlicensedAgent = in_array(User::ROLE_TYPE_UNLICENSED_AGENT, $roles);

            //do not update/create if impersonating
            if($isUnlicensedAgent && !isset($raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole'])) {
                $user = User::updateOrCreate(['email' => $agent_email], [
                    'name' => $agent->AgentName ?? '',
                    'auth0_metadata' => json_encode($raw_user->user),
                    'agent_code' => $agent_code,
                    'sub' => $raw_user->user['sub']
                ]);
            } elseif(isset($raw_user->user[User::AUTH0_KEY_METADATA]['OverrideRole'])) {
                //impersonating agent
                $user = User::where([
                    'agent_code' => $agent_code,
                    'placeholder' => 0
                ])->first();
                //abort if not found by agent_code
                if(!$user)
                    abort(404, "User not found");
            } else {
                try {
                    $user = User::firstOrCreate(['sub' => $raw_user->user['sub']], [
                        'email' => $agent_email,
                        'name' => $agent->AgentName ?? '',
                        'auth0_metadata' => json_encode($raw_user->user),
                        'agent_code' => $agent_code
                    ]);
                } catch(\Exception $e) {
                    $user = User::firstOrCreate(['email' => $raw_user->user['email']], [
                        'email' => $agent_email,
                        'name' => $agent->AgentName ?? '',
                        'auth0_metadata' => json_encode($raw_user->user),
                        'agent_code' => $agent_code,
                    ]);
                }

                $user->update([
                    'email' => $agent_email,
                    'name' => $agent->AgentName == User::SFGAGENCY_ACCOUNT ?
                        $raw_user->user['name'] : ($agent->AgentName ?? ''),
                    'auth0_metadata' => json_encode($raw_user->user),
                    'sub' => $raw_user->user['sub'],
                    'agent_code' => $agent_code,
                ]);
            }
        }

        if (sizeof($roles) > 0) {
            $user->assignRole($roles);
        }

        Auth::login($user);

        return redirect('/auth/login');
    }
}
