<?php

namespace App\Http\Controllers;

use App\Models\StoredEmail;

class EmailController extends Controller
{
    public function index()
    {
        $emails = StoredEmail::orderBy('sent_at', 'desc')->paginate(20);
        return response()->json($emails);
    }

    public function show($id)
    {
        $email = StoredEmail::findOrFail($id);
        return response()->json($email);
    }

    public function destroy($id)
    {
        StoredEmail::findOrFail($id)->delete();
        return response()->json(['message' => 'Email deleted']);
    }
} 
