<?php

namespace App\Http\Controllers;

use App\Http\Resources\Agent as ResourcesAgent;
use App\Models\FormLookup;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserEntry;
use App\Models\UserStatusHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Models\Agent;
use App\Models\UserSignature;
use App\Models\UserNotification;
use App\Models\FormField;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use FormBuilder;
use File;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function show(User $user)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function edit(Request $request)
    {
        $user = User::find($request->user_id);
        if($request->upline_agent_id > 0) {
            $user->upline_agent_id = $request->upline_agent_id;
            if($uplineAgent = $user->uplineAgent()->first()) {
                $user->upline_agent_name = $uplineAgent->AgentName;
            }
        }
        if($request->contract_level != null)
            $user->contract_level = $request->contract_level;

        if($request->user_status_slug != null) {
            if($user_status = UserStatus::where('slug', $request->user_status_slug)->first()){
                if($request->user_status_slug == 'submitted-ho')
                    $user->approved_by_ao = 1;
                $user->user_status_id = $user_status->id;
            }
        }
    
        $user->save();
        return $user;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, User $user)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function destroy(User $user)
    {
        //
    }

    public function corporateDivision(Request $request){
        $user = $request->user();
        return json_encode($user->corporateDivision());
    }

    public function possibleContractLevels(Request $request)
    {
        $user = $request->user();
        // Staff or SuperAdmin get all contract levels with no upper limit
        if ($user->hasRole('Staff') || $user->hasRole('SuperAdmin')) {
            if (config('app.tenant') == 'Q2B') {
                return array_map('strval', range(60, 165, 5));
            }
            return $user->possibleContractLevels()->pluck('LevelName')->unique()->sort()->values();
        }

        if (config('app.tenant') == 'Q2B') {
            $levels = array_map('strval', range(60, 165, 5));
            if ($user->agent_code) {
                $agent = \App\Models\Agent::where('AgentCode', $user->agent_code)->first();
                // Remove any levels that are equal or greater than the agent's contract level
                if ($agent && $agent->ContractLevel) {
                    $levels = array_filter($levels, function($level) use ($agent) {
                        return $level < $agent->ContractLevel;
                    });
                    // Return the filtered levels array
                    return array_values($levels);
                }
            }
            return $levels;
        }

        $levels = $user->possibleContractLevels()->pluck('LevelName')->filter(function($level) {
            return $level >= 80;
        })->flatten()->push("80")->unique()->sort();
        return $levels;
    }

    public function possibleStates(Request $request)
    {
        $states = config('states');
        $result = [];
        foreach($states as $key => $val)
            $result[] = ['value' => $key, 'text' => $val];

        return $result;
    }

    public function assignableUpline(Request $request)
    {
        $user = $request->user();
        return $user->possibleDownLine($request['agent_code']);
    }

    public function getCurrentUser(Request $request) {
        $user = $request->user();
        $user->load(['roles.permissions', 'status', 'statusHistory.userStatus', 'notifications']);
        $user->is_signature_signed = App::environment(['local']) || $user->isSignatureSigned();
        // $user->is_signature_signed = $user->isSignatureSigned();

        //user id of the admin user if the current user is being impersonated
        $user->impersonated_by = $request->session()->get('impersonated_by');

        $user->npn = $user->getMeta('npn', '');
        $user->last_read_notification = $user->getMeta('last_read_notification', '2023-01-01');

        $user->is_citizen = FormBuilder::fetchUserEntry($user, FormLookup::IS_CITIZEN_BOOL) == FormField::SELECT_BOOL_YES;

        //strip roles from impersonated user?
        // \Log::info("getCurrentUser". print_r($user, true));
        return $user;
    }

    public function setUserHQNPN(Request $request)
    {

        $request->validate([
            'NPN' => 'required|numeric',
            'AgentCode' => 'required',
        ]);

        $agentcode = $request->AgentCode;
        $npn = $request->NPN;

        $success = false;
        $message = "";

        $user = User::where('agent_code', $agentcode)->first();
        $form = FormLookup::where('field_slug', FormLookup::LICENSE_NPN)->first();

        $agent = Agent::find($agentcode);

        if(isset($user->id)){
            try {
                UserEntry::updateOrCreate([
                    'user_id' => $user->id,
                    'form_field_id' => $form->form_field_id,
                    'value' => $npn,
                    'sort' => 0
                ]);

                /*
                 * BEGIN UPDATE USER LICENSE FIELD
                 * This field is used in reporting to track a "converted" status, 
                 * meaning they became licenses after starting their app
                 */
                $licensed_field = FormLookup::where('field_slug', FormLookup::LICENSE_IS_LICENSED_BOOL)->first();
                $entry = UserEntry::where('user_id', $user->id)->where('form_field_id', $licensed_field->form_field_id)->first();

                if(!isset($entry->value)) {
                    // NULL would indicate that the Licensed question is unanswered, which shouldn't be the case anymore
                    // create a new entry with the updated date matching the contract start date
                    $entry = UserEntry::create([
                        'user_id' => $user->id,
                        'form_field_id' => $licensed_field->form_field_id,
                        'value' => 'YES',
                        'sort' => 0
                    ]);
                    
                    // manually override create and update dates
                    $entry->updated_at = $user->ContractStartDt;
                    $entry->created_at = $user->created_at; //using this so that it's before the updated_at date
                    $entry->save();

                } elseif ($entry->value == 'NO') {
                    // update the entry from NO to YES and the updated_at date as the contract start date
                    $entry->value = 'YES';
                    $entry->save();
                }
                // If the entry value is YES, then we leave it as is.
                // END UPDATE USER LICENSE FIELD


                $success = true;
                $message = 'Success';

            } catch (\Exception $e) {
                $success = false;
                $message = $e->getMessage() . "\n" . $e->getTraceAsString();
            }
        } else {
            $success = false;
            $message = "No agent found for AgentCode: $agentcode";
        }

        return [
            'success' => $success,
            'message' => $message,
            'agent' => new ResourcesAgent($agent),
            // 'user_id' => $user->id,
            // 'form_id' => $form->id,
            // 'form_field_id' => $form->form_field_id,
        ];
    }

    public function download_ica(Request $request)
    {
        $user = $request->user();
        $email = str_replace("-stale", "", $user->email);
        $signature = UserSignature::where('user_email', $email)
                              ->where('event_type', UserSignature::EVENT_DOWNLOADABLE)
                              ->first();

        if (!$signature) {
            abort(404);
        }

        $relativePath = "uploads/signatures/{$signature->file_name}";
        $completePath = storage_path("app/{$relativePath}");

        if (!File::exists($completePath)) {
            // TODO: refactor to HelloSignService
            $HelloSignController = resolve('App\Http\Controllers\HelloSignController');
            $HelloSignController->downloadFiles($signature->signature_request_id);
        }
        
        // Use the relative path with Storage facade (fixes the metadata error)
        return Storage::download($relativePath, basename($relativePath));
    }

    public function updateLastReadNotification(Request $request)
    {
        $user = $request->user();
        $now = Carbon::now();
        $user->setMeta('last_read_notification', $now);

        return $now;
    }

    public function getNotifications(Request $request)
    {
        $user = $request->user();
        $notifications = UserNotification::where('user_id', $user->id)->get();
        return $notifications;
    }

    public function adminUpdateEmail(Request $request, $id)
    {
        $request->validate([
            'email' => 'required|email|unique:users,email,' . $id,
            'name'  => 'required|string|max:255'
        ]);

        $user = User::findOrFail($id);
        $oldEmail = $user->email;
        $oldName = $user->name;
        
        try {
            
            $user->forceFill([
                'email' => $request->email,
                'name' => $request->name
            ])->save();

            return response()->json([
                'message' => 'Email and name updated successfully'
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating user email and name', [
                'user_id' => $user->id,
                'error_message' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'error_trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'message' => 'Failed to update email and name: ' . $e->getMessage()
            ], 500);
        }
    }
}
