<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use App\Models\FormField;
use App\Models\FormOption;
use App\Models\FormSection;
use App\Models\FormPage;
use App\Models\UserStatus;
use App\Models\UserEntry;
use App\Models\User;
use Defuse\Crypto\Crypto;
use Defuse\Crypto\Key;
use Log;
use FormBuilder;
use App\Models\FormLookup;

class FormController extends Controller
{
    public function view(Request $request) {

        $user = Auth::user();

        // Check application status
        if(!$user->status->formPage && $user->status->slug != 'revision-ho' && !$request->session()->get('impersonated_by')) {
            Log::error("FORM ERROR: Your application is not editable now. #" . $user->id);
            abort(403);
        }

        Log::info("Viewing form for user ID: {$user->id}", $request->all());

        // TODO: Rework this endpoint to pull from active user account
        // this setup is temporary for now  while in early development

        $validator = Validator::make($request->all(), [
            'step' => ['bail', 'required', 'max:255'],
        ]);

        if ($validator->fails()) {
            Log::warning("FORM VALIDATION ERROR: Validation failed for user ID: {$user->id}. Errors: ", $validator->errors()->all());
            abort(400);
        }

        $step = $request->input('step');
        if(config('app.tenant') == 'Q2B') {
            if (!$page = FormPage::where('step_ident', $step)->where('application_slug', $user->type)->first()) {
                Log::error("FORM ERROR: Could not find form page with step ident {$step} and application_slug {$user->type}.");
                abort(404);
            }
        } else {
            if (!$page = FormPage::where('step_ident', $step)->first()) {
                Log::error("FORM ERROR: Could not find form page with step ident {$step}.");
                abort(404);
            }
        }
        

        $user_status = UserStatus::where('form_page_id', $page->id)->first();
        if($user_status) {
            $user->update([
                'user_status_id' => $user_status->id
            ]);
            Log::info("User ID: {$user->id} status updated to {$user_status->id} for page ID: {$page->id}");

        }

        $sortedEntries = [];

        if ($userEntries = UserEntry::where('user_id', $user->id)->get()) {
            Log::info("Fetched user entries for user ID: {$user->id}. Total entries: " . $userEntries->count());
            foreach ($userEntries as $userEntry) {
                $sortedEntries[$userEntry->form_field_id][$userEntry->sort] = $userEntry->value;
            }
        }

        $pageContent = $page->load(['formSections' => function($query) use ($user) {
            $query
                ->where(function($query) use ($user) {
                    $query->where('active_start', '<=', $user->created_at)
                        ->orWhereNull('active_start');
                })
                ->where(function($query) use ($user) {
                    $query->where('active_end', '>=', $user->created_at)
                    ->orWhereNull('active_end');
                })
                ->with('formFields');
        },'formSections.formConditions']);

        foreach ($pageContent->formSections as &$pageSection) {
            foreach ($pageSection->fields as &$pageField) {
                $returnValue = '';
                if (isset($sortedEntries[$pageField->id])) {
                    if ($pageField->is_secure == 1) {

                        //decryp information
                        $secureKey = config('system.securekey', null);
                        $key = Key::loadFromAsciiSafeString($secureKey);;
                        $returnValue = Crypto::decrypt((sizeof($sortedEntries[$pageField->id]) > 1 ? $sortedEntries[$pageField->id] : $sortedEntries[$pageField->id][0]), $key);
                        
                        if(substr($pageField->placeholder,0,2) == '**'){
                            $returnValue = config('system.secure-placeholder');
                        }else{
                            //REMOVING MASKING FOR APP FORM FIELD VALUE WHILE IT'S BEING EDITED
                            // //obtain suffix
                            // $suffix = substr($returnValue, -3);
                            // $prefix = str_repeat('*',max(0, strlen($returnValue)-3));
                            // //returns special characters
                            // $returnValue = $prefix.$suffix;
                        }
                    
                    } else {
                        $returnValue = (sizeof($sortedEntries[$pageField->id]) > 1 ? $sortedEntries[$pageField->id] : $sortedEntries[$pageField->id][0]);
                    }
                }
                $pageField['value'] = $returnValue;
            }
        }

        $pageContent->is_citizen = FormBuilder::fetchUserEntry($user, FormLookup::IS_CITIZEN_BOOL) == FormField::SELECT_BOOL_YES;

        Log::info("Successfully retrieved form content for user ID: {$user->id}");

        return response()->json($pageContent);
    }

    public function getOnBoardSteps($application_slug = null)
    {
        $user = Auth::user();

        $steps = FormPage::whereNotNull('sort')
                         ->where('status', 'active')
                         ->where('application_slug', $user->type)
                         ->orderBy('sort')
                         ->get();
                         
        Log::info("Retrieved onboarding steps. Total steps: " . $steps->count());
        return response()->json($steps);
    }
}
