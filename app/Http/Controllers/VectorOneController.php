<?php

namespace App\Http\Controllers;

use App\Services\VectorService;
use Illuminate\Http\Request;

class VectorOneController extends Controller
{
    protected $vectorService;

    public function __construct(VectorService $vectorService)
    {
        $this->vectorService = $vectorService;
    }

    public function searchSSN(Request $request)
    {
        $ssn = $request->input('ssn');
        $state = $request->input('state');
        $response = $this->vectorService->searchBySSN($ssn, $state);

        return response()->json($response);
    }

    public function searchTaxID(Request $request)
    {
        $taxId = $request->input('tax_id');
        $state = $request->input('state');
        $response = $this->vectorService->searchByTaxID($taxId, $state);

        return response()->json($response);
    }
}
