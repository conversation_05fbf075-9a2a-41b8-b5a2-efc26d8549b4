<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
use Mail;
use App\Mail\SupportTicketMail;

class SupportTicketController extends Controller
{
    public function submitted(Request $request)
    {
        $user = Auth::user();
        $subject = $request->subject;
        $message = $request->message;

        Mail::to('<EMAIL>')->send(new SupportTicketMail($user, $subject, $message));

        return 'ok';
    }
}
