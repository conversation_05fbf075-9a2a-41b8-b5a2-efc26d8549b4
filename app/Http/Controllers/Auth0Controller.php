<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\User;

class Auth0Controller extends Controller
{
    /**
     * Get the user metadata from Auth0
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserMetadata(Request $request)
    {
        // Get the authenticated user
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['error' => 'User not authenticated'], 401);
        }
        
        // Get Auth0 token for Management API
        $token = $this->getManagementApiToken();
        
        if (!$token) {
            return response()->json(['error' => 'Failed to get Auth0 Management API token'], 500);
        }
        
        // Use the sub field from the user model as the Auth0 ID
        if (!$user->sub) {
            return response()->json(['error' => 'User does not have an Auth0 ID'], 404);
        }
        
        $auth0UserId = $user->sub;
        
        // Make request to Auth0 Management API
        $auth0Domain = config('services.auth0.base_url', 'https://test-laravel.auth0.com');
        $response = Http::withToken($token)
            ->get($auth0Domain . '/api/v2/users/' . $auth0UserId);
            
        if ($response->successful()) {
            $metadata = $response->json();
            
            // Structure the response data
            $responseData = [
                'auth0_id' => $auth0UserId,
                'auth0_metadata' => $metadata['app_metadata'] ?? null,
                // 'user_metadata' => $metadata['user_metadata'] ?? null,
            ];
            
            // Add metadata from our stored user for comparison
            if ($user->auth0_metadata) {
                $responseData['stored_metadata'] = $user->auth0_metadata[User::AUTH0_KEY_METADATA];
            }
            
            // Check if AgentCode in auth0_metadata is different from stored_metadata.AgentCode
            $auth0AgentCode = $responseData['auth0_metadata']['AgentCode'] ?? null;
            $storedAgentCode = $responseData['stored_metadata']['AgentCode'] ?? null;
            
            if ($auth0AgentCode && $storedAgentCode && $auth0AgentCode !== $storedAgentCode) {
                // Look up user by agent_code
                $newUser = User::where('agent_code', $auth0AgentCode)->first();
                
                if ($newUser) {
                    // Log the agent code switch
                    Log::info('Switching user due to AgentCode change', [
                        'from_user_id' => $user->id,
                        'from_agent_code' => $storedAgentCode,
                        'to_user_id' => $newUser->id,
                        'to_agent_code' => $auth0AgentCode
                    ]);
                    
                    // Instead of using Auth::login, store the user ID in the session
                    // We'll let the frontend handle the reload
                    $responseData['reload_required'] = true;
                    $responseData['new_user_id'] = $newUser->id;
                    
                    // The frontend will reload, which will cause a new session to be created
                    // with the new user ID from our redirect
                }
            }
            
            return response()->json($responseData);
        }
        
        Log::error('Failed to retrieve Auth0 user metadata', [
            'status' => $response->status(),
            'response' => $response->body(),
            'user_id' => $user->id,
            'auth0_id' => $auth0UserId
        ]);
        
        return response()->json(['error' => 'Failed to retrieve user metadata'], 500);
    }
    
    /**
     * Handle login redirection when AgentCode has changed
     * 
     * @param Request $request
     * @param string $userId
     * @return \Illuminate\Http\Response
     */
    public function switchUser(Request $request, $userId)
    {
        $newUser = User::find($userId);
        
        if (!$newUser) {
            return response()->json(['error' => 'User not found'], 404);
        }
        
        // Log out the current user
        Auth::guard('web')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        // Log in as the new user
        Auth::guard('web')->loginUsingId($newUser->id);
        $request->session()->regenerate();
        
        return redirect('/');
    }
    
    /**
     * Get a token for the Auth0 Management API
     *
     * @return string|null
     */
    private function getManagementApiToken()
    {
        // Using existing Auth0 HQ credentials from config
        $auth0Domain = config('services.auth0.base_url', 'https://test-laravel.auth0.com');
        $clientId = config('services.hq_auth0.client_id', config('services.auth0.client_id'));
        $clientSecret = config('services.hq_auth0.client_secret', config('services.auth0.client_secret'));
        $audience = $auth0Domain . '/api/v2/';
        
        $response = Http::post($auth0Domain . '/oauth/token', [
            'client_id' => $clientId,
            'client_secret' => $clientSecret,
            'audience' => $audience,
            'grant_type' => 'client_credentials'
        ]);
        
        if ($response->successful()) {
            $data = $response->json();
            return $data['access_token'];
        }
        
        Log::error('Failed to get Auth0 Management API token', [
            'status' => $response->status(),
            'response' => $response->body()
        ]);
        
        return null;
    }
} 