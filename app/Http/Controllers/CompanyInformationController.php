<?php

namespace App\Http\Controllers;

use App\Models\CompanyInformation;
use App\Models\CompanyManage;
use App\Models\User;
use Illuminate\Http\Request;
use DB;
use Str;
use Agents;
use Illuminate\Support\Facades\Log;

class CompanyInformationController extends Controller
{

  public function getCompanyInfo(Request $request)
  {
    $user = $request->user();
    Log::info('User ID ' . $user->id . ' is requesting company information.');

    //Return same content for everyone 
    if(env('APP_TENANT') == 'Q2B')
      return CompanyInformation::find('27dcd7b3-d862-4c6a-88c2-01da7ee4f7ec');
    return CompanyInformation::find('91C5305F-A0BA-4315-94CD-91451EBF1D20');
    

    if($company = CompanyManage::where('user_id', $user->id)->first()){
      Log::info('Returning company information for user ID: ' . $user->id);
      return $company->company_information;
    }

    $agencyOwner = $user->agencyOwner();
    if($agencyOwner && $company = CompanyManage::where('user_id', $agencyOwner->id)->first()){
      Log::info('Returning company information for agency owner ID: ' . $agencyOwner->id);
      return $company->company_information;
    }

    $inviter = $user->invitedBy;
    if($inviter && $company = CompanyManage::where('user_id', $inviter->id)->first()){
      Log::info('Returning company information for inviter ID: ' . $inviter->id);
      return $company->company_information;
    }

    $superAdmin = Agents::getOneWithRole(User::ROLE_TYPE_SUPER_ADMIN);
    if($superAdmin && $company = CompanyManage::where('user_id', $superAdmin->id)->first()){
      Log::info('Returning company information for super admin ID: ' . $superAdmin->id);
      return $company->company_information;
    }
    Log::warning('No company information found for user ID: ' . $user->id);
    return null;
  }

  public function saveInformation(Request $request)
  {
    $user_id = $request->user_id;
    Log::info('Saving information for user ID: ' . $user_id);

    $info = null;
    $company = CompanyManage::where('user_id', $user_id)->first();
    if($company){
      $info = $company->company_information;
    }

    if ($info) {
      Log::info('Updating existing company information for user ID: ' . $user_id);

      $info = CompanyInformation::where('id', $info->id)->first();
      $info->content = $request->content ?? $info->content;
      $info->admin_content = $request->admin_content ?? $info->admin_content;
      $info->save();
      Log::info('Company information updated successfully for user ID: ' . $user_id);

    } else {
      Log::info('Creating new company information for user ID: ' . $user_id);

      try {
        DB::beginTransaction();
        
        $info = new CompanyInformation();
        $info->id = Str::uuid();
        $info->content = $request->content ?? $info->content;
        $info->admin_content = $request->admin_content ?? $info->admin_content;
        $info->save();

        //Create Company
        if(!$company){
          $company = new CompanyManage();
          $company->id = Str::uuid();
        }

        $company->user_id = $user_id;
        $company->company_information_id = $info->id;
        $company->save();

        DB::commit();
        Log::info('New company information created successfully for user ID: ' . $user_id);
        
      } catch (\Throwable $th) {
        DB::rollback();
        Log::error('Failed to save company information for user ID: ' . $user_id . '. Error: ' . $th->getMessage());

        throw $th;
      }
      
    }

    return $info;
  }
}
