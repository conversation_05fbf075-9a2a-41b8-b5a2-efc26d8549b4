<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\UserInvitesGroup;
use Illuminate\Support\Facades\Mail;
use App\Mail\GroupInvites\NewRequestMail;
use App\Mail\GroupInvites\RequestApprovedMail;
use App\Models\User;

use Auth;
use Str;

class UserInvitesGroupController extends Controller
{

    protected $config;
    protected $userInviteService;

    public function index(Request $request)
    {
        $user = Auth::user();
        if($user->hasRole([User::ROLE_TYPE_STAFF, User::ROLE_TYPE_SUPER_ADMIN]))
            $query = UserInvitesGroup::orderBy('created_at', 'desc')->with('user');    
        else
            $query = UserInvitesGroup::where('user_id', $user->id)->with('user')->orderBy('created_at', 'desc');
        
        // if(config('app.tenant') == 'Q2B') {
        //     $query->whereHas('user', function($q) {
        //         $q->whereIn('type', ['b2b-principal', 'b2b-agent']);
        //     });
        // }
        
        return response()->json([
            'groups' => $query->get()
        ]);
    }

    public function store(Request $request)
    {
        $user = Auth::user();
        if (!$user->can('create invites')) {
            abort(404);
        }

        $validatedData = $request->validate([
            'group_name' => 'required|string',
            'group_size' => 'required|string',
            // 'contact_info' => 'required|string',
            // 'preferred_times' => 'string',
            'notes' => 'string'
        ]);

        $validatedData['user_id'] = $user->id;
        $validatedData['id'] = (string) Str::uuid();
        $validatedData['group_code'] = (string) Str::uuid();
        // $validatedData['group_invite_status'] = 'submitted';
        $validatedData['group_invite_status'] = 'approved';

        $userInvitesGroup = UserInvitesGroup::create($validatedData);


        //send email notification to contracting
        $emailRecipients = env('GROUP_ONBOARDING_REQUEST_EMAIL_RECIPIENTS', '<EMAIL>');
        $emails = explode(',', $emailRecipients);

        // send notification to staff about new request
        Mail::to($emails)->send(
            new NewRequestMail([
                "group_name" => $request->group_name,
                "group_size" => $request->group_size,
                // "contact_info" => $request->contact_info,
                // "preferred_times" => $request->preferred_times,
                "notes" => $request->notes,
                "agent_name" => $user->name
            ])
        );

        return $userInvitesGroup;
    }

    function approve(Request $request)
    {
        // Find the UserInvitesGroup model by ID
        $userInvitesGroup = UserInvitesGroup::find($request->id);
        $user = User::find($userInvitesGroup->user_id);

        if (!$user->can('create invites')) {
            abort(404);
        }

        // Check if the model was found
        if ($userInvitesGroup) {
            // Update the 'user_group_status' field
            $userInvitesGroup->group_invite_status = 'approved';
            $userInvitesGroup->save();


            //send notification email
            Mail::to($user->email)->send(
                new RequestApprovedMail([
                    "group_name" => $userInvitesGroup->group_name,
                    "agent_name" => $user->name,
                    "group_url" => "https://".$_SERVER['HTTP_HOST']."/auth/register?group_code=".$userInvitesGroup->group_code
                ])
            );

            // Optionally, return a response or redirect
            // return response()->json(['message' => 'User invite group approved successfully']);
            return $this->index($request);
        } else {
            // Handle the case where the model is not found
            return response()->json(['message' => 'User invite group not found'], 404);
        }
    }

    function updateActive(Request $request)
    {
        $user = Auth::user();
        if (!$user->can('create invites')) {
            abort(404);
        }

        if($userInvitesGroup = UserInvitesGroup::find($request->id)) {
            $userInvitesGroup->active = $request->active;
            $userInvitesGroup->save();
        }

        return response()->json(['message' => 'success']);
    }
}
