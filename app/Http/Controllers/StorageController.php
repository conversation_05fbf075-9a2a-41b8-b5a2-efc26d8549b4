<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use UserTasks;
use Log;
use File;
use Illuminate\Support\Facades\Storage;

class StorageController extends Controller
{

    /**
     * Return user uploads
     */
    public function getUploads($path, $fn)
    {
        if (!$user = Auth::user()) {
            abort(404);
        }

        if($user->can('view uploads') || $user->id == $path){
        }else{ abort(404);}

        $path = storage_path('app/public/uploads/files/'.$path.'/'.$fn);

        if (!File::exists($path)) {
            abort(404);
        }
    
        return response()->file($path);
    }

    public function getSignature($path, $fn){
        if (!$user = Auth::user()) {
            abort(404);
        }

        if($user->can('view uploads') || $user->id == $path){
        }else{ abort(404);}

        $path = storage_path('app/public/signature/'.$path.'/'.$fn);

        if (!File::exists($path)) {
            abort(404);
        }
    
        return response()->file($path);

    }

    public function getSignaturePdf($path, $fn){
        
        Log::info("getSignaturePdf IP Address: ".$_SERVER['REMOTE_ADDR']);

        $path = storage_path('app/public/signature/'.$path.'/'.$fn);

        if (!File::exists($path)) {
            abort(404);
        }
    
        return response()->file($path);

    }

    public function passthrough(Request $request)
    {
        try {
            // Get the full URL from the request
            $url = $request->input('url');
            
            // Validate URL is from your Azure storage
            if (!str_starts_with($url, 'https://agentdocuments.blob.core.windows.net/')) {
                abort(404);
            }

            // Parse the URL to extract container and blob path
            $parsedUrl = parse_url($url);
            $path = ltrim($parsedUrl['path'], '/');
            
            // Split path to get container and blob name
            $pathParts = explode('/', $path);
            $containerName = array_shift($pathParts);
            $blobName = implode('/', $pathParts);

            // Get the file contents from Azure
            $fileContents = Storage::disk('azure')->get($blobName);
            
            // Get the mime type
            $mimeType = Storage::disk('azure')->mimeType($blobName);
            
            // Create response with proper headers
            return response($fileContents)
                ->header('Content-Type', $mimeType)
                ->header('Content-Disposition', 'inline; filename="' . basename($blobName) . '"');

        } catch (\Exception $e) {
            Log::error('Failed to passthrough Azure file: ' . $e->getMessage());
            abort(404);
        }
    }
}