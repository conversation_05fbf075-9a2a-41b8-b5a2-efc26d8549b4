<?php

namespace App\Http\Controllers;

use App\Models\Content;
use Illuminate\Http\Request;
use App\Models\User;
use Agents;
use App\Models\ContactInformationState;
use App\Models\FormField;
use App\Models\FormPage;
use App\Models\UserEntry;
use Log;

class ContentController extends Controller
{

  
    /**
     * @param Request list of requested parameters 
     * 
     * @return json with the list of content that matches the user's role SuperAdmin
     */
    public function getContentList(Request $request)
    {
        Log::info("Request to getContentList", ['request' => $request->all()]);

        $user = $request->user();
        $page = $request->page ?? 1;
        $itemsPerPage = $request->itemsPerPage ?? 10;
        $sortBy = $request->sortBy ?? "state_name";
        // $sortDesc = $request->sortDesc == "true" ? "asc" : "desc";
        $start = ($page - 1) * $itemsPerPage;

        if ($user->hasRole(User::ROLE_TYPE_SUPER_ADMIN)) {
            $permission = User::ROLE_TYPE_SUPER_ADMIN;
            $admin_ids = User::role(User::ROLE_TYPE_SUPER_ADMIN)->pluck('id');
            $contents = Content::whereIn('user_id', $admin_ids)->get();
            Log::info("Admin content retrieval", ['admin_ids' => $admin_ids->toArray(), 'contents_count' => $contents->count()]);

            $states = config('states');
            if ($contents->count() === 0) {
                $content = new Content;
                $content->user_id = $user->id;
                $content->content_type = 0;
                $content->content = "";
                $content->permission = $permission;
                $content->save();
                foreach($states as $key => $state) {
                    $content = new Content;
                    $content->content_type = 1;
                    $content->user_id = $user->id;
                    $content->state_name = $state;
                    $content->content = "";
                    $content->permission = $permission;
                    $content->save();
                }
            }

            $query = Content::whereIn('user_id', $admin_ids)->orderBy($sortBy);
            $total_count = $query->count();
            while ($start > $total_count)
                $start -= $itemsPerPage;
            $contents = $query->skip($start)->take($itemsPerPage)->get();
            
            $state_content_permission = true;
            
        } else if ($user->hasRole(User::ROLE_TYPE_STAFF) || $user->hasRole(User::ROLE_TYPE_AGENCY_OWNER)) {
            $content = Content::where('user_id', $user->id)->get();
            $state_content_permission = false;
        } else {
            return response()->json('User does not have permission', 400);
        }
        Log::info("Response from getContentList", ['items' => $contents, 'total' => $total_count]);

        return response()->json(['items' => $contents, 'total' => $total_count, 'start'=>$start, 'itemPerPage' => $itemsPerPage, 'state_content_permission' => $state_content_permission], 200);
    }

    public function getAdminContent($id, Request $request)
    {
        Log::info("Request to getAdminContent", ['id' => $id, 'user_id' => $request->user()->id]);

        $user = $request->user();
       
        if ($user->hasRole(User::ROLE_TYPE_SUPER_ADMIN)) {
            $admin_ids = User::role(User::ROLE_TYPE_SUPER_ADMIN)->pluck('id');
            $content = Content::whereIn('user_id', $admin_ids)->where('id', $id)->first();
            Log::info("Admin content found", ['content_id' => $id, 'found' => $content != null]);

        } else {
            return response()->json('User does not have permission', 400);
        }
        return response()->json($content, 200);
    }

    public function getAgencyContent(Request $request)
    {
        Log::info("Request to getAgencyContent", ['user_id' => $request->user()->id]);

        $user = $request->user();
   
        if ($user->hasRole(User::ROLE_TYPE_AGENCY_OWNER)) {
            $content = Content::where('user_id', $user->id)->first();
            Log::info("Agency content found", ['content_found' => $content != null]);

        } else {
            return response()->json('User does not have permission', 400);
        }
        return response()->json($content, 200);
    }

    public function getGuideContent(Request $request)
    {
        Log::info("Request to getGuideContent", ['user_id' => $request->user()->id]);

        $user = $request->user();

        $step = 'personal-information';
        if (!$page = FormPage::where('step_ident', $step)->first()) {
            Log::error("FORM ERROR: Could not find form page with step ident {$step}.");
            abort(404);
        }

        $form_page = FormPage::where('step_ident', $step)->with(array('formSections' => function($query) {
            $query->where('label', 'Residential Address')->with(array('formFields' => function ($field_query) {
                $field_query->where('label', 'State/Province');
            }));
        }))->first();

        $form_field_id = $form_page->formSections->first()->formFields->first()->id;

        $user_entry = UserEntry::select('value')
                                ->where('user_id', $user->id)
                                ->where('form_field_id', $form_field_id)
                                ->first();
        $state_name = $user_entry ? $user_entry->value : "";
        
        if ($user->hasRole(User::ROLE_TYPE_RECRUIT) || $user->hasRole(User::ROLE_TYPE_UNLICENSED_AGENT)) {
            $content = Content::where('state_name', $state_name)->first();
            $company = Content::where('content_type', 0)
                                ->where('permission', User::ROLE_TYPE_SUPER_ADMIN)->first();
        } else {
            abort(403, 'You don\'t have permission to see the state content');
        }
        return response()->json(['content' => $content, 'company' => $company], 200);
    }

    public function getStates(Request $request)
    {
        Log::info("Request to getStates", ['user_id' => $request->user()->id]);

        $user = $request->user();
        $states = config('states');
        $stateList = [];
        foreach ($states as $key => $state) {
            $tempState = [
                'value' => $key,
                'label' => $state
            ];
            array_push($stateList, $tempState);
        }
        return response()->json($stateList, 200);
    }

    public function saveAdminContent(Request $request, Content $content)
    {
        Log::info("Request to saveAdminContent", ['request' => $request->all()]);

        $request->validate([
            'content_type' => 'required|string',
            'content' => 'required|string'
        ]);

        $user = $request->user();

        $content_type = $request->content_type === "state" ? 1 : 0;
        $state_name = $request->state_name ? $request->state_name : "";
        $content_html = $request->content ? $request->content : "";
        $states = config("states");

        if ($user->hasRole(User::ROLE_TYPE_SUPER_ADMIN)) {
            $permission = User::ROLE_TYPE_SUPER_ADMIN;
            $admin_ids =  User::role(User::ROLE_TYPE_SUPER_ADMIN)->pluck('id');

            if ($content_type === 1) {
                $contents = Content::whereIn('user_id', $admin_ids)->where('state_name', $states[$state_name])->first();
            } else {
                $contents = Content::whereIn('user_id', $admin_ids)->where('content_type', $content_type)->first();
            }
            
            Log::info("Saving admin content", ['content_type' => $content_type, 'state_name' => $states[$state_name] ?? null]);

            if (!empty($contents) && $contents->count() > 0) {
                $contents->user_id = $user->id;
                $contents->content_type = $content_type;
                $contents->state_name = $content_type === 1 ? $states[$state_name] : null;
                $contents->content = $content_html;
                $contents->permission = $permission;
                $contents->save();
            } else if (empty($contents) || $contents->count() === 0) {
                $content = new Content;
                $content->user_id = $user->id;
                $content->content_type = $content_type;
                $content->state_name = $content_type === 1 ? $states[$state_name] : null;
                $content->content = $content_html;
                $content->permission = $permission;
                $content->save();
            } else {
                return response()->json('error', 400);
            }
        } else {
            return response()->json('error', 400);
        }

        return response()->json('success', 200);
    }

    public function saveAgencyContent(Request $request)
    {
        $request->validate([
            'content_type' => 'required|string',
            'content' => 'required|string'
        ]);

        $user = $request->user();

        $content_type = $request->content_type === "state" ? 1 : 0;
        $content_html = $request->content ? $request->content : "";

        if ($user->hasRole(User::ROLE_TYPE_AGENCY_OWNER)) {
            $permission = User::ROLE_TYPE_AGENCY_OWNER;
            $contents = Content::where('user_id', $user->id)->first();
            if (!empty($contents) && $contents->count() > 0) {
                $contents->user_id = $user->id;
                $contents->content_type = $content_type;
                $contents->content = $content_html;
                $contents->permission = $permission;
                $contents->save();
            } else if (empty($contents) || $contents->count() === 0) {
                $content = new Content;
                $content->user_id = $user->id;
                $content->content_type = $content_type;
                $content->content = $content_html;
                $content->permission = $permission;
                $content->save();
            } else {
                return response()->json('error', 400);
            }
        } else {
            return response()->json('error', 400);
        }

        return response()->json('success', 200);
    }

    /**
     * obtains the contact information according to the state identifier
     * @param id is an identifier of state licensing
     * 
     * @method GET
     * @return contactInformation the contact information according to the state identifier
     * @throws errorMessage an message with the error
     * 
     */
    public function getInformationContact($id)
    {
        try {
            $contactInformation = ContactInformationState::where('content_id',$id)->first();
            return response()->json($contactInformation,200);
        } catch (\Exception $ex) {
            return response()->json(['errorMessage'=> $ex->getMessage()],400);
        }
    }

    /**
     * create or update the contact information of  state licensing
     * @param state is an identifier of content state licensing
     * @param id if the operation consists of updating an existing record, 
     *           it will be the identifier, if it is a new record, the identifier will be null
     * 
     * @method POST
     * 
     * @return status status code, and message
     * @throws errorMessage an message with the error
     * 
     */
    public function updateInformationContact(Request $request, $state, $id=null)
    {
        try {
            $contact = null;
            if($id==0  || $id == null){
                $contact =  new ContactInformationState();
            }else{
                $contact = ContactInformationState::find($id);
            }
                $contact->content_id = $state;
                $contact->name = $request->name ?? '';
                $contact->email = $request->email ?? '';
                $contact->phone = $request->phone ?? '';
                $contact->address = $request->address ?? '';
                $contact->zip = $request->zip ?? '';
                $contact->save();
            return response()->json(200);
        } catch (\Exception $ex) {
            return response()->json(['errorMessage'=> $ex->getMessage()],400);
        }
    } 
}
