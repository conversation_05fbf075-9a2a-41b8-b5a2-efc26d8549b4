<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Events\UserInvite\Created as UserInviteCreated;
use App\Events\UserInvite\Blocked as UserInviteBlocked;
use App\Events\UserInvite\Deleted as UserInviteDeleted;
use App\Services\UserInviteService;
use App\Http\Requests\UserInviteRequest;
use App\Models\UserInvite;
use App\Models\User;
use App\Models\UserInvitesGroup;
use App\Models\AgentB2B;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Auth;
use DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;


class UserInviteController extends Controller
{

    protected $config;
    protected $userInviteService;
    protected $tenant;

    public function __construct(UserInviteService $userInviteService) 
    {
        $this->tenant = config('app.tenant');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $text = $request->text ?? '';
        $status = $request->status ?? '';
        $type = $request->type ?? '';

        $page = $request->page ?? 1;
        $itemsPerPage = $request->itemsPerPage ?? 10;
        $start = ($page - 1) * $itemsPerPage;

        $user = Auth::user();

        if($user->hasRole(User::ROLE_TYPE_STAFF) || $user->hasRole(User::ROLE_TYPE_SUPER_ADMIN))
        {
            $query = UserInvite::with(['uplineAgent.agencyOwner', 'uplineAgent.baseshopOwner', 'user', 'invitedUser.userStatus']);
        }
        elseif(isset($request->all_invites) && $request->input('all_invites') == 'true')
        {
            $downline_agent_codes = $user->getDownlineAgents()->pluck('AgentCode');
            $downline_agent_codes[] = $user->agent_code;
            $query = UserInvite::with(['uplineAgent.agencyOwner', 'uplineAgent.baseshopOwner', 'user', 'invitedUser.userStatus'])->whereIn('user_id', function($query) use($downline_agent_codes){
                $query->select('id')
                    ->from('users')
                    ->whereIn('agent_code', $downline_agent_codes);
            });
        }
        else
        {
            $query = $user->invites()->with(['uplineAgent.agencyOwner', 'uplineAgent.baseshopOwner', 'user', 'invitedUser.userStatus']);
        }

        // Apply the type filter
        // if (!empty($type)) {
        //     if ($type === 'b2b-principal') {
        //         $query->where('type', 'b2b-principal');
        //     }
        // }
        // Apply the type filter
        // if (!empty($type)) {
        //     if ($type === 'b2b_agent') {
        //         $query->where('type', 'b2b_agent');
        //     }
        // }

        if($this->tenant == 'Q2B') {
            $query->whereIn('type', ['b2b-principal', 'b2b-agent']);
        } else {
            // default is Q2A, exclude b2b agents
            $query->whereNotIn('type', ['b2b-principal', 'b2b-agent']);
        }

        $sortBy = $request->sortBy ? $request->sortBy : 'updated_at';
        $sortDesc = $request->sortDesc == 'true' ? 'desc' : 'asc';
        $query->where('blocked', 0)->orderBy($sortBy, $sortDesc);

        if(!empty($text)) {
            // Get all AgentIDs where AgentName matches the search text, cast to string for comparison
            $matchingUplineAgentIds = \App\Models\Agent::on('sqlsrv')
                ->where('AgentName', 'LIKE', "%$text%")
                ->pluck('AgentID')
                ->map(fn($id) => (string)$id)
                ->toArray();

            // Get all AgentIDs where AgencyOwner's AgentName matches the search text
            $matchingAgencyOwnerIds = \App\Models\Agent::on('sqlsrv')
                ->where('IsAgencyOwner', 1)
                ->where('AgentName', 'LIKE', "%$text%")
                ->pluck('AgentID')
                ->map(fn($id) => (string)$id)
                ->toArray();

            // Get all AgentIDs whose BaseShopOwnerAgentID is in the matchingAgencyOwnerIds
            $uplineAgentIdsWithMatchingAO = [];
            if (!empty($matchingAgencyOwnerIds)) {
                $uplineAgentIdsWithMatchingAO = \App\Models\Agent::on('sqlsrv')
                    ->whereIn('BaseShopOwnerAgentID', $matchingAgencyOwnerIds)
                    ->pluck('AgentID')
                    ->map(fn($id) => (string)$id)
                    ->toArray();
            }

            // Get all AgentIDs where the baseshop owner's AgentName matches the search text
            $matchingBaseShopOwnerIds = \App\Models\Agent::on('sqlsrv')
                ->where('AgentName', 'LIKE', "%$text%")
                ->pluck('AgentID')
                ->map(fn($id) => (string)$id)
                ->toArray();
            $uplineAgentIdsWithMatchingBaseShopOwner = [];
            if (!empty($matchingBaseShopOwnerIds)) {
                $uplineAgentIdsWithMatchingBaseShopOwner = \App\Models\Agent::on('sqlsrv')
                    ->whereIn('BaseShopOwnerAgentID', $matchingBaseShopOwnerIds)
                    ->pluck('AgentID')
                    ->map(fn($id) => (string)$id)
                    ->toArray();
            }

            // Merge all unique upline agent IDs
            $allMatchingUplineAgentIds = array_unique(array_merge($matchingUplineAgentIds, $uplineAgentIdsWithMatchingAO, $uplineAgentIdsWithMatchingBaseShopOwner));

            $query->where(function($q) use($text, $allMatchingUplineAgentIds) {
                $q->where('name', 'LIKE', "%$text%")
                  ->orWhere('email', 'LIKE', "%$text%") ;

                if (!empty($allMatchingUplineAgentIds)) {
                    $q->orWhereIn('upline_agent_id', $allMatchingUplineAgentIds);
                }
            });
        }
        if($status == 'accepted') {
            $query->where('invite_accepted', 1);
        }
        else if($status == 'expired') {
            $query->where('invite_expired', 1);
            $query->where('invite_accepted', 0);
        }
        elseif ($status == 'stalled') {
            $fiveDaysAgo = Carbon::now()->subDays(5);
            $tenDaysAgo = Carbon::now()->subDays(10);
            $query->whereHas('invitedUser', function ($q) use ($fiveDaysAgo, $tenDaysAgo) {
                $q->where('updated_at', '>=', $tenDaysAgo)
                  ->where('updated_at', '<', $fiveDaysAgo)
                  ->whereNull('last_approved');
            });
        } 
        elseif ($status == 'lapsed') {
            $tenDaysAgo = Carbon::now()->subDays(10);
            $query->whereHas('invitedUser', function ($q) use ($tenDaysAgo) {
                $q->where('updated_at', '<', $tenDaysAgo)
                ->whereNull('last_approved');
            });
        }

        $total_count = $query->count();
        while ($start > $total_count)
            $start -= $itemsPerPage;

        $invites = $query->skip($start)->take($itemsPerPage)->get();

        // Attach agency owner and baseshop owner info to each invite for API consumers
        $invites->transform(function($invite) {
            $agencyOwner = $invite->uplineAgent && $invite->uplineAgent->agencyOwner ? $invite->uplineAgent->agencyOwner : null;
            $baseshopOwner = $invite->uplineAgent && $invite->uplineAgent->baseshopOwner ? $invite->uplineAgent->baseshopOwner : null;
            $invite->agency_owner = $agencyOwner;
            $invite->baseshop_owner = $baseshopOwner;
            return $invite;
        });

        return response()->json([
            'items' => $invites,
            'total' => $total_count
        ]);
    }

    /**
     * Check wholesale/b2b db for existing user
     *
     * @param  \App\Http\Requests $request
     * @return \Illuminate\Http\Response
     */
    public function preflight(Request $request)
    {
        // return ['found' => false];
        $search_name = str_replace(" ", "%", $request->name);
        $agent_by_name = AgentB2B::where('AgentName', 'LIKE', $search_name)->active()->first();
        $agent_by_email = AgentB2B::where('Email', 'LIKE', $request->email)->first();
        $agent_by_phone = AgentB2B::where('Phone', $request->phone)->first();

        $found = false;
        if($agent_by_name) {
            if($agent_by_name->Email == $request->email)
                $found = true;
            if($agent_by_name->State == $request->state)
                $found = true;
            if($agent_by_name->Phone == $request->phone)
                $found = true;
        }

        if($agent_by_email) {
            if($agent_by_email->State == $request->state)
                $found = true;
            if($agent_by_email->Phone == $request->phone)
                $found = true;
        }

        if($agent_by_phone) {
            if($agent_by_phone->State == $request->state)
                $found = true;
            if($agent_by_phone->Email == $request->email)
                $found = true;
        }

        return ['found' => $found];
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \App\Http\Requests\UserInviteRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(UserInviteRequest $request)
    {
        if (!$user = User::find($request->user_id)) {
            abort(404);
        }

        if (!$user->can('create invites')) {
            abort(404);
        }

        $blocked = false;

        // NOTE: We would use "having" with numerical selects to add up matching columns in MySQL
        // before it hits PHP but we're using MSSQL and eloquent no likey so... enjoy

        $invites = UserInvite
            ::where('invite_expired', 0)
            ->where('blocked', 0)
            ->where(function ($query) use ($request) {
                $query->where('name', strtoupper($request->name))
                    ->orWhere('email', strtoupper($request->email))
                    ->orWhere('phone', $request->phone);
            })
            ->orderBy('updated_at')
            ->get();

        foreach ($invites as $invite) {

            $matches = 0;
            $matches += (strtoupper($invite->name) == strtoupper($request->name) ? 1 : 0);
            $matches += (strtoupper($invite->email) == strtoupper($request->email) ? 1 : 0);
            $matches += ($invite->phone == $request->phone ? 1 : 0);

            if ($matches >= 2) {
                $originalInviter = $invite->user()->first();
                $emailSecond = $originalInviter->email;
                $nameSecond = $originalInviter->name;
                $blocked = true;
                break;
            }

        }

        if(empty($request->person_message)){
            $request->person_message = "We're excited that you're interested in joining us at Symmetry Financial Group! Use the link below to activate your account and complete your application.";
            if(config('app.tenant') == 'Q2B')
                $request->person_message = "We're excited that you're interested in working with Quility® B2B! Use the link below to activate your account and complete your contracting application.";
        }

        $group = $request->group_name != "null" ? $request->group_name : null;
        $userInviteGroupID = null;
        if(!empty($group)) {
            if(is_string($group)) {
                $userInviteGroup = UserInvitesGroup::create(array(
                    'id'            =>  (string) Str::uuid(),
                    'user_id'       => $user->id,
                    'group_name'    => $group
                ));
                $userInviteGroupID = $userInviteGroup->id;
            } else  {
                $userInviteGroupID = $group->id ?? $group['id'] ?? null;
            }
        }

        $upline_agent_id = $request->upline;
        if($request->type === User::USER_TYPE_B2B_PRINCIPAL && $request->upline == 'null') {
            //get the default upline agent for b2b
            $upline_agent_code = config('app.principal_upline_agent_code');
            $upline_agent = Agent::where('AgentCode', $upline_agent_code)->first();
            $upline_agent_id = $upline_agent->AgentID;
        } else {
            $upline_agent_id = $request->upline;
        }

        $userInviteData = [
            'user_id'           => $user->id,
            'email'             => $request->email,
            'name'              => $request->name,
            'phone'             => $request->phone,
            'invite_expired'    => ($blocked ? 1 : 0),
            'blocked'           => ($blocked ? 1 : 0),
            'code'              => (string) Str::uuid(),
            'contract_level'    => $request->contract_level,
            'upline_agent_id'   => $upline_agent_id,
            'corporate'         => $request->coporate ?? false,
            'person_message'    => $request->person_message,
            'source'            => $request->source,
            'license_status'    => $request->license_status,
            'experience'        => $request->experience,
            'advanced_markets'  => $request->advanced_markets,
            'group_id'          => $userInviteGroupID,
            'alt_email'         => $request->alt_email,
            'carrier'           => $request->carrier,
            'agreement_type'    => $request->agreement_type,
            'service_level'     => $request->service_level,
            'bonus_addendum'    => $request->bonus_addendum,
            'multiple_owners'   => $request->multiple_owners,
            'type'              => is_string($request->type) && $request->type != 'null' && $request->type != '[object SubmitEvent]' ? $request->type : NULL,
            'commissions'       => $request->commissions,
            // 'custom_contract_upload'=> $request->custom_contract_upload
        ];

        if ($request->input('type') === 'b2b-principal' && $request->hasFile('custom_contract_upload') && $request->input('agreement_type') === 'Custom') {
            try {
                $file = $request->file('custom_contract_upload');
                $filePath = $file->store('custom-contracts', 'azure');
                $fileUrl = Storage::disk('azure')->url($filePath);
                $userInviteData['custom_contract_upload'] = $fileUrl;
            } catch (\Exception $e) {
                Log::error('Failed to upload custom contract: ' . $e->getMessage());
            }
        }

        if ($blocked) {
            $userInviteData['block_reason'] = 'user-invite-blocked';
            $userInvite = UserInvite::create($userInviteData);
            UserInviteBlocked::dispatch($user->email, $user->name, $userInvite->name, $emailSecond ?? '', $nameSecond ?? '');
            return response()->json(['error' => $userInvite->block_reason, 'message' => 'Sorry, your invitation is blocked!'], 400);
        }
        $userInvite = UserInvite::create($userInviteData);
        
        // Check if save_for_later is true, if not dispatch the event
        if (!$request->has('save_for_later') || $request->input('save_for_later') != "true") {
            UserInviteCreated::dispatch($userInvite);
        }
        
        return $userInvite->load('user');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\UserInvite  $userInvite
     * @return \Illuminate\Http\Response
     */
    public function show(UserInvite $userInvite)
    {
        $data = [
            'user-invite' => $userInvite->load('user')->load('group')
        ];

        return $data;
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \App\Http\Requests\UserInviteRequest $request
     * @param  \App\Models\UserInvite  $userInvite
     * @return \Illuminate\Http\Response
     */
    public function update(UserInviteRequest $request, UserInvite $userInvite)
    {
        if (!$user = User::find($request->user_id)) {
            abort(404);
        }

        if (!$user->can('create invites')) {
            abort(404);
        }

        $blocked = false;

        // NOTE: We would use "having" with numerical selects to add up matching columns in MySQL
        // before it hits PHP but we're using MSSQL and eloquent no likey so... enjoy

        $invites = UserInvite
            ::where('invite_expired', 0)
            ->where('id', '!=', $userInvite->id)
            ->where('blocked', 0)
            ->where(function ($query) use ($request) {
                $query->where('name', strtoupper($request->name))
                    ->orWhere('email', strtoupper($request->email))
                    ->orWhere('phone', $request->phone);
            })
            ->orderBy('updated_at')
            ->get();

        foreach ($invites as $invite) {

            $matches = 0;
            $matches += (strtoupper($invite->name) == strtoupper($request->name) ? 1 : 0);
            $matches += (strtoupper($invite->email) == strtoupper($request->email) ? 1 : 0);
            $matches += ($invite->phone == $request->phone ? 1 : 0);

            if ($matches >= 2) {
                $originalInviter = $invite->user()->first();
                $emailSecond = $originalInviter->email;
                $nameSecond = $originalInviter->name;
                $blocked = true;
                break;
            }

        }

        if(empty($request->person_message)){
            $request->person_message = "We're excited that you're interested in joining us at Symmetry Financial Group! Use the link below to activate your account and complete your application.";
            if(config('app.tenant') == 'Q2B')
                $request->person_message = "We're excited that you're interested in working with Quility® B2B! Use the link below to activate your account and complete your contracting application.";
        }

        $group = $request->group_name;
        $userInviteGroupID = null;
        if(!empty($group) && $group != "null") {
            if(is_string($group)) {
                $data = array(
                    'id'            =>  (string) Str::uuid(),
                    'user_id'       => $user->id,
                    'group_name'    => $group
                );
                $userInviteGroup = UserInvitesGroup::create($data);
                $userInviteGroupID = strtoupper($userInviteGroup->id);
            } elseif (is_object($group) || is_array($group)) {
                $userInviteGroupID = $group['id'];
            }
        }
        

        // $userInviteData = [
        //     'user_id'           => $user->id,
        //     'email'             => $request->email,
        //     'name'              => $request->name,
        //     'phone'             => $request->phone,
        //     'invite_expired'    => ($blocked ? 1 : 0),
        //     'blocked'           => ($blocked ? 1 : 0),
        //     'code'              => (string) Str::uuid(),
        //     'contract_level'    => $request->contract_level,
        //     'upline_agent_id'   => $request->upline,
        //     'corporate'         => $request->coporate ?? false,
        //     'person_message'    => $request->person_message,
        //     'invite_sent'       => 0,
        //     'source'            => $request->source,
        //     'license_status'    => $request->license_status,
        //     'experience'        => $request->experience,
        //     'advanced_markets'  => $request->advanced_markets,
        //     'group_id'          => $userInviteGroupID,
        // ];
        $upline_agent_id = $request->upline;
        if($request->type === User::USER_TYPE_B2B_PRINCIPAL && $request->upline == 'null') {
            //get the default upline agent for b2b
            $upline_agent_code = config('app.principal_upline_agent_code');
            $upline_agent = Agent::where('AgentCode', $upline_agent_code)->first();
            $upline_agent_id = $upline_agent->AgentID;
        } else {
            $upline_agent_id = $request->upline;
        }

        $userInviteData = [
            'user_id'           => $userInvite->user_id,
            'email'             => $request->email,
            'name'              => $request->name,
            'phone'             => $request->phone,
            'invite_expired'    => ($blocked ? 1 : 0),
            'blocked'           => ($blocked ? 1 : 0),
            'code'              => (string) Str::uuid(),
            'contract_level'    => $request->contract_level,
            'upline_agent_id'   => $upline_agent_id,
            'corporate'         => $request->coporate ?? false,
            'person_message'    => $request->person_message,
            'source'            => $request->source,
            'license_status'    => $request->license_status,
            'experience'        => $request->experience,
            'advanced_markets'  => $request->advanced_markets,
            'group_id'          => $userInviteGroupID,
            'alt_email'         => $request->alt_email,
            'carrier'           => $request->carrier,
            'agreement_type'    => $request->agreement_type,
            'service_level'     => $request->service_level,
            'bonus_addendum'    => $request->bonus_addendum,
            'multiple_owners'   => $request->multiple_owners,
            'type'              => is_string($request->type) && $request->type != 'null' && $request->type != '[object SubmitEvent]' ? $request->type : NULL,
            'commissions'       => $request->commissions,
            // 'custom_contract_upload'=> $request->custom_contract_upload
        ];

        if ($blocked) {
            return response()->json(['error' => $userInvite->block_reason, 'message' => 'Sorry, we have a duplicated invite.'], 400);
        }

        

        // TODO: update existing user contract_level and upline?

        if ($request->input('type') === 'b2b-principal' && $request->hasFile('custom_contract_upload') && $request->input('agreement_type') === 'Custom') {
            try {
                $file = $request->file('custom_contract_upload');
                $filePath = $file->store('custom-contracts', 'azure');
                $fileUrl = Storage::disk('azure')->url($filePath);
                $userInviteData['custom_contract_upload'] = $fileUrl;
            } catch (\Exception $e) {
                Log::error('Failed to upload custom contract: ' . $e->getMessage());
            }
        }

        $userInvite->update($userInviteData);

        // do not dispatch if invite already accepted and/or this is a staff update of contract_level or upline
        if(!$userInvite->invite_accepted && $request->input('save_for_later') != "true")
            UserInviteCreated::dispatch($userInvite);
        return $userInvite->load('user');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\UserInvite  $userInvite
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserInvite $userInvite)
    {
        UserInviteDeleted::dispatch($userInvite->email, $userInvite->name);
        $userInvite->delete();

        return true;
    }

    /**
     * Resend the requested invite
     *
     * @param  \App\Models\UserInvite  $userInvite
     * @return \Illuminate\Http\Response
     */
    public function resend(UserInvite $userInvite)
    {
        $userInvite->update([
            'invite_expired' => 0,
            'invite_sent' => 0,
        ]);

        UserInviteCreated::dispatch($userInvite);
        return true;
    }

    /**
     * Request the creation of a demo account on the demo.quilityonboarding.com account
     *
     * @param  \App\Http\Requests\UserInviteRequest $request
     * @param  \App\Services\UserInviteService $userInviteService
     * @return \Illuminate\Http\Response
     */
    public function requestDemoInvite(Request $request, UserInviteService $userInviteService)
    {
        $this->userInviteService = $userInviteService;
        $response = $this->userInviteService->requestDemoInvite($request->name, $request->email);
        return $response;
    }

    /**
     * Process the incoming demo account creation request
     *
     * @param  \App\Http\Requests\UserInviteRequest $request
     * @param  \App\Services\UserInviteService $userInviteService
     * @return string $invite->id
     */
    public function createDemoInvite(Request $request, UserInviteService $userInviteService) 
    {
        $this->userInviteService = $userInviteService;
        $invite = $this->userInviteService->createDemoInvite($request);
        return $invite->id;
    }

    public function searchCandidateExists(Request $request, UserInviteService $userInviteService)
    {
        return $userInviteService->candidateExists($request);
    }
}
