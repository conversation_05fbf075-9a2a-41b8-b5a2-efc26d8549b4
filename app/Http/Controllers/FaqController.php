<?php

namespace App\Http\Controllers;

use App\Models\FaqModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class FaqController extends Controller
{
    

    /**
     * @OA\Post(
     * path="/api/license/faq/{id}",
     * summary="this endpoint can be create and update ",
     * description="create new registry",
     * tags={"FAQ"},
     * @OA\RequestBody(
     *    required=true,
     *    description="FAQ json Object",
     *    @OA\JsonContent(
     *       @OA\Property(property="question", type="string", example="some question?"),
     *       @OA\Property(property="answer", type="string", example="some answer"),
     *    ),
     * ),
     * 
     * 
     *@OA\Response(
    *     response=200,
    *     description="Success",
    *     @OA\JsonContent(
    *         @OA\Property(property="success", type="string", example="ok")
    *     )
    *  ),
     * 
     * @OA\Response(
     *    response=400,
     *    description="errorMessage",
     *    @OA\JsonContent(
     *       @OA\Property(property="errorMessage", type="string", example="cause of error")
     *        )
     *     )
     * )
     * 
     * 
     * @param
     * @return success a message, and status 201ok
     * @throws errorMessage the cause of the error with status 400
     */
    public function createFAQ(Request $request, $id)
    {
        try {
            $request->validate([
                'answer' => 'required'
            ]);

            Log::info("Creating or updating FAQ", ['id' => $id, 'question' => $request->question, 'answer' => $request->answer]);

            $faq = null;
            if($id==0  || $id == null){
                $faq =  new FaqModel();
            }else{
                $faq = FaqModel::find($id);
                Log::info("Updating FAQ", ['id' => $id]);

            }
                $faq->answer = $request->answer;
                $faq->question = $request->question;
                $faq->content_id = $request->content_id;
                $faq->save();
            return response()->json(['success'=>true],201);

        } catch (\Exception $ex) {
            Log::error("Error creating/updating FAQ", ['error' => $ex->getMessage(), 'id' => $id]);
            return response()->json(['errorMessage'=> $ex],400);
        }
       
    }

    /**
    * @OA\Get(
    * path="/api/license/faq{content_id}",
    * summary="get all FAQ",
    * description="",
    * operationId="get FAQ",
    * tags={"FAQ"},
    * @OA\Response(
    *    response=200,
    *    description="json with all faq",
    *    @OA\JsonContent(
    *       @OA\Property(property="question", type="string", example="????"),
    *       @OA\Property(property="answer", type="string", example="some answer")
    *        )
    *     )
    * )
    *
     * @param
     * @return faqs list of frequently asked questions
     * @throws errorMessage the cause of the error with status 400
     * 
     */
    public function getAllFAQ($id)
    {
        try {
            Log::info("Fetching all FAQs for content ID", ['content_id' => $id]);
            $faqs = FaqModel::where('content_id',$id)->get();
            return response()->json($faqs);
        } catch (\Exception $ex) {
            Log::error("Error fetching FAQs", ['error' => $ex->getMessage(), 'content_id' => $id]);
            return response()->json(['errorMessage'=> $ex],400);

        }
    }


    /**
     * @param id identifier of the question to delete
     * @return json status ok
     * @throws errorMessage the cause of the error with status 400
     * 
     */
    public function removeFAQ($id)
    {
        try {
            Log::info("Removing FAQ", ['id' => $id]);
            $question = FaqModel::find($id);
            $question->delete();
            return response()->json(200);
            
        } catch (\Exception $ex) {
            Log::error("Error removing FAQ", ['error' => $ex->getMessage(), 'id' => $id]);
            return response()->json(['errorMessage'=> $ex],400);
        }
    }

}
