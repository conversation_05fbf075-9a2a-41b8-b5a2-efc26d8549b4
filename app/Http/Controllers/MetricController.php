<?php

namespace App\Http\Controllers;

use App\Models\FormLookup;
use App\Models\Metric;
use App\Models\UserStatus;
use Illuminate\Http\Request;

class MetricController extends Controller
{
    public function getMetricByDemographics($slug) {
        
        $formLookup = FormLookup::where('field_slug', $slug)->first();
        
        if(!$formLookup || !$formLookup->formField || !$formLookup->formField->formSection) {
            abort(404);
        }

        $metrics = Metric::where('model_type', Metric::MODEL_TYPE_FORM_FIELD)
                        ->where('model_id', $formLookup->form_field_id)
                        ->get();

        $result = [];
        
        foreach($metrics as $metric) {
            $result[$metric->application_type] = [];
            foreach($metric->metricValues as $mv) {
                $result[$metric->application_type][$mv->date->format('Y-m-d')] = $mv->value;
            }
        }

        return response()->json($result);

    }

    public function getMetricByUserStatusAll() {
        return $this->getMetricByUserStatus('');
    }

    public function getMetricByUserStatus($user_status) {

        if(!empty($user_status)) {
            $userStatus = UserStatus::where('slug', $user_status)->first();
            if(!$userStatus) {
                abort(404);
            }

            $metrics = Metric::where('model_type', Metric::MODEL_TYPE_USER_STATUS)
                            ->where('model_id', $userStatus->id)
                            ->get();
        } else {
            $metrics = Metric::where('model_type', Metric::MODEL_TYPE_USER_STATUS)
                            ->whereNull('model_id')
                            ->get();
        }

        $result = [];
        
        foreach($metrics as $metric) {
            $result[$metric->application_type] = [];
            foreach($metric->metricValues as $mv) {
                $result[$metric->application_type][$mv->date->format('Y-m-d')] = $mv->value;
            }
        }

        return response()->json($result);
    }

    public function getMetricByUserInvite() {

        $metric = Metric::where('model_type', Metric::MODEL_TYPE_USER_INVITE)->first();
            
        $result = [];
        
        foreach($metric->metricValues as $mv) {
            $result[$mv->date->format('Y-m-d')] = $mv->value;
        }

        return response()->json($result);
    }
}
