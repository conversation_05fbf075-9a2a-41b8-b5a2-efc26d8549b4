<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Mail\OnboardingMail;
use App\Mail\Review\RevisionRequestedMail;
use App\Models\User;
use Agents;

class EmailTestController extends Controller
{
    public function onboarding()
    {
        $email = (new OnboardingMail('<PERSON> Bobby', '12345', ['name' => 'agent', 'email' => '<EMAIL>']))->render();

        $email = html_entity_decode($email);
        $email = str_replace('%7C', '|', $email);
        $email = str_replace('%7D', '}', $email);
        $email = str_replace('%7B', '{', $email);

        // \Mail::to('<EMAIL>')->send(new OnboardingMail());

        return $email;
    }

    public function revision_requested()
    {
        $recruit = Agents::getOneWithRole(User::ROLE_TYPE_RECRUIT);
        $email = (new RevisionRequestedMail($recruit, "Hey you need to revise your application."));
        \Mail::to('<EMAIL>')->send($email);

        $email = html_entity_decode($email->render());
        $email = str_replace('%7C', '|', $email);
        $email = str_replace('%7D', '}', $email);
        $email = str_replace('%7B', '{', $email);

        return $email;
    }
}
