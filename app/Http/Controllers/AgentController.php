<?php

namespace App\Http\Controllers;

use App\Models\Agent;
use App\Models\FormLookup;
use App\Models\UserEntry;
use App\Models\User;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Defuse\Crypto\Crypto;
use Defuse\Crypto\Key;
use DB;
use Log;

class AgentController extends Controller
{
    public function getSsn(Request $request, Agent $agent)
    {
        Log::info('Fetching SSN for agent.', ['agent_code' => $agent->AgentCode]);

        $user = User::where('agent_code', $agent->AgentCode)->first();
        $ssn_field = FormLookup::where('field_slug', FormLookup::FIELD_SSN)->first();
        if(!$ssn = UserEntry::where('user_id', $user->id)->where('form_field_id', $ssn_field->form_field_id)->first()){
            Log::info('No SSN found for user.', ['user_id' => $user->id]);
            return response()->json([
                'ssn' => ''
            ]);
        }

        Log::info('SSN found, decrypting.', ['user_id' => $user->id]);

        $secureKey = config('system.securekey', null);
        $key = Key::loadFromAsciiSafeString($secureKey);
        $ssnValue = Crypto::decrypt($ssn->value, $key);
        return response()->json([
            'ssn' => $ssnValue
        ]);
    }

    public function getPriorWeekRecruitingStatsPersonal(Request $request, Agent $agent)
    {
        Log::info('Fetching personal prior week recruiting stats.', ['agent_code' => $agent->AgentCode]);

        $date = $request->date ??  Carbon::now()->toDateTimeLocalString();

        if($agent->AgentCode == 'SFG0000001')
        {
            Log::info('Fetching overall recruiting stats for main agent.', ['date' => $date]);
            $recruits = $this->getPriorWeekRecruitingStatsOverall($date);
            return response()->json([
                'recruits' => $recruits
            ]);
        }

        $recruits = $this->getPriorWeekRecruitingStats($agent, $date);
        return response()->json([
            'recruits' => $recruits
        ]);
    }

    public function getPriorWeekRecruitingStatsBaseshop(Request $request, Agent $agent)
    {
        Log::info('Fetching baseshop prior week recruiting stats.', ['agent_code' => $agent->AgentCode]);

        $date = $request->date ??  Carbon::now()->toDateTimeLocalString();

        if($agent->AgentCode == 'SFG0000001')
        {
            Log::info('Fetching overall recruiting stats for main agent.', ['date' => $date]);
            $recruits = $this->getPriorWeekRecruitingStatsOverall($date);
            return response()->json([
                'recruits' => $recruits
            ]);
        }

        $codes = [...$agent->baseShop->pluck('AgentCode')->all(), $agent->AgentCode];
        Log::info('Fetching recruiting stats for baseshop agents.', ['agent_codes' => $codes]);
        $recruits = $this->getPriorWeekRecruitingStatsChunked($codes, $date);

        return response()->json([
            'recruits' => $recruits
        ]);
    }

    public function getPriorWeekRecruitingStatsTotalAgency(Request $request, Agent $agent)
    {
        Log::info('Fetching total agency prior week recruiting stats.', ['agent_code' => $agent->AgentCode]);
        
        $date = $request->date ??  Carbon::now()->toDateTimeLocalString();

        if($agent->AgentCode == 'SFG0000001')
        {
            Log::info('Fetching overall recruiting stats for main agent.', ['date' => $date]);
            $recruits = $this->getPriorWeekRecruitingStatsOverall($date);
            return response()->json([
                'recruits' => $recruits
            ]);
        }

        $codes = [...$agent->directDownline->pluck('AgentCode')->all(), $agent->AgentCode];
        Log::info('Fetching recruiting stats for total agency agents.', ['agent_codes' => $codes]);
        $recruits = $this->getPriorWeekRecruitingStatsChunked($codes, $date);

        return response()->json([
            'recruits' => $recruits
        ]);
    }

    public function getPriorWeekRecruitingStats(Agent $agent, $date)
    {
        $businessDays = resolve('App\Services\BusinessTimeService');

        $prev_week = Carbon::parse($date)->subDays(7);
        $prev_week_start_end_dates = $businessDays->getBusinessWeeks($prev_week);

        $recruits = DB::select( DB::raw("
            select
                OPB_Report.user_name,
                OPB_Report.licenced,
                OPB_Report.passed_state_exam,
                OPB_Report.agency_owner_code,
                OPB_Report.converted,
                OPB_Report.user_id
            from OPB_Report
            where inviter_agent_code = :agent_code
                and OPB_Report.user_created_at_datetime BETWEEN :start and :end
        "
        ), [
            'agent_code' => $agent->AgentCode,
            'start' => $prev_week_start_end_dates['beginDate'],
            'end' => $prev_week_start_end_dates['endDate'],
        ]);
        return $recruits;
    }

    public function getPriorWeekRecruitingStatsChunked(Array $agent_codes, $date, $chunk_size = 200)
    {
        $businessDays = resolve('App\Services\BusinessTimeService');

        $prev_week = Carbon::parse($date)->subDays(7);
        $prev_week_start_end_dates = $businessDays->getBusinessWeeks($prev_week);

        $agent_code_collection = collect($agent_codes);
        $agent_code_chunks = $agent_code_collection->chunk($chunk_size);

        $recruits = [];

        foreach ($agent_code_chunks as $chunk) {
            $codes = $chunk->all();
            $codes = "'" . implode("', '", $codes) . "'";

            $start = $prev_week_start_end_dates['beginDate'];
            $end = $prev_week_start_end_dates['endDate'];

            $r = DB::select( DB::raw("
                select
                    OPB_Report.user_name,
                    OPB_Report.licenced,
                    OPB_Report.passed_state_exam,
                    OPB_Report.agency_owner_code,
                    OPB_Report.converted,
                    OPB_Report.user_id
                from OPB_Report
                where inviter_agent_code in ($codes)
                    and OPB_Report.user_created_at_datetime BETWEEN '$start' and '$end'
            "));

            $recruits = array_merge($recruits, $r);
        }
        return $recruits;
    }


    public function getPriorWeekRecruitingStatsOverall($date)
    {
        $businessDays = resolve('App\Services\BusinessTimeService');

        $prev_week = Carbon::parse($date)->subDays(7);
        $prev_week_start_end_dates = $businessDays->getBusinessWeeks($prev_week);

        $recruits = [];

        $start = $prev_week_start_end_dates['beginDate'];
        $end = $prev_week_start_end_dates['endDate'];

        $recruits = DB::select( DB::raw("
            select
                OPB_Report.user_name,
                OPB_Report.licenced,
                OPB_Report.passed_state_exam,
                OPB_Report.agency_owner_code,
                OPB_Report.converted,
                OPB_Report.user_id
            from OPB_Report
            where OPB_Report.user_created_at_datetime BETWEEN '$start' and '$end'
        "));

        return $recruits;
    }

    public function getLicensedLeaderboard(Request $request, Agent $agent) 
    {
        Log::info('Fetching licensed leaderboard stats.', ['agent_code' => $agent->AgentCode]);
        
        $start = $request->input('startDate');
        $end = $request->input('endDate');
        
        if($agent->AgentCode == 'SFG0000001')
        {
            Log::info('Fetching overall licensed stats for main agent.', ['start_date' => $start, 'end_date' => $end]);
            $recruits = DB::select( DB::raw("
                select distinct 
                    o.user_name as 'name', 
                    o.recruited_by as 'recruited_by', 
                    o.licensed as 'contract_submitted', 
                    o.converted as 'coverted',
                    o.inviter_agent_code
                    from dbo.OBP_Report_2 as o 
                    where o.licensed is not null
                    and o.licensed BETWEEN '$start' and '$end'
                    order by licensed asc
            "));
            return response()->json([
                'recruits' => $recruits
            ]);
        }

        switch(strtolower($request->statType))
        {
            case 'baseshop':
                $agent_codes = [...$agent->baseShop->pluck('AgentCode')->all(), $agent->AgentCode];
                Log::info('Fetching baseshop licensed stats.', ['agent_codes' => $agent_codes]);
                break;
            case 'personalproduction':
                $agent_codes = [$agent->AgentCode];
                Log::info('Fetching personal production licensed stats.', ['agent_code' => $agent->AgentCode]);
                break;
            case 'totalagency':
            default:
                $agent_codes = [...$agent->directDownline->pluck('AgentCode')->all(), $agent->AgentCode];
                Log::info('Fetching total agency licensed stats.', ['agent_codes' => $agent_codes]);
        }

        $agent_code_collection = collect($agent_codes);
        $agent_code_chunks = $agent_code_collection->chunk(200);

        $data = [];

        foreach ($agent_code_chunks as $chunk) {
            $codes = $chunk->all();
            $codes = "'" . implode("', '", $codes) . "'";

            Log::info('Fetching licensed stats for chunk of agent codes.', ['chunk_size' => count($codes)]);

            $recruits = DB::select( DB::raw("
                select distinct 
                    o.user_name as 'name', 
                    o.recruited_by as 'recruited_by', 
                    o.licensed as 'contract_submitted', 
                    o.converted as 'coverted',
                    o.inviter_agent_code
                    from dbo.OBP_Report_2 as o 
                    where o.licensed is not null
                    and o.licensed BETWEEN '$start' and '$end'
                    and o.inviter_agent_code IN($codes)
                    order by licensed asc
            "));
            $data = [...$data, ...$recruits];
        }

        // return $data;
        return response()->json([
            'recruits' => $data
        ]);
    }

}
