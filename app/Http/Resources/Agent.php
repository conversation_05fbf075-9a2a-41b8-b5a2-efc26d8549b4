<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\AgentManagement\CarrierWritingNumber as CarrierWritingNumberResource;
use Illuminate\Support\Carbon;

class Agent extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $showDownline = false;
        if ($request->input('attr')) {
            $attrs = explode(",", $request->input('attr'));
            $showDownline = in_array('downline', $attrs);
        }

        $r = [
            'AgentID' => $this->AgentID,
            'AgentCode' => $this->AgentCode,
            'NPN' => $this->NPN,
            'OptID' => $this->OptID,
            'Status' => $this->Status,
            'Division' => $this->Division,
            'ContractStartDt' => $this->ContractStartDt,
            'OriginalAgentName' => $this-><PERSON><PERSON>ame,
            'AgentName' => $this->AgentDisplayName, //$this->AgentName,
            'FirstName' => $this->FirstName,
            'LastName' => $this->LastName,
            'MiddleName' => $this->MiddleName,
            'NickName' => $this->NickName,
            'PreferredName' => $this->PreferredName,
            'AgentEmail' => $this->AgentEmail,
            //'Role' => $this->ContractLevel, /// needs refactoring to be removed as Role is confusing... an old term left over from opt.
            'ContractLevel' => $this->ContractLevel,
            'AddressLine1' => $this->AddressLine1,
            'AddressLine2' => $this->AddressLine2,
            'City' => $this->City,
            'County' => $this->County,
            'State' => $this->State,
            'Zip' => $this->ZipCode,
            'AgentPhone' => $this->AgentPhone,
            'LastActivityDate' => $this->LastActivityDate,
            'IsAgencyOwner' => $this->IsAgencyOwner == 0 ? false : true,
            'HasTotalAgency' => $this->HasTotalAgency == 0 ? false : true,
            'LeadershipLevel' => $this->LeadershipLevel,
            'ProducerLevel' => $this->ProducerLevel,
            'IsSFGDirect' => $this->IsSFGDirect == 0 ? false : true,
            'UplineOptID' => $this->UplineOptID,
            'UplineAgentID' => $this->UplineAgentID,
            'UplineAgentCode' => $this->UplineAgentCode,
            'BaseShopOwnerAgentCode' => $this->BaseShopOwnerAgentCode,
            'BaseShopOwnerOptID' => $this->BaseShopOwnerOptID,
            'BaseShopOwnerAgentID' => $this->BaseShopOwnerAgentID,
            'SFGDirectAgentCode' => $this->SFGDirectAgentCode,
            'SFGDirectOptID' => $this->SFGDirectOptID,
            'SFGDirectAgentID' => $this->SFGDirectAgentID,
            'LeadershipLevelOverride' => $this->agent ? $this->agent->LeadershipLevelOverride : null,
            'HasManagerAccess' => $this->HasManagerAccess == 0 ? false : true,
            'IsOwnerCircleMember' => $this->IsOwnerCircleMember == 0 ? false : true,
            'DoingBusinessAsName' => $this->DoingBusinessAsName,
            'DoingBusinessAsNPN' => $this->DoingBusinessAsNPN,
            'IsFullyLicensed' => $this->IsFullyLicensed == 0 ? false : true,
            'IsAdvisoryBoardMember' => $this->IsAdvisoryBoardMember == 0 ? false : true,
            'HasManagerAccess' => $this->HasManagerAccess == 0 ? false : true,
            'QMSParticipant' => $this->QMSParticipantInd == 0 ? false : true,
            'QMSStartDate' => $this->QMSStartDate == null ? null : Carbon::parse($this->QMSStartDate)->format('m/d/Y'),
            'Available' => $this->AvailableInd == 1 ? true : false,
            'TerminationReason' => $this->TerminationReason,
        ];
        if ($request->input('attr') != null) {
            foreach (explode(",", $request->input('attr')) as $attr) {
                switch ($attr) {
                    case 'UplineAgentName':
                        $r['UplineAgentName'] = $this->directUpline ? $this->directUpline->AgentDisplayName : null;
                        $r['UplineAgentLeadershipLevel'] = $this->directUpline ? $this->directUpline->LeadershipLevel : null;
                        break;
                    case 'BaseshopOwnerAgentName':
                        $r['BaseshopOwnerAgentName'] = $this->baseshopOwner ? $this->baseshopOwner->AgentDisplayName : null;
                        $r['BaseshopAgentLeadershipLevel'] = $this->baseshopOwner ? $this->baseshopOwner->LeadershipLevel : null;
                        break;
                    case 'SFGDirectAgentName':
                        $r['SFGDirectAgentName'] = $this->directAgencyOwner ? $this->directAgencyOwner->AgentDisplayName : null;
                        break;
                    case 'DirectAgencyOwner':
                        $r['DirectAgencyOwner'] = $this->directAgencyOwner;
                        break;
                    case 'Upline':
                        $r['Upline'] = $this->upline;
                        break;
                    case 'Avatar':
                        $r['Avatar'] = $this->avatar;
                        break;
                    case 'Signature':
                        //wierd ass behavior here, was always returning {} instead of null.
                        $r['Signature'] = json_encode($this->signature) == "{}" ? null : url($this->signature);
                        break;
                    case 'StateLicenses':
                        $r['StateLicenses'] = $this->state_licenses()->get();
                        break;
                    case 'WritingNumbers':
                        $r['WritingNumbers'] = CarrierWritingNumberResource::collection($this->resource->writingNumbers);
                        break;
                    case 'LastLoginDate':
                        $lastLoginDate = $this->recentLogin()->first();
                        $r['LastLoginDate'] = $lastLoginDate->CreateDate == null ? null : Carbon::parse($lastLoginDate->CreateDate)->format('m/d/Y');
                        break;
                }
            }
        }
        return $r;
    }
}
