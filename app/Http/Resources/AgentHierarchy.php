<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentHierarchy extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        //return collect($this->resource)->toArray();
        return [
            "UplineAgentCode"=> $this->UplineAgentCode,
            "DownlineAgentCode"=> $this->DownlineAgentCode,
            "DownlineLevel"=> $this->DownlineLevel,
            "InBaseShopInd"=>$this->InBaseShopInd,
            "UplineAgentEmail" => $this->UplineAgent->AgentEmail,
            "DownlineAgentEmail" => $this->DownlineAgent->AgentEmail
        ];
    }
}
