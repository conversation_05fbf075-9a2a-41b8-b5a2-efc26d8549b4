<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;

class BaseAgent extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $LeadershipLevel = $this->agent->AgentLevels()->Leadership()->current()->first();
        $CommissionLevel = $this->agent->AgentLevels()->Contract()->current()->first();
        $r = collect($this->resource)->toArray();
        $r["UplineAgentCode"] = $this->agent->UplineAgentCode;
        $r["LeadershipLevelID"] = $LeadershipLevel != null ? (int)$LeadershipLevel->LevelID : null;
        $r["ContractLevelID"] = $CommissionLevel != null ? (int)$CommissionLevel->LevelID : null;
        $r["OptPassword"] = isset($this->OptPassword) ? $this->OptPassword : '';
        $r["HideMiddlename"] = isset($this->agent->HideMiddlename) ? $this->agent->HideMiddlename : false;
        $r["IsFullyLicensed"] = $this->IsFullyLicensed == 0 ? false : true;
        $r["IsAdvisoryBoardMember"] = $this->IsAdvisoryBoardMember == 0 ? false : true;
        $r['Available'] = $this->AvailableInd == 1 ? true : false;
        return $r;
    }
}
