<?php

namespace App\Http\Resources\AgentManagement;

use App\Helpers\API\Filters;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CarrierCollection extends ResourceCollection
{

    public $collects = 'App\Http\Resources\AgentManagement\Carrier';

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $meta = [];
        if ($request->input('needAvailableFilters') == "true") {
            $filters = Filters::getPossibleFilters('App\Models\Base\Carrier');
            $meta['filters'] = $filters;
        }
        return [
            'data' => $this->collection,
            'meta' => $meta,
        ];
    }
}
