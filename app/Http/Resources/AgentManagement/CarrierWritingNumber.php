<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;

class CarrierWritingNumber extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'CarrierID' => $this->CarrierID,
            'CarrierName' => $this->carrier->CarrierName,
            'ID' => $this->ID,
            'NPN' => $this->NPN,
            'WritingNumber' => $this->WritingNumber,
            'KnownWritingNumberNumberFormats' => $this->carrier->KnownWritingNumberFormats,
            'Active' => $this->ActiveInd == 1,
            'LastChangeDate' => $this->LastChangeDate,
        ];
    }
}
