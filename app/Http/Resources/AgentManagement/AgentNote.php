<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class AgentNote extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $r = [
            'ID' => $this->ID,
            'AgentID' => $this->AgentID,
            'NoteTitle' => $this->NoteTitle,
            'NoteText' => $this->NoteText,
            'StickyInd' => $this->StickyInd == 1 ? true : false,
            'Link' => $this->Link,
            'LastChangeBy' => $this->LastChangeBy,
            'LastChangeDate' => Carbon::parse($this->LastChangeDate)->format('Y-m-d'),
        ];
        return $r;
    }
}
