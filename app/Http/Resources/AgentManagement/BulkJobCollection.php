<?php

namespace App\Http\Resources\AgentManagement;

use App\Helpers\API\Filters;
use Illuminate\Http\Resources\Json\ResourceCollection;

class BulkJobCollection extends ResourceCollection
{

    public $collects = 'App\Http\Resources\AgentManagement\BulkJob';

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $meta = [];
        if ($request->input('needAvailableFilters') == "true") {
            $filters = Filters::getPossibleFilters('App\Models\AgentManagement\BulkJob');
            $meta['filters'] = $filters;
        }
        return [
            'data' => $this->collection,
            'meta' => $meta,
        ];
    }
}
