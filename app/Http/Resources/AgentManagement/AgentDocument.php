<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;
use \App;
use Illuminate\Support\Carbon;

class AgentDocument extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $r = [
            'ID' => $this->ID,
            'AgentID' => $this->AgentID,
            'CategoryName' => $this->CategoryName,
            'Division' => $this->Division,
            'DocType' => $this->DocType,
            'DocTitle' => $this->DocTitle,
            'DocDesc' => $this->DocDesc,
            'DownloadLink' => App::make('url')->to($this->DownloadLink),
			'ViewLink' => App::make('url')->to($this->ViewLink),
            'LastChangeDate' => Carbon::parse($this->LastChangeDate)->format('Y-m-d'),
        ];
        return $r;
    }
}
