<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentLevel extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if (!isset($this->ID)) {
            return [];
        }
        return [
            'id' => $this->ID,
            'AgentCode' => $this->agent_code,
            'AgentID' => (int) $this->AgentID,
            'LevelID' => (int) $this->LevelID,
            'LevelName' => $this->Level->LevelName,
            'LevelType' => $this->Level->LevelType,
            'LevelRank' => $this->Level->LevelRank,
            'OverrideLevelID' => (int) $this->OverrideLevelID,
            'OverrideLevelName' => $this->OverrideLevelID ? $this->OverrideLevel->LevelName : null,
            'StartDate' => $this->StartDate,
            'EndDate' => $this->EndDate,
            'LastChangeBy' => $this->LastChangeBy,
            'LockedInd' => $this->LockedInd == 0 ? false : true,
            'Notes' => $this->Notes,
        ];
    }
}
