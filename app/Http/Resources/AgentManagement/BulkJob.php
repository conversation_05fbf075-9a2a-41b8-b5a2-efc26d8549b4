<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;

class BulkJob extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
    
        $r = [
            'ID' => $this->ID,
            'JobName' => $this->JobName,
            'StartDate' => $this->StartDate,
            'EndDate' => $this->EndDate,
            'UserName' => $this->UserName,
            'NumProcessed' => $this->NumProcessed,
            'NumErrors' => $this->NumErrors,
            'HasErrorLog' => isset($this->ErrorLogFile) ? true : false,
        ];
        return $r;
    }
}
