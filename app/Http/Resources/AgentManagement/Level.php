<?php

namespace App\Http\Resources\AgentManagement;

use Illuminate\Http\Resources\Json\JsonResource;

class Level extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->ID,
            'LevelName' => $this->LevelName,
            'LevelType' => $this->LevelType,
            'LevelRank' => $this->LevelRank
        ];
    }
}
