<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $array = [
            'id'            => $this->id,
            'user_id'       => $this->user_id,
            'target_id'     => $this->target_id,
            'type'          => $this->type,
        ];

        if (config('tasks.show_completed', false)) {
            $array['completed'] = $this->completed;
        }

        return $array;
    }
}
