<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentProfile extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'AgentCode' => $this->AgentCode,
            'DisplayName' => $this->DisplayName,
            'Titles' => json_decode($this->Titles),
            'ProfilePhoto' => $this->ProfilePhoto,
            'Section' => $this->Section,
            'ListOrder' => $this->ListOrder,
            'Division' => $this->Division,
            'Title' => $this->Title,
            'Email' => $this->Email,
            'Bio' => $this->Bio,
        ];
    }
}
