<?php

namespace App\Http\Resources;

use App\Models\AgentManagement\AgentLevel;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AgentProfileCollection extends ResourceCollection
{

    public $collects = 'App\Http\Resources\AgentProfile';

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        if ($request->input("Division") == "Corporate") {
            $sections = [
                'Founders',
                'Chiefs',
                'Executive',
                'Directors'
                /*'Accounting',
                'Audio/Video',
                'Business Intelligence',
                'Contracting',
                'Human Resources',
                'Initiatives',
                'IT',
                'Leads',
                'Maintenance',
                'Marketing',
                'Special Projects',
                'Support Services'
                */
            ];
        } else {
            $sections = array_reverse(AgentLevel::AgencyOwnerLevels);
        }
        $meta = [];
        return [
            'data' => $this->collection,
            'meta' => [
                "Sections" => $sections,
            ],
        ];
    }
}
