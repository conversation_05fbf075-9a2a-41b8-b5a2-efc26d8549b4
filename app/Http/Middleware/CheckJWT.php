<?php
// app/Http/Middleware/CheckJWT.php

namespace App\Http\Middleware;

use App\Models\User;
use Auth;
use Closure;
use App\Services\Auth0TokenService;

class CheckJWT
{

    // protected $role_namespace = "";
    // private $userRepository;

    /**
     * Validate an incoming JWT access token.
     *
     * @param \Illuminate\Http\Request $request - Illuminate HTTP Request object.
     * @param Closure $next - Function to call when middleware is complete.
     *
     * @return mixed
     */
    public function handle($request, Closure $next, $permissionRequired = null)
    {
        $accessToken = $request->bearerToken();
        try {
            //this will fail if it's a bad token
            $auth0 = new Auth0TokenService($accessToken);
            $decodedToken = $auth0->token;

            // var_dump($auth0);
            if (!$decodedToken) {
                return response()->json(["message" => "Unauthorized user"], 401);
            }

            //this checks the scope of the token - to determine if the user has permissions needed for the resource requested.
            if ($permissionRequired && !$this->tokenHasPermission($decodedToken, $permissionRequired)) {
                return response()->json(['message' => 'Insufficient scope : requires a scope of ' . $permissionRequired], 403);
            }

            //machine-to-machine API request... use a blank user and log them in.
            $user = new User();
            Auth::login($user);
        } catch (\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 401);
        }
        // Return statement is unchanged.
        return $next($request);
    }

    /**
     * Check if a token has a specific scope.
     *
     * @param \stdClass $token - JWT access token to check.
     * @param string $scopeRequired - Scope to check for.
     *
     * @return bool
     */
    protected function tokenHasScope($token, $scopeRequired)
    {
        if (empty($token->scope)) {
            return false;
        }
        $tokenScopes = explode(' ', $token->scope);
        return in_array($scopeRequired, $tokenScopes);
    }

    public function tokenHasPermission($token, $perm)
    {
        if ($token->permissions == null) {
            return false;
        }
        return in_array($perm, $token->permissions);
    }

    /**
    Check if a token has a specific role...
    this is only used in the /routes/api.php file for staff and execs etc.
    Agent permissions are set using policies.
    **/

    // protected function tokenHasRole($token, $roleRequired)
    // {
    //     $namespace = $this->role_namespace;
    //     $roles = explode("|", $roleRequired);
    //     foreach ($roles as $role) {
    //         if (in_array($role, $token->$namespace)) {
    //             return true;
    //         }
    //     }
    //     return false;
    // }
}
