<?php

namespace App\CustomProviders;

use App;
use App\Models\User;
use App\Models\FormLookup;
use App\Models\FormField;
use App\Models\UserRemoteAccount;
use FormBuilder;
use Log;
use App\Models\Agent;
use App\Models\UserUpload;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use App\Models\UserInvite;
use App\Models\UserSignature;
use Agents;
use Illuminate\Support\Facades\Mail;
use App\Mail\ApprovalErrorMail;
use App\Mail\AdvancedMarketMail;
use File;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;


class HQAccountServiceProvider {

    protected $guzzle;
    protected $authPath;
    protected $apiPath;
    protected $audience;
    protected $clientId;
    protected $clientSecret;
    protected $accessToken = null;
    protected $newAgentOutput;

    public function __construct()
    {
        $this->guzzle = new \GuzzleHttp\Client;
        $this->clientId = config('quilityaccounts.clientId', null);
        $this->clientSecret = config('quilityaccounts.clientSecret', null);
        $this->authPath = config('quilityaccounts.authPath', null);
        $this->audience = config('quilityaccounts.audience', null);
        $this->apiPath = config('quilityaccounts.apiPath', null);
    }

    public function isFullyLicensed($user) {

        // NOTE: $user can be either an id or user object

        // Q2B agents are always considered licensed
        if(config('app.tenant') == 'Q2B')
            return true;

        return (FormBuilder::fetchUserEntry($user, FormLookup::LICENSE_IS_LICENSED_BOOL) == FormField::SELECT_BOOL_YES);
    }

    public function isReturningToSymmetry($user) {
        return (FormBuilder::fetchUserEntry($user, FormLookup::RETURNING_TO_SYMMETRY) == FormField::SELECT_BOOL_YES);
    }

    public function hasExistingLiteAccount($userId) {
        return UserRemoteAccount::where('user_id', $userId)
                                ->where('status', UserRemoteAccount::STATUS_CREATED)
                                ->exists();
    }

    private function isJson($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    public function generateAccessToken($onlyReturnAccessToken = false)
    {
        // Cache the token for 12 hours (720 minutes)
        return Cache::remember('quility_access_token', 12 * 60, function () use ($onlyReturnAccessToken) {

        // Make the API request to get the token
        $response = $this->guzzle->request('POST', $this->authPath, [
            'form_params' => [
                'grant_type'        => 'client_credentials',
                'client_id'         => $this->clientId,
                'client_secret'     => $this->clientSecret,
                'audience'          => $this->audience,
            ],
        ]);

        $code = $response->getStatusCode();


        if ($code != 200) {
            $reason = $response->getReasonPhrase();
            Log::error("QUILITY GENERATE ACCESS TOKEN FAILED: {$code} {$reason}.");
            return false;
        }

        $body = $response->getBody();

        if (!$this->isJson($body)) {
            Log::error("QUILITY GENERATE ACCESS TOKEN FAILED: Server did not return valid JSON.");
            return false;
        }

        $returnBody = json_decode($body);
        // Return the access token or the full response body
        return ($onlyReturnAccessToken ? $returnBody->access_token : $returnBody);

        });
    }

    public function sendFileRequest($localFile, $agentCode, $name = null)
    {
        Log::info("Sending file request for agentCode: {$agentCode}, file: {$localFile}");

        $requestPath = $this->apiPath.config('quilityaccounts.paths.document', 'trusted/agent_document');
        $accessToken = $this->accessToken != null ? $this->accessToken : $this->generateAccessToken(true);

        Log::info("File request path: {$requestPath}, Access token generated.");

        try {
            $response = $this->guzzle->request('POST', $requestPath, [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                ],
                'multipart' => [
                    [
                        'name'     => 'document',
                        'contents' => file_get_contents($localFile),
                        'filename' => $name == null ? basename($localFile) : $name,
                    ],
                    [
                        'name'     => 'AgentCode',
                        'contents' => $agentCode,
                    ],
                    [
                        'name'     => 'DocumentTags',
                        'contents' => stristr($name, 'E&O') !== false ? 'E&O Cert' : '' //can send multiple comma-seperated tags if needed
                    ]
                ],
            ]);

            Log::info('File request sent, awaiting response.');

        } catch (\Exception $e) {
            Log::error('QUILITY DOCUMENT REQUEST FAILED 1: '.$localFile.' '.$agentCode.' '.$e);
            return false;
        }

        if (!isset($response) || $response === null) {
            Log::error('QUILITY DOCUMENT REQUEST FAILED RESPONSE NULL: '.$localFile.' '.$agentCode);
            return false;
        }

        $code = $response->getStatusCode();

        Log::info("File request response code: {$code}");

        if ($code != 200) {
            $reason = $response->getReasonPhrase();
            Log::error("QUILITY DOCUMENT REQUEST FAILED: {$code} {$reason}.");
            return false;
        }

        $body = $response->getBody();

        if (!$this->isJson($body)) {
            Log::error("QUILITY DOCUMENT REQUEST FAILED: Server did not return valid JSON.");
            return false;
        }

        Log::info('File request completed successfully.');
        return json_decode($body);
    }

    public function sendAgentRequest($userId)
    {
        Log::info("Sending agent request for userId: {$userId}");

        try {
            $requestType =  'POST';
            $requestPath = $this->apiPath.config('quilityaccounts.paths.agent', 'trusted/base_agent');
            $accessToken = $this->accessToken = $this->generateAccessToken(true);
            $apiPayload = $this->generateUserObject($userId);
            $currentUser = $this->userExsistsInHQ($apiPayload);
            $app_tenant = config('app.tenant');

            if($currentUser){
                $requestPath = $requestPath . '/' . $currentUser->AgentCode;
                $requestType = 'PUT';
            }

            Log::info("API Request Path: {$requestPath}, Request Type: {$requestType}");

            //Log Post Payload
            $logApiPayload = $apiPayload;
            unset($logApiPayload['SSN']);
            Log::info("API Payload: ".print_r($logApiPayload, true));
            // Log::info("QUILITY AGENT REQUEST ".$userId.": START");
            Log::info( "QUILITY AGENT REQUEST PAYLOAD ".$app_tenant." ".$userId." ".print_r($logApiPayload, true));

            $response = $this->guzzle->request($requestType, $requestPath, [
                'headers' => [
                    'Content-Type'  => 'application/json',
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept'        => 'application/json',
                ],
                'json' => $apiPayload,
                'connect_timeout' => 240
            ]);

            $code = $response->getStatusCode();
            Log::info("Agent request response code: {$code}");

            if ($code < 200 || $code >= 300) {
                $reason = $response->getReasonPhrase();
                Log::error("QUILITY AGENT REQUEST FAILED ".$app_tenant." ".$userId.": {$code} {$reason}.");
                return false;
            }

            $body = $response->getBody();

            if (!$this->isJson($body)) {
                Log::error("QUILITY AGENT REQUEST FAILED ".$app_tenant." ".$userId.": Server did not return valid JSON.");
                return false;
            }

            Log::info("Agent request completed successfully for userId: {$userId}");
            $result = json_decode($body, true);

            if (isset($result['error'])) {
                if ($result['error'] == true) {
                    Log::error("QUILITY AGENT REQUEST FAILED ".$app_tenant." ".$userId.": {$result['message']}");
                    $recruit = User::find($userId);
                    $recruitStatusProvider = (new Agents())->for($recruit);
                    $user_status = UserStatus::where('slug', 'approval-error')->first();

                    $error_message = $result['message'] ?? 'Approval Error';
                    $recruitStatusProvider->setStatus($user_status->slug, $userId, $error_message);
                    $this->sendApprovalErrorNotification($recruit, $error_message);
                    
                    return false;
                }
            }

            // Send Advanced Market Email?
            if(config('app.tenant') == 'Q2A') {
                $user = User::find($userId);
                $advanced_market_enrollment = $user->getMeta('advanced_market_enrollment', null);
                if($advanced_market_enrollment == true) {
                    Mail::to($user->email)->send(new AdvancedMarketMail($user->name));
                }
            }

            return $result;
        } catch (\Exception $ex) {
            //This is usually a guzzle error
            if(strpos($ex, 'error code: 524') > 0) {
                Log::error('QUILITY AGENT REQUEST FAILED TIMEOUT ERROR FOR '.$userId.' '.$ex);
                // wait 20 seconds to give HQ time to complete setting up the agent
                sleep(20);
                // see if this user exists in HQ and the request timed out
                if($agent = Agent::where('AgentName', 'LIKE', $this->newAgentOutput['FirstName']."%".$this->newAgentOutput['LastName'])->where('CreateDate', '>', Carbon::now()->subMinutes(10))->first()) {
                    Log::info('QUILITY AGENT REQUEST AGENT FOUND IN HQ '.$agent->AgentCode.' '.$userId);
                    $response['data'] = ['agent' => $agent->toArray()];
                    return $response;
                }
            }

            Log::error('QUILITY AGENT REQUEST FAILED GENERAL ERROR FOR '.$userId.' '.$ex);
            $recruit = User::find($userId);

            $recruitStatusProvider = (new Agents())->for($recruit);
            $user_status = UserStatus::where('slug', 'approval-error')->first();

            $error_message = $result['message'] ?? $ex->getMessage() ?? 'Approval Error';
            $recruitStatusProvider->setStatus($user_status->slug, $userId, $error_message);
            $this->sendApprovalErrorNotification($recruit, $error_message);
        }

    }

    function sendApprovalErrorNotification($recruit, $error_message)
    {
        $ho_approved_status = UserStatus::where('slug', User::ONBOARD_STATUS_HO_APPROVED)->first();
        $approval = UserStatusHistory::where('user_id', $recruit->id)->where('user_status_id', $ho_approved_status->id)->orderBy('created_at', 'desc')->first();
        $staff = User::find($approval->trigger_id)->first();
        $app_url = config('app.url') . '/app/application/review/'.$recruit->id;
        try {
            Mail::to($staff->email)->send(new ApprovalErrorMail($staff->name, $recruit->name, $error_message, $app_url));
        } catch (\Exception $ex) {
            Log::error("Failed to send Approval Error email: {$ex}");
        }
        return;
    }

    public function generateUserObject($userId, $returnJson = false)
    {
        Log::info("Generating user object for userId: {$userId}");

        try {
            $user = User::find($userId);
            if (!$user) {
                Log::error("QUILITY GENERATE USER OBJECT FAILED: User with id of {$userId} not found.");
                return false;
            }

            $output = [];

            if (is_null($user->upline_agent_id)) {
                Log::error("QUILITY GENERATE USER OBJECT FAILED: User with id of {$userId} does not have an upline agent code set.");
                return false;
            }

            // fetch contract level
            $contractLevel = $user->contract_level ?? config('quilityaccounts.defaultContractLevel', 80);

            $uplineAgent = Agent::where(['AgentID' => $user->upline_agent_id])->first();

            if(config('app.tenant') == 'Q2B') {
                if($user->type == 'b2b-principal') {
                    $output = array_merge($output, [
                    "UplineAgentCode"       => $uplineAgent->AgentCode ?? $user->agency_owner_code ,
                        "ContractStartDt"       => date('Y-m-d'),
                        "ContractLevelID"       => config("quilityaccounts.contractLevels.{$contractLevel}", 775346),
                        "LeadershipLevelID"     => config('quilityaccounts.leadershipLevels.AgencyOwner', 622186),
                    ]);
                } else if($user->type == 'b2b-agent') {
                    $output = array_merge($output, [
                        "UplineAgentCode"       => $uplineAgent->AgentCode ?? $user->agency_owner_code ,
                        "ContractStartDt"       => date('Y-m-d'),
                        "ContractLevelID"       => config("quilityaccounts.contractLevels.{$contractLevel}", 775346),
                        "LeadershipLevelID"     => config('quilityaccounts.leadershipLevels.SalesRep', 622183),
                    ]);
                }
            } else {
                // default/q2a
                $output = array_merge($output, [
                    "UplineAgentCode"       => $uplineAgent->AgentCode ?? $user->agency_owner_code ,
                    "LeadTypeDefault"       => config('quilityaccounts.leadTypes.default', "0-1007"),
                    "LeadTypesAvailable"    => config('quilityaccounts.leadTypesAvailable.default', []),
                    "ContractStartDt"       => date('Y-m-d'),
                    "ContractLevelID"       => config("quilityaccounts.contractLevels.{$contractLevel}", 775346),
                    "LeadershipLevelID"     => config('quilityaccounts.leadershipLevels.SalesRep', 622183),
                ]);
            }

            $output["isBusiness"] = (FormBuilder::fetchUserEntry($user, FormLookup::FIELD_IS_BUSINESS_BOOL) == FormField::SELECT_BUSINESS_TYPE_BUSINESS);

            // NOTE: only send these values if it is a business
            if ($output["isBusiness"]) {
                $output = array_merge($output, [
                    "business" => [
                        "Type"  => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_TYPE),
                        "Phone" => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_PHONE, true),
                    ],
                ]);
            }

            
            $output["IsFullyLicensed"] = $this->isFullyLicensed($user);  
            // NOTE: NPN is required, but we may not have an NPN yet
            // UPDATE: NPN is no longer collected in the application. It's looked up in HQ via AgentSync.
            // $output["NPN"] = ($output["IsFullyLicensed"] ? FormBuilder::fetchUserEntry($user, FormLookup::LICENSE_NPN, true) : config('quilityaccounts.defaultNPNValue', null));
            
            // NPN can now be verified via AgentSync and stored in the user's meta data
            $output["NPN"] = $user->getMeta('npn', null);

            if($output["NPN"] === false)
                $output["NPN"] = null;

            // will add a sticky note in HQ if returning to symmetry
            $output["IsReturning"] = $this->isReturningToSymmetry($user);
            $output["ReturningSfgAgentCode"] = FormBuilder::fetchUserEntry($user, FormLookup::RETURNING_SFG_AGENT_CODE);

            // fields will help HQ decide what emails to send
            if($invite = UserInvite::find($user->user_invite_id)) {
                $output['advanced_markets'] = $invite->advanced_markets;
                $output['experience'] = $invite->experience;
            } else {
                $output['advanced_markets'] = '';
                $output['experience'] = '';
            }
            $output['carrier_selections'] = $user->getMeta('carriers', '');
            if(empty($output['carrier_selections']))
                $output['carrier_selections'] = 'Contact your Agency Owner';

            $states = array_flip(config('states'));
            //look to see if they used the full state name.  Else use the user enty.  This should be updated to be caught prior to submission.
            $state = $states[strtoupper(FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STATE))] ?? FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STATE);

            $middle_name = FormBuilder::fetchUserEntry($user, FormLookup::FIELD_MIDDLE_NAME);
            if(!$middle_name)
                $middle_name = '';

            // pull fields
            if(config('app.tenant') == 'Q2B') {
                if($user->type == 'b2b-principal') {
                    $output = array_merge($output, [
                        "Division"              => config('quilityaccounts.divisions.default', 'Field'),
                        "Status"                => config('quilityaccounts.defaultAccountStatus', 'Active'),
                        "FirstName"             => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_FIRST_NAME),
                        "LastName"              => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_LAST_NAME),
                        "BirthDt"               => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_DOB),
                        "SSN"                   => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_SSN),
                        "Email"                 => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_EMAIL),
                        "State"                 => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_STATE),
                    ]);
                } else if($user->type == 'b2b-agent') {
                    // $firstName = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_FIRST_NAME);
                    // $lastName = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_LAST_NAME);
                    // $email = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_EMAIL);
                    $output = array_merge($output, [
                        "Division"              => config('quilityaccounts.divisions.default', 'Field'),
                        "Status"                => config('quilityaccounts.defaultAccountStatus', 'Active'),
                        "FirstName"             => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_FIRST_NAME),
                        "LastName"              => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_LAST_NAME),
                        "BirthDt"               => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_DOB),
                        "SSN"                   => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_SSN),
                        "Email"                 => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_EMAIL),
                        "State"                 => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_STATE),
                    ]);
                }
            } else {
                $output = array_merge($output, [
                    "Division"              => config('quilityaccounts.divisions.default', 'Field'),
                    "Status"                => config('quilityaccounts.defaultAccountStatus', 'Active'),
                    "FirstName"             => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_FIRST_NAME),
                    "LastName"              => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_LAST_NAME),
                    "Suffix"                => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_NAME_SUFFIX),
                    "MiddleName"            => $middle_name,
                    "HideMiddlename"        => config('quilityaccounts.defaultHideMiddlename', false),
                    "BirthDt"               => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BIRTHDATE),
                    "SSN"                   => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_SSN, true),
                    "Email"                 => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_WORK_EMAIL),
                    "Phone"                 => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_PHONE, true),
                    "Street1"               => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STREET1),
                    "City"                  => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_CITY),
                    "County"                => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_COUNTY),
                    "Zip"                   => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_ZIP),
                    "State"                 => $state,
                    "MaritalStatus"         => empty(FormBuilder::fetchUserEntry($user, FormLookup::DEMO_MARRIAGE)) ? 'NONE' : FormBuilder::fetchUserEntry($user, FormLookup::DEMO_MARRIAGE),
                    "Gender"                => empty(FormBuilder::fetchUserEntry($user, FormLookup::DEMO_GENDER)) ? 'NONE' : FormBuilder::fetchUserEntry($user, FormLookup::DEMO_GENDER),
                    // "Gender"                => 'NONE',
                ]);
            }

            if(empty($output["Suffix"]) || $output["Suffix"] == "0")
                $output["Suffix"] = "";

            // NOTE: do send these values, but as null if not a business
            if ($output["isBusiness"]) {
                $dbaFields = [
                    "DoingBusinessAsName"   => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_NAME),
                    "DoingBusinessAsNPN"    => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_NPN, true),
                ];
            } else {
                $dbaFields = [
                    "DoingBusinessAsName"   => null,
                    "DoingBusinessAsNPN"    => null,
                ];
            }


            // Add tenant ID to the output
            $output['TenantID'] = config('app.tenant_id');

            $output = array_merge($output, $dbaFields);
            $this->newAgentOutput = $output;
            Log::info("User object generated successfully for userId: {$userId}");
            return ($returnJson ? json_encode($output) : $output);

        } catch (\Exception $ex) {
            Log::error('GENERAL ERROR '. $ex);
        }
    }

    public function userExsistsInHQ($user){
        if(!$user || !isset($user['Email']))
            return;
        $agent = Agent::where([
            //'npn' => $user['NPN'],
            'AgentEmail' =>  $user['Email']
        ])->first();
        return $agent;
    }

    public function uploadFilesToHq($userId) {
        $uploads = UserUpload::where('user_id',$userId)->where('hq_uploaded', 0)->get();
        // $agent = UserRemoteAccount::where('user_id', $userId)->where('status', UserRemoteAccount::STATUS_CREATED)->first();
        $agent = UserRemoteAccount::where('user_id', $userId)->first();
        if(config('app.tenant') == 'Q2B') {
            $user = User::find($userId);
            if($user->type == 'b2b-principal') {
                $first_name = FormBuilder::fetchUserEntry($userId, FormLookup::PRINCIPAL_FIRST_NAME);
                $last_name = FormBuilder::fetchUserEntry($userId, FormLookup::PRINCIPAL_LAST_NAME);
            } elseif($user->type == 'b2b-agent') {
                $first_name = FormBuilder::fetchUserEntry($userId, FormLookup::B2B_AGENT_FIRST_NAME);
                $last_name = FormBuilder::fetchUserEntry($userId, FormLookup::B2B_AGENT_LAST_NAME);
            }
        } else {
            $first_name = FormBuilder::fetchUserEntry($userId, FormLookup::FIELD_FIRST_NAME);
            $last_name = FormBuilder::fetchUserEntry($userId, FormLookup::FIELD_LAST_NAME);
        }

        $total = 0;
        $pdf = resolve('App\Services\GenerateFCRAService');
        try {
            foreach($uploads as $upload) {

                $field = FormField::where('id', $upload->form_field_id)->first();

                $filename = $field->filename
                    ? $field->filename.'-'.$first_name.'_'.$last_name.'-'.$upload->original_name
                    : $first_name.'_'.$last_name.'-'.$upload->original_name;

                $filename = str_replace(" ", "_", $filename);

                $fullLocalPath = storage_path("app/public/uploads/files/{$userId}/{$upload->file_name}");

                if(file_exists($fullLocalPath)) {
                    $uploaded = $this->sendFileRequest($fullLocalPath, $agent->agent_code, $filename);
                    if(!!$uploaded) {
                        //mark file as uploaded
                        $upload->hq_uploaded = 1;
                        $upload->save();
                        $total++;
                    }
                }
            }

            if(config('app.tenant') == 'Q2A') {
                // FCRA.pdf
                $fullLocalPath = storage_path("/app/public/uploads/files/" . $userId . "/FCRA.pdf");
                $exist = file_exists($fullLocalPath);

                if($exist)
                {
                    $uploaded = $this->sendFileRequest($fullLocalPath, $agent->agent_code, "FCRA.pdf");
                    if(!!$uploaded) {
                        $total++;
                    }
                } else {
                    // create file and upload it
                    $fullLocalPath = $pdf->makePDF($userId);
                    $uploaded = $this->sendFileRequest($fullLocalPath, $agent->agent_code, "FCRA.pdf");
                    if(!!$uploaded) {
                        $total++;
                        }
                }
            }

            //ICA
            $user = User::find($userId);
            $email = str_replace("-stale", "", $user->email);
            $signature = UserSignature::where('user_email', $email)
                                ->where('event_type', UserSignature::EVENT_DOWNLOADABLE)
                                ->first();

            if($signature) {
                $relativePath = "uploads/signatures/{$signature->file_name}";
                $completePath = storage_path("app/{$relativePath}");

                if (!File::exists($completePath)) {
                    //TODO: refactor to HelloSignService
                    $HelloSignController = resolve('App\Http\Controllers\HelloSignController');
                    $HelloSignController->downloadFiles($signature->signature_request_id);
                }
                $uploaded = $this->sendFileRequest($completePath, $agent->agent_code, "ICA.zip");
                if(!!$uploaded) {
                    $total++;
                } else {
                    Log::error("FILE UPLOAD ERROR (ICA) COULDN'T BE UPLOADED TO HQ FOR ".$userId);
                }
            } else {
                Log::error("FILE UPLOAD ERROR (ICA) COULDN'T FIND SIGNATURE RECORD FOR ".$userId);
            }
        } catch (\Exception $ex) {
            Log::error('FILE UPLOAD ERROR '. $ex);
        }
        Log::info("Uploaded $total files to HQ for user $userId");
        return $total;
    }
}