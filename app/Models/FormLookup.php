<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\FormLookup
 *
 * @property string $id
 * @property string $form_field_id
 * @property string $field_slug
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup whereFieldSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup whereFormFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormLookup whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FormLookup extends Model
{
    use HasFactory, UsesUuid;

    // personal lookup
    const FIELD_FIRST_NAME  = 'personal-first-name';
    const FIELD_MIDDLE_NAME = 'personal-middle-name';
    const FIELD_LAST_NAME   = 'personal-last-name';
    const FIELD_NAME_SUFFIX = 'personal-name-suffix';
    const FIELD_BIRTHDATE   = 'birthdate';
    const FIELD_WORK_EMAIL  = 'work-email';
    const FIELD_PHONE       = 'personal-phone';
    const FIELD_STREET1     = 'personal-street1';
    const FIELD_CITY        = 'personal-city';
    const FIELD_COUNTRY     = 'personal-country';
    const FIELD_COUNTY      = 'personal-county';
    const FIELD_STATE       = 'personal-state';
    const FIELD_ZIP         = 'personal-zip';
    const FIELD_MOVE_IN_AT  = 'personal-move-in-date';
    const IS_CITIZEN_BOOL   = 'is-citizen-boolean';

    // confirm address
    const FIELD_C_STREET1     = 'personal-confirm-street1';
    const FIELD_C_CITY        = 'personal-confirm-city';
    const FIELD_C_COUNTRY     = 'personal-confirm-country';
    const FIELD_C_COUNTY      = 'personal-confirm-county';
    const FIELD_C_STATE       = 'personal-confirm-state';
    const FIELD_C_ZIP         = 'personal-confirm-zip';
    const FIELD_C_MOVE_IN_AT  = 'personal-confirm-move-in-date';

    // for users with a separate mailing address
    const FIELD_ADDRESS_MAILING_BOOL = 'mailing-address-boolean';
    const FIELD_MAILING_STREET1     = 'mailing-street1';
    const FIELD_MAILING_CITY        = 'mailing-city';
    const FIELD_MAILING_STATE       = 'mailing-state';
    const FIELD_MAILING_ZIP         = 'mailing-zip';

    // basic lookup fields
    const FIELD_SSN         = 'ssn';
    const FIELD_CRD_NUMBER  = 'crd-number';

    // business fields
    const FIELD_IS_BUSINESS_BOOL    = 'is-business-boolean';
    const FIELD_BUSINESS_TYPE       = 'business-type';
    const FIELD_BUSINESS_PHONE      = 'business-phone';
    const FIELD_BUSINESS_NAME       = 'business-name';
    const FIELD_BUSINESS_NPN        = 'business-npn';

    // important steps
    const LICENSE_IS_LICENSED_BOOL  = 'is-licensed-boolean';
    const LICENSE_NPN               = 'npn-number';
    const LICENSE_STATE             = 'npn-state';
    const LICENSE_DATE              = 'npn-date';
    const RETURNING_TO_SYMMETRY     = 'returning-to-symmetry';
    const RETURNING_SFG_AGENT_CODE  = 'returning-sfg-agent-code';


    // exam and license status
    const STATE_EXAM_STATUS     = 'state-exam-status';
    const PRE_LICENSE_STATUS    = 'pre-license-status';

    // demographics fields
    const DEMO_GENDER           = 'demographic-gender';
    const DEMO_ETHNICITY        = 'demographic-ethnicity';
    const DEMO_EDUCATION        = 'demographic-education';
    const DEMO_RECRUITED        = 'demographic-recruited';
    const DEMO_INCOME           = 'demographic-income';
    const DEMO_SELF_EMPLOYED    = 'demographic-self-employed';
    const DEMO_EXPERIENCE       = 'demographic-experience';
    const DEMO_MARRIAGE         = 'demographic-married';
    const DEMO_EMPLOYMENT       = 'demographic-employment';

    // PRINCIPAL FIELDS
    const PRINCIPAL_STATE           = 'principal-state';
    const PRINCIPAL_SSN             = 'principal-ssn';
    const PRINCIPAL_EIN             = 'principal-ein';
    const PRINCIPAL_VECTOR_CHECK    = 'principal-vector-check';
    const PRINCIPAL_FIRST_NAME      = 'principal-first-name';
    const PRINCIPAL_LAST_NAME       = 'principal-last-name';
    const PRINCIPAL_EMAIL           = 'principal-email';
    const PRINCIPAL_DOB             = 'principal-dob';
    const PRINCIPAL_ENTITY_NAME     = 'principal-entity-name';
    // B2B AGENT FIELDS
    const B2B_AGENT_STATE           = 'b2b-agent-state';
    const B2B_AGENT_SSN             = 'b2b-agent-ssn';
    const B2B_AGENT_EIN             = 'b2b-agent-ein';
    const B2B_AGENT_VECTOR_CHECK    = 'b2b-agent-vector-check';
    const B2B_AGENT_FIRST_NAME      = 'b2b-agent-first-name';
    const B2B_AGENT_LAST_NAME       = 'b2b-agent-last-name';
    const B2B_AGENT_EMAIL           = 'b2b-agent-email';
    const B2B_AGENT_DOB             = 'b2b-agent-dob';
    const B2B_AGENT_ENTITY_NAME     = 'b2b-agent-entity-name';

    // other fields
    const Q2A_PREVIOUS_EXPERIENCE       = 'previous-experience';
    const Q2A_ACTIVE_SYMMETRY_CONTRACTS = 'active-symmetry-contracts';
    const Q2A_ACTIVE_OTHER_CONTRACTS    = 'active-other-contracts';
    const Q2A_PREVIOUSLY_CONTRACTED     = 'previously-contracted';
    const Q2A_ADVANCED_MARKETS          = 'advanced-markets';
    // echo relation
    const echo = [
        self::FIELD_STREET1 => self::FIELD_C_STREET1,
        self::FIELD_CITY => self::FIELD_C_CITY,
        self::FIELD_STATE => self::FIELD_C_STATE,
        self::FIELD_ZIP => self::FIELD_C_ZIP,
        self::FIELD_COUNTRY => self::FIELD_C_COUNTRY,
        self::FIELD_MOVE_IN_AT => self::FIELD_C_MOVE_IN_AT,
    ];


    public function formField()
    {
        return $this->belongsTo(FormField::class);
    }
}
