<?php

// Agent/App configurations are configs for the app and are mainly controlled server side.
// User configurations are user driven configs that they can reset and change at will.

namespace App\Models;

use App\Models\CloseRatio;
use App\Models\PopupAcknowledgement;
use App\Models\AgentHierarchy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AgentManagement\AgentLevel;
use DB;
use Carbon\Carbon;
use App\Models\Audit\BaseAudit;
use App\Models\User;

class UserLog extends BaseAudit
{

    protected $connection = 'sqlsrv';
    protected $table = "UserLog";
    protected $primaryKey = 'ID'; // or null
    public $timestamps = false;
    protected $fillable = ['UserID', 'AgentID', 'Action', 'URL', 'Params', 'ImpersonatedInd', 'Label'];

    public static $allowedFilters = ['Action', 'Label'];

    static $available_columns = ['ID', 'CreateDate', 'Agent', 'User', 'Action', 'Label', 'URL', 'ImpersonatedInd'];

    //===================================
    // Relationships
    //===================================

    public function agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }



    //===================================
    // Scopes
    //===================================

    public function scopeForAgent($query, Agent $agent)
    {
        return $query->where("AgentID", $agent->AgentID);
    }

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }

    public function scopeRecentLogins($query)
    {
        return $query->groupBy('AgentID', 'UserID', 'ImpersonatedInd')
                        ->orderBy('CreateDate', 'DESC')
                        ->where('Label', 'DataInit')
                        ->select(DB::raw('AgentID, COUNT(ID) as Logins, UserID, Max(CreateDate) as CreateDate, ImpersonatedInd'));
    }

    //===================================
    // Attributes
    //===================================
    //
    public function getUserAttribute()
    {
        return User::find($this->UserID);
    }
}
