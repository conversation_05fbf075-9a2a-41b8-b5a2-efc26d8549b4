<?php

/**
 * AgentHierarchy connects an agent model to their upline and downlines. It's a pivot table that has a column for the downline(agent's) Id and an entry for each of their uplines, all the way up to the 120.
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Agent;

class AgentHierarchyHistorical extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "AgentHierarchy_Historical";
    protected $primaryKey = 'ID'; // or null
    //public $incrementing = false;
    //protected $keyType = 'string';
    public $timestamps = false;
    protected $hidden = [
       'laravel_through_key', 'pivot'
    ];
    protected $fillable = [];

    public static $allowedFilters = [];

    public function UplineAgent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'UplineAgentID');
    }

    public function DownlineAgent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'DownlineAgentID');
    }

    public function scopeForAgencyOwner($query, $agent)
    {
        return $query->where('UplineAgentID', $agent->AgentID);
    }

    public function scopeForBaseshop($query)
    {
        return $query->where("InBaseShopInd", 1);
    }

    public function scopeNotBaseshop($query)
    {
        return $query->where("InBaseShopInd", 0);
    }

    public function scopeForDate($query, $date)
    {
        return $query->where('EffDate', "<=", $date)
            ->where('ExpDate', ">=", $date);
    }
}
