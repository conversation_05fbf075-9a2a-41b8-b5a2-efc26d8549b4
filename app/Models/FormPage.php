<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FormSection;
use App\Traits\UsesUuid;
use Log;

/**
 * App\Models\FormPage
 *
 * @property string $id
 * @property string $label
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $step_ident
 * @property string|null $subline
 * @property int|null $sort
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\FormField[] $formFields
 * @property-read int|null $form_fields_count
 * @property-read \Illuminate\Database\Eloquent\Collection|FormSection[] $formSections
 * @property-read int|null $form_sections_count
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereStepIdent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereSubline($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormPage whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property-read mixed $sections
 */
class FormPage extends Model
{
    use HasFactory, UsesUuid;

    // statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_DISABLED = 'disabled';

    protected $fillable = [
        'label',
        'status',
        'step_ident',
        'subline',
        'sort',
        'application_slug',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $appends = [
        'sections',
    ];

    // TO DO: change fronend to use defualt names
    public function getSectionsAttribute()
    {
        $data = null;
        // to prevent from loading in relationship when not wanted
        if(array_key_exists('formSections', $this->relations)) {
            $data = $this->formSections;
        }
        return $data;
    }

    /**
     * Get the sections for this form page.
     */
    public function formSections()
    {
        return $this->hasMany(FormSection::class)->where('status', 'active')->orderBy('sort', 'asc');
    }

    public function fetchFormatted()
    {
        if (!$this->formSections()->exists()) {
            Log::error("FORM ERROR: Form page {$this->label} does not have any sections.");
            return false;
        }
        $sections = [];
        foreach ($this->formSections()->get() as $section) {
            $sections[] = $section->fetchFormatted();
        }
        return [
            'label' => $this->label,
            'sections' => $sections,
        ];
    }

    public function formFields()
    {
        return $this->hasManyThrough(FormField::class, FormSection::class);
    }
}
