<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\UserSignature
 *
 * @property string $id
 * @property string|null $user_email
 * @property string|null $signature_id
 * @property string|null $account_id
 * @property string|null $app_id
 * @property string|null $event_type
 * @property string|null $event_hash
 * @property string|null $event_message
 * @property string|null $event_time
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $form_name
 * @property string|null $file_name
 * @property string|null $signature_request_id
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereAppId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereEventHash($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereEventMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereEventTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereEventType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereFormName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereSignatureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereSignatureRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserSignature whereUserEmail($value)
 * @mixin \Eloquent
 */
class UserSignature extends Model
{
    use HasFactory, UsesUuid;

    // types of signature lines created by us
    const TYPE_EPOCH = 'signature-sent';
    const TYPE_ALL_SIGNED = 'signature_request_all_signed';

    // role specified in template setup on HelloSign site
    const ROLE_RECRUIT = 'Recruit';

    // event types
    const EVENT_DOWNLOADABLE = 'signature_request_downloadable';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_signatures';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_email',
        'signature_id',
        'account_id',
        'app_id',
        'event_type',
        'event_hash',
        'event_message',
        'event_time',
    ];
}
