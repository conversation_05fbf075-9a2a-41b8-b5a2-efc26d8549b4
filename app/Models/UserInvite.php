<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;

/**
 * App\Models\UserInvite
 *
 * @property string $id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $email
 * @property string $name
 * @property string $code
 * @property bool $invite_sent
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property bool $invite_expired
 * @property-read \App\Models\User $user
 * @method static \Database\Factories\UserInviteFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite newQuery()
 * @method static \Illuminate\Database\Query\Builder|UserInvite onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereInviteExpired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereInviteSent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|UserInvite withTrashed()
 * @method static \Illuminate\Database\Query\Builder|UserInvite withoutTrashed()
 * @mixin \Eloquent
 * @property string $phone
 * @property bool $blocked
 * @property string|null $block_reason
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereBlockReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereBlocked($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite wherePhone($value)
 * @property string|null $contract_level
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereContractLevel($value)
 * @property string|null $upline_agent_id
 * @property-read \App\Models\Agent|null $uplineAgent
 * @method static \Illuminate\Database\Eloquent\Builder|UserInvite whereUplineAgentId($value)
 */
class UserInvite extends Model
{
    use HasFactory, SoftDeletes, UsesUuid;

    protected $fillable = [
        'user_id',
        'email',
        'name',
        'phone',
        'code',
        'blocked',
        'block_reason',
        'invite_sent',
        'invite_expired',
        'contract_level',
        'commissions',
        'upline_agent_id',
        'invite_accepted',
        'corporate',
        'person_message',
        'source',
        'license_status',
        'experience',
        'advanced_markets',
        'group_id',
        'state',
        'type',
        'alt_email',
        'carrier',
        'agreement_type',
        'bonus_addendum',
        'multiple_owners',
        'contract_type',
        'custom_contract_upload',
        'service_level'
    ];

    const TYPE_PRINCIPAL = 'b2b-principal';
    const AGREEMENT_STANDARD = 'Standard';
    const AGREEMENT_CUSTOM = 'Custom';

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function uplineAgent()
    {
        return $this->hasOne(Agent::class, 'AgentID', 'upline_agent_id');
    }

    public function group()
    {
        return $this->hasOne(UserInvitesGroup::class, 'id', 'group_id');
    }

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    //===================================
    //Scopes
    //===================================

    public static function scopeDownlineInvites($query, User $user)
    {
        return $query->where('AvailableInd', 1);
    }

    public function invitedUser()
    {
        return $this->hasOne(User::class, 'user_invite_id', 'id');
    }

    /**
     * Get the user status associated with the user invite.
     */
    public function userStatus()
    {
        return $this->hasOneThrough(
            UserStatus::class,
            User::class,
            'user_invite_id', // Foreign key on User table
            'id', // Foreign key on UserStatus table
            'id', // Local key on UserInvite table
            'user_status_id' // Local key on User table
        );
    }
}
