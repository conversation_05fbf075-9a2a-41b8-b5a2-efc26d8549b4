<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ContactInformationState
 *
 * @property-read \App\Models\Content $contents
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState query()
 * @mixin \Eloquent
 * @property int $id
 * @property string $content_id
 * @property string|null $name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $address
 * @property string|null $city
 * @property string|null $zip
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereContentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContactInformationState whereZip($value)
 */
class ContactInformationState extends Model
{
    use HasFactory;
    protected $table = 'contact_state_licensing';

    protected $fillable = [
        'content_id','name','email','phone', 'address','city', 'zip'
    ];

    public function contents()
    {
        return $this->belongsTo(Content::class);
    }


}
