<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentB2B extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv-b2b';
    protected $table = 'Agent';
    
    // Prevent writing to the database (update, save, delete)
    public function save(array $options = [])
    {
        // Do nothing
    }

    public function update(array $attributes = [], array $options = [])
    {
        // Do nothing
    }

    public function delete()
    {
        // Do nothing
    }

    //prevent mass assignment
    protected $guarded = ['*'];

    //===================================
    //Scopes
    //===================================

    public function scopeActive($query)
    {
        return $query->where('AvailableInd', 1);
    }
}
