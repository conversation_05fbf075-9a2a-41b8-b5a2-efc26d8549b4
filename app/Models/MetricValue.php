<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;

/**
 * App\Models\MetricValue
 *
 * @property string $id
 * @property \Illuminate\Support\Carbon $date
 * @property string $metric_id
 * @property string $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Metric $metric
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue newQuery()
 * @method static \Illuminate\Database\Query\Builder|MetricValue onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue query()
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereMetricId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MetricValue whereValue($value)
 * @method static \Illuminate\Database\Query\Builder|MetricValue withTrashed()
 * @method static \Illuminate\Database\Query\Builder|MetricValue withoutTrashed()
 * @mixin \Eloquent
 */
class MetricValue extends Model
{
    use HasFactory, SoftDeletes, UsesUuid;

    protected $fillable = [
        'date',
        'metric_id',
        'value',
    ];

    protected $casts = [
        'date' => 'datetime',
    ];

    public function metric()
    {
        return $this->belongsTo(Metric::class);
    }

    public function getValueAttribute($value)
    {   
        $json = json_decode($value, true);

        if($json) {
            return $json;
        } else {
            return $value;
        }
        
    }
}
