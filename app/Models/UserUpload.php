<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\UserUpload
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload query()
 * @mixin \Eloquent
 * @property string $id
 * @property string $user_id
 * @property string $form_field_id
 * @property string $user_entry_id
 * @property string $file_name
 * @property string $file_type
 * @property int $hq_uploaded
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $original_name
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereFileType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereFormFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereHqUploaded($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereOriginalName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereUserEntryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserUpload whereUserId($value)
 */
class UserUpload extends Model
{
    use HasFactory, UsesUuid;

    protected $fillable = [
        'user_id',
        'form_field_id',
        'user_entry_id',
        'file_name',
        'file_type',
        'original_name',
        'hq_uploaded',
    ];

    public function localPath() {
        $filePathConfig = config('uploads.localPath','uploads/files');
        return "{$filePathConfig}/{$upload->user_id}/{$upload->file_name}";
    }

    public function fullLocalPath() {
        $publicPath = public_path();
        $filePathConfig = config('uploads.localPath','uploads/files');
        return "{$publicPath}/{$filePathConfig}/{$upload->user_id}/{$upload->file_name}";
    }

    public function userEntry() {
        return $this->belongsTo(UserEntry::class);
    }
}
