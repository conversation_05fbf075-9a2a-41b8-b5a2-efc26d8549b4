<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserInvitesGroup extends Model
{
    public $incrementing = false;
    protected $keyType = 'string';
    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'user_id',
        'group_name',
        'group_code',
        'active',
        'group_imo_name',
        'group_size',
        'contact_info',
        'preferred_times',
        'notes',
        'group_invite_status'
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
