<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Log;
use App\Traits\UsesUuid;

/**
 * App\Models\FormSection
 *
 * @property string $id
 * @property string $label
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $form_page_id
 * @property int $sort
 * @property string|null $subline
 * @property string $fill_mode
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\FormFieldCondition[] $formConditions
 * @property-read int|null $form_conditions_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\FormField[] $formFields
 * @property-read int|null $form_fields_count
 * @property-read \App\Models\FormPage $formPage
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereFillMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereFormPageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereSubline($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormSection whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property-read mixed $conditions
 * @property-read mixed $fields
 */
class FormSection extends Model
{
    use HasFactory, UsesUuid;

    // statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_DISABLED = 'disabled';

    // fill mode
    const FILLMODE_MANUAL = 'manual';
    const FILLMODE_GOOGLE_AUTOCOMPLETE = 'google_autocomplete';

    protected $fillable = [
        'label',
        'status',
        'sort',
        'subline',
        'form_page_id',
        'active_start',
        'active_end',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $appends = [
        'fields',
        'conditions',
    ];

    // TO DO: change fronend to use defualt names
    public function getFieldsAttribute()
    {
        $data = null;
        // to prevent from loading in relationship when not wanted
        if(array_key_exists('formFields', $this->relations)) {
            $data = $this->formFields;
        }
        return $data;
    }

    // TO DO: change fronend to use defualt names
    public function getConditionsAttribute()
    {
        $data = null;
        // to prevent from loading in relationship when not wanted
        if(array_key_exists('formConditions', $this->relations)) {
            $data = $this->formConditions;
        }
        return $data;
    }

    /**
     * Get the fields for this form section.
     */
    public function formFields()
    {
        return $this->hasMany(FormField::class)->orderBy('sort', 'asc');
    }

    public function formPage()
    {
        return $this->belongsTo(FormPage::class);
    }

    /**
     * Get the conditions for this form section.
     */
    public function formConditions()
    {
        return $this->hasMany(FormFieldCondition::class);
    }

    public function fetchFormatted()
    {
        if (!$this->formFields()->exists()) {
            Log::error("FORM ERROR: Form section {$this->label} does not have any fields.");
            return false;
        }
        $fields = [];
        foreach ($this->formFields()->get() as $field) {
            $fields[] = $field->fetchFormatted();
        }
        $conditions = [];
        foreach ($this->formConditions()->get() as $condition) {
            $conditions[] = $condition->fetchFormatted();
        }
        return [
            'id' => $this->id,
            'label' => $this->label,
            'subline' => $this->subline,
            'conditions' => $conditions,
            'fields' => $fields,
            'fill_mode' => $this->fill_mode,
        ];
    }
}
