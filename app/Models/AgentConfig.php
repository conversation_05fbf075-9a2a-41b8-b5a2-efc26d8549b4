<?php

// Agent/App configurations are configs for the app and are mainly controlled server side.
// User configurations are user driven configs that they can reset and change at will.

namespace App\Models;

use App\Models\CloseRatio;
use App\Models\Agent;
use App\Models\PopupAcknowledgement;
use App\Models\AgentHierarchy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AgentManagement\AgentLevel;
use DB;
use Carbon\Carbon;

class AgentConfig extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "AgentConfig";
    protected $primaryKey = 'ID'; // or null
    public $timestamps = false;
    protected $fillable = ['ConfigName', 'ConfigValue', 'DataType'];

    public static $allowedFilters = [];

    //Applies the global scope to the model to insure we only return Agents with a ArrangementToDate of null
    protected static function boot()
    {
        parent::boot();
        //static::addGlobalScope('ArrangementToDate', function (Builder $builder) {
        //    $builder->whereNull('ArrangementToDate');
        //});
    }

    //===================================
    // Relationships
    //===================================
    
    public function agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }


    //===================================
    // Scopes
    //===================================

    public function scopeForUser($query, $UserID)
    {
        return $this->where("UserID", $UserID);
    }

    public function scopeForAgent($query, Agent $agent)
    {
        return $this->where("AgentID", $agent->AgentID);
    }

    public function scopeForKey($query, $key)
    {
        return $this->where("ConfigName", $key)->orderBy('ID', 'desc');
    }

    public function scopeGetKey($query, $key)
    {
        $item = $query->where("ConfigName", $key)->orderBy('ID', 'desc')->first();
        if (!isset($item->ConfigValue)) {
            return null;
        }
        return  $item->value;
    }

    public function scopeExcludeKeys($query, $keys)
    {
        foreach ($keys as $key) {
            $query->where("ConfigName", 'not like', $key .'%');
        }
        return $query;
    }

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }


    //===================================
    // Attributes
    //===================================
    
    public function getValueAttribute()
    {
        if ($this->ConfigValue == null) {
            return null;
        }
        switch ($this->DataType) {
            case "json":
                return json_decode($this->ConfigValue);
                break;
            case "int":
                return intval($this->ConfigValue);
                break;
            case "decimal":
                return floatval($this->ConfigValue);
                break;
            case "boolean":
                return boolval($this->ConfigValue);
                break;
            case "string":
                return strval($this->ConfigValue);
                break;
            default:
                return $this->ConfigValue;
                break;
        }
    }



    public static function getScorecardGoal($scorecard, Agent $agent)
    {
        $key = "MyStat_ScorecardGoals";
        $item = self::forAgent($agent)->where("ConfigName", $key)->orderBy('ID', 'desc')->first();
        if ($item == null || !isset($item->ConfigValue)) {
            return null;
        }
        return  isset($item->value->$scorecard) && $item->value->$scorecard !== null ? $item->value->$scorecard : null;
    }

    public static function setScorecardGoal($agent, $scorecard, $value)
    {
        $key = "MyStat_ScorecardGoals";
        $item = self::forAgent($agent)->where("ConfigName", $key)->orderBy('ID', 'desc')->first();
        if ($item == null) {
            $item = new AgentConfig();
            $item->AgentID = $agent->AgentID;
            $item->DataType = 'json';
            $item->ConfigName = $key;
            $item->ConfigValue = json_encode([
                $scorecard => $value
            ]);
        } else {
            $v = $item->value;
            $v->$scorecard = $value;
            $item->ConfigValue = json_encode($v);
        }
        $auth0 = resolve('App\Services\Auth0TokenService');
        $item->LastChangeBy = $auth0->user->email;
        $item->save();
        return $item;
    }
}
