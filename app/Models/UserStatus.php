<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;

/**
 * App\Models\UserStatus
 *
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property string|null $form_page_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\FormPage|null $formPage
 * @method static \Database\Factories\UserStatusFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus newQuery()
 * @method static \Illuminate\Database\Query\Builder|UserStatus onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereFormPageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatus whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|UserStatus withTrashed()
 * @method static \Illuminate\Database\Query\Builder|UserStatus withoutTrashed()
 * @mixin \Eloquent
 */
class UserStatus extends Model
{
    use HasFactory, UsesUuid, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'form_page_id',
    ];

    public function formPage()
    {
        return $this->belongsTo(FormPage::class);
    }
}
