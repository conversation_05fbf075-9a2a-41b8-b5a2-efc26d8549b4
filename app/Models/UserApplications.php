<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Agent;

class UserApplications extends Model
{
    use HasFactory;

    // protected $connection = 'sqlsrv';
    protected $table = "v_Applications";
    protected $primaryKey = 'user_id'; // or null
    public $incrementing = false;
    protected $keyType = 'string';

    public function agencyOwner()
    {
        return $this->hasOne('App\Models\Agent', 'AgentCode', 'agency_owner_code');
    }

    public function uplineAgent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'upline_agent_id');
    }

    
}
