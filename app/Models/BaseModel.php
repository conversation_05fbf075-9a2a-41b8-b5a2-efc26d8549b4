<?php

/**
 * This base model class is what all models will inherit from so that they pick up the cachingtraits to redis.
 * Redis server is located ont he jobs.quility.com box.
 * We cache for different times based on the environment.
 * Default is 1 hour
 * Local dev is null
 * Production is 3 hours.
 */

namespace App\Models;

use Rennok<PERSON>\QueryCache\Traits\QueryCacheable;
use Illuminate\Database\Eloquent\Model;
use App;

class BaseModel extends Model
{
    use QueryCacheable;
    public $cacheFor = null;
    public $cacheDriver = null;
    protected static $flushCacheOnUpdate = true;

    public function __construct()
    {
        parent::__construct();
        //we only set the default cache if it's null
        //if you want to set the time for a specific model, then set it in the model definition. otherwise it will set it based on the environment.
        $this->cacheDriver = config('cache.default');
        if ($this->cacheFor == null) {
            switch (App::environment()) {
                case "local":
                    $this->cacheFor = 0;
                    break;
                case "dev":
                    $this->cacheFor = 60 * 60;
                    break;
                case "production":
                    $this->cacheFor = 60 * 60 * 3;
                    break;
            }
        }
    }

    protected static function boot()
    {
        parent::boot();
    }

    public static function getAllowedFilters()
    {
        return static::$allowedFilters;
    }
}
