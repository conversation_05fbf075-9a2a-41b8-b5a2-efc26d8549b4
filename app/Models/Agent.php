<?php
/**
 * This is the model definition for the QuilityAgent view. It is meant as a read only model and has pre joined columns for easy access to what you need.
 * If you need to update an agent's attribute, use the Base/Agent models.
 */

namespace App\Models;

use App\Models\CloseRatio;
use App\Models\PopupAcknowledgement;
use App\Models\AgentHierarchy;
use App\Models\PolicyPending;
use App\Models\Base\StateLicense;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AgentManagement\AgentLevel;
use DB;
use Carbon\Carbon;
use App\Models\Application;
// use App\Models\UserLog;
use App\Models\Commission;

class Agent extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "vw_QuilityAgent";
    protected $primaryKey = 'AgentCode'; // or null
    public $incrementing = false;
    protected $keyType = 'string';

    //protected $with = ['baseAgent'];
    //protected $appends = ['LeadershipLevel'];
    protected $hidden = array('laravel_through_key', 'pivot');
    protected $fillable = ['AgentCode', 'AgentName', 'AgentEmail', 'OptID', 'Role', 'Division'];

    protected $AgencyOwnerLevels = [
        'Agency Owner',
        'Agency Director',
        'Regional Agency Director',
        'Managing Vice President',
        'Senior Vice President',
        'Executive Vice President',
        'Associate Partner',
        'Senior Partner',
        'Managing Partner',
        'Founders',
    ];

    public static $allowedFilters = ['Status', 'ContractLevel', 'State', 'LeadershipLevel', "Division", "Licensed", "QMSParticipant","AvailableInd"];

    //Applies the global scope to the model to insure we only return Agents with a ArrangementToDate of null
    protected static function boot()
    {
        parent::boot();
        //static::addGlobalScope('ArrangementToDate', function (Builder $builder) {
        //    $builder->whereNull('ArrangementToDate');
        //});
    }

    //===================================
    // Relationships
    //===================================

    public function needsAnalysisLead()
    {
        return $this->hasMany('App\Models\NeedsAnalysis\Lead', 'AgentID', 'AgentCode');
    }

    public function AgentDocuments()
    {
        return $this->hasMany('App\Models\AgentManagement\AgentDocument', 'AgentID', 'AgentID');
    }

    public function AgentNotes()
    {
        return $this->hasMany('App\Models\AgentManagement\AgentNote', 'AgentID', 'AgentID');
    }

    public function baseShop($status = ["Active","Available"])
    {
        $query = $this->hasManyThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID')
            ->where('DownlineLevel', '1');
        if ($status == null) {
            return $query;
        }
        return $query
                ->whereIn('Status', $status)
                ->where('isAgencyOwner', '!=', 1);
    }

    public function agencyOwner()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgencyOwnerAgentID');
    }

    public function baseshopOwner()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'BaseShopOwnerAgentID');
    }

    public function directUpline()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'UplineAgentID');
    }

    public function sfgDirectOwner()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'SFGDirectAgentID');
    }

    public function AgentLevels()
    {
        return $this->hasMany('App\Models\AgentManagement\AgentLevel', 'AgentID', 'AgentID');
    }

    public function SubmittedApps()
    {
        return $this->hasMany('App\Models\Application', 'AgentID', 'AgentID');
    }

    public function AgentConfig()
    {
        return $this->hasMany('App\Models\AgentConfig', 'AgentID', 'AgentID');
    }

    public function AgentHierarchyUpline()
    {
        return $this->hasMany('App\Models\AgentHierarchy', 'DownlineAgentID', 'AgentID');
    }

    public function AgentHierarchyDownline()
    {
        return $this->hasMany('App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID');
    }

    public function baseAgent()
    {
        return $this->hasOne('App\Models\Base\BaseAgent', 'ID', 'AgentID');
    }

    public function writingNumbers()
    {
        return $this->hasMany('App\Models\Base\CarrierWritingNumber', 'NPN', 'NPN');
    }

    public function agencyWritingNumbers()
    {
        return $this->hasMany('App\Models\Base\CarrierWritingNumber', 'NPN', 'DoingBusinessAsNPN');
    }

    public function applications()
    {
        return $this->hasMany('App\Models\Application', 'AgentID', 'AgentID');
    }

    public function baseShopApplications()
    {
        return $this->hasManyThrough('App\Models\Application', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID')
            ->where('InBaseShopInd', true);
    }

    public function totalAgencyApplications()
    {
        return $this->hasManyThrough('App\Models\Application', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID');
    }

    public function state_licenses()
    {
        return $this->hasMany('App\Models\Base\StateLicense', 'AgentID', 'AgentID');
    }

    public function leads()
    {
                return $this->belongsToMany('App\Models\Leads\Lead', 'App\Models\Leads\LeadAssignment', 'AgentID', 'LeadID', 'AgentID', 'ID')
                    ->withPivot('AgentID', 'AssignDate', 'AssignedLeadLevel', 'MetaData', 'Comments', 'LeadStatus', 'LeadCode', 'LeadCodeTrimmed');
    }


    /**
     * This is pulls the top most (130) AgentHierarchy for this agent... Their direct.
     * @return [type] [description]
     */
    public function agentDirectHierarchy()
    {
        return $this->hasOne('App\Models\AgentHierarchy', 'DownlineAgentID', 'AgentID')->orderBy('DownlineLevel', 'desc')->first();
    }

    /* type can be AgencyDirector, AgencyOwner, PersonalProducer etc... to match the leaderboard table names. */
    public function baseShopData($type)
    {
        $downlineTable = (new AgentHierarchy())->getTable();
        return DB::connection('sqlsrv')->table($this->getTable())
            ->join(
                $downlineTable,
                'DownlineAgentID',
                '=',
                $this->getTable() . ".AgentID"
            )
            ->join(
                "vw_QuilityStats_$type",
                "vw_QuilityStats_$type.AgentID",
                '=',
                $this->getTable() . ".AgentID"
            )
            ->where('AgencyOwnerAgentID', $this->AgentID);
    }

    public function masterShop()
    {
        return $this->hasManyThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID');
    }

    public function directDownline()
    {
        return $this->hasManyThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID')
            //->where('DownlineLevel', '1')
            ->where(function ($query) {
                return $query->where('AvailableInd', 1);
            });
    }

    public function fullDirectDownline()
    {
        return $this->hasManyThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'DownlineAgentID')
            ->where('DownlineLevel', '1');
    }


    //===================================
    // Attributes
    //===================================

    public function getAgentDisplayNameAttribute()
    {
        $name = $this->AgentName;
        $suffix = ($this->Suffix == null || $this->Suffix == '') ? '' : ' ' . $this->Suffix;
        //use nickname
        if (isset($this->NickName) && $this->NickName != "" && $this->NickName != $name) {
            if (isset($this->MiddleName) && $this->NickName != '' && $this->NickName != $this->MiddleName && !$this->hideMiddlename) {
                return $this->NickName . " " . $this->MiddleName . ' ' . $this->LastName . $suffix;
            }
            return $this->NickName . " " . $this->LastName . $suffix;
        }
        if (isset($this->MiddleName) && $this->MiddleName != '') {
            if (!$this->hideMiddlename) {
                return $this->FirstName . " " . $this->MiddleName . ' ' . $this->LastName . $suffix;
            }
            return $this->FirstName . " " . $this->LastName . $suffix;
        }
        return $this->FirstName . " " . $this->LastName . $suffix;
    }

    public function getUplineAttribute()
    {
        $r = [];
        $upline = $this->AgentHierarchyUpline()->with('UplineAgent')->orderBy('DownlineLevel', 'ASC')->get();
        if ($upline == null) {
            return $r;
        }
        $r[] = $this;
        foreach ($upline as $up) {
            $up->UplineAgent->append('Avatar');
            $r[] = $up->UplineAgent;
            //stop if we reach a 130.
            if ($up->UplineAgent->ContractLevel == "130") {
                break;
            }
        }
        return array_reverse($r);
    }

    /**
     * @return removed during refactor october 2020

    public function getIsSFGDirectAttribute()
    {
        return $this->SFGDirectAgentCode == $this->AgentCode;
    }
     */

    public function getDirectAgencyOwnerAttribute()
    {
        return Agent::find($this->SFGDirectAgentCode);
    }


    /** These are in the column already... in case we need to add these in later, I am keeping the logic here commented out. */
    /*
    public function getLeadershipLevelAttribute()
    {
        $current_agent_level = $this->AgentLevels()->Leadership()->current()->first();
        if ($current_agent_level) {
            return $current_agent_level->level->LevelName;
        }
        return null;
    }

    public function getLeadershipLevelOverrideAttribute()
    {
        $current_agent_level = $this->AgentLevels()->Leadership()->current()->first();
        if ($current_agent_level) {
            if ($current_agent_level->overrideLevel) {
                return $current_agent_level->overrideLevel->LevelName;
            }
        }
        return null;
    }
    */

    public function getLastNameFirstFullNameAttribute()
    {
        $n = explode(" ", str_replace("  ", " ", trim($this->AgentName)));
        $name = $n[count($n) - 1];
        $fn = array_slice($n, 0, -1);
        $suffix = "";
        if (strlen($name) <= 3) {
            $name = $n[count($n) - 2] . " " . $n[count($n) - 1];
            $fn = array_slice($n, 0, -2);
        }
        return $name . ", " . implode(" ", $fn);
    }


    public function getBaseshopAgencyOwnerAttribute()
    {
        $base = Agent::find($this->BaseShopOwnerAgentCode);
        if ($base == null) {
            return $this;
        }
        return $base;
    }

    public function getProfileAttribute()
    {
        return AgentProfile::where('AgentCode', $this->AgentCode)->first();
    }

    /**
     * This does not seem right... shouldn't we be using AgentCode? ... also... wondering if this shouldn't be a relationship instead joined on AgentCode.
     * @return [type] [description]
     */
    public function getUserAttribute()
    {
        return User::where('email', $this->AgentEmail)->first();
    }

    public function getAvatarAttribute()
    {
        if ($this->user && $this->user->avatar != null) {
            return $this->user->avatar;
        }
        if ($this->profile && $this->profile->ProfilePhoto != null) {
            return $this->profile->ProfilePhoto;
        }
        return '/assets/images/user-solid.png';
    }

    public function getSignatureAttribute()
    {
        return $this->agentConfig()->getKey('AgentSignatureImage');
    }

    public function getHideMiddlenameAttribute()
    {
        return $this->agentConfig()->getKey('HideMiddlename');
    }

    public function setHideMiddlenameAttribute(bool $value)
    {
        $this->setAgentConfig("HideMiddlename", $value, 'boolean');
    }

    /**
     * Used for Quility U to see if they should be forced to do the FastTrack course first.
     * @return [type] [description]
     */
    public function getFastTrackAttribute()
    {
        $FastTrack = false;
        if (isset($this->ContractStartDt)) {
            if (strtotime($this->ContractStartDt) > strtotime('-30 days')) {
                $FastTrack = true;
            }
        }
        return $FastTrack;
    }

    //===================================
    //Scopes
    //===================================



    public function scopeActive($query)
    {
        return $query->where('AvailableInd', 1);
    }

    public function scopeNotTerminated($query)
    {
        return $query->where('Status', '<>', 'Terminated');
    }

    public function scopeActiveSince($query, $d)
    {
        return $query->where('LastActivityDate', '>=', $d);
    }

    public function scopeForDivision($query, $division)
    {
        if ($division == "All") {
            return $query;
        }
        return $query->where('Division', '=', $division);
    }

    public function scopeSimpleSearch($query, $keyword)
    {
        $keyword = str_replace(" ", "%", $keyword);
        return $query->where(function ($query) use ($keyword) {
            $query->whereRaw("UPPER(AgentDirectorySearch) LIKE ? ", ['%' . strtoupper($keyword) . '%'])
                ->orWhereRaw("UPPER(AgentCode) = ? ", [strtoupper($keyword)])
                ->orWhereRaw("UPPER(OptID) = ? ", [strtoupper($keyword)]);
        });
    }

    public function scopeSuperSimpleSearch($query, $keyword)
    {
        $keyword = str_replace(" ", "%", $keyword);
        return $query->where(function ($query) use ($keyword) {
            $query->whereRaw("UPPER(AgentDirectorySearch) LIKE ? ", ['%' . strtoupper($keyword) . '%'])
                ->orWhereRaw("UPPER(AgentCode) = ? ", [strtoupper($keyword)])
                ->orWhereRaw("UPPER(OptID) = ? ", [strtoupper($keyword)]);
        });
    }

    /**
     * Get an agent lookup by email address.
     * @param  [type] $query [description]
     * @param  [type] $email [description]
     * @return [type]        [description]
     */
    public function scopeAgentByEmail($query, $email)
    {
        return $query->where('AgentEmail', $email);
    }

    public function scopeNewAgent($query, $days = 90, $startDate = null)
    {
        if (isset($startDate)) {
            return $query->where('ContractStartDt', '>', $startDate)
                    ->where('ContractStartDt', '<', (new Carbon($startDate))->addDays($days));
        }
        return $query->where('ContractStartDt', '>', Carbon::now()->subDays($days));
    }

    public function scopeForBaseShopOwner($query, $agent)
    {
        if ($agent == null) {
            return $query->where('BaseShopOwnerAgentID', '=', 'XXXXXXXXXX');
        }
        return $query->where('BaseShopOwnerAgentID', '=', $agent->AgentID);
    }

    /**
     * Returns a joined table of agents and apps so it's only those agents with business and includes apps.
     * Only use for getting a count. If you need more you have to set up something with the $query->with in the comments below.
     * @param  [type] $query [description]
     * @return [type]        [description]
     */
    public function scopeWithBusiness($query)
    {
        $appsTable = (new Application())->getTable();
        return $query
            ->join(
                $appsTable,
                $appsTable . '.AgentID',
                '=',
                $this->getTable() . '.AgentID'
            );
        //takes way to long to pulll... laravel using 'exists'
        //$query->with(["SubmittedData"])->whereHas("SubmittedData");
    }

    public function scopeDistinctAgentCount($query)
    {
        return $query->select(DB::raw("
                count(DISTINCT " . $this->getTable() . ".AgentID) as aggregate
            "));
        //takes way to long to pulll... laravel using 'exists'
        //$query->with(["SubmittedData"])->whereHas("SubmittedData");
    }


    public function scopeFindByAgentId($query, $id)
    {
        return $query->where("AgentID", $id);
    }

    public function scopeGroupByContractStartDateMonth($query)
    {
        return $query->select(DB::raw("
                count(DISTINCT " . $this->getTable() . ".AgentID) as NewAgents,
                DATEPART(YY, ContractStartDt) as PeriodYear,
                DATEPART(MM, ContractStartDt) as PeriodMonth
            "))
            ->groupBy(DB::raw('DATEPART(yy, ContractStartDt), DATEPART(mm, ContractStartDt)'))
            ->orderBy('PeriodYear', 'DESC')
            ->orderBy('PeriodMonth', 'DESC');
    }

    public function scopeForNpn($query, $npn)
    {
        return $query->where(function ($query) use ($npn) {
            $query->where("NPN", $npn)
                ->orWhere("DoingBusinessAsNPN", $npn);
        });
    }

    public function scopeForLeadershipLevel($query, $leadership_level)
    {
        return $query->where('LeadershipLevel', $leadership_level);
    }

    // Active carriers not curently supplying
    // pending policy information
    // that the agent has writing numbers
    // for.
    public function scopeForNonPolicyCarriers($query)
    {
        // get Carrier ID's
        // of the carriers that
        // are sending pending
        // policy data
        $carrierIDs = PolicyPending::distinct('CarrierID')->pluck('CarrierID');

        // filter out
        // the carrier's
        // that are not sending
        // pending policy data
        return $this->writingNumbers->whereNotIn('CarrierID', $carrierIDs);
    }

    public function scopeIsAgencyOwner($query)
    {
        return $query->where('IsAgencyOwner', 1);
    }

    public function scopeNonAgencyOwner($query)
    {
        return $query->where('IsAgencyOwner', 0);
    }

    public function scopeForOptID($query, $optID)
    {
        return $query->where('OptID', $optID);
    }

    public function scopeForActiveAgentWritingNumbers($query)
    {
        return $this->writingNumbers->where('ActiveInd', 1);
    }

    public function scopeForActiveAgencyWritingNumbers($query)
    {
        return $this->agencyWritingNumbers->where('ActiveInd', 1);
    }

    public function scopeForPendingPolicyCount($query)
    {
        $writingNumbers = $this->writingNumbers->pluck('WritingNumber')->all();
        $agencyWritingNumbers = $this->agencyWritingNumbers->pluck('WritingNumber')->all();
        $writingNumbers = array_merge($writingNumbers, $agencyWritingNumbers);

        return PolicyPending::whereIn('WritingNumber', $writingNumbers)->count();
    }

    public function scopeForAgentCodeOrOptID($query, array $agent_codes)
    {
        return $query->where(function ($query) use ($agent_codes) {
            $query->whereIn("AgentCode", $agent_codes)
                ->orWhereIn("OptID", $agent_codes);
        });
    }

    public function scopeQmsParticipant($query)
    {
        return $query->where("QMSParticipantInd", 1);
    }

    public function recentLogin()
    {
        return collect(DB::connection('sqlsrv')->select(
            DB::raw(
                "select MAX(CreateDate) as CreateDate from UserLog
                where AgentID = ? AND ImpersonatedInd = 0 and Label = 'DataInit'"
            ),
            [$this->AgentID]
        ));
        /*
        return UserLog::where('AgentID', $this->AgentID)
                        ->where('ImpersonatedInd', 0)
                        ->where('Label', 'DataInit')
                        ->orderBy('CreateDate', 'DESC')
                        ->select('UserLog.CreateDate');
        */
    }

    public function scopeKickoutAgents($query, $contractDate)
    {
        return $query->where('IsFullyLicensed', '=', 0)
                    ->where('ContractStartDt', '<', $contractDate);
    }

    public function scopeForContractStartDatePeriod($query, $startDate, $endDate)
    {
        return $query->where('ContractStartDt', '>=', $startDate)
            ->where('ContractStartDt', '<=', $endDate);
    }

    public function scopeContractStartDateBefore($query, $endDate)
    {
        return $query->where('ContractStartDt', '<=', $endDate);
    }

    public function scopeForDirectDownlineHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('DownlineLevel', 1);
        });
    }

    public function scopeForBaseshopOwnerHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('InBaseShopInd', 1);
        });
    }

    public function scopeForTotalAgencyOwnerHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID);
        });
    }


    public function scopeHasDownlineHistorical($query, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereExists(function ($query) use ($endDate) {
            $query->select(DB::raw(1))
                ->from('AgentHierarchy_Historical')
                ->whereColumn('AgentHierarchy_Historical.UplineAgentID', 'vw_QuilityAgent.AgentID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where("DownlineLevel", 1);
        });
    }


    public function scopeHasRecruitedAgentHistorical($query, $startDate, $endDate)
    {
        return $query->whereExists(function ($query) use ($startDate, $endDate) {
            $query->select(DB::raw(1))
                ->from('AgentHierarchy_Historical')
                ->whereColumn('AgentHierarchy_Historical.UplineAgentID', 'vw_QuilityAgent.AgentID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->whereIn('AgentHierarchy_Historical.DownlineAgentID', function ($query) use ($startDate, $endDate) {
                    $query->select(DB::raw('DISTINCT ID'))
                        ->from('Agent')
                        ->where('ContractStartDt', '>=', $startDate)
                        ->where('ContractStartDt', '<=', $endDate);
                });
        });
    }


    //===================================
    // Helper Functions
    //===================================

    /**
     * @return removed during refactor October 2020 - now a column in the view.

    public function isAgencyOwner()
    {
        return AgentLevel::isAgencyOwner($this->LeadershipLevel);
    }
     */

    public function popups()
    {
        return PopupAcknowledgement::where('AgentCode', $this->AgentCode)->where('acknowledged', 0);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'AgentCode';
    }

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }

    public static function inDownline($AgentCode, $DownlineAgentCode)
    {
        $agent = self::find($AgentCode);
        if ($agent) {
            return $agent->masterShop()->where('AgentCode', $DownlineAgentCode)->count() > 0;
        }
        return false;
    }

    public static function inBaseShop($AgentCode, $BaseShopAgentCode)
    {
        $agent = self::find($AgentCode);
        if ($agent) {
            return $agent->baseShop()->where('AgentCode', $BaseShopAgentCode)->count() > 0;
        }
        return false;
    }

    public function setAgentConfig($key, $value, $datatype = 'string')
    {
        $cfg = $this->agentConfig()->forKey($key)->first();
        if ($cfg == null) {
            $cfg = new AgentConfig();
        }
        $cfg->fill([
            'ConfigName' => $key,
            'ConfigValue' => $value,
            "DataType" => $datatype
        ]);
        if ($datatype == "json") {
            $cfg->ConfigValue = json_encode($value);
        } else {
            $cfg->ConfigValue = $value;
        }
        if (app()->runningUnitTests()) {
            $cfg->LastChangeBy = "Laravel Unit Test";
        } else {
            $auth0 = resolve('App\Services\Auth0TokenService');
            $cfg->LastChangeBy = (isset($auth0->user->email)) ? $auth0->user->email : (isset($auth0->token->sub) ? $auth0->token->sub : "Laravel Console/Job");
        }
        
        $cfg->AgentID = $this->AgentID;
        $cfg->save();
    }

    public function historicalDownlineAgencyOwners($date)
    {
        $ids = DB::connection('sqlsrv')->select(
            DB::raw(";with cte as (
                -- all AOs on date
                select distinct AgentID from AgentLevel al join Level l on l.ID = al.LevelID where l.IsAgencyOwner = 1 and al.StartDate <= ?
            )
            --hist hierarchy at a date with a given upline
            select AgentID from cte
                join dbo.AgentHierarchy_Historical ah on ah.DownlineAgentID = cte.AgentID
                where EffDate <= ? AND ExpDate >= ? and ah.UplineAgentID = ?"),
            [$date, $date, $date, $this->AgentID]
        );
        return Agent::whereIn("AgentID", collect($ids)->pluck('AgentID'));
    }

    public function historicalBaseshopWithBusiness($date)
    {
        $ids = DB::connection('sqlsrv')->select(
            DB::raw(
                "select DISTINCT DownlineAgentID from dbo.AgentHierarchy_Historical
                INNER JOIN HistoricalSalesReports_SalesReport ON DownlineAgentID = AgentID
                where EffDate <= ? AND ExpDate >= ? and UplineAgentID = ? and InBaseShopInd = 1"
            ),
            [$date, $date, $this->AgentID]
        );
        $agent_ids = collect($ids)->pluck('DownlineAgentID');
        $agent_ids->push($this->AgentID);
        return Agent::whereIn("AgentID", $agent_ids);
    }


    public function historicalTotalAgencyWithBusiness($date)
    {
        $ids = DB::connection('sqlsrv')->select(
            DB::raw(
                "select DISTINCT DownlineAgentID from dbo.AgentHierarchy_Historical
                INNER JOIN HistoricalSalesReports_SalesReport ON DownlineAgentID = AgentID
                where EffDate <= ? AND ExpDate >= ? and UplineAgentID = ?"
            ),
            [$date, $date, $this->AgentID]
        );
        $agent_ids = collect($ids)->pluck('DownlineAgentID');
        $agent_ids->push($this->AgentID);
        return Agent::whereIn("AgentID", $agent_ids);
    }

    public function historicalBaseshopWithPlacedBusiness($startDate, $endDate)
    {
        $ids = DB::connection('sqlsrv')->select(
            DB::raw(
                "select DISTINCT DownlineAgentID from dbo.AgentHierarchy_Historical
                INNER JOIN vw_QuilityStats_Placed ON DownlineAgentID = AgentID
                where EffDate <= ? AND ExpDate >= ? and
                (DATEFROMPARTS([PeriodYear], [PeriodMonth], 15) >= ? AND
                DATEFROMPARTS([PeriodYear], [PeriodMonth], 15) < ?)
                and
                UplineAgentID = ? and InBaseShopInd = 1"
            ),
            [$endDate, $endDate, $startDate, $endDate, $this->AgentID]
        );
        $agent_ids = collect($ids)->pluck('DownlineAgentID');
        $agent_ids->push($this->AgentID);
        return Agent::whereIn("AgentID", $agent_ids);
    }

    public function historicalBaseshopWithCloseratio($startDate, $endDate) //pass start date
    {
        $ids = DB::connection('sqlsrv')->select(
            DB::raw(
                "select DISTINCT DownlineAgentID from dbo.AgentHierarchy_Historical
                INNER JOIN vw_QuilityStats_CloseRatio ON DownlineAgentID = AgentID
                where EffDate <= ? AND ExpDate >= ?
                    AND PeriodStartDate <= ? AND PeriodEndDate >= ?
                    AND UplineAgentID = ? AND InBaseShopInd = 1"
            ),
            [$endDate, $endDate, $startDate, $startDate, $this->AgentID]
        );
        $agent_ids = collect($ids)->pluck('DownlineAgentID');
        $agent_ids->push($this->AgentID);
        return Agent::whereIn("AgentID", $agent_ids);
    }

    public function hasCommissionData()
    {
        return Commission::forAgent($this)->get()->count() > 0;
    }
}
