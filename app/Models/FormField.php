<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FormOption;
use App\Models\FormSection;
use App\Traits\UsesUuid;

/**
 * App\Models\FormField
 *
 * @property string $id
 * @property string $label
 * @property int $is_required
 * @property int $is_secure
 * @property int $max_length
 * @property string $type
 * @property string $placeholder
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $form_section_id
 * @property string $width
 * @property int $sort
 * @property string|null $alias_autofill
 * @property-read \Illuminate\Database\Eloquent\Collection|FormOption[] $formOptions
 * @property-read int|null $form_options_count
 * @property-read FormSection $formSection
 * @method static \Illuminate\Database\Eloquent\Builder|FormField newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormField newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormField query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereAliasAutofill($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereFormSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereIsRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereIsSecure($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereMaxLength($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField wherePlaceholder($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormField whereWidth($value)
 * @mixin \Eloquent
 * @property bool $pii
 * @method static \Illuminate\Database\Eloquent\Builder|FormField wherePii($value)
 */
class FormField extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'label',
        'form_section_id',
        'is_required',
        'is_secure',
        'max_length',
        'type',
        // 'hash',
        'placeholder',
        'alias_autofill',
        'width',
        'sort',
        'pii',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // field types
    const TYPE_TEXT = 'text';
    const TYPE_SELECT = 'select';
    const TYPE_EMAIL = 'email';
    const TYPE_PASSWORD = 'password';
    const TYPE_DATE = 'date';
    const TYPE_PHONE = 'phone';
    const TYPE_SSN = 'ssn';
    const TYPE_ROUTING_NUMBER = 'routing_number';
    const TYPE_HTML = 'html';
    const TYPE_UPLOAD = 'upload';
    const TYPE_TEXTAREA = 'textarea';
    const TYPE_CHECKBOX = 'checkbox';
    const TYPE_SIGNATURE = 'signature';
    const TYPE_HIDDEN = 'hidden';

    // field widths
    const WIDTH_FULL = 'full-width';
    const WIDTH_HALF = 'half-width';
    const WIDTH_THIRD = 'third-width';

    // simple select yes/no
    const SELECT_BOOL_YES = 'YES';
    const SELECT_BOOL_NO = 'NO';
    const SELECT_BOOL = [
        self::SELECT_BOOL_YES => self::SELECT_BOOL_YES,
        self::SELECT_BOOL_NO => self::SELECT_BOOL_NO,
    ];
    const CHECKBOX_CHECKED = 'checked';

    // simple select yes/no/maybe
    const SELECT_BOOL_MAYBE = 'MAYBE';
    const SELECT_BOOL_YES_NO_MAYBE = [
        self::SELECT_BOOL_YES => self::SELECT_BOOL_YES,
        self::SELECT_BOOL_NO => self::SELECT_BOOL_NO,
        self::SELECT_BOOL_MAYBE => self::SELECT_BOOL_MAYBE,
    ];

    // simple select business/individual
    const SELECT_BUSINESS_TYPE_BUSINESS = 'BUSINESS';
    const SELECT_BUSINESS_TYPE_INDIVIDUAL = 'INDIVIDUAL';
    const SELECT_BUSINESS_TYPE = [
        self::SELECT_BUSINESS_TYPE_BUSINESS => self::SELECT_BUSINESS_TYPE_BUSINESS,
        self::SELECT_BUSINESS_TYPE_INDIVIDUAL => self::SELECT_BUSINESS_TYPE_INDIVIDUAL,
    ];

    // account type selects
    const SELECT_ACCOUNT_TYPE_CHECKING = 'CHECKING';
    const SELECT_ACCOUNT_TYPE_SAVINGS = 'SAVINGS';
    const SELECT_ACCOUNT_TYPE = [
        self::SELECT_ACCOUNT_TYPE_CHECKING => self::SELECT_ACCOUNT_TYPE_CHECKING,
        self::SELECT_ACCOUNT_TYPE_SAVINGS => self::SELECT_ACCOUNT_TYPE_SAVINGS,
    ];

    // name suffix selects - '', 'Sr', 'Jr', 'II', 'III', 'IV'
    const SELECT_NAME_SUFFIX_NONE = '';
    const SELECT_NAME_SUFFIX_SR = 'Sr';
    const SELECT_NAME_SUFFIX_JR = 'Jr';
    const SELECT_NAME_SUFFIX_II = 'II';
    const SELECT_NAME_SUFFIX_III = 'III';
    const SELECT_NAME_SUFFIX_IV = 'IV';
    const SELECT_NAME_SUFFIX = [
        self::SELECT_NAME_SUFFIX_NONE => self::SELECT_NAME_SUFFIX_NONE,
        self::SELECT_NAME_SUFFIX_SR => self::SELECT_NAME_SUFFIX_SR,
        self::SELECT_NAME_SUFFIX_JR => self::SELECT_NAME_SUFFIX_JR,
        self::SELECT_NAME_SUFFIX_II => self::SELECT_NAME_SUFFIX_II,
        self::SELECT_NAME_SUFFIX_III => self::SELECT_NAME_SUFFIX_III,
        self::SELECT_NAME_SUFFIX_IV => self::SELECT_NAME_SUFFIX_IV,
    ];

    const SELECT_DEMOGRAPHICS_OTHER = 'OTHER';

    // value for date fields / expiration
    const DATE_FIELD_7_YEARS_PRIOR = '-7 years';
    const DATE_FIELD_1_YEAR_PRIOR = '-1 year';

    const SELECT_GOAL = [
        'PARTTIME' => 'Supplement your current income as a Part Time Agent',
        'FULLTIME' => 'Exclusively rely on this income as a Full Time Agent',
    ];

    const SELECT_ACTIVE_CONTRACTS = [
        'symmetry' => 'I was previously contracted with Symmetry',
        'other' => 'I was previously contracted with another group',
        'none' => 'Neither, I\'ve been away from the industry more than 2 years.'
    ];

    const SELECT_STATE = [
        'AL'=>'ALABAMA',
        'AK'=>'ALASKA',
        'AS'=>'AMERICAN SAMOA',
        'AZ'=>'ARIZONA',
        'AR'=>'ARKANSAS',
        'CA'=>'CALIFORNIA',
        'CO'=>'COLORADO',
        'CT'=>'CONNECTICUT',
        'DE'=>'DELAWARE',
        'DC'=>'DISTRICT OF COLUMBIA',
        'FM'=>'FEDERATED STATES OF MICRONESIA',
        'FL'=>'FLORIDA',
        'GA'=>'GEORGIA',
        'GU'=>'GUAM GU',
        'HI'=>'HAWAII',
        'ID'=>'IDAHO',
        'IL'=>'ILLINOIS',
        'IN'=>'INDIANA',
        'IA'=>'IOWA',
        'KS'=>'KANSAS',
        'KY'=>'KENTUCKY',
        'LA'=>'LOUISIANA',
        'ME'=>'MAINE',
        'MH'=>'MARSHALL ISLANDS',
        'MD'=>'MARYLAND',
        'MA'=>'MASSACHUSETTS',
        'MI'=>'MICHIGAN',
        'MN'=>'MINNESOTA',
        'MS'=>'MISSISSIPPI',
        'MO'=>'MISSOURI',
        'MT'=>'MONTANA',
        'NE'=>'NEBRASKA',
        'NV'=>'NEVADA',
        'NH'=>'NEW HAMPSHIRE',
        'NJ'=>'NEW JERSEY',
        'NM'=>'NEW MEXICO',
        'NY'=>'NEW YORK',
        'NC'=>'NORTH CAROLINA',
        'ND'=>'NORTH DAKOTA',
        'MP'=>'NORTHERN MARIANA ISLANDS',
        'OH'=>'OHIO',
        'OK'=>'OKLAHOMA',
        'OR'=>'OREGON',
        'PW'=>'PALAU',
        'PA'=>'PENNSYLVANIA',
        'PR'=>'PUERTO RICO',
        'RI'=>'RHODE ISLAND',
        'SC'=>'SOUTH CAROLINA',
        'SD'=>'SOUTH DAKOTA',
        'TN'=>'TENNESSEE',
        'TX'=>'TEXAS',
        'UT'=>'UTAH',
        'VT'=>'VERMONT',
        'VI'=>'VIRGIN ISLANDS',
        'VA'=>'VIRGINIA',
        'WA'=>'WASHINGTON',
        'WV'=>'WEST VIRGINIA',
        'WI'=>'WISCONSIN',
        'WY'=>'WYOMING',
        'AE'=>'ARMED FORCES AFRICA \ CANADA \ EUROPE \ MIDDLE EAST',
        'AA'=>'ARMED FORCES AMERICA (EXCEPT CANADA)',
        'AP'=>'ARMED FORCES PACIFIC',
    ];

    public function validate($value) {
        switch ($this->type) {
            case self::TYPE_TEXT:
            case self::TYPE_SELECT:
                if (strlen(trim($value)) < 1) {
                    return __('errors.string');
                }
                break;
        }
        return true;
    }

    /**
     * Get the fields for this form section.
     */
    public function formOptions()
    {
        return $this->hasMany(FormOption::class)->orderBy('sort', 'asc');
    }

    public function formSection()
    {
        return $this->belongsTo(FormSection::class);
    }

    public function isRequired() {
        return ($this->is_required == 1);
    }

    public function isSecure() {
        return ($this->is_secure == 1);
    }

    public function fetchFormatted()
    {
        return [
            'label'             => $this->label,
            'is_required'       => $this->is_required,
            'is_secure'         => $this->is_secure,
            'max_length'        => $this->max_length,
            'type'              => $this->type,
            'id'                => $this->id,
            'placeholder'       => $this->placeholder,
            'width'             => $this->width,
            'alias_autofill'    => $this->alias_autofill,
            'field_slug'        => $this->field_slug
        ];
    }

    public function markAsPii($bool=true)
    {
        $this->update([
            'pii' => $bool
        ]);

        return $this;
    }

    public function findEchoField() {
        $lookup = FormLookup::where('form_field_id', $this->id)->first();
        if(!$lookup)
            return null;
        if(!isset(FormLookup::echo[$lookup->field_slug]))
            return null;

        $echoLookup = FormLookup::where('field_slug', FormLookup::echo[$lookup->field_slug])->first();
        if($echoLookup)
            return $echoLookup->formField;

        return null;
    }
}
