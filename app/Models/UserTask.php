<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;

/**
 * App\Models\UserTask
 *
 * @property string $id
 * @property string $user_id
 * @property string $type
 * @property bool $completed
 * @property string|null $target_id
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $target
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask newQuery()
 * @method static \Illuminate\Database\Query\Builder|UserTask onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereCompleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|UserTask withTrashed()
 * @method static \Illuminate\Database\Query\Builder|UserTask withoutTrashed()
 * @mixin \Eloquent
 * @property string|null $description
 * @method static \Illuminate\Database\Eloquent\Builder|UserTask whereDescription($value)
 */
class UserTask extends Model
{
    use HasFactory, UsesUuid, SoftDeletes;

    protected $fillable = [
        'user_id',
        'copy',
        'type',
        'completed',
        'target_id',
        'description'
    ];

    const TYPE_FILL_APPLICATION             = 'Fill Application';
    const TYPE_UPDATE_APPLICATION           = 'Update Application';
    const TYPE_APPROVE_APPLICATION          = 'Approve Application';
    const TYPE_CREATE_UNLICENSED_ACCOUNT    = 'Create Unlicensed Account';
    const TYPE_ASSIGN_TO_ONBOARDING         = 'Assign to Onboarding Dept';
    const TYPE_UNENROLLED_CONTACT_AO        = 'Contact AO for Pre-Licensing Course Enrollment';
    const TYPE_ENROLLED_EXAM                = 'Complete course, then register for and complete your state exam';

    public function target()
    {
        return $this->belongsTo(User::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
