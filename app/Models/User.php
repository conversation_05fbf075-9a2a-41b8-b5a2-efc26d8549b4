<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Sanctum\Sanctum;
use App\Notifications\ResetPasswordNotification;
use App\Events\Auth\ForgotPassword;
use Exception;
use Spatie\Permission\Traits\HasRoles;
use App\Models\AgentManagement\Level;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use DB;
use Cache;
use Lab404\Impersonate\Models\Impersonate;
use Plank\Metable\Metable;
use HQAccounts;

/**
 * App\Models\User
 *
 * @property string $id
 * @property string $name
 * @property string $email
 * @property string|null $sub
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string|null $password
 * @property string|null $auth0_metadata
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $manager_id
 * @property string|null $inviter_user_id
 * @property string|null $inviter_agent_code
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $user_status_id
 * @property-read User|null $invitedBy
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\UserInvite[] $invites
 * @property-read int|null $invites_count
 * @property-read User|null $manager
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\UserStatusHistory[] $statusHistory
 * @property-read int|null $status_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Sanctum\PersonalAccessToken[] $tokens
 * @property-read int|null $tokens_count
 * @method static \Database\Factories\UserFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Query\Builder|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAuth0Metadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereInvitedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereManagerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereSub($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserStatusId($value)
 * @method static \Illuminate\Database\Query\Builder|User withTrashed()
 * @method static \Illuminate\Database\Query\Builder|User withoutTrashed()
 * @mixin \Eloquent
 * @property-read \App\Models\UserStatus|null $status
 * @property bool $approved_by_ao
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\UserNote[] $notes
 * @property-read int|null $notes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Spatie\Permission\Models\Permission[] $permissions
 * @property-read int|null $permissions_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Spatie\Permission\Models\Role[] $roles
 * @property-read int|null $roles_count
 * @method static \Illuminate\Database\Eloquent\Builder|User permission($permissions)
 * @method static \Illuminate\Database\Eloquent\Builder|User role($roles, $guard = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereApprovedByAo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereOnboardStatus($value)
 * @property string|null $contract_level
 * @method static \Illuminate\Database\Eloquent\Builder|User whereContractLevel($value)
 * @property string $onboard_status
 * @property string|null $upline_agent_id
 * @property string|null $agent_code
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\UserTask[] $tasks
 * @property-read int|null $tasks_count
 * @property-read \App\Models\Agent|null $uplineAgent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\UserEntry[] $userEntries
 * @property-read int|null $user_entries_count
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAgentCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereInviterAgentCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereInviterUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUplineAgentId($value)
 * @property bool $placeholder
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePlaceholder($value)
 * @property string|null $agency_owner_code
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAgencyOwnerCode($value)
 */
class User extends Authenticatable
{
    use HasFactory, SoftDeletes, UsesUuid, HasApiTokens, Notifiable, HasRoles, Impersonate, Metable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'email',
        'sub',
        'password',
        'manager_id',
        'hash',
        'user_status_id',
        'inviter_user_id',      // inviter id
        'inviter_agent_code',   // inviter agent code ( also exists in Agent table )
        'agency_owner_code',    // inviter agent code ( also exists in Agent table )
        'auth0_metadata',
        'approved_by_ao',
        'contract_level',
        'agent_code', // only for auth0 users
        'upline_agent_id',
        'updated_at', // needed for testing/seeding data in /app/Console/Commands/Testing/AddTestMetricData.php,
        'placeholder', // needed for pre-creation of agent user
        'work_email', // updated from user app entry
        'licensed',
        'first_ao_submission',
        'first_homeoffice_submission',
        'homeoffice_submission',
        'last_approved',
        'last_revision',
        'passed_exam',
        'recruiter_name',
        'upline_agent_name',
        'upline_agent_code',
        'agency_owner_name',
        'source',
        'user_invite_id',
        'blacklist',
        'phone',
        'last_name',
        'dob',
        'ssn',
        'transferring',
        'returning',
        'type'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    const AUTH0_KEY_METADATA        =  'http://quility.com/meta_data';
    const AUTH0_KEY_ROLES           =  'http://quility.com/roles';
    const AUTH0_KEY_USERDATA        =  'http://quility.com/user_data';

    // user types
    const ROLE_TYPE_RECRUIT         = 'Recruit';
    const ROLE_TYPE_UNLICENSED_AGENT= 'UnlicensedAgent';
    const ROLE_TYPE_SALES_REP       = 'SalesRep';
    const ROLE_TYPE_AGENCY_OWNER    = 'AgencyOwner';
    const ROLE_TYPE_STAFF           = 'Staff';
    const ROLE_TYPE_SUPER_ADMIN     = 'SuperAdmin';
    const ROLE_TYPE_B2B_STAFF       = 'B2BOnboardingStaff';

    const USER_TYPE_B2B_AGENT       = 'b2b-agent';
    const USER_TYPE_B2B_PRINCIPAL   = 'b2b-principal';

    // user application status type
    const ONBOARD_STATUS_SUBMITTED_AO   = 'submitted-ao';
    const ONBOARD_STATUS_SUBMITTED_HO   = 'submitted-ho';
    // const ONBOARD_STATUS_SUBMITTED      = 'submitted';
    const ONBOARD_STATUS_AO_APPROVED    = 'ao-approved';
    const ONBOARD_STATUS_HO_APPROVED    = 'ho-approved';
    const ONBOARD_STATUS_REJECTED_AO    = 'rejected-ao';
    const ONBOARD_STATUS_REJECTED_HO    = 'rejected-ho';
    const ONBOARD_STATUS_UNLICENSED     = 'created-unlicensed-account';
    const ONBOARD_STATUS_ASSIGNED       = 'assigned-to-onboarding-dept';
    const ONBOARD_STATUS_UNENROLLED     = 'unenrolled';
    const ONBOARD_STATUS_ENROLLED       = 'enrolled';
    const ONBOARD_STATUS_PENDING        = 'pending';
    const ONBOARD_STATUS_APPROVAL_ERROR = 'approval-error';
    const ONBOARD_STATUS_REVISION_AO    = 'revision-ao';
    const ONBOARD_STATUS_REVISION_HO    = 'revision-ho';
    const ONBOARD_STATUS_EXISTS         = 'flag-exists-%';
    const ONBOARD_STATUS_EXISTS_HQ      = 'flag-exists-hq';
    const ONBOARD_STATUS_EXISTS_B2BHQ   = 'flag-exists-b2bhq';

    const SFGAGENCY_ACCOUNT = 'SFGAGENCY';

    const SERVICE_LEVEL_ADVISORY = 'Advisory';
    const SERVICE_LEVEL_DRIVEN = 'Driven';

    protected static function booted()
    {
        static::updated(function ($user) {
            if($user->isDirty('user_status_id')) {
                UserStatusHistory::create([
                    'user_id' => $user->id,
                    'user_status_id' => $user->user_status_id,
                ]);
            }
        });
    }

    public function getAuth0MetadataAttribute($value)
    {
        return json_decode($value, true);
    }

    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    public function invitedBy()
    {
        return $this->belongsTo(User::class, 'inviter_user_id');
    }

    public function status()
    {
        return $this->belongsTo(UserStatus::class, 'user_status_id');
    }

    public function invites()
    {
        return $this->hasMany(UserInvite::class);
    }

    public function tokens()
    {
        return $this->morphMany(Sanctum::$personalAccessTokenModel, 'tokenable', "tokenable_type", "tokenable_uuid");
    }

    public function statusHistory($sort = 'desc')
    {
        return $this->hasMany(UserStatusHistory::class)->orderBy('created_at', $sort);
    }

    public function sendPasswordResetNotification($token)
    {
        event(new ForgotPassword($this, $token));
    }

    public function manager()
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function userEntries()
    {
        return $this->hasMany(UserEntry::class, 'user_id');
    }

    public function notes()
    {
        return $this->hasMany(UserNote::class, 'recruit_id');
    }

    public function tasks()
    {
        return $this->hasMany(UserTask::class, 'user_id');
    }
    
    public function notifications()
    {
        return $this->hasMany(UserNotification::class, 'user_id', 'id')->limit(50);
    }


    public function getFullDownlineAgents()
    {
        if (!empty($this->agent_code)) {
            $agent = Agent::where('AgentCode', $this->agent_code)->first();
            if (!$agent) {
                throw new Exception("Cannot find the agent with code: {$this->agent_code}", 404);
            }

            $ext_uid = $agent->ExtUID;

            if (empty($ext_uid)) {
                throw new Exception("ExtUID is null");
            }

            return AgentHierarchy
                ::where('FullDownline', 'LIKE', "$ext_uid\%")
                ->orderBy('DownlineLevel', 'asc')
                ->get();
        }

        return null;
    }

    public function uplineAgent()
    {
        return $this->hasOne(Agent::class, 'AgentID', 'upline_agent_id');
    }

    /**
     * get nearest upline AgencyOwner
     * @return Agent
     */
    public function agencyOwner()
    {
        // First check if the current user is an agency owner themselves
        if (!empty($this->agent_code)) {
            $currentUserAgent = Agent::where('AgentCode', $this->agent_code)->first();
            if ($currentUserAgent && $currentUserAgent->IsAgencyOwner) {
                return $currentUserAgent;
            }
        }

        // If current user is not an agency owner, proceed with original logic
        $agent = $this->uplineAgent()->first();
        if($agent){
            $upline = Agent::where('AgentCode', $agent->AgentCode)->first();
            if($upline)
            {
                do{
                    if($upline->IsAgencyOwner)
                        return $upline;
                }while($upline = $upline->directUpline);
            }
        }
        return null;
    }

    public function corporateDivision(){
        if (config('app.tenant') != 'Q2B' && Auth::user()->agent_code) {
            $agent_code =  Auth::user()->agent_code;
            $agent = Agent::select('Division')->where('AgentCode', $agent_code)->first();
            if($agent->Division === 'Corporate'){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }

    public function possibleContractLevels()
    {
        $agent_code = '';
        if ($this->auth0_metadata && $this->auth0_metadata[User::AUTH0_KEY_METADATA]) {
            $agent_code = $this->auth0_metadata[User::AUTH0_KEY_METADATA]['AgentCode'];
        } else if (Auth::user()->agent_code) {
            $agent_code = Auth::user()->agent_code;
        } else {
            return collect();
        }

        $agent_levels = Agent::with('AgentLevels')->where('AgentCode', $agent_code)->first()
            ->AgentLevels->pluck('Level')
            ->where('LevelType', 'Contract')
            ->sortByDesc('LevelRank');

        if ($agent_levels->first()['LevelRank'] == 1) {
            $levels = Level::where('LevelRank', '<=', $agent_levels->first()['LevelRank'])
            ->where('LevelType', 'Contract')
            ->where('LevelRank', '>', '0')
                ->orderBy('LevelRank', 'ASC')
                ->get(['ID', 'LevelName', 'LevelRank', 'LevelType']);

            return $levels;
        } else {
            $levels = Level::where('LevelRank', '<', $agent_levels->first()['LevelRank'])
            ->where('LevelType', 'Contract')
            ->where('LevelRank', '>', '0')
                ->orderBy('LevelRank', 'ASC')
                ->get(['ID', 'LevelName', 'LevelRank', 'LevelType']);

            return $levels;
        }
    }

    public function possibleDownLine($agent_code = '')
    {
        if($agent_code != '' && $agent_code != 'undefined') {
            //
        } elseif ($this->auth0_metadata && $this->auth0_metadata[User::AUTH0_KEY_METADATA]) {
            $agent_code = $this->auth0_metadata[User::AUTH0_KEY_METADATA]['AgentCode'];
        } elseif (Auth::user()->agent_code) {
            $agent_code = Auth::user()->agent_code;
        } else {
            return [];
        }
        $is_ao = Auth::user()->hasRole('AgencyOwner');

        //Get contract level
        $contract_level = Cache::remember('contract_level_'.$agent_code, 60*60*24, function () use ($agent_code) {
            return DB::connection('sqlsrv')->select("SELECT TOP 1
                    ContractLevel
                FROM vw_QuilityAgent
                WHERE AgentCode = :agentCode",
                ['agentCode' => $agent_code]);
        });
        //Get upline based on contract level
        // if(is_array($contract_level) && $contract_level[0]->ContractLevel == "130"){
        //     $sorted = Cache::remember('possible_dl_130_query_'.$agent_code, 60*60*24, function () use ($agent_code) {
        //         return DB::connection('sqlsrv')->select("SELECT DISTINCT
        //             Agent.AgentName as name
        //             ,DownlineAgentCode as agent_code
        //             ,DownlineAgentID as agent_id
        //             FROM dbo.AgentHierarchy
        //             INNER JOIN Agent ON Agent.ID = DownlineAgentID
        //             WHERE Agent.Status in ('Active','Available')
        //                 AND (AgentHierarchy.UplineAgentCode= :agentCode OR Agent.AgentCode = :agentCode )
        //                 AND AgentHierarchy.InSFGDirectAgencyInd=1
        //             ORDER BY  Agent.AgentName",
        //             ['agentCode' => $agent_code]);
        //     });
        // }else{
            $sorted = Cache::remember('possible_dl_query_'.$agent_code, 60*60*24, function () use ($agent_code) {
                return DB::connection('sqlsrv')->select("SELECT DISTINCT
                    Agent.AgentName as name
                    ,DownlineAgentCode as agent_code
                    ,DownlineAgentID as agent_id
                    FROM dbo.AgentHierarchy
                    INNER JOIN Agent ON Agent.ID = DownlineAgentID
                    WHERE Agent.Status in ('Active','Available', 'Stalled', 'Lapsed')
                        AND (AgentHierarchy.UplineAgentCode= :agentCode
                        OR Agent.AgentCode = :agentCode)
                    ORDER BY  Agent.AgentName",
                    ['agentCode' => $agent_code]);
            });
        // }

        $sorted = collect($sorted)->map(function($x){ return (array) $x; })->toArray();
        
        //append current agent to results
        $agent = Agent::where('AgentCode', $agent_code)->first();
        array_unshift($sorted, array("agent_id" => $agent->AgentID, "name" => $agent->AgentName, "agent_code" => $agent->AgentCode));
        
        return $sorted;
    }

    private function getNestedDownline($downline)
    {
        $scoped_downline = collect();

        $nested_agents = Agent::whereIn('AgentID', $downline->pluck('AgentID')->toArray())
            ->has('directDownline')
            ->with('directDownline.AgentLevels.Level')
            ->get();

        foreach ($nested_agents as $na) {
            foreach ($na->directDownline as $dl) {
                $is_ao = 0;
                foreach ($dl->AgentLevels as $l) {
                    if ($l->Level->IsAgencyOwner) {
                        $is_ao = 1;
                    }
                }

                if (!$is_ao) {
                    $scoped_downline->push($dl);
                }
            }
        }

        return $scoped_downline;
    }

    public function possibleDownLineRegistered()
    {
        $downlines = $this->possibleDownLine();
        return User::whereIn('agent_code', collect($downlines)->pluck('agent_code'))->get();
    }

    /**
     * get last status object of a certain type
     * $type: a status slug | array of slugs
     * $identical: a slug: where/whereNot | array of slugs: whereIn/whereNotIn
     * @return: UserStatus object | null
     */
    public function lastStatusHistoryOf($slug, $identical = true)
    {
        $query = $this->statusHistory()->whereHas(
            'userStatus',
            function ($q) use ($slug, $identical) {
                if(is_array($slug))
                    if($identical)
                        $q->whereIn('slug', $slug);
                    else
                        $q->whereNotIn('slug', $slug);
                else
                    if($identical)
                        $q->where('slug', $slug);
                    else
                        $q->where('slug', '!=', $slug);
            }
        );

        if($slug == User::ONBOARD_STATUS_SUBMITTED_AO)
            $query->whereRaw('user_id=trigger_id');

        return $query->first();
    }

    public function getLastVisitedFormStatus()
    {
        return $this->lastStatusHistoryOf([
            User::ONBOARD_STATUS_SUBMITTED_AO,
            User::ONBOARD_STATUS_SUBMITTED_HO,
            User::ONBOARD_STATUS_AO_APPROVED,
            User::ONBOARD_STATUS_HO_APPROVED,
            User::ONBOARD_STATUS_REJECTED_AO,
            User::ONBOARD_STATUS_REJECTED_HO,
            User::ONBOARD_STATUS_UNLICENSED,
            User::ONBOARD_STATUS_ASSIGNED,
            User::ONBOARD_STATUS_REVISION_AO,
            User::ONBOARD_STATUS_REVISION_HO,
        ], false);
    }

    public function getLastUpdatedFormStatus()
    {
        $user_entry = $this->userEntries()->with('formField.formSection.formPage')->orderBy('updated_at', 'desc')->first();

        if($user_entry) {
            $page = $user_entry->formField->formSection->formPage;
            var_dump($page->step_ident);
            return $this->lastStatusHistoryOf($page->step_ident, false);
        }

        return null;
    }

    public function lastUpdatedAt()
    {
        $last_user_entry = $this->userEntries()->orderBy('updated_at', 'desc')->first();
        if($last_user_entry)
            return $last_user_entry->updated_at;
        return null;
    }

    public function lastSubmittedAt()
    {
        $status_history = $this->lastStatusHistoryOf(self::ONBOARD_STATUS_SUBMITTED_HO);
        if($status_history)
            return $status_history->created_at;
        return null;
    }

    public function lastApprovedAt()
    {
        $status_history = $this->lastStatusHistoryOf([self::ONBOARD_STATUS_AO_APPROVED, self::ONBOARD_STATUS_HO_APPROVED]);
        if($status_history)
            return $status_history->created_at;
        return null;
    }

    public function lastRejectedAt()
    {
        $status_history = $this->lastStatusHistoryOf(self::ONBOARD_STATUS_REJECTED_HO);
        if($status_history)
            return $status_history->created_at;
        return null;
    }

    public function unlicensedAt()
    {
        $status_history = $this->lastStatusHistoryOf(self::ONBOARD_STATUS_UNLICENSED);
        if($status_history)
            return $status_history->created_at;
        return null;
    }

    public function assignedAt()
    {
        $status_history = $this->lastStatusHistoryOf(self::ONBOARD_STATUS_ASSIGNED);
        if($status_history)
            return $status_history->created_at;
        return null;
    }

    public function isSignatureSigned()
    {
        return UserSignature::where('user_email', $this->email)
                            ->where('event_type', UserSignature::TYPE_ALL_SIGNED)
                            ->count() > 0;
    }

    public function comments()
    {
        return $this->statusHistory()
                    ->with('trigger.roles')
                    ->with('userStatus')
                    ->whereNotNull('note')
                    ->where('note', '!=', '');
    }

    public function countSubmitted()
    {
        return $this->statusHistory()->whereHas(
            'userStatus',
            function ($q) {
                $q->whereIn('slug', [User::ONBOARD_STATUS_SUBMITTED_AO, User::ONBOARD_STATUS_SUBMITTED_HO]);
            }
        )->count();
    }

    public function getHomeOfficeApprovalDate()
    {
        $approvalDate = $this->statusHistory()
                                ->whereHas('userStatus',function($query){
                                    $query->where('slug', self::ONBOARD_STATUS_HO_APPROVED);
                                })
                                ->first();

        return $approvalDate->created_at ?? null;
    }

    /*
     * The first date the candidate submitted their app to the Agency Owner
     */
    public function getFirstAgencyOwnerSubmissionDate()
    {
        $submissionDate = $this->statusHistory('asc')
            ->whereHas('userStatus',function($query){
                $query->where('slug', self::ONBOARD_STATUS_SUBMITTED_AO);
            })->first();

        if($submissionDate)
        {
            return $submissionDate->created_at;
        }
        return null;
    }

    public function getFirstHomeOfficeSubmissionDate($user_id)
    {
        $submissionDates = $this->statusHistory('asc')
        ->whereHas('userStatus',function($query){
            $query->where('slug', self::ONBOARD_STATUS_SUBMITTED_HO);
        })
        ->get();

        $firstSubmissionDate = null;
        foreach($submissionDates as $submission) {
            //the first submission by someone other than the user is the AO
            if($submission->user_id != $user_id && $firstSubmissionDate == null)
                $firstSubmissionDate = $submission->created_at;
        }
        return $firstSubmissionDate;
    }

    public function getLatestHomeOfficeSubmissionDate($user)
    {
        if($user->approved_by_ao) {
            $submission = $this->statusHistory()
                ->whereHas('userStatus',function($query){
                    $query->where('slug', self::ONBOARD_STATUS_SUBMITTED_HO);
                })
                ->first();
            if($submission)
                return $submission->created_at;
        }
        return null;
    }

    public function getBaseshopAgents()
    {
        if($this->agent_code)
        {
            $agent = Agent::where('AgentCode',$this->agent_code)->first();
            return $agent->baseshop(['Active', 'Available', 'Stalled', 'Lapsed']);
        }

        return null;
    }

    public function getDownlineAgents()
    {
        if($this->agent_code)
        {
            $agent = Agent::where('AgentCode',$this->agent_code)->first();
            return $agent->directDownline();
        }

        return null;
    }

    public function updateStatusFields()
    {
        $first_ao_submission = $this->getStatusBySlug(['submitted-ao'], 'asc');
        $first_homeoffice_submission = $this->getStatusBySlug(['submitted-ho'], 'asc');
        $homeoffice_submission = $this->getStatusBySlug(['submitted-ho'], 'desc');
        $last_approved = $this->getStatusBySlug(['ho-approved', 'ao-approved'], 'desc');
        $last_revision = $this->getStatusBySlug(['revision-ao', 'revision-ho'], 'desc');

        $this->update([
            'first_ao_submission' => $first_ao_submission->created_at ?? null, 
            'first_homeoffice_submission' => $first_homeoffice_submission->created_at ?? null, 
            'homeoffice_submission' => $homeoffice_submission->created_at ?? null,
            'last_approved' => $last_approved->created_at ?? null,
            'last_revision' => $last_revision->created_at ?? null
        ]);
    }

    public function getStatusBySlug(Array $slugs, String $sort)
    {
        $ush = UserStatusHistory::select('user_statuses.slug', 'user_status_history.user_status_id', 'user_status_history.created_at')
            ->join('user_statuses', 'user_statuses.id', '=', 'user_status_history.user_status_id')
            ->where('user_id', $this->id)
            ->whereIn('slug', $slugs)
            ->orderBy('user_status_history.created_at', $sort)
            ->first();

        return $ush;
    }

    public function userStatus()
    {
        return $this->belongsTo(UserStatus::class, 'user_status_id');
    }

    public function userInvite()
    {
        return $this->belongsTo(UserInvite::class, 'user_invite_id');
    }
    /**
     * Get the service level attribute for the user.
     *
     * This function determines the service level of the user based on their agency owner's invitation.
     * 'Advistory' means that the home office does not review or approve applications, the AO makes the decision.
     * 'Driven' means that the home office reviews and approves applications after approved by the AO.
     * 
     * It follows these steps:
     * 1. Retrieves the user's agency owner.
     * 2. Finds the agency owner's user record, including their invitation details.
     * 3. Extracts the service level from the agency owner's invitation.
     *
     * @return string The service level of the user, or 'Driven' if not found.
     */

    public function getServiceLevelAttribute()
    {
        $agencyOwner = $this->agencyOwner();

        if (!$agencyOwner || !$agencyOwner->AgentCode) {
            return self::SERVICE_LEVEL_DRIVEN; // Default to Driven if no agency owner found
        }

        // Try to get from cache first
        $cacheKey = 'agent_service_level_' . $agencyOwner->AgentCode;
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $agencyOwner->AgentCode . '/config';
            $accessToken = HQAccounts::generateAccessToken(true);

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET', $requestPath, [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
            ]);

            $configs = json_decode($response->getBody(), true);
            
            if (empty($configs) || !isset($configs['data']['AgentConfig'])) {
                // Fall back to old implementation
                return $this->getServiceLevelFromInvite();
            }

            foreach ($configs['data']['AgentConfig'] as $config) {
                if ($config['ConfigName'] === 'ServiceLevel') {
                    $serviceLevel = $config['ConfigValue'] ?? self::SERVICE_LEVEL_DRIVEN;
                    // Cache the result for 24 hours
                    Cache::put($cacheKey, $serviceLevel, 60 * 60 * 24);
                    return $serviceLevel;
                }
            }

            // If no ServiceLevel config found, fall back to old implementation
            return $this->getServiceLevelFromInvite();

        } catch (\Exception $e) {
            \Log::error("Error fetching service level from HQ: " . $e->getMessage());
            // Fall back to old implementation on error
            return $this->getServiceLevelFromInvite();
        }
    }

    /**
     * Get the service level from the agency owner's invitation (old implementation)
     *
     * @return string The service level of the user, or 'Driven' if not found.
     */
    protected function getServiceLevelFromInvite()
    {
        $agencyOwner = $this->agencyOwner();
        if (!$agencyOwner) {
            return self::SERVICE_LEVEL_DRIVEN;
        }
        
        // Get the agency owner's user record with the invite record
        $user = User::where('agent_code', $agencyOwner->AgentCode)->with('userInvite')->first();
        // Get the agency owner's invitation service level
        return $user->userInvite->service_level ?? self::SERVICE_LEVEL_DRIVEN;
    }
}
