<?php
/**
 * This is the model definition for the QuilityAgent view. It is meant as a read only model and has pre joined columns for easy access to what you need.
 * If you need to update an agent's attribute, use the Base/Agent models.
 */

namespace App\Models;

use App\Models\CloseRatio;
use App\Models\PopupAcknowledgement;
use App\Models\AgentHierarchy;
use App\Models\PolicyPending;
use App\Models\Base\StateLicense;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AgentManagement\AgentLevel;
use DB;
use Carbon\Carbon;
use App\Models\Application;
// use App\Models\UserLog;
use App\Models\Commission;

class DailyApplication extends BaseModel
{

    // protected $connection = 'sqlsrv';
    protected $table = "v_Applications_Daily_Report";
    protected $primaryKey = 'user_id'; // or null
    public $incrementing = false;
    protected $keyType = 'string';

}
