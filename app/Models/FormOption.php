<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FormField;
use App\Traits\UsesUuid;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\FormOption
 *
 * @property string $id
 * @property string $form_field_id
 * @property string $label
 * @property string $value
 * @property int $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read FormField $formField
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereFormFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormOption whereValue($value)
 * @mixin \Eloquent
 */
class FormOption extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'form_field_id',
        'label',
        'value',
        'sort',
    ];

    protected $dates = ['deleted_at'];

    protected $hidden = [
        'created_at',
        'updated_at'
    ];

    public function formField()
    {
        return $this->belongsTo(FormField::class);
    }

    public function fetchFormatted()
    {
        return [
            'label' => $this->label,
            'value' => $this->value,
        ];
    }
}
