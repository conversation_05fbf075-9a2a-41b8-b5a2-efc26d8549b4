<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\UserStatusHistory
 *
 * @property string $id
 * @property string $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $user_status_id
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereUserStatusId($value)
 * @mixin \Eloquent
 * @property-read \App\Models\User $trigger
 * @property-read \App\Models\UserStatus|null $user_status
 * @property string|null $note
 * @property string|null $trigger_id
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserStatusHistory whereTriggerId($value)
 */
class UserStatusHistory extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_status_history';

    protected $fillable = [
        'user_id',
        'user_status_id',
        'note',
        'trigger_id'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function userStatus()
    {
        return $this->belongsTo(UserStatus::class);
    }

    public function trigger()
    {
        return $this->belongsTo(User::class);
    }
}
