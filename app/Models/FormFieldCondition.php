<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\FormFieldCondition
 *
 * @property string $id
 * @property string $form_field_id
 * @property string $value
 * @property string $action
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $form_section_id
 * @property string|null $type
 * @property-read \App\Models\FormField $formField
 * @property-read \App\Models\FormSection|null $formSection
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition query()
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereFormFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereFormSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FormFieldCondition whereValue($value)
 * @mixin \Eloquent
 * @property-read mixed $field
 */
class FormFieldCondition extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'form_field_conditions';

    protected $fillable = [
        'form_field_id',
        'value',
        'action',
        'form_section_id',
        'type',
    ];

    protected $appends = [
        'field'
    ];

    const ACTION_SHOW = 'show';
    const ACTION_HIDE = 'hide';
    const ACTION_REPEAT_DATE = 'repeat';

    const TYPE_EQUALS = '=';
    const TYPE_MORE_THAN = '>';
    const TYPE_LESS_THAN = '<';
    const TYPE_LIKE = 'LIKE';

    // TO DO: change fronend to use defualt names
    public function getFieldAttribute()
    {
        return $this->form_field_id;
    }

    public function formField()
    {
        return $this->belongsTo(FormField::class);
    }

    public function formSection()
    {
        return $this->belongsTo(FormSection::class);
    }

    public function getFieldHash()
    {
        return $this->formField()->first()->id;
    }

    public function fetchFormatted()
    {
        return [
            'field' => $this->getFieldHash(),
            'value' => $this->value,
            'action' => $this->action,
            'type' => $this->type,
        ];
    }
}
