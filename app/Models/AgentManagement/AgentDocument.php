<?php

namespace App\Models\AgentManagement;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;
use App\Models\Agent;
use Illuminate\Support\Str;
use Storage;
use App\Models\AgentConfig;

class AgentDocument extends BaseModel
{
    protected $connection = 'sqlsrv';
    protected $table = "AgentDocument";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["LastChangeBy", "DocTitle", "CategoryName", "DocType", "DocDesc", "FilePath", "Hash", "AgentID", "Division"];
    public $timestamps = false;
    public static $allowedFilters = ['CategoryName', 'DocType'];

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }

    public function Agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }

    // Generates a time sensetive download link that is only available for 1 day.
    public function getDownloadLinkAttribute()
    {
        if ($this->AgentID == null) {
            //this is a public document, so give a public url here that will never change.
            return "/api/public/document/" . $this->ID . "/" . Str::slug($this->DocTitle);
        }
        $timestamp = time();
        $hash = $this->generateHash($timestamp);
        return "/api/public/document/" . $this->ID . "/download/" . $timestamp . "/" . $hash;
    }

    // similar to DownLink attribute
    // except we're viewing the doc
    // instead of downloading it
    public function getViewLinkAttribute()
    {
        if ($this->AgentCode == null) {
        //     //this is a public document, so give a public url here that will never change.
            return "/api/public/document/" . $this->ID . "/view/" . Str::slug($this->DocTitle);
        }
        $timestamp = time();
        $hash = $this->generateHash($timestamp);
        return "/api/public/document/" . $this->ID . "/view/" . $timestamp . "/" . $hash;
    }

    public function scopeExists($query, $title, $type)
    {
        return $query->where("DocTitle", 'LIKE', $title)
            ->where("DocType", 'LIKE', $type);
    }

    public function scopeForAgent($query, $agent)
    {
        return $query->where("AgentID", $agent->AgentID);
    }

    public function scopeIsPublic($query)
    {
        return $query->whereNull("AgentID");
    }

    public function scopeForDivisions($query, $divisions)
    {
        if (!is_array($divisions)) {
            $divisions = [$divisions];
        }
        return $query->whereIn("division", $divisions);
    }

    public function generateHash($timestamp)
    {
        $str = $this->ID . "|" . $this->Hash . "|" . $this->AgentID . "|" . $timestamp;
        return MD5($str);
    }

    public function validateHash($hash, $timestamp)
    {
        //hashes are only valid for 1 day;
        $diff = (time() - ((int) $timestamp)) / 60 / 60 / 24;
        if ($diff > 1) {
            return false;
        }
        return $hash == $this->generateHash($timestamp);
    }

    public static function uploadAgentDocument($file_location, $filename, $agent = null, $user, $division = null, $category = null)
    {
        //sometimes there are more than one file separated by a comma (coming from onboarding portal) that we need to split up.
        if (strpos($file_location, ",") > -1) {
            $file_locations = explode(",", $file_location);
        } else {
            $file_locations = [$file_location];
        }
        foreach ($file_locations as $file_location) {
            $storage_driver = "azure";
            //gets the uploaded file's info from zendesk... includes the download url.
            $info = pathinfo($filename);
            $contents = file_get_contents($file_location);
            $ext = $info['extension'];
            $name = $info['filename'];
            $documentName = $name . "_" . Str::random(7) . "." . $ext;
            if ($agent != null) {
                $document_path = app()->env . "/public/agent_documents/" . $agent->AgentCode . "/" . $documentName;
            } else {
                $document_path = app()->env . "/public/agent_documents/" . Str::slug($division) . "/" . Str::slug($category) . "/" . $documentName;
            }
            //stores doc in Azure Blob storage
            Storage::disk($storage_driver)->put($document_path, $contents);
            //echo $temp_file;
            //now create and save the model.
            $document = new AgentDocument();
            $document->fill([
                'LastChangeBy' => is_string($user) ? $user : $user->email,
                'AgentID' => $agent == null ? null : $agent->AgentID,
                'DocType' => $ext,
                'DocTitle' => $name,
                'FilePath' => $document_path,
                'Division' => $division,
                'CategoryName' => $category
            ]);
            $document->Hash = $document->generateHash(time());
            $document->save();
            //check if this is a signature document... if so... set an agent config for storing the url
            if ($name == "Signature" && $agent != null) {
                $agent->setAgentConfig("AgentSignatureImage", $document->downloadLink);
            }
        }
        return $document;
    }
}
