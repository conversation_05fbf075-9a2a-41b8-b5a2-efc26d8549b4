<?php

namespace App\Models\AgentManagement;

use Illuminate\Database\Eloquent\Model;
use Storage;
use App\Models\BaseModel;

class AgentNote extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "AgentNote";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["LastChangeBy", "NoteTitle", "NoteText", "AgentID", "Sticky", "Link"];
    public $timestamps = false;
    public static $allowedFilters = [];

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }

    public function Agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }

    public function scopeForAgent($query, $agent)
    {
        return $query->where("AgentID", $agent->AgentID);
    }

    public function scopeSticky($query)
    {
        return $query->where("StickyInd", 1);
    }

    public function scopeExistsForAgent($query, $NoteTitle, $agent)
    {
        return $query->where("NoteTitle", $NoteTitle)
                    ->where("AgentID", $agent->AgentID);
    }
}
