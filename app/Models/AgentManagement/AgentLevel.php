<?php

namespace App\Models\AgentManagement;

use Illuminate\Database\Eloquent\Model;
use App\Support\Database\CacheQueryBuilder;
use App\Models\BaseModel;

class AgentLevel extends BaseModel
{

    //use CacheQueryBuilder;

    protected $connection = 'sqlsrv';
    protected $table = "AgentLevel";
    protected $primaryKey = 'ID'; // or null
    public $incrementing = false;
    // protected $with = ["Level"];
    // protected $appends = ["Level"];
    protected $fillable = ["LevelID", "AgentID", "OverrideLevelID", "StartDate", "EndDate", "Notes", "LockedInd", "LastChangeBy"];
    public $timestamps = false;
    const AgencyOwnerLevels = [
        'Agency Owner',
        'Agency Director',
        'Regional Agency Director',
        'Managing Vice President',
        'Senior Vice President',
        'Executive Vice President',
        'Associate Partner',
        'Senior Partner',
        'Managing Partner',
        'Founders',
    ];
    //public static $allowedFilters = ['Status', 'Role', 'State', 'RAAgentName'];

    public function scopeCurrent($query)
    {
        return $query->whereNull('EndDate')->orderBy('StartDate', "DESC");
    }

    public function scopeExcludeID($query, $id)
    {
        return $query->where('ID', "!=", $id);
    }

    public function scopeOfLevelType($query, $type)
    {
        return $query->whereHas('Level', function ($query) use ($type) {
            return $query->where("LevelType", $type);
        });
    }

    public function scopeLeadership($query)
    {
        return $query->ofLevelType("Leadership");
    }

    public function scopeContract($query)
    {
        return $query->ofLevelType("Contract");
    }

    public function scopeProducer($query)
    {
        return $query->ofLevelType("Producer");
    }

    public function scopeAgentCurrentLevels($query, $AgentID, $LevelType)
    {
        return $query->where("AgentID", $AgentID)
            ->whereNull("EndDate")
            ->whereHas('Level', function ($query) use ($LevelType) {
                return $query->where("LevelType", $LevelType);
            })
            ->orderBy('ID', 'DESC');
    }

    public function scopeMatchingDates($query, $AgentID, $LevelType, $StartDate, $EndDate)
    {
        if ($EndDate == null) {
            return $query->where("AgentID", $AgentID)
                //->whereNull("EndDate") /// removed cause sometimes we actually need to update this.
                ->ofLevelType($LevelType)
                ->where('StartDate', $StartDate);
        } else {
             return $query->where("AgentID", $AgentID)
                ->ofLevelType($LevelType)
                ->where('StartDate', $StartDate)
                ->where('EndDate', $EndDate);
        }
    }

    public function scopeOverlappingDates($query, $AgentID, $LevelType, $StartDate, $EndDate)
    {
        if ($EndDate == null) {
            return $query->where("AgentID", $AgentID)
                ->ofLevelType($LevelType)
                ->where(function ($query) use ($StartDate) {
                    return $query->where(function ($query) use ($StartDate) {
                            return $query->where('StartDate', '<=', $StartDate)
                                ->where('EndDate', '>=', $StartDate);
                    });
                });
        } else {
            return $query->where("AgentID", $AgentID)
                ->ofLevelType($LevelType)
                ->where(function ($query) use ($StartDate, $EndDate) {
                    return $query->where(function ($query) use ($StartDate, $EndDate) {
                            return $query->where('StartDate', '<=', $StartDate)
                                ->where('EndDate', '>=', $EndDate);
                    })
                        ->orWhere(function ($query) use ($StartDate, $EndDate) {
                            return $query->where('StartDate', '<=', $StartDate)
                                ->where('EndDate', '>=', $EndDate);
                        })
                        ->orWhere(function ($query) use ($StartDate, $EndDate) {
                            return $query->where('StartDate', '>=', $StartDate)
                                ->where('StartDate', '<=', $EndDate);
                        });
                });
        }
    }

    public function Agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }

    public function Level()
    {
        return $this->hasOne('App\Models\AgentManagement\Level', 'ID', 'LevelID');
    }

    public function OverrideLevel()
    {
        return $this->hasOne('App\Models\AgentManagement\Level', 'ID', 'OverrideLevelID');
    }

    public static function getAgencyOwnerLevels()
    {
        return self::AgencyOwnerLevels;
    }

    public static function isAgencyOwner($level)
    {
        return in_array($level, self::AgencyOwnerLevels);
    }

    public function getAgentCodeAttribute()
    {
        return $this->Agent->AgentCode;
    }

    public function getLevelNameAttribute()
    {
        return $this->Level->LevelName;
    }
}
