<?php

namespace App\Models\AgentManagement;

use Illuminate\Database\Eloquent\Model;
use App\Support\Database\CacheQueryBuilder;
use App\Models\BaseModel;

class Level extends BaseModel
{
    //use CacheQueryBuilder;
    
    protected $connection = 'sqlsrv';
    protected $table = "Level";
    protected $primaryKey = 'ID'; // or null

    
    //public static $allowedFilters = ['Status', 'Role', 'State', 'RAAgentName'];


    public function AgentLevel()
    {
        return $this->hasMany('App\Models\AgentManagement\AgentLevel', 'LevelID', 'ID');
    }

    public function scopeForLevel($query, $level_name)
    {
        return $query->where("LevelName", $level_name);
    }

    public function scopeOfLevelType($query, $type)
    {
        return $query->where("LevelType", $type);
    }

    public function scopeLeadership($query)
    {
        return $query->ofLevelType("Leadership");
    }

    public function scopeContract($query)
    {
        return $query->ofLevelType("Contract");
    }

    public function scopeProducer($query)
    {
        return $query->ofLevelType("Producer");
    }
}
