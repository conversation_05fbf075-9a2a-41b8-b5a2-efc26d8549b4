<?php

namespace App\Models\AgentManagement;

use App\Models\GuidModel;
use Storage;
use App\Models\BaseModel;

class BulkJob extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $primaryKey = 'ID';
    public $incrementing = false;
    public $timestamps = false;
    protected $table = "Job";
    protected $fillable = ["LastChangeBy", "DataFilePath", "JobName", "StartDate", "EndDate", "UserName", "NumProcessed", "NumErrors", "ErrorLogFile"];
    public static $allowedFilters = ['UserName'];

    public function getSampleDataAttribute()
    {
        $r = [];
        $disk = Storage::disk('azure');
        $stream = $disk
            ->getDriver()
            ->readStream($this->DataFilePath);
        $headers = $this->cleanHeadings(fgetcsv($stream, 1024));
        for ($i=0; $i < 10 && !feof($stream); $i++) {
            array_push($r, array_combine($headers, fgetcsv($stream, 1024)));
        }
        return $r;
    }

    //removes the \ufeff that is at the front of some CSV uploads.
    public static function cleanHeadings($heading_data)
    {
        $headings=array();
        foreach ($heading_data as $heading) {
              // Remove any invalid or hidden characters
              $heading = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $heading);
              array_push($headings, $heading);
        }
         return $headings;
    }

    public static function getRecentlyInserted($attributes)
    {
        return static::where(function ($q) use ($attributes) {
            foreach ($attributes as $key => $value) {
                $q->where($key, $value);
            }
        })->orderBy("ID", "DESC")->first();
    }

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }
}
