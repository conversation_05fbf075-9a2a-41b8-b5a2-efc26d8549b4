<?php

namespace App\Models;

use DB;
use App\Models\Agent;
use App\Models\Base\Carrier;
use App\Models\Product;
use Illuminate\Database\Eloquent\Model;

class Commission extends BaseModel
{
    protected $connection = 'sqlsrv';
    protected $table = "CommissionAPL";
    protected $primaryKey = "ID"; // laravel defaults to id

    public static $allowedFilters = [];

    //===================================
    //Relationships
    //===================================

    public function carrier()
    {
        return $this->belongsTo(Carrier::class, 'CarrierID', 'ID');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'CarrierProductID', 'ID');
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'QMSParticipantAgentCode', 'AgentCode');
    }

    //===================================
    //Scopes
    //===================================

    public function scopeForAgent($query, Agent $agent)
    {
        return $query->where('CommRecipientAgentID', $agent->AgentID);
    }

    public function scopeForStartDate($query, $startDate)
    {
        return $query->where('AccountingCycle', '>=', $startDate);
    }

    public function scopeForEndDate($query, $endDate)
    {
        return $query->where('AccountingCycle', '<=', $endDate);
    }

    public function scopeForNonZeroEarnedCredit($query)
    {
        return $query->where('EarnedCredit', '!=', 0);
    }
    //===================================
    // Helper Functions
    //===================================
}
