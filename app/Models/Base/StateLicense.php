<?php

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;
use App\Models\Base\StateLicenseLOA;
use App\Models\Agent;

class StateLicense extends BaseModel
{
    protected $connection = 'sqlsrv';
    protected $table = "dbo.StateLicense";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["AgentID", "LicensedOnDate", "State", "IsResidentState", "Status", "LicenseNumber", "LicenseClass", "RenewalStatus", "RenewalFee", "RenewalCheckedAt"];
    public $timestamps = false;

    //===================================
    // Relationships
    //===================================

    public function stateLicenseLoas()
    {
        return $this->hasMany(StateLicenseLOA::class, 'StateLicenseID', 'ID');
    }
    


    //===================================
    // Attributes
    //===================================
    






    //===================================
    //Scopes
    //===================================
    
    public function scopeForAgentAndState($query, Agent $agent, $state)
    {
        return $query->where("AgentID", $agent->AgentID)
                    ->where("State", $state);
    }




    //===================================
    // Helper Functions
    //===================================
}
