<?php

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;

class Carrier extends BaseModel
{
    protected $connection = 'sqlsrv';
    protected $table = "dbo.Carrier";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["LastChangeBy", "CarrierName", "CarrierShortName", "CarrierNameSureLC", "Solution", "Phone", "Email", "ContactName", "AddressID", "CarrierURL", "AgentLoginURL", "CarrierCMSURL", "NAICCode", "NAICGroupCode", "ParentCarrierID", "ActiveInd", "HiddenInd","KnownWritingNumberFormats"];
    public $timestamps = false;
    public static $allowedFilters = ["Solution", "Active", "Hidden"];

    //===================================
    // Relationships
    //===================================

    public function writingNumbers()
    {
        return $this->hasMany('App\Models\Base\CarrierWritingNumber', 'ID', 'CarrierID');
    }

    public function products()
    {
        return $this->hasMany('App\Models\Product', 'CarrierID', 'ID');
    }

    public function parentCarrier()
    {
        return $this->belongsTo('App\Models\Base\Carrier', 'ParentCarrierID', 'ID');
    }

    
    //===================================
    // Attributes
    //===================================
    

    //===================================
    //Scopes
    //===================================
    
    public function scopeSimpleSearch($query, $keyword)
    {
        return $query->where(function ($query) use ($keyword) {
            $query->whereRaw("CarrierName LIKE ? ", ['%' . strtoupper($keyword) . '%']);
        });
    }


    //===================================
    // Helper Functions
    //===================================
}
