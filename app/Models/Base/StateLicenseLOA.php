<?php

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model;
use App\Models\BaseModel;
use App\Models\StateLicense;

class StateLicenseLOA extends BaseModel
{
    protected $connection = 'sqlsrv';
    protected $table = "dbo.StateLicenseLOA";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["StateLicenseID", "LOA", "IssueDate"];
    public $timestamps = false;

    //===================================
    // Relationships
    //===================================


    public function stateLicense()
    {
        return $this->belongsTo(StateLicense::class, 'StateLicenseID', 'ID');
    }


    //===================================
    // Attributes
    //===================================
    






    //===================================
    //Scopes
    //===================================
    

    public function scopeForLoa($query, $loa)
    {
        return $query->where("LOA", $loa);
    }



    //===================================
    // Helper Functions
    //===================================
}
