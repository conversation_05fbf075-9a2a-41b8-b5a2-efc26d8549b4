<?php
/**
 * Model contains the actual stored values of agents and is the one we right to when we need to update an agent.
 */

namespace App\Models\Base;

use App\Models\CloseRatio;
use App\Models\PopupAcknowledgement;
use App\Models\AgentHierarchy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use App\Models\AgentManagement\AgentLevel;
use App\Models\Agent;
use DB;
use Carbon\Carbon;

class BaseAgent extends Model
{

    protected $connection = 'sqlsrv';
    protected $table = "dbo.Agent";
    protected $primaryKey = 'ID'; // or null
    //protected $keyType = 'string';
    public $timestamps = false;
    protected $fillable = ['OptID', 'PreferredName', 'AgentName', 'FirstName', 'LastName', 'MiddleName', 'Suffix', 'NickName', 'Status', 'ST', 'Address', 'Street1', 'City', 'State', 'Zip', 'County', 'Phone', 'Email', 'ContractStartDt', 'BirthDt', 'NPN', 'SSNLast4', 'DoingBusinessAsName', 'DoingBusinessAsNPN', 'Upline', 'UplineOptID', 'Division', 'CommUplineOptID', 'CommUplineName', 'Suffix', 'IsFullyLicensed', 'TerminationReason'];

    public static $allowedFilters = ['Status', 'ST'];

    //Applies the global scope to the model to insure we only return Agents with a ArrangementToDate of null
    protected static function boot()
    {
        parent::boot();
        //static::addGlobalScope('ArrangementToDate', function (Builder $builder) {
        //    $builder->whereNull('ArrangementToDate');
        //});
    }

    //===================================
    // Relationships
    //===================================

    public function agent()
    {
        return $this->belongsTo('App\Models\Agent', 'ID', 'AgentID');
    }

    public function leads()
    {
        return $this->belongsToMany('App\Models\Leads\Lead', 'App\Models\Leads\LeadAssignment', 'AgentID', 'LeadID', 'ID', 'ID');
    }




    //===================================
    // Attributes
    //===================================

    public function setPhoneAttribute($phone)
    {
        // strip all non-numeric characters
        $this->attributes['Phone'] = preg_replace("/[^0-9]/", "", $phone);
    }


    public function setSSNLast4Attribute($ssn)
    {
        // strip all non-numeric characters
        $this->attributes['SSNLast4'] = preg_replace("/[^0-9]/", "", $ssn);
    }


    //===================================
    //Scopes
    //===================================

    public function scopeFindByAgentCode($query, $code)
    {
        return $query->where("AgentCode", $code);
    }


    public function scopeActive($query)
    {
        return $query->where('AvailableInd', 1);
    }

    public function scopeIsAgencyOwner($query)
    {
        return $query->where('IsAgencyOwner', 1);
    }

    public function scopeSimpleSearch($query, $keyword)
    {
        return $query->where(function ($query) use ($keyword) {
            $query->whereRaw("UPPER(Agent) LIKE ? ", ['%' . strtoupper($keyword) . '%'])
                ->orWhereRaw("UPPER(Email) LIKE ? ", [strtoupper($keyword) . '%']);
        });
    }

    public function scopeSuperSimpleSearch($query, $keyword)
    {
        return $query->where(function ($query) use ($keyword) {
            $query->whereRaw("UPPER(Agent) LIKE ? ", ['%' . strtoupper($keyword) . '%'])
                ->orWhereRaw("UPPER(Email) LIKE ? ", [strtoupper($keyword) . '%']);
        });
    }


    public function scopeForDirectDownlineHistorical($query, Agent $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('ID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('DownlineLevel', 1);
        });
    }

    public function scopeForBaseshopOwnerHistorical($query, Agent $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('ID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('InBaseShopInd', 1);
        });
    }

    public function scopeForTotalAgencyOwnerHistorical($query, Agent $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereIn('ID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID);
        });
    }


    public function scopeWithRecruitedAgentHistorical($query, $startDate, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        //important to prevent sql injection.
        $startDate = date("Y-m-d H:i:s", strtotime($startDate));
        $endDate = date("Y-m-d H:i:s", strtotime($endDate));
        return $query->select(DB::raw("
                *, (SELECT
                        COUNT(*)
                    FROM
                        [AgentHierarchy_Historical]
                    WHERE
                        [AgentHierarchy_Historical].[UplineAgentID] = [Agent].[ID]
                        AND [EffDate] <= '$endDate'
                        AND [ExpDate] >= '$endDate'
                        AND [AgentHierarchy_Historical].[DownlineLevel] = 1
                        AND [AgentHierarchy_Historical].[DownlineAgentID] IN (
                            SELECT
                                DISTINCT ID
                            FROM
                                [Agent]
                            WHERE
                                [ContractStartDt] >= '$startDate'
                                AND [ContractStartDt] <= '$endDate'
                        )
            ) as Amount
                "));
    }

    public function scopeHasDownlineHistorical($query, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->whereExists(function ($query) use ($endDate) {
            $query->select(DB::raw(1))
            ->from('AgentHierarchy_Historical')
            ->whereColumn('AgentHierarchy_Historical.UplineAgentID', 'Agent.ID')
            ->where('EffDate', '<=', $endDate)
            ->where('ExpDate', '>=', $endDate)
            ->where("DownlineLevel", 1);
        });
    }



    //===================================
    // Helper Functions
    //===================================
}
