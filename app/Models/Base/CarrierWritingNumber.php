<?php

namespace App\Models\Base;

use Illuminate\Database\Eloquent\Model;

class CarrierWritingNumber extends Model
{
    protected $connection = 'sqlsrv';
    protected $table = "CarrierWritingNumber";
    protected $primaryKey = 'ID'; // or null
    protected $fillable = ["LastChangeBy", "CarrierID", "NPN", "WritingNumber"];
    public $timestamps = false;
    public static $allowedFilters = [];

    //===================================
    // Relationships
    //===================================

    public function carrier()
    {
        return $this->hasOne('App\Models\Base\Carrier', 'ID', 'CarrierID');
    }

    public function agent()
    {
        return $this->hasOne('App\Models\Agent', 'NPN', 'NPN');
    }

    public function agency()
    {
        return $this->hasOne('App\Models\Agent', 'DoingBusinessAsNPN', 'NPN');
    }

    


    //===================================
    // Attributes
    //===================================
    






    //===================================
    //Scopes
    //===================================
    
    public function scopeNumberExists($query, $npn, $writing_number, $carrier_id)
    {
        return $query->where("NPN", $npn)
            ->where("WritingNumber", $writing_number)
            ->where("CarrierID", $carrier_id);
    }

    public function scopeCarrierNumberExists($query, $npn, $carrier_id)
    {
        return $query->where("NPN", $npn)
            ->where("CarrierID", $carrier_id);
    }




    //===================================
    // Helper Functions
    //===================================
}
