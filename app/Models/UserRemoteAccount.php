<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\UserRemoteAccount
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount query()
 * @mixin \Eloquent
 * @property int $id
 * @property string $user_id
 * @property string|null $type
 * @property string|null $status
 * @property string|null $agent_code
 * @property string|null $opt_id
 * @property string|null $opt_pass
 * @property string|null $hq_metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereAgentCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereHqMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereOptId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereOptPass($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserRemoteAccount whereUserId($value)
 */
class UserRemoteAccount extends Model
{
    use HasFactory;
    protected $fillable = [
        'id',
        'user_id',
        'type',
        'status',
        'agent_code',
        'opt_id',
        'opt_pass',
        'hq_metadata',
        'created_at',
        'updated_at'
    ];

    const TYPE_UNLICENSED = 'unlicensed';
    const TYPE_LICENSED = 'licensed';

    const STATUS_CREATED = 'created';
    const STATUS_FAILED = 'failed';

}
