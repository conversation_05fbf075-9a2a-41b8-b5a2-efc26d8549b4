<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;
use App\Models\FormLookup;
use App\Models\User;

/**
 * App\Models\UserEntry
 *
 * @property string $id
 * @property string $user_id
 * @property string $form_field_id
 * @property string $value
 * @property int|null $sort
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\FormField $formField
 * @property-read \App\Models\FormSection|null $formPage
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereFormFieldId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserEntry whereValue($value)
 * @mixin \Eloquent
 * @property-read \App\Models\User $user
 */
class UserEntry extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_entries';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'value',
        'sort',
        'form_field_id',
    ];

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($entry) {

            //see if this entry has a corresponding field in the user table we want to update
            // $form_lookup = FormLookup::where('form_field_id', $entry->form_field_id)->whereNotNull('user_field')->first();
            
            if(!$form_lookup = FormLookup::where('form_field_id', $entry->form_field_id)->whereNotNull('user_field')->first())
                return;

            // get the user record that corresponds to this update
            if (!$user = User::find($entry->user_id))
                return;
        
            // Update the corresponding user field in the users table. This is to make searching/listing apps run faster.
            $user->update([
                $form_lookup->user_field => $entry->value,
            ]);
        });
    }

    public function user() {
        return $this->belongsTo(User::class);
    }

    public function formField() {
        return $this->belongsTo(FormField::class);
    }

    public function formPage()
    {
        return $this->hasOneThrough(FormSection::class, FormField::class);
    }
}
