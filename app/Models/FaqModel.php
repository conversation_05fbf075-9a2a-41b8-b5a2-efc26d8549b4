<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\FaqModel
 *
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel query()
 * @mixin \Eloquent
 * @property int $id
 * @property string|null $question
 * @property string|null $answer
 * @property string $content_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereAnswer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereContentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereQuestion($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaqModel whereUpdatedAt($value)
 */
class FaqModel extends Model
{
    use HasFactory;

    protected $table = 'frequently_ask_questions';
    protected $fillable = [
        'question', 'answer', 'content_id', 'created_at', 'updated_at'
    ];
}
