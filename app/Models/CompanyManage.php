<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyManage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_information_id'
    ];
    public $timestamps = false;
    public $incrementing = false;
    protected $keyType = 'string';
    protected $casts = [
        'id' => 'string'
    ];

    public function company_information()
    {
        return $this->belongsTo(CompanyInformation::class);
    }

}
