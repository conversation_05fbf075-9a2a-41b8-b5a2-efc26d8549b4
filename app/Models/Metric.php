<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\UsesUuid;

/**
 * App\Models\Metric
 *
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property string $application_type
 * @property string $type
 * @property string|null $model_type
 * @property string|null $model_id
 * @property string|null $model_value
 * @property bool $breakdown_by_upline
 * @property bool $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\MetricValue[] $metricValues
 * @property-read int|null $metric_values_count
 * @method static \Illuminate\Database\Eloquent\Builder|Metric newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Metric newQuery()
 * @method static \Illuminate\Database\Query\Builder|Metric onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Metric query()
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereApplicationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereBreakdownByUpline($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereModelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereModelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereModelValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Metric whereUpdatedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Metric withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Metric withoutTrashed()
 * @mixin \Eloquent
 */
class Metric extends Model
{
    use HasFactory, SoftDeletes, UsesUuid;

    protected $fillable = [
        'name',
        'description',
        'application_type',
        'type',
        'model_type',
        'model_id',
        'model_value',
        'breakdown_by_upline',
    ];

    const APPLICATION_TYPE_IN_PROGRESS  = 'In Progress';
    const APPLICATION_TYPE_COMPLETED    = 'Completed';
    const APPLICATION_TYPE_ABANDONED    = 'Abandoned';

    const MODEL_TYPE_FORM_FIELD         = 'App\Models\FormField';
    const MODEL_TYPE_USER_STATUS        = 'App\Models\UserStatus';
    const MODEL_TYPE_USER_INVITE        = 'App\Models\UserInvite';

    const MODEL_VALUE_USER_INVITE_BLOCKED   = 'Blocked';
    const MODEL_VALUE_USER_INVITE_SENT      = 'Sent';
    const MODEL_VALUE_USER_INVITE_ACCEPTED  = 'Accepted';
    
    public function metricValues()
    {
        return $this->hasMany(MetricValue::class);
    }
}
