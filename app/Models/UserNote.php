<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\UsesUuid;

/**
 * App\Models\UserNote
 *
 * @property string $id
 * @property string $recruit_id
 * @property string $form_section_id
 * @property bool $fixed
 * @property string $content
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $reviewer_id
 * @property int $form_section_sort
 * @property-read \App\Models\FormSection $formSection
 * @property-read \App\Models\User $recruit
 * @property-read \App\Models\User $reviewer
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereFixed($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereFormSectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereFormSectionSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereRecruitId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereReviewerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserNote whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class UserNote extends Model
{
    use HasFactory, UsesUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'reviewer_id',
        'recruit_id',
        'form_section_id',
        'form_section_sort',
        'fixed',
        'content',
    ];

    public function reviewer()
    {
        return $this->belongsTo(User::class);
    }

    public function recruit()
    {
        return $this->belongsTo(User::class);
    }

    public function formSection()
    {
        return $this->belongsTo(FormSection::class);
    }
}
