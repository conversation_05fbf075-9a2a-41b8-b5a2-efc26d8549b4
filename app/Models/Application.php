<?php

/**
 * AgentHierarchy connects an agent model to their upline and downlines. It's a pivot table that has a column for the downline(agent's) Id and an entry for each of their uplines, all the way up to the 120.
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Agent;
use DB;

class Application extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "Applications";
    protected $primaryKey = 'ID'; // or null
    public $timestamps = false;
    protected $fillable = [];
    protected $apvSumQuery = null;
    protected $hidden = [
       'laravel_through_key', 'pivot'
    ];

    protected $appends = ['CappedApv'];

    public static $allowedFilters = [];

    //===================================
    // Global Scopes
    //===================================
    protected static function boot()
    {
        parent::boot();
        //static::addGlobalScope('ArrangementToDate', function (Builder $builder) {
        //    $builder->whereNull('ArrangementToDate');
        //});
    }

    //===================================
    // Relationships
    //===================================
    
    public function agent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'AgentID');
    }

    public function baseShopOwner()
    {
        return $this->hasOneThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'AgentID')
            ->where('InBaseShopInd', true);
    }

    public function totalAgencyOwner()
    {
        return $this->hasManyThrough('App\Models\Agent', 'App\Models\AgentHierarchy', 'UplineAgentID', 'AgentID', 'AgentID', 'AgentID');
    }


    //===================================
    // Attributes
    //===================================

    public function getCappedApvAttribute()
    {
        return $this->APV > 7500 ? 7500 : $this->APV;
    }

    public function getAgentNameAttribute()
    {
        return $this->Agent;
    }

    //===================================
    //Scopes
    //===================================

    public function scopeForAgent($query, Agent $agent)
    {
        return $query->where('AgentID', $agent->AgentID);
    }

    public function scopeForBaseShopOwner($query, Agent $agent)
    {
        if ($agent->AgentID == 458424) {
            return $query;
        }
        return $query->join('AgentHierarchy', 'AgentHierarchy.DownlineAgentID', '=', 'Applications.AgentID')
                    ->where(function ($query) use ($agent) {
                        $query->where('UplineAgentID', $agent->AgentID)
                            ->orWhere('Applications.AgentID', $agent->AgentID);
                    })
                    ->where(function ($query) use ($agent) {
                        $query->where('InBaseShopInd', true)
                            ->orWhere('Applications.AgentID', $agent->AgentID);
                    });
    }

    public function scopeForTotalAgencyOwner($query, Agent $agent)
    {
        if ($agent->AgentID == 458424) {
            return $query;
        }
        return $query->join('AgentHierarchy', 'AgentHierarchy.DownlineAgentID', '=', 'Applications.AgentID')
                    ->where(function ($query) use ($agent) {
                        $query->where('UplineAgentID', $agent->AgentID)
                            ->orWhere('Applications.AgentID', $agent->AgentID);
                    });
    }

    public function scopeForNewWriter($query, $startDate, $endDate)
    {
        return $query->whereExists(function ($query) use ($startDate, $endDate) {
            $query->select(DB::raw(1))
                ->from(DB::raw('Applications a2'))
                ->where('Status', '=', 'Approved')
                ->whereRaw('a2.AgentID = Applications.AgentID') /// <<---- this is such bullshit and caused me issues forever with the Error converting data type nvarchar to bigint. ERROR
                ->havingRaw('MIN(ReceivedDate) >= ? AND MIN(ReceivedDate) <= ?', [$startDate, $endDate]);
        });
    }

    public function scopeSumApv($query, $type = null)
    {
        $query->select(DB::raw("
                COUNT(DISTINCT(AgentID)) as UniqueWriters,
                COUNT(Applications.ID) as NumApps,
                SUM(APV) as SumAPV, 
                SUM(CASE When APV > 7500 Then 7500 Else APV End)  as SumCappedAPV
            "));
        if ($type == 'baseshop' || $type == 'totalagency') {
            return $query->groupBy("AgentHierarchy.UplineAgentID");
        }
        return $query;
    }

    public function scopeSumUniqueWriters($query, $type = null)
    {
        $query->select(DB::raw("
                COUNT(DISTINCT(Applications.AgentID)) as UniqueWriters,
                COUNT(Applications.ID) as NumApps
            "));
        return $query;
    }

    public function scopeGroupByAgents($query)
    {
        $query->select(DB::raw("
                Applications.AgentID as AgentID,
                COUNT(Applications.ID) as NumApps
            "))->groupBy("Applications.AgentID");
        return $query;
    }

    public function scopeForDatePeriod($query, $startDate, $endDate)
    {
        return $query->where('ReceivedDate', '>=', date('Y-m-d 00:00:00', strtotime($startDate)))
                ->where('ReceivedDate', '<=', date('Y-m-d 23:59:59', strtotime($endDate)));
    }

    public function scopeApproved($query)
    {
        return $query->where('Status', 'Approved');
    }

    public function scopeForDirectDownlineHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }
        return $query->join('AgentHierarchy_Historical', function ($join) use ($agent, $endDate) {
            $join->on('AgentHierarchy_Historical.DownlineAgentID', '=', 'Applications.AgentID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('DownlineLevel', '=', 1)
                ->where('UplineAgentID', $agent->AgentID);
        });

        //'AgentHierarchy.DownlineAgentID', '=', 'Applications.AgentID')

        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('DownlineLevel', 1);
        });
    }

    public function scopeForBaseshopOwnerHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }

        return $query->join('AgentHierarchy_Historical', function ($join) use ($agent, $endDate) {
            $join->on('AgentHierarchy_Historical.DownlineAgentID', '=', 'Applications.AgentID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('InBaseShopInd', 1)
                ->where('UplineAgentID', $agent->AgentID);
        });

        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID)
                ->where('InBaseShopInd', 1);
        });
    }

    public function scopeForTotalAgencyOwnerHistorical($query, $agent, $endDate)
    {
        if ($endDate == null) {
            $endDate = date("Y-m-d H:i:s");
        }

        return $query->join('AgentHierarchy_Historical', function ($join) use ($agent, $endDate) {
            $join->on('AgentHierarchy_Historical.DownlineAgentID', '=', 'Applications.AgentID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('UplineAgentID', $agent->AgentID);
        });

        return $query->whereIn('AgentID', function ($query) use ($agent, $endDate) {
            $query->select(DB::raw('DISTINCT DownlineAgentID'))
                ->from('AgentHierarchy_Historical')
                //->join('Agent', 'AgentHierarchy_Historical.DownlineAgentID', '=', 'Agent.ID')
                ->where('EffDate', '<=', $endDate)
                ->where('ExpDate', '>=', $endDate)
                ->where('AgentHierarchy_Historical.UplineAgentID', $agent->AgentID);
        });
    }


   


    //===================================
    // Helper Functions
    //===================================

    /**
     * Some real magic happening here... courtesy of our boy Kameron Kowa.
     * Takes into account splits when figuring an apv total.
     * Can't use normal Laravel ORM, so start with calling this... then call additional methods below based on what you want to return, agent , baseShop etc.
     * Finally call getApvSums() to get the results.
     * @param  [type] $query [description]
     * @param  [type] $type  [description]
     * @return [type]        [description]
     */
    public function scopeMagicallySplitApvSums($query, $startDate, $endDate, $needs_vw_agent = true)
    {
        $params = array_merge(
            [
            $startDate, $endDate,
            $startDate, $endDate,
            $startDate, $endDate,
            ],
        );
        //significantly improves the query speed if this is not included... but by default we do.
        $inject_sql = "";
        if ($needs_vw_agent) {
            $inject_sql = "inner join vw_QuilityAgent qa ON qa.AgentID =  a.AgentID";
        }
        $sqlStr = "
            WITH agent AS (
                select AgentID, OptID, APV = sum( APV ),COUNT(DISTINCT(AgentID)) as UniqueWriters, COUNT(Applications.ID) as NumApps
                from Applications
                where Status LIKE '%Approved'
                    and ReceivedDate >= ?
                    and ReceivedDate < dateadd(day, 1, ?)
                group by AgentID, OptID
            ),
            agent_split AS (
                select AgentID, OptID, APV = sum( APV * ( 0.01 * ( 100 - [Percentage] ) ) )
                from Applications
                where Status LIKE '%Approved'
                    and ReceivedDate >= ?
                    and ReceivedDate < dateadd(day, 1, ?)
                group by AgentID, OptID
            ),
            agent2_remainder AS (
                select AgentID = Agent2AgentID, OptID = Agent2OptID ,APV = sum( APV * ( 0.01 * ( 100 - [Percentage] ) ) )
                from Applications
                where Status LIKE '%Approved'
                    and ReceivedDate >= ?
                    and ReceivedDate < dateadd(day, 1, ?)
                group by Agent2AgentID, Agent2OptID
            )
            select Apps=SUM(NumApps), APV = SUM( coalesce(a.APV, 0) - coalesce(a_s.APV, 0) + coalesce(a2_r.APV, 0)),
                Agent_APV = SUM(a.APV),Agent_Split_APV = SUM(a_s.APV),Agent2_Rem_APV = SUM(a2_r.APV),FinalAPV =SUM( coalesce(a.APV, 0) - coalesce(a_s.APV, 0) + coalesce(a2_r.APV, 0)),UniqueWriters=SUM(UniqueWriters),NumApps=SUM(NumApps)
            from agent a
                left outer join agent_split a_s on a_s.AgentID = a.AgentID
                left outer join agent2_remainder a2_r on a2_r.AgentID = a.AgentID
                $inject_sql
            ";
        $this->apvSumQuery = [
        [
            'sqlStr' => $sqlStr,
            'params' => $params
        ]
        ];
        return $this;
    }

    /**
     * Appends to above starting sql str and filters for agent.
     * @param  [type] $agent [description]
     * @return [type]        [description]
     */
    public function forAgentApvSums($agent)
    {
        //don't do this for the SFGAGENCY account.
        if ($agent->AgentCode == "SFG0000001") {
            return $this;
        }
        array_push(
            $this->apvSumQuery,
            [
            'sqlStr' => " where a.AgentID = ?",
            'params' => [$agent->AgentID]
            ]
        );
        return $this;
    }


    /**
     * Appends to above starting sql str and filters for agent.
     * @param  [type] $agent [description]
     * @return [type]        [description]
     */
    public function forBaseShopApvSums($agent)
    {
        array_push(
            $this->apvSumQuery,
            [
            'sqlStr' => " where (a.AgentID IN (
                    select aa.ID 
                    from dbo.Agent aa
                    inner join AgentHierarchy ha on ha.DownlineAgentID = aa.ID
                    where ha.UplineAgentID = ?
                        AND ha.InBaseShopInd = 1
                ) OR a.AgentID = ?)",
            'params' => [$agent->AgentID,$agent->AgentID]
            ]
        );
        return $this;
    }


    /**
     * Appends to above starting sql str and filters for agent.
     * @param  [type] $agent [description]
     * @return [type]        [description]
     */
    public function forTotalAgencyApvSums($agent)
    {
        array_push(
            $this->apvSumQuery,
            [
            'sqlStr' => " where (a.AgentID IN (
                    select aa.ID 
                    from dbo.Agent aa
                    inner join AgentHierarchy ha on ha.DownlineAgentID = aa.ID
                    where ha.UplineAgentID = ?
                ) OR a.AgentID = ?)",
            'params' => [$agent->AgentID, $agent->AgentID]
            ]
        );
        return $this;
    }


    /**
     * Appends to above starting sql str and filters for agent.
     * @param  [type] $agent [description]
     * @return [type]        [description]
     */
    public function forKeyLeaderApvSums($agent)
    {
        array_push(
            $this->apvSumQuery,
            [
            'sqlStr' => " where (a.AgentID IN (
                    select aa.ID 
                    from dbo.Agent aa
                    inner join AgentHierarchy ha on ha.DownlineAgentID = aa.ID
                    where ha.UplineAgentID = ?
                ) OR a.AgentID = ?)",
            'params' => [$agent->AgentID,$agent->AgentID]
            ]
        );
        return $this;
    }




    /**
     * After applying all the filters call this to get the
     * @return [type] [description]
     */
    public function getApvSums($groupby = false)
    {
        $sqlStr = '';
        $params = [];
        foreach ($this->apvSumQuery as $q) {
            $sqlStr = $sqlStr . $q['sqlStr'];
            $params = array_merge($params, $q['params']);
        }
        if ($groupby) {
            $sqlStr = str_replace('select Apps=SUM(NumApps),', 'select qa.AgentID, qa.AgentName, qa.PreferredName as PreferredAgentName, qa.AgentCode, qa.LeadershipLevel, Apps=SUM(NumApps),', $sqlStr);
            $sqlStr = $sqlStr . 'GROUP BY qa.AgentID, qa.AgentName, qa.AgentCode, qa.LeadershipLevel, qa.PreferredName ORDER BY APV DESC';
        }
        return collect(DB::connection('sqlsrv')->select(
            DB::raw($sqlStr),
            $params
        ));
    }


    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }
}
