<?php

/**
 * AgentHierarchy connects an agent model to their upline and downlines. It's a pivot table that has a column for the downline(agent's) Id and an entry for each of their uplines, all the way up to the 130.
 */
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Agent;

class AgentHierarchy extends BaseModel
{

    protected $connection = 'sqlsrv';
    protected $table = "AgentHierarchy";
    protected $primaryKey = 'ID'; // or null
    //public $incrementing = false;
    //protected $keyType = 'string';
    public $timestamps = false;
    protected $hidden = [
       'laravel_through_key', 'pivot'
    ];
    protected $fillable = ['LastChangeBy', 'UplineAgentID', 'UplineAgentCode', 'UplineOptID', 'DownlineAgentID', 'DownlineAgentCode', 'DownlineOptID', 'DownlineLevel', 'FullDownline', 'InBaseShopInd', 'CommissionUplineInd'];

    public static $allowedFilters = [];

    public function UplineAgent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'UplineAgentID');
    }

    public function DownlineAgent()
    {
        return $this->hasOne('App\Models\Agent', 'AgentID', 'DownlineAgentID');
    }

    public function scopeAgency($query, $OptID)
    {
        return $query->where('AgencyOwnerOptID', '=', $OptID)->where("DownlineLevel", 1);
    }

    public function scopeForAgencyOwnerID($query, $AgentID)
    {
        return $query->where('UplineAgentID', $AgentID);
    }

    public function scopeForDownlineAgentID($query, $AgentID)
    {
        return $query->where('DownlineAgentID', $AgentID);
    }

    public static function getAllowedFilters()
    {
        return self::$allowedFilters;
    }

    public function scopeSortDown($query)
    {
        return $query->OrderBy('DownlineLevel', 'ASC');
    }
}
