<?php

namespace App\Events\Onboarding;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class UnlicensedAccountCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $recruit;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $recruit)
    {
        $this->recruit = $recruit;
    }
}
