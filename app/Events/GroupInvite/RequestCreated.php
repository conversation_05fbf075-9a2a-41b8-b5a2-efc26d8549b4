<?php

namespace App\Events\UserInvite;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\UserInvite;

class RequestCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $group;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->group = $userInvite;
        $this->group = [
            'agent_name' => 'Test Agent',
            'group_name' => 'Test Group Name',
            'group_imo_name' => 'IMO Name',
            'group_size' => '5-10',
            // 'contact_info' => '555-123-1234',
            // 'preferred_times' => 'random text',
            'notes' => 'notes, details, etc',
        ];
    }
}
