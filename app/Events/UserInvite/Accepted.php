<?php

namespace App\Events\UserInvite;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\UserInvite;

class Accepted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $email;
    public $name;
    public $recruit_name;

    public $emailSecond;
    public $nameSecond;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($email, $name, $recruit_name, $emailSecond, $nameSecond)
    {
        $this->email = $email;
        $this->name = $name;
        $this->recruit_name = $recruit_name;

        $this->emailSecond = $emailSecond;
        $this->nameSecond = $nameSecond;
    }
}
