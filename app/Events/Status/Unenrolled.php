<?php

namespace App\Events\Status;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Agent;

class Unenrolled
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $recruit;
    public $ao;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $recruit, Agent $ao)
    {
        $this->recruit = $recruit;
        $this->ao = $ao;
    }
}
