<?php

namespace App\Events\Review\HO;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class Rejected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $recruit;
    public $note;
    public $felony_knockout;
    public $npn_rejected;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $recruit, $note, $felony_knockout = false, $npn_rejected = false)
    {
        $this->recruit = $recruit;
        $this->note = $note;
        $this->felony_knockout = $felony_knockout;
        $this->npn_rejected = $npn_rejected;
    }
}
