<?php
namespace App\Socket;

use Ratchet\ConnectionInterface;
use Ratchet\RFC6455\Messaging\MessageInterface;
use Ratchet\WebSocket\MessageComponentInterface;
use Illuminate\Support\Str;
use App\Models\User;
use Log;

class SocketHandler implements MessageComponentInterface
{
    protected $clients;
    protected $pages;
    protected $users;
    protected $appId;

    public function __construct()
    {
        $this->clients = [];
        $this->pages = [];
        $this->users = [];
        $this->appId = 'sfg-onboarding';
    }

    public function onOpen(ConnectionInterface $connection)
    {
        $connection->socketId = sprintf('%d.%d', random_int(1, 1000000000), random_int(1, 1000000000));
        $connection->app = new \stdClass();
        $connection->app->id = $this->appId;
        $this->clients[$connection->resourceId] = &$connection;
    }
    
    public function onClose(ConnectionInterface $connection)
    {
        $this->clearUser($connection);
    }

    public function onError(ConnectionInterface $connection, \Exception $exception)
    {
        $this->clearUser($connection);
        Log::error($exception);
    }

    public function onMessage(ConnectionInterface $connection, MessageInterface $message)
    {
        if (!$data = $this->messageDecode($message)) {
            $connection->close();
            return;
        }

        if ($data->event == 'login') {
            if (!$user = User::find($data->data)) {
                $connection->close();
                return;
            }
            if (!$user->can('review applications')) {
                $connection->close();
                return;
            }
            $this->users[$connection->resourceId] = $user;
            $connection->send(json_encode([
                'action' => 'loggedin',
                'data' => true,
            ]));
            $connection->send(json_encode([
                'action' => 'pagelist',
                'data' => $this->pages,
            ]));
        }

        if ($data->event == 'edit') {
            if (!isset($this->users[$connection->resourceId])) {
                return;
            }
            if (!$target = User::find($data->data)) {
                return;
            }
            $user = $this->users[$connection->resourceId];

            if(!isset($this->pages[$target->id])) {
                $this->pages[$target->id] = [
                    'uuid' => $user->id,
                    'name' => $user->name,
                    'conn' => $connection->resourceId,
                ];
                $this->updateUsers(json_encode([
                    'action' => 'pagelock',
                    'data' => [
                        'target' => $target->id,
                        'uuid' => $user->id,
                        'name' => $user->name,
                    ],
                ]));
            }
        }

        if ($data->event == 'endEdit') {
            if (!isset($this->users[$connection->resourceId])) {
                return;
            }
            if (!$target = User::find($data->data)) {
                return;
            }
            if(!isset($this->pages[$target->id])) {
                return;
            }
            
            $this->updateUsers(json_encode([
                'action' => 'pageunlock',
                'data' => $target->id,
            ]));
            unset($this->pages[$target->id]);
        }
    }

    public function clearUser(ConnectionInterface $connection) 
    {
        unset($this->clients[$connection->resourceId]);
        unset($this->users[$connection->resourceId]);

        foreach ($this->pages as $pageId => $pageData) {
            if ($pageData['conn'] == $connection->resourceId) {
                $this->updateUsers(json_encode([
                    'action' => 'pageunlock',
                    'data' => $pageId,
                ]));
                unset($this->pages[$pageId]);
            }
        }

    }

    public function updateUsers($message) {
        foreach ($this->clients as $userConnection) {
            $userConnection->send($message);
        }
    }

    public function messageDecode($message) {
        
        try {

            # check if json is valid
            $data = json_decode($message);
            if (is_null($data)) {
                return false;
            }

            # check for properties
            if (!isset($data->event, $data->data)) {
                return false;
            }

            # check that data payload is valid uuid
            if (!Str::isUuid($data->data)) {
                return false;
            }

        } catch (Exception $exception) {

            Log::error($exception);
            return false;

        }

        return $data;
    }
}