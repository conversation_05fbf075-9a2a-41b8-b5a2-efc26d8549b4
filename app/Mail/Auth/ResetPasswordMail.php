<?php

namespace App\Mail\Auth;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class ResetPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $user, $token)
    {
        $this->data = [
            'user' => $user,
            'recruiter' => $user->invitedBy,
            'url' => url("/auth/password/reset?token={$token}&email={$user->email}"),
            'count' => config('auth.passwords.'.config('auth.defaults.passwords').'.expire'),
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.auth.reset_password', $this->data)
            ->subject('Reset Password Request');
    }
}
