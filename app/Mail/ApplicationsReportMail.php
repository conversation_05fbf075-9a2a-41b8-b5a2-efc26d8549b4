<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ApplicationsReportMail extends Mailable
{
    use Queueable, SerializesModels;

    private $file_path;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($file_path, $subject = '')
    {
        $this->file_path = $file_path;
        $this->subject = $subject;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // return $this->markdown('emails.report.applications', $this->data)
        //     ->subject("Daily Applications Report");

        $subject = 'Daily Applications Report - ' . now()->format('Y-m-d-H-i-s');
        if($this->subject != '')
            $subject = $this->subject;

        return $this->subject($subject)
            ->markdown('emails.report.applications') // Replace with your email view
            ->attach($this->file_path);
            
    }
}
