<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class PrincipalOnboardingMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name, $code, $invited_by, $message, $agency_owner_name)
    {
        $this->data = [
            'name' => $name,
            'code' => $code,
            'invited_by' => $invited_by,
            'message' => $message,
            'agency_owner_name' => $agency_owner_name
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.principal_onboarding', $this->data)
            ->subject("Principal Invitation Created");
    }
}
