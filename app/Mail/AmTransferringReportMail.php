<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class AmTransferringReportMail extends Mailable
{
    use Queueable, SerializesModels;

    private $file_path;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($file_path)
    {
        $this->file_path = $file_path;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // return $this->markdown('emails.report.applications', $this->data)
        //     ->subject("Daily Applications Report");

        $subject = 'Daily Advanced Markets and Transferring Agents Report - ' . now()->format('Y-m-d-H-i-s');

        return $this->subject($subject)
            ->markdown('emails.report.am_transferring')
            ->attach($this->file_path);
            
    }
}
