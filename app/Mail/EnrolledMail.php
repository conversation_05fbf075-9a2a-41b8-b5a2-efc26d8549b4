<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EnrolledMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($email, $name, $recruit_name, $recruiter_name)
    {
        $this->data = [
            'email' => $email,
            'name' => $name,
            'recruit_name' => $recruit_name,
            'recruiter_name' => $recruiter_name
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.status.enrolled', $this->data)
            ->subject("Your Candidate has Enrolled in a Pre-Licensing Course");
    }
}
