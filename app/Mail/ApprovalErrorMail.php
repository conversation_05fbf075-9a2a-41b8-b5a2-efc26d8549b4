<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ApprovalErrorMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($staff_name, $recruit_name, $error_message, $application_url)
    {
        $this->data = [
            'staff_name' => $staff_name,
            'recruit_name' => $recruit_name,
            'error_message' => $error_message,
            'application_url' => $application_url,
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.approvalerror', $this->data)
            ->subject("Application approval error: {$this->data['recruit_name']}");
    }
}
