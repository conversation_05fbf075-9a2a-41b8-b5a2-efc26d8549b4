<?php

namespace App\Mail\Transport;

use Symfony\Component\Mailer\Transport\TransportInterface;
use Symfony\Component\Mailer\SentMessage;
use Symfony\Component\Mailer\Envelope;
use Symfony\Component\Mime\RawMessage;
use Symfony\Component\Mime\MessageConverter;
use App\Models\StoredEmail;

class LoggedTransport implements TransportInterface
{
    public function send(RawMessage $message, ?Envelope $envelope = null): ?SentMessage
    {
        // Convert the RawMessage to an Email instance
        $email = MessageConverter::toEmail($message);

        // Extract email addresses from Address objects
        $toAddresses = $email->getTo();
        $fromAddresses = $email->getFrom();

        $toEmails = [];
        foreach ($toAddresses as $address) {
            $toEmails[] = $address->getAddress();
        }

        $fromEmails = [];
        foreach ($fromAddresses as $address) {
            $fromEmails[] = $address->getAddress();
        }

        // Save the email to the database
        StoredEmail::create([
            'to' => implode(', ', $toEmails),
            'from' => implode(', ', $fromEmails),
            'subject' => $email->getSubject(),
            'body' => $email->getHtmlBody() ?? $email->getTextBody(),
            'sent_at' => now(),
        ]);

        // Create and return a SentMessage instance since we're not actually sending the email
        $sentMessage = new SentMessage($email, $envelope ?? new Envelope($email->getFrom()[0], $email->getTo()));

        return $sentMessage;
    }

    public function __toString(): string
    {
        return 'logged';
    }
}