<?php

namespace App\Mail\GroupInvites;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestApprovedMail extends Mailable
{
    use Queueable, SerializesModels;

    private $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $this->data['link'] = sprintf('<a href="%s">%s</a>', $this->data['group_url'], $this->data['group_url']);
        return $this->markdown('emails.group_invites.group_invite_approved')
            ->with([
                'data' => $this->data
            ])
            ->subject("Group Onboarding Request Approved: ".$this->data['group_name']);
    }

}
