<?php

namespace App\Mail\Review\HO;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class SubmittedPrincipalApplicationMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $data;

    /**
     * Create a new message instance.
     *
     * @param User $recruit
     * @return void
     */
    public function __construct(User $recruit)
    {
        $this->data = [
            'recruit' => $recruit,
            'recruiter' => $recruit->invitedBy,
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.review.ho.submitted_principal', $this->data)
            ->subject("New Principal Application Submitted");
    }
}
