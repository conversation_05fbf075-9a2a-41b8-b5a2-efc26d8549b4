<?php

namespace App\Mail\Review\HO;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class RejectedApplicationToRecruitMail extends Mailable
{
    use Queueable, SerializesModels;

    protected $data;
    
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $recruit, $felony_knockout = false, $npn_rejected = false)
    {
        $this->data = [
            'recruit' => $recruit,
            'recruiter' => $recruit->invitedBy,
            'content' => 'Unfortunately, we cannot move forward with your application. Please reach out to your AO for details.'
        ];
        if($felony_knockout)
            $this->data['content'] = 'Due to the nature of <PERSON><PERSON><PERSON>’s business, including the financial and family (including minor children) information that our insurance agents have access to, we do not contract with prospective insurance agents that have been convicted of any of the following felonies: Child Abuse, Fraud, and/or Financial Crimes';
        if($npn_rejected)
            $this->data['content'] = "We've identified that you're affiliated with one of our partners. We're unable to move forward with your application at this time. Please reach out to your upline for further assistance.";
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.review.ho.rejected_to_recruit', $this->data)
            ->subject("Application rejected by Home Office");
    }
}
