<?php

namespace App\Mail\Review\AO;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;
use App\Models\Agent;

class SubmittedApplicationToAoMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $recruit, Agent $agency_owner)
    {
        $this->data = [
            'recruit' => $recruit,
            'recruiter' => $recruit->invitedBy,
            'agency_owner' => $agency_owner
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $x = 0;
        return $this->markdown('emails.review.ao.submitted_to_ao', $this->data)
            ->subject("Your Downline's Candidate Submitted an Application");
    }
}
