<?php

namespace App\Mail\Review\AO;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\User;

class SubmittedApplicationUnlicensedToRecruiterMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(User $recruit)
    {
        $this->data = [
            'recruit' => $recruit,
            'recruiter' => $recruit->invitedBy,
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.review.ao.submitted_unlicense_to_recruiter', $this->data)
            ->subject("Application arrived from an unlicensed candidate");
    }
}
