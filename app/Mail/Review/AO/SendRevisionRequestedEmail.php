<?php

namespace App\Mail\Review\AO;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SendRevisionRequestedEmail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($recruit, $upline, $note)
    {
        $this->data = [
            'recruit' => $recruit,
            'upline' => $upline,
            'note' => $note
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.review.ao.returned_to_recruit_revisions', $this->data)
            ->subject("Recruit Application Sent Back For Revisions");
    }
}
