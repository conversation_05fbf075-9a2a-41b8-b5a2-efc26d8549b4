<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InviteBlockedNotification extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($email, $name, $recruit_name)
    {
        $this->data = [
            'email' => $email,
            'name' => $name,
            'recruit_name' => $recruit_name
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.inviteblockednotification', $this->data)
            ->subject("You have attempted to invite another agent's recruit.");
    }
}
