<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OnboardingMail extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($name, $code, $invited_by, $message, $agency_owner_name)
    {
        $this->data = [
            'name' => $name,
            'code' => $code,
            'invited_by' => $invited_by,
            'message' => $message,
            'agency_owner_name' => $agency_owner_name
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.onboarding', $this->data)
            ->subject("Invitation Created");
    }
}
