<?php

namespace App\Mail\admin;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CreateHqLiteAccountEmail extends Mailable
{
    use Queueable, SerializesModels;

    protected $data;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($email, $name, $password)
    {
        $this->data = [
            'email'     => $email,
            'name'      => $name,
            'password'  => $password,
            'url'       => config('app.hq_lite')
        ];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.agent.hqlite_account',$this->data)->subject('HQ Lite access');
    }
}
