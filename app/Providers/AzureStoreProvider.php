<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\App;
use Illuminate\Http\File;

class AzureStoreProvider extends ServiceProvider
{

    const SUBFOLDER = 'onboarding';

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    public static function buildPath($sub_path = null) {

        if (is_null($sub_path)) {
            return false;
        }

        # mimic folder layout that pre-exists in azure store
        $environment = App::environment();

        switch ($environment) {
            case "dev":
            case "local":
                $base_folder = 'dev';
                break;
            case "staging":
            case "production":
                $base_folder = $environment;
                break;
            case "default":
                return false;
                break;
        }

        return "{$base_folder}/".self::SUBFOLDER."/{$sub_path}";

    }

    public static function moveLocalFileToStorage($remote_folder_path, $path_to_local_file) {
        
        if (!$remote_path = self::buildPath($remote_folder_path)) {
            return false;
        }

        if (!Storage::disk('azure')->exists($remote_path)) {
            Storage::disk('azure')->makeDirectory($remote_path);
        }

        if (!\Illuminate\Support\Facades\File::exists($path_to_local_file)) {
            return false;
        }

        Storage::disk('azure')->putFile($remote_path, new File($path_to_local_file));
        return true;

    } 

    public static function copyRemoteFileToLocal($remote_file, $local_path, $local_filename) {
        if (!Storage::disk('azure')->exists($remote_file)) {
            return false;
        }

        if (!\Illuminate\Support\Facades\File::exists($path_to_local_file)) {
            return false;
        }

        $file = Storage::disk('azure')->get($remote_file);

        if (!Storage::exists($local_path)) {
            Storage::makeDirectory($local_path);
        }

        Storage::put("{$local_path}/{$local_filename}", $file);

        return true;
    } 

}
