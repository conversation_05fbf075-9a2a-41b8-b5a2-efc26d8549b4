<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use App\Http\Resources\UserTaskCollection;
use App\Http\Resources\UserTaskResource;
use App\Models\UserTask;
use App\Models\User;
use Log;

class UserTaskProvider extends ServiceProvider
{
    public $targetId;
    public $type;
    public $user;
    public $description;

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    public function __construct()
    {
        // override
    }

    public static function get($user)
    {
        $tasks = $user->tasks();
        // if (!config('tasks.show_completed', false)) {
        //     $tasks = $tasks->where('completed', 0);
        // }
        return new UserTaskCollection($tasks->with('target')->get());
    }

    public static function completeById($taskId)
    {
        if (!$task = UserTask::find($taskId)) {
            return false;
        }
        $task->completed = 1;
        return ($task->save() ? new UserTaskResource($task) : false);
    }

    public static function complete($task)
    {
        if (!$task) {
            return false;
        }
        $task->completed = 1;
        return ($task->save() ? new UserTaskResource($task) : false);
    }

    public static function create($type)
    {
        $builder = new UserTaskProvider;
        $builder->targetId = false;
        $builder->type = false;
        $builder->user = false;
        $builder->description = null;

        // check task type
        if (!in_array($type, config('tasks.task_types'))) {
            Log::error("MY TASKS ERROR: Unknown task type of {$type}");
            return false;
        }

        $builder->type = $type;
        return $builder;
    }

    public function for($user)
    {
        if (!is_subclass_of($user, 'Illuminate\Database\Eloquent\Model')) {
            Log::error("MY TASKS ERROR: Unknown variable entered in place of user model.");
            return false;
        }
        $this->user = $user;
        return $this;
    }

    public function forId($userId)
    {
        if (!$user = User::find($userId)) {
            Log::error("MY TASKS ERROR: Unknown user id entered of {$userId}.");
            return false;
        }
        $this->user = $user;
        return $this;
    }

    public function target($targetId)
    {
        if (!Str::isUuid($targetId)) {
            Log::error("MY TASKS ERROR: Invalid UUID passed as User Task target.");
            return false;
        }
        $this->targetId = $targetId;
        return $this;
    }

    public function description($description)
    {
        $this->description = $description;
        return $this;
    }

    public function save()
    {
        $task = new UserTask;
        $task->user_id = $this->user->id;
        $task->type = $this->type;
        $task->description = $this->description;
        $task->completed = 0;

        if ($this->targetId != false) {
            $task->target_id = $this->targetId;
        }

        return ($task->save() ? new UserTaskResource($task) : false);
    }

    public function find($active = true)
    {
        $criteria = [];
        if($this->type)
            $criteria['type'] = $this->type;
        if($this->user)
            $criteria['user_id'] = $this->user->id;
        if($this->targetId)
            $criteria['target_id'] = $this->targetId;

        if($active)
            $criteria['completed'] = 0;

        return UserTask::where($criteria)->first();
    }
}
