<?php

namespace App\Providers;

use App\Models\FormLookup;
use App\Models\Metric;
use Illuminate\Support\ServiceProvider;
use App\Models\UserStatus;

class MetricBuilderProvider extends ServiceProvider
{
    private $name;
    private $description;
    private $applicationType;
    private $modelType;

    private $logger;
    
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
    }

    public function __construct() 
    {
        $this->logger = app('logger.metric');
        $this->name = '';
        $this->description = '';
        $this->applicationType = '';
        $this->modelType = '';
    }

    public function withName($name = '', $description = '')
    {
        $this->name = $name;
        $this->description = $description;
        return $this;
    }

    public function withApplicationType($applicationType)
    {
        $this->applicationType = $applicationType;
        return $this;
    }

    public function withModelType($modelType)
    {
        $this->modelType = $modelType;
        return $this;
    }
    
    public function addByFormLookup($slug)
    {
        if(empty($this->applicationType)) {
            $this->logger->error("MetricProvider::addByFormLookup: Application type not set");
            return;
        }

        $this->modelType = Metric::MODEL_TYPE_FORM_FIELD;
        $formLookup = FormLookup::where('field_slug', $slug)->first();
        if(!$formLookup || !$formLookup->formField || !$formLookup->formField->formSection) {
            $this->logger->error("MetricProvider::addByFormLookup: Cannot find form field by lookup - " . $slug);
            return;
        }

        return Metric::create([
            'name' => $this->name ?: "Metric-FormField-{$formLookup->formField->formSection->label}-{$formLookup->formField->label}-{$this->applicationType}",
            'description' => $this->description,
            'application_type' => $this->applicationType,
            'type' => '',
            'model_type' => $this->modelType,
            'model_id' => $formLookup->formField->id,
            'model_value' => ''
        ]);
    }

    public function addByUserStatus($slug = '')
    {
        if(empty($this->applicationType)) {
            $this->logger->error("MetricProvider::addByFormLookup: Application type not set");
            return;
        }

        $this->modelType = Metric::MODEL_TYPE_USER_STATUS;
        $user_status = UserStatus::where('slug', $slug)->first();

        if($user_status) {
            return Metric::create([
                'name' => $this->name ?: "Metric-UserStatus-{$user_status->name}-{$this->applicationType}",
                'description' => $this->description,
                'application_type' => $this->applicationType,
                'type' => '',
                'model_type' => $this->modelType,
                'model_id' => $user_status->id,
                'model_value' => ''
            ]);
        }

        return Metric::create([
            'name' => $this->name ?: "Metric-UserStatus-All-{$this->applicationType}",
            'description' => $this->description,
            'application_type' => $this->applicationType,
            'type' => '',
            'model_type' => $this->modelType,
            'model_id' => null,
            'model_value' => null
        ]);
    }

    public function addByUserInviteStatus($status = '')
    {
        $this->modelType = Metric::MODEL_TYPE_USER_INVITE;

        return Metric::create([
            'name' => $this->name ?: "Metric-UserInvite {$status}",
            'description' => $this->description,
            'application_type' => $this->applicationType,
            'type' => '',
            'model_type' => $this->modelType,
            'model_id' => null,
            'model_value' => $status
        ]);
    }
}
