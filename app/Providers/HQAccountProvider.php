<?php

namespace App\Providers;

use App\CustomProviders\HQAccountServiceProvider;
use Illuminate\Support\ServiceProvider;


class HQAccountProvider extends ServiceProvider
{
   
       /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
     
        $this->app->bind('HQAccounts', function($app) {
            return new HQAccountServiceProvider();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
