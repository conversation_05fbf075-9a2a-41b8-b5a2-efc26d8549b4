<?php

namespace App\Providers;

use App\Models\Agent;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;

class AgentProvider extends ServiceProvider
{
    public $user;

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    public function __construct() 
    {
        // override
        $this->user = null;
    }

    public static function getOneWithRole($role) 
    {
        return User::whereHas(
            'roles', function($q) use($role){
                $q->where('name', $role);
            }
        )->first();
    }

    public static function getByRole($role) 
    {
        return User::whereHas(
            'roles', function($q) use($role){
                $q->where('name', $role);
            }
        )->get();
    }

    public function for($user) 
    {
        $this->user = $user;
        return $this;
    }
    /**
     * Sets the status of the user and creates a status history record
     * 
     * @param string $status The status slug to set
     * @param int $trigger_id The user ID of the person who triggered the status change
     * @param string $note Optional note to include with the status change
     * @return bool True if status was set successfully, false otherwise
     */

    public function setStatus($status, $trigger_id, $note = '')
    {
        $newStatus = UserStatus::where('slug', $status)->first();
        
        if($newStatus && $this->user){
            $this->user->update([
                'user_status_id' => $newStatus->id
            ]);

            $lastHistory = $this->user->statusHistory()->first();
            if($lastHistory && $lastHistory->user_status_id == $newStatus->id) {
                $lastHistory->update([
                    'trigger_id' => $trigger_id,
                    'note' => $note
                ]);
            }

            return true;
        }

        return false;
    }

    public static function createPlaceholderUserForAgent(Agent $agent)
    {
        $user = new User;
        $user->password = Hash::make('placeholder');
        $user->email = $agent->AgentEmail;
        $user->name = $agent->AgentName;
        $user->agent_code = $agent->AgentCode;
        $user->auth0_metadata = json_encode([
            User::AUTH0_KEY_METADATA => [
                'AgentCode' => $agent->AgentCode
            ]
        ]);
        $user->placeholder = 1;
        
        if($user->save()) {
            $user->assignRole(User::ROLE_TYPE_AGENCY_OWNER);
            return $user->fresh();
        }

        return null;
    }
}
