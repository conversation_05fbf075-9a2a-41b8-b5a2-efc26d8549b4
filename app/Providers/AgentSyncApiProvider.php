<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7;
use Log;

class AgentSyncApiProvider extends ServiceProvider
{
    const VERSION = 'v54.0';

    protected $guzzle;
    protected $apiUrl;
    protected $clientKey;
    protected $clientSecret;
    protected $securityToken;
    protected $username;
    protected $password;
    protected $bearerToken;
    
    /**
     * Constructor.
     *
     * @return void
     */
    function __construct()
    {
        // pull env/config values into class
        $this->apiUrl = config('agentsync.apiUrl');
        $this->clientKey = config('agentsync.clientKey');
        $this->clientSecret = config('agentsync.clientSecret');
        $this->securityToken = config('agentsync.securityToken');
        $this->username = config('agentsync.username');
        $this->password = config('agentsync.password');

        // set default bearer as null
        $this->bearerToken = null;

        // start our guzzle instance.
        $this->guzzle = new \GuzzleHttp\Client([
            'base_uri' => $this->apiUrl,
            // 'http_errors' => false,
        ]);
    }

    /**
     * Check if json string is valid.
     *
     * @return bool
     */
    private function isJson($string) {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     * Authorize our connection to the AgentSync API.
     *
     * @return bool
     */
    private function authorize() 
    {
        // send authorization request
        $response = $this->guzzle->request('POST', '/services/oauth2/token', [
            'form_params' => [
                'grant_type'    => 'password',
                'client_id'     => $this->clientKey,
                'client_secret' => $this->clientSecret,
                'username'      => $this->username,
                'password'      => $this->password.$this->securityToken,
            ]
        ]);

        // check if response can be processed
        if (!$data = $this->processResponse($response)) {
            Log::error("AGENT SYNC AUTHORIZATION FAILED: Process response method returned false.");
            return false;
        }

        // check if access token exists
        if (!isset($data['access_token'])) {
            Log::error("AGENT SYNC AUTHORIZATION FAILED: JSON response did not include access token.");
            return false;
        }

        // set class bearer token
        $this->bearerToken = $data['access_token'];
        return true;
    }

    /**
     * Send a generic API request.
     *
     * @return string|bool
     */
    private function sendRequest($requestType = 'POST', $uri, $payload = [])
    {
        // check for bearer token and authorize
        if (is_null($this->bearerToken)) {
            if (!$this->authorize()) {
                Log::error("AGENT SYNC API REQUEST FAILED: Unable to authorize with AgentSync API.");
                return false;
            }
        }

        // build request payload
        $dataToSend = [
            'headers' => [
                'Authorization' => "Bearer {$this->bearerToken}",
                'Accept'        => 'application/json',
            ],
        ];

        // check for replacement string
        if (stristr($uri, '%s') !== false) {
            $uri = sprintf($uri, self::VERSION);
        }

        // check if payload is a json string instead of array
        if (is_string($payload) && $this->isJson($payload)) {
            $dataToSend['json'] = json_decode($payload);
        }

        // check if we should append form parameters instead of json
        if (is_array($payload) && sizeof($payload) > 0) {
            $dataToSend['form_params'] = $payload;
        }

        // send request and process data
        try {
            
            $response = $this->guzzle->request($requestType, $uri, $dataToSend);

        } catch (ClientException $e) {

            Log::error("AGENT SYNC API REQUEST FAILED! Error output below.");
            Log::error(Psr7\Message::toString($e->getRequest()));
            Log::error(Psr7\Message::toString($e->getResponse()));
            return false;

        }

        return $this->processResponse($response);
    }

    /**
     * Process a Guzzle request and deserialize json.
     *
     * @return string|bool
     */
    private function processResponse($response) 
    {
        // fetch  status code
        $code = $response->getStatusCode();

        // check status code
        if ($code >= 300) {
            $reason = $response->getReasonPhrase();
            Log::error("AGENT SYNC REQUEST FAILED: {$code} {$reason}.");
            return false;
        }

        // status is 200 OK, fetch body
        $body = $response->getBody();

        // check for valid json
        if (!$this->isJson($body)) {
            Log::error("AGENT SYNC REQUEST FAILED: Server did not return valid JSON.");
            return false;
        }

        // convert json to assoc array
        return json_decode($body, true);
    }

    /**
     * Get available API version data.
     *
     * @return array|bool
     */
    public function listApiVersions()
    {
        return $this->sendRequest('GET', '/services/data');
    }

    /**
     * Get available API resources.
     *
     * @return array|bool
     */
    public function listAvailableResources()
    {
        return $this->sendRequest('GET', '/services/data/%s');
    }

    /**
     * Get available API organization limits.
     *
     * @return array|bool
     */
    public function listOrganizationLimits()
    {
        return $this->sendRequest('GET', '/services/data/%s/limits');
    }

    /**
     * List objects in instance.
     *
     * @return array|bool
     */
    public function listAllObjectTypes()
    {
        return $this->sendRequest('GET', '/services/data/%s/sobjects');
    }

    /**
     * Describe object.
     *
     * @return array|bool
     */
    public function describeObject($objectType)
    {
        return $this->sendRequest('GET', "/services/data/%s/sobjects/{$objectType}/describe");
    }

    /**
     * Run an SOQL search. 
     * Urlencoding sometimes tricks sprintf so we append version by hand here.
     *
     * @return array|bool
     */
    public function genericSearch($searchString)
    {
        return $this->sendRequest('GET', '/services/data/'.self::VERSION.'/query/?q='.urlencode($searchString));
    }

    /**
     * Get Contact object by Salesforce id.
     *
     * @return array|bool
     */
    public function getContactById($id)
    {
        return $this->sendRequest('GET', "/services/data/%s/sobjects/Contact/{$id}");
    }

    /**
     * Get Contact object by Agent Code.
     *
     * @return array|bool
     */
    public function getContactByAgentCode($agentCode)
    {
        $agentCodeField = config('agentsync.insertKeys.AgentCode', 'Agent_Code__c');
        return $this->sendRequest('GET', "/services/data/%s/sobjects/Contact/{$agentCodeField}/{$agentCode}");
    }


    /**
     * Create a new Contact object.
     *
     * @return array|bool
     */
    public function createContact($json)
    {
        return $this->sendRequest('POST', '/services/data/%s/sobjects/Contact/', $json);
    }

    /**
     * Update a Contact object by Salesforce id.
     *
     * @return array|bool
     */
    public function updateContactById($id, $json)
    {
        return $this->sendRequest('PATCH', "/services/data/%s/sobjects/Contact/{$id}", $json);
    }

    /**
     * Update a Contact object by Agent Code.
     *
     * @return array|bool
     */
    public function updateContactByAgentCode($agentCode, $json)
    {
        $agentCodeField = config('agentsync.insertKeys.AgentCode', 'Agent_Code__c');
        return $this->sendRequest('PATCH', "/services/data/%s/sobjects/Contact/{$agentCodeField}/{$agentCode}", $json);
    }

    /**
     * Get Account object by Salesforce id.
     *
     * @return array|bool
     */
    public function getAccountById($id)
    {
        return $this->sendRequest('GET', "/services/data/%s/sobjects/Account/{$id}");
    }

    /**
     * Find an Account object by Agent Code.
     *
     * @return array|bool
     */
    public function getAccountByAgentCode($agentCode)
    {
        $agentCodeField = config('agentsync.insertKeys.AgentCode', 'Agent_Code__c');
        return $this->sendRequest('GET', "/services/data/%s/sobjects/Account/{$agentCodeField}/{$agentCode}");
    }

    /**
     * Create a new Account object.
     *
     * @return array|bool
     */
    public function createAccount($json)
    {
        return $this->sendRequest('POST', '/services/data/%s/sobjects/Account/', $json);
    }

    /**
     * Update an Account object by Salesforce id.
     *
     * @return array|bool
     */
    public function updateAccountById($id, $json)
    {
        return $this->sendRequest('PATCH', "/services/data/%s/sobjects/Account/{$id}", $json);
    }

    /**
     * Update an Account object by Agent Code.
     *
     * @return array|bool
     */
    public function updateAccountByAgentCode($agentCode, $json)
    {
        $agentCodeField = config('agentsync.insertKeys.AgentCode', 'Agent_Code__c');
        return $this->sendRequest('PATCH', "/services/data/%s/sobjects/{$agentCodeField}/{$agentCode}", $json);
    }
}
