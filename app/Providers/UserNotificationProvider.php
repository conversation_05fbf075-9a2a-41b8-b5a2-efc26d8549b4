<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;
use App\Http\Resources\UserNotificationCollection;
use App\Http\Resources\UserNotificationResource;
use App\Models\UserNotification;
use App\Models\User;
use Log;

class UserNotificationProvider extends ServiceProvider
{
    public $targetId;
    public $notification;
    public $user;

    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    public function __construct()
    {
        // override
    }

    public static function get($user)
    {
        $notifications = $user->notifications();
        // if (!config('tasks.show_completed', false)) {
        //     $tasks = $tasks->where('completed', 0);
        // }
        return new UserNotificationCollection($notifications->with('target')->get());
    }

    public static function create($notification)
    {
        $builder = new UserNotificationProvider;
        $builder->targetId = false;
        $builder->notification = $notification;
        $builder->user = false;

        return $builder;
    }

    public function for($user)
    {
        if (!is_subclass_of($user, 'Illuminate\Database\Eloquent\Model')) {
            Log::error("MY TASKS ERROR: Unknown variable entered in place of user model.");
            return false;
        }
        $this->user = $user;
        return $this;
    }

    // public function forId($userId)
    // {
    //     if (!$user = User::find($userId)) {
    //         Log::error("MY TASKS ERROR: Unknown user id entered of {$userId}.");
    //         return false;
    //     }
    //     $this->user = $user;
    //     return $this;
    // }

    public function target($targetId)
    {
        if (!Str::isUuid($targetId)) {
            Log::error("MY TASKS ERROR: Invalid UUID passed as User Task target.");
            return false;
        }
        $this->targetId = $targetId;
        return $this;
    }

    public function notification($notification)
    {
        $this->notification = $notification;
        return $this;
    }

    public function save()
    {
        $notification = new UserNotification;
        $notification->user_id = $this->user->id;
        $notification->notification = $this->notification;
        
        if ($this->targetId != false) {
            $notification->target_id = $this->targetId;
        }

        return ($notification->save() ? new UserNotificationResource($notification) : false);
    }
}
