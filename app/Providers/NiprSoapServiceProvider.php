<?php

namespace App\Providers;

use App\Services\NiprSoapService;
use Illuminate\Support\ServiceProvider;

class NiprSoapServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(NiprSoapService::class, function ($app) {
            return new NiprSoapService();
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
