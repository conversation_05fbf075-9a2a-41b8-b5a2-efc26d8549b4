<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Providers\AgentSyncApiProvider;
use Log;

class AgentSyncBuildProvider extends ServiceProvider
{
    public $type;
    protected $keys;
    protected $data;
    protected $api;
    protected $identType;
    protected $identId;

    function __construct() 
    {
        $this->keys = config('agentsync.insertKeys', array());
        $this->data = new \stdClass;
        $this->api = new AgentSyncApiProvider;
        $this->type = null;
        $this->identType = null;
        $this->identId = null;
    }

    /**
     * Static query function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function query($soql) 
    {
        $api = new AgentSyncApiProvider;
        return $api->genericSearch($soql);
    }

    /**
     * Static describe function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function describe($type) 
    {
        $api = new AgentSyncApiProvider;
        return $api->describeObject($type);
    }

    /**
     * Static list api versions function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function listApiVersions() 
    {
        $api = new AgentSyncApiProvider;
        return $api->listApiVersions();
    } 

    /**
     * Static list organization limits function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function listOrganizationLimits() 
    {
        $api = new AgentSyncApiProvider;
        return $api->listOrganizationLimits();
    }     

    /**
     * Static list resources function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function listResources() 
    {
        $api = new AgentSyncApiProvider;
        return $api->listAvailableResources();
    }    

    /**
     * Static list objects function -- returns results directly.
     *
     * @return stdClass|bool
     */
    public static function listObjects() 
    {
        $api = new AgentSyncApiProvider;
        return $api->listAllObjectTypes();
    } 

    /**
     * Initial static fetch function -- starts syntactic sugar.
     *
     * @return stdClass|bool
     */
    public static function fetch($type) 
    {
        $builder = new AgentSyncBuildProvider;
        
        switch ($type) {
            case "Account":
            case "Contact":
                $builder->type = $type;
                break;
        }

        if (is_null($builder->type)) {
            Log::error("AGENT SYNC BUILDER INIT FAILED: Valid type not set.");
            return false;
        }

        return $builder;
    }

    /**
     * Static find alias for fetch.
     *
     * @return stdClass
     */
    public static function find($type) 
    {
        return self::fetch($type);
    }

    /**
     * Filter where based on Salesforce id.
     *
     * @return stdClass
     */
    public function whereId($id) 
    {
        $this->identType = 'id';
        $this->identId = $id;
        return $this;
    }

    /**
     * Filter where based on Agent Code.
     *
     * @return stdClass
     */
    public function whereAgentCode($id) 
    {
        $this->identType = 'agentCode';
        $this->identId = $id;
        return $this;
    }

    /**
     * Fetch the set data from the AgentSync API.
     *
     * @return stdClass|bool
     */
    public function get() 
    {
        if (is_null($this->type)) {
            Log::error("AGENT SYNC FETCH FAILED: Valid type not set.");
            return false;
        }

        if (is_null($this->identType)) {
            Log::error("AGENT SYNC FETCH FAILED: Valid identifier type not set.");
            return false;
        }

        if (!$agentSync = new AgentSyncApiProvider) {
            Log::error("AGENT SYNC FETCH FAILED: Unable to find API Provider.");
            return false;
        }

        $functionName = "";

        switch ($this->identType) {
            case "id":
                $functionName = "get{$this->type}ById";
                break;
            case "agentCode": 
                $functionName = "get{$this->type}ByAgentCode";
                break;
        }

        if (!is_string($functionName) || strlen($functionName) <= 0) {
            Log::error("AGENT SYNC FETCH FAILED: Function call string not built correctly.");
            return false;
        }

        if (!method_exists($agentSync, $functionName)) {
            Log::error("AGENT SYNC FETCH FAILED: AgentSync API Provider does not have method named {$functionName}.");
            return false;   
        }

        return $agentSync->$functionName($this->identId);
    }

    /**
     * Update this object via API.
     *
     * @return string
     */
    public function update() 
    {
        if (is_null($this->type)) {
            Log::error("AGENT SYNC UPDATE FAILED: Valid type not set.");
            return false;
        }

        if (is_null($this->identType)) {
            Log::error("AGENT SYNC UPDATE FAILED: Valid identifier type not set.");
            return false;
        }

        if (!$agentSync = new AgentSyncApiProvider) {
            Log::error("AGENT SYNC UPDATE FAILED: Unable to find API Provider.");
            return false;
        }

        $functionName = "";

        switch ($this->identType) {
            case "id":
                $functionName = "update{$this->type}ById";
                break;
            case "agentCode": 
                $functionName = "update{$this->type}ByAgentCode";
                break;
        }

        if (!is_string($functionName) || strlen($functionName) <= 0) {
            Log::error("AGENT SYNC UPDATE FAILED: Function call string not built correctly.");
            return false;
        }

        if (!method_exists($agentSync, $functionName)) {
            Log::error("AGENT SYNC UPDATE FAILED: AgentSync API Provider does not have method named {$functionName}.");
            return false;   
        }

        return $agentSync->$functionName($this->identId, json_encode($this->data));
    }

    /**
     * Initial static build function -- starts syntactic sugar.
     *
     * @return stdClass|bool
     */
    public static function create($type) 
    {
        $builder = new AgentSyncBuildProvider;
        
        switch ($type) {
            case "Account":
            case "Contact":
                $builder->type = $type;
                break;
        }

        if (is_null($builder->type)) {
            Log::error("AGENT SYNC BUILDER INIT FAILED: Valid type not set.");
            return false;
        }

        return $builder;
    }

    /**
     * Set a single piece of metadata. Alias for add field.
     *
     * @return stdClass
     */
    public function setField($key, $value) 
    {
        return $this->addField($key, $value);
    }

    /**
     * Add a single piece of metadata.
     *
     * @return stdClass
     */
    public function addField($key, $value) 
    {
        $key = (isset($this->keys[$key]) ? $this->keys[$key] : $key);
        $this->data->{$key} = $value;
        return $this;
    }


    /**
     * Set multiple pieces of metadata. Alias for add fields.
     *
     * @return stdClass
     */
    public function setFields($fields) 
    {
        return $this->addFields($fields);
    }

    /**
     * Add bulk fields from assoc array.
     *
     * @return stdClass
     */
    public function addFields($fields = []) 
    {
        if (!is_array($fields)) {
            Log::error("AGENT SYNC BUILDER ADD FAILED: Fields value is not an array.");
            return false;
        }

        foreach ($fields as $key => $value) {
            $this->addField($key, $value);
        }

        return $this; 
    }

    /**
     * Save this built object via API.
     *
     * @return string
     */
    public function save() 
    {
        if (is_null($this->type)) {
            Log::error("AGENT SYNC BUILDER SAVE FAILED: Valid type not set.");
            return false;
        }

        if (!$agentSync = new AgentSyncApiProvider) {
            Log::error("AGENT SYNC BUILDER SAVE FAILED: Unable to find API Provider.");
            return false;
        }

        $jsonPayload = json_encode($this->data);
        $response = false;

        switch ($this->type) {
            case "Contact":
                $response = $agentSync->createContact($jsonPayload);
                break;
            case "Account":
                $response = $agentSync->createAccount($jsonPayload);
                break;
        }

        return $response;
    }
}
