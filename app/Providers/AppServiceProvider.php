<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (! \App::environment('production')) {
            \DB::listen(function ($query) {
                \Log::channel('dq_queries')->info($query->sql.'::'.json_encode($query->bindings).'::'.$query->time);
            });
        }
    }
}
