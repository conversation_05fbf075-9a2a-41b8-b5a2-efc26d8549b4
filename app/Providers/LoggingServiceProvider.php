<?php

namespace App\Providers;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class LoggingServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('logger.task-generator', function(){
            return Log::channel('task-generator');
        });

        $this->app->singleton('logger.metric', function(){
            return Log::channel('metric');
        });

        $this->app->singleton('logger', function(){
            return Log::channel();
        });

        $this->app->singleton('logger.notification-generator', function(){
            return Log::channel('notification-generator');
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
