<?php

namespace App\Providers;

use App\Console\Commands\Onboarding\SendInviteAcceptedEmail;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

use App\Events\UserInvite\Created as UserInviteCreated;
use App\Events\UserInvite\Blocked as UserInviteBlocked;
use App\Events\UserInvite\Accepted as UserInviteAccepted;
use App\Events\UserInvite\Deleted as UserInviteDeleted;
use App\Events\Auth\ForgotPassword;
use App\Events\Review\AO\Submitted as AOSubmitted;
use App\Events\Review\AO\Rejected as AORejected;
use App\Events\Review\AO\Approved as AOApproved;
use App\Events\Review\HO\Submitted as HOSubmitted;
use App\Events\Review\HO\Rejected as HORejected;
use App\Events\Review\HO\Approved as HOApproved;
use App\Events\Status\Unenrolled as Unenrolled;
use App\Events\Status\Enrolled as Enrolled;

use App\Events\Review\RevisionRequested;

use App\Events\Onboarding\UnlicensedAccountCreated;
use App\Events\Onboarding\AssignedToDept;

use App\Listeners\UserInvite\SendEmail as SendUserInviteEmail;
use App\Listeners\UserInvite\SendBlockedEmail as SendUserInviteBlockedEmail;
use App\Listeners\UserInvite\SendAcceptedEmail as SendUserInviteAcceptedEmail;
use App\Listeners\UserInvite\SendDeletedEmail as SendUserInviteDeletedEmail;
use App\Listeners\Auth\SendWelcomeEmail;
use App\Listeners\Auth\SendForgotPasswordEmail;
use App\Listeners\Review\HO\SendApprovedEmail as HOSendApprovedEmail;
use App\Listeners\Review\HO\SendRejectedEmail as HOSendRejectedEmail;
use App\Listeners\Review\HO\SendSubmittedEmail as HOSendSubmittedEmail;
use App\Listeners\Review\AO\SendApprovedEmail as AOSendApprovedEmail;
use App\Listeners\Review\AO\SendRejectedEmail as AOSendRejectedEmail;
use App\Listeners\Review\AO\SendSubmittedEmail as AOSendSubmittedEmail;
use App\Listeners\Review\SendRevisionRequestedMail;
use App\Listeners\Status\UnenrolledEmail;
use App\Listeners\Status\EnrolledEmail;

use App\Listeners\Onboarding\GenerateTask;
use App\Listeners\Onboarding\GenerateNotification;
use App\Listeners\Review\AO\CreateHQLiteAccount;
use App\Listeners\Review\HO\CreateFullHQAccount;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            //SendEmailVerificationNotification::class,
            SendWelcomeEmail::class,
            GenerateTask::class,
            GenerateNotification::class,
        ],

        UserInviteCreated::class => [
            SendUserInviteEmail::class,
        ],

        UserInviteBlocked::class => [
            SendUserInviteBlockedEmail::class,
        ],

        UserInviteAccepted::class => [
            SendUserInviteAcceptedEmail::class,
        ],
        
        UserInviteDeleted::class => [
            SendUserInviteDeletedEmail::class,
        ],

        ForgotPassword::class => [
            SendForgotPasswordEmail::class,
        ],

        AOApproved::class => [
            AOSendApprovedEmail::class,
            // CreateHQLiteAccount::class,
            GenerateTask::class,
            GenerateNotification::class,
        ],

        AORejected::class => [
            AOSendRejectedEmail::class,
            GenerateNotification::class,
        ],

        AOSubmitted::class => [
            AOSendSubmittedEmail::class,
            GenerateTask::class,
            GenerateNotification::class,
        ],

        HOApproved::class => [
            HOSendApprovedEmail::class,
            CreateFullHQAccount::class,
            GenerateTask::class,
            GenerateNotification::class,
        ],

        HORejected::class => [
            HOSendRejectedEmail::class,
            GenerateNotification::class,
        ],

        HOSubmitted::class => [
            HOSendSubmittedEmail::class,
            GenerateNotification::class,
        ],

        RevisionRequested::class => [
            SendRevisionRequestedMail::class,
            GenerateTask::class,
            GenerateNotification::class,
        ],

        UnlicensedAccountCreated::class => [
            GenerateTask::class,
        ],

        Unenrolled:: class => [
            UnenrolledEmail::class,
            GenerateNotification::class,
        ],

        Enrolled:: class => [
            EnrolledEmail::class,
            GenerateNotification::class,
        ],

        \SocialiteProviders\Manager\SocialiteWasCalled::class => [
            // add your listeners (aka providers) here
           'SocialiteProviders\\Auth0\\Auth0ExtendSocialite@handle',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
