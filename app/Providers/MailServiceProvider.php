<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Mail\MailManager;
use App\Mail\Transport\LoggedTransport;

class MailServiceProvider extends ServiceProvider
{
    public function register()
    {
        //
    }

    public function boot()
    {
        app('mail.manager')->extend('logged', function ($config) {
            return new LoggedTransport();
        });
    }
} 