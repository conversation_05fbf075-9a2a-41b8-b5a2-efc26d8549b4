<?php

namespace App\Traits;

use Defuse\Crypto\Crypto;
use Defuse\Crypto\Key;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

trait UsesEncryption
{
    private static function getKey()
    {
        $secureKey = config('system.securekey', null);

        if (is_null($secureKey)) {
            Log::error("FIELD ERROR: System secure key not set.");
            return false;
        }

        return Key::loadFromAsciiSafeString($secureKey);
    }

    public static function encrypt($value)
    {
        $key = self::getKey();
        if(!$key)
            return null;

        return Crypto::encrypt($value, $key);
    }

    public static function decrypt($cipher)
    {
        $key = self::getKey();
        if(!$key)
            return null;

        return Crypto::decrypt($cipher, $key);
    }
}
