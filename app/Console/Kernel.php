<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
        \App\Console\Commands\Testing\TestServiceLevelAttribute::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();

        $schedule->command('onboarding:expire-old-invites')->hourly();
        //$schedule->command('clean-up:purge-completed-applications')->daily();
        //$schedule->command('clean-up:send-pending-deletion-email')->daily();
        $schedule->command('clean-up:purge-abandoned-applications')->daily();
        $schedule->command('metrics:daily-calculations')->daily();// need to run php artisan add:hq:metrics on setup
        $schedule->command('report:daily-approvals')->dailyAt('4:00');
        $schedule->command('report:daily-am-transferring')->dailyAt('4:05');
        $schedule->command('report:weekly-approvals')->saturdays()->at('5:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
