<?php

namespace App\Console\Commands;

use App\Models\FormLookup;
use App\Models\Metric;
use Exception;
use Illuminate\Console\Command;
use MetricBuilder;

class AddHQMetrics extends Command
{
    private $logger;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:hq:metrics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add metrics requested by HQ team';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->logger = app('logger.metric');
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->log("Truncate metrics");
        Metric::whereNotNull('name')->delete();

        $this->addFormFieldMetrics();
        $this->addUserStatusMetrics();
        $this->addUserInviteMetrics();

        return 0;
    }
    
    private function addFormFieldMetrics()
    {
        $mb = new MetricBuilder();

        $applicationTypes = [
            Metric::APPLICATION_TYPE_IN_PROGRESS,
            Metric::APPLICATION_TYPE_COMPLETED,
            Metric::APPLICATION_TYPE_ABANDONED
        ];

        $formLookups = [
            FormLookup::DEMO_GENDER,
            FormLookup::DEMO_ETHNICITY,
            FormLookup::DEMO_EDUCATION,
            FormLookup::DEMO_RECRUITED,
            FormLookup::DEMO_INCOME,
            FormLookup::DEMO_SELF_EMPLOYED,
            FormLookup::DEMO_EXPERIENCE,
            FormLookup::DEMO_MARRIAGE,
            FormLookup::DEMO_EMPLOYMENT
        ];

        foreach($applicationTypes as $applicationType) {
            foreach($formLookups as $formLookup) {
                try{
                    $metric = $mb->withApplicationType($applicationType)->addByFormLookup($formLookup);
                    if($metric) {
                        $this->log("FormField Metric added successfully: " . $metric->name);
                    } else {
                        $this->log("Failed to add FormField Metric: " . $applicationType . "-" . $formLookup);
                    }
                }catch(Exception $e) {
                    $this->log("Exception while adding FormField Metric: " . $applicationType . "-" . $formLookup . " :: " . $e->getMessage());
                }
            }
        }
    }

    private function addUserStatusMetrics()
    {
        $mb = new MetricBuilder();
        
        $applicationTypes = [
            Metric::APPLICATION_TYPE_IN_PROGRESS,
            Metric::APPLICATION_TYPE_COMPLETED,
            Metric::APPLICATION_TYPE_ABANDONED
        ];

        foreach($applicationTypes as $applicationType) {
            try{
                $metric = $mb->withApplicationType($applicationType)->addByUserStatus();
                if($metric) {
                    $this->log("UserStatus Metric added successfully: " . $metric->name);
                } else {
                    $this->log("Failed to add UserStatus Metric: " . $applicationType . "-");
                }
            }catch(Exception $e) {
                $this->log("Exception while adding UserStatus Metric: " . $applicationType . " :: " . $e->getMessage());
            }
        }
    }

    private function addUserInviteMetrics()
    {
        $mb = new MetricBuilder();

        try{
            $metric = $mb->addByUserInviteStatus('');
            if($metric) {
                $this->log("UserInvite Metric added successfully: " . $metric->name);
            } else {
                $this->log("Failed to add UserInvite Metric");
            }
        }catch(Exception $e) {
            $this->log("Exception while adding UserInvite Metric: " . " :: " . $e->getMessage());
        }
    }

    private function log($msg)
    {
        $this->line($msg);
        $this->logger->info("AddHQMetrics: " . $msg);
    }
}
