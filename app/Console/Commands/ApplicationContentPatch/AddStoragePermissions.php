<?php

namespace App\Console\Commands\ApplicationContentPatch;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AddStoragePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:02:add-storage-permission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 2
        Add new permission [view uploads] and add it to all roles
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:02:add-storage-permission] Start");
        Permission::firstOrCreate(['name' => 'view uploads']);

        $roles = ['AgencyOwner', 'SalesRep', 'Staff', 'SuperAdmin'];

        foreach($roles as $r){
            $role = Role::findByName($r, null);
            $role->givePermissionTo([
                'view uploads',
            ]);
        }

        $this->line("[app-content-patch:02:add-storage-permission] Finish");

        return 0;
    }
}
