<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use Carbon\Carbon;
use FormBuilder;

class UpdateLifeLoaQuestionPatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:57:update-life-loa-question';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 57
        Updates the Life LOA question to 'What lines of authority do you or will you have on your insurance license?'
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:57:update-life-loa-question] Start");

        // already ran?
        $existingSection = FormSection::where('label', 'What lines of authority do you or will you have on your insurance license? (Check all that apply)')
                                        ->whereNull('active_end')
                                        ->where('status', 'active')
                                        ->count();

        if ($existingSection) {
            $this->warn("The patch has already been run.");
            return 1;
        }

        // Step 1: Get the existing section and update the active_end date to yesterday
        $existingSection = FormSection::where('label', 'Do you have a Life Line of Authority on your insurance license?')
                                      ->whereNull('active_end')
                                      ->first();

        if ($existingSection) {
            $existingSection->update(['active_end' => Carbon::yesterday()]);
            $this->info("Deactivated existing section: '{$existingSection->label}'");
        } else {
            $this->warn("The existing section is already deactivated or not found.");
            // return 1;
        }

        // Step 2: Find the License Information page
        $page = FormPage::where('step_ident', 'license-information')->first();
        if (!$page) {
            $this->error("Can't find License Information Page");
            return 1;
        }

        $sort = 70; // Force to bottom of page

        // Step 3: Create the new section with the new label
        $newSection = FormBuilder::createSection('What lines of authority do you or will you have on your insurance license? (Check all that apply)', $page->id, $sort);
        $this->info("Created new section: '{$newSection->label}'");

        // Step 4: Add the new checkbox field options to the new section
        $sort++;
        FormBuilder::createField('Life', FormField::TYPE_CHECKBOX, FormField::WIDTH_FULL, $newSection->id, $sort++, 'Life', 0);
        FormBuilder::createField('Life and Health', FormField::TYPE_CHECKBOX, FormField::WIDTH_FULL, $newSection->id, $sort++, 'Life and Health', 0);
        FormBuilder::createField('None', FormField::TYPE_CHECKBOX, FormField::WIDTH_FULL, $newSection->id, $sort++, 'None (You will need "Life" or "Life and Health" to move your application forward.)', 0);

        // Set the active_start date for the new section
        $newSection->update(['active_start' => Carbon::today()]);
        $this->info("Set active start date for new section to today.");

        $this->line("[app-content-patch:57:update-life-loa-question] Finish");

        return 0;
    }
}
