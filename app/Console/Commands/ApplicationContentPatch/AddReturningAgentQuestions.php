<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddReturningAgentQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:32:add-returning-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 32
        Adds questions for existing/returning agents
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:32:add-returning-questions] Start");

        $exists = FormSection::where('label', 'Do you have previous experience in life insurance sales?')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        $sort = 50; //force to bottom of page

        $section = FormBuilder::createSection('Do you have previous experience in life insurance sales?', $page->id, $sort);
        $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            // If (YES) previous experience
            $subSection = FormBuilder::createSection('Are you transferring from another IMO or insurance agency?', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $subSection->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            // If (YES) transferring from another IMO
            $html_section = FormBuilder::createSection('', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
            FormBuilder::createField(
                '<p style="font-weight:bold; font-size:1.5em;">
                    If you are transferring carriers from a previous company, you will need to obtain a release to expedite your onboarding. Otherwise, your onboarding may be delayed. Please follow up with your Agency Owner to facilitate this process during your initial onboarding.
                </p>', 
                FormField::TYPE_HTML, 
                FormField::WIDTH_FULL, 
                $html_section->id
            );

        $this->line("[app-content-patch:32:add-returning-questions] Finish");

        return 0;
    }
}
