<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class AddNoLicensingPrompt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:04:add-no-licensing-prompt';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 4
        Add No Licensing Prompt
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:04:add-no-licensing-prompt] Start");

        $fs = FormSection::where('label', 'Are you enrolled in a pre-licensing course?')->first();
        if(!$fs) {
            $this->error("Can't find Target Section [Are you enrolled in a pre-licensing course?]");
            return;
        }

        // Shift first field of target section
        $ff = FormField::where('form_section_id', $fs->id)->where('sort', 0)->first();
        if(!$ff) {
            $this->error("Can't find First Field of target section. Shifting failed.");
            return;
        } else if($ff->type === FormField::TYPE_HTML) {
            $this->warn("This patch has already run.");
            return;
        }

        $ff->sort = 1;
        $ff->save();

        $this->line("Adding new field at sort=0");
        FormBuilder::createField(
            "<p class='mb-0'>Once you have completed your pre-licensing course, your next step is to register and complete your state exam.</p>
            <a target='_blank' href='https://www.examfx.com/insurance-prelicensing-training/view-insurance-state-requirements'>Click on the link for requirements in your state</a>
            <p class='mt-2'>Once you've passed your state exam, please come back to this page and complete your application.</p>", 
            FormField::TYPE_HTML, 
            FormField::WIDTH_FULL, 
            $fs->id
        );

        $this->line("[app-content-patch:04:add-no-licensing-prompt] Finish");

        return 0;
    }
}
