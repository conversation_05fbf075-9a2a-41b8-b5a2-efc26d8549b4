<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class AddSubmittedStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:26:add-submitted-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 26
        Add Additional SUBMITTED Statuses and retroactively update old statuses.
        submitted-ao, submitted-ho
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:26:add-submitted-statuses] Start");

        // Statuses already exist
        $us = UserStatus::whereIn('slug', ['submitted-ao', 'submitted-ho'])->count();
        if($us > 0) {
            $this->warn("This patch has already ran.");
            return;
        }

        // Create new statuses
        $submitted_ao = UserStatus::create([
            'name' => 'Submitted to AO',
            'slug' => 'submitted-ao',
        ]);
        $submitted_ho = UserStatus::create([
            'name' => 'Submitted to HO',
            'slug' => 'submitted-ho',
        ]);

        // Update all users with a user_status_id set to the old 'submitted' status
        $submitted_status = UserStatus::where('slug', 'submitted')->first();

        // submitted-ao
        $users = User::where('user_status_id', $submitted_status->id)->where('approved_by_ao', '0')->get();
        foreach($users as $user) {
            $user->user_status_id = $submitted_ao->id;
            $user->save();
        }

        // submitted-ho
        $users = User::where('user_status_id', $submitted_status->id)->where('approved_by_ao', '1')->get();
        foreach($users as $user) {
            $user->user_status_id = $submitted_ho->id;
            $user->save();
        }

        // UPDATE HISTORICAL SUBMITTED TYPES
        //Get all user records with a submitted status record in user_status_history 
        $submits = DB::select("SELECT
                    ush.id ush_id,
                    u.id user_id,
                    ush.trigger_id
                FROM
                    user_status_history ush
                    JOIN users u ON u.id = ush.user_id
                    JOIN user_statuses us ON us.id = ush.user_status_id
                    JOIN users u2 ON u2.id = ush.trigger_id
                WHERE
                    us.slug = 'submitted'
                ");

        foreach($submits as $submit) {
            $ush = UserStatusHistory::find($submit->ush_id);
            //if the user id and the trigger id are the same, then the status is 'submitted-ao' (submitted by the recruit to the ao)
            //otherwise, it's submitted-ho (submitted by the ao to the ho)
            if($submit->user_id == $submit->trigger_id)
                $ush->user_status_id = $submitted_ao->id;
            else
                $ush->user_status_id = $submitted_ho->id;

            $ush->save();
        }

        $this->line("[app-content-patch:26:add-submitted-statuses] Finish");

        return 0;
    }
}
