<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class AddApprovalStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:21:add-approval-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 21
        Add Additional Approval Statuses and retroactively update old 'approved' statuses
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:21:add-approval-statuses] Start");

        // Statuses already exist?
        $us = UserStatus::whereIn('slug', ['ao-approved', 'ho-approved'])->count();
        if($us > 0) {
            $this->warn("This patch has already run.");
            return;
        }
        
        // Add an updated_by_user_id to the user_status_history table

        // Create new statuses
        $ao_approved = UserStatus::create([
            'name' => 'AO Approval',
            'slug' => 'ao-approved',
        ]);
        $ho_approved = UserStatus::create([
            'name' => 'HO Approved',
            'slug' => 'ho-approved',
        ]);

        //Get all user records with an approval status record in user_status_history 
        $users = DB::select("SELECT u.id
            FROM user_status_history ush
            join users u on u.id = ush.user_id
            join user_statuses us on us.id = ush.user_status_id
            where us.slug = 'approved'
            group by u.id");
        
        // loop through results
        // update each user_status_history user_status_id with the new $ao_approved->id
        foreach($users as $user) {
            //get the approval records for this user/recruit
            $approval_records = DB::select("SELECT ush.user_id as user_id, ush.id as ush_id  
                FROM user_status_history ush
                JOIN user_statuses us on us.id = ush.user_status_id
                WHERE ush.user_id = ?
                AND us.slug = 'approved'
                ORDER BY ush.created_at ASC", [$user->id]);

            $index = 0;
            foreach($approval_records as $approval_record) {

                //get the user_status_history record
                $user_status_history = UserStatusHistory::find($approval_record->ush_id);
                if($index == 0) {
                    //first record is AO approval
                    $user_status_history->user_status_id = $ao_approved->id;
                } else {
                    //second record is HO approval
                    $user_status_history->user_status_id = $ho_approved->id;
                }
                $user_status_history->save();
                $index++;

            }
        }
        $this->line("[app-content-patch:21:add-approval-statuses] Finish");
        return 0;
    }
}
