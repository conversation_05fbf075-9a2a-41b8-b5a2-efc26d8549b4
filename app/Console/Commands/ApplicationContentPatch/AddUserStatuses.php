<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use Illuminate\Console\Command;
use FormBuilder;

class AddUserStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:18:add-user-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 18
        Add Additional User Statuses
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:18:add-user-statuses] Start");

        // Statuses already exist?
        $us = UserStatus::where('slug', 'approval-error')->count();
        if($us > 0) {
            $this->warn("This patch has already run.");
            return;
        }
        
        // Add new statuses
        UserStatus::create([
            'name' => 'Approval Error',
            'slug' => 'approval-error',
        ]);
        UserStatus::create([
            'name' => 'Enrolled',
            'slug' => 'enrolled',
        ]);
        UserStatus::create([
            'name' => 'Unenrolled',
            'slug' => 'unenrolled',
        ]);

        $this->line("[app-content-patch:18:add-user-statuses] Finish");
        return 0;
    }
}
