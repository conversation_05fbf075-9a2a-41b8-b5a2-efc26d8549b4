<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddEoPurchaseInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:41:add-eo-purchase-info';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 41
        Adds EO purchase info if they answer NO to EO insurance question
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:41:add-eo-purchase-info] Start");

        $exists = FormSection::where('label', 'LIKE', '%You can proceed without E&O insurance, but you will have limited carrier options.%')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        if(!$page = FormPage::where('step_ident', 'eo-insurance')->first()) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        if(!$section = FormSection::where('label', 'Do you currently have E&O Insurance?')->whereNull('active_end')->first()) {
            $this->error("Can't find EO section."); 
            return;
        }

        if(!$field = FormField::where('form_section_id', $section->id)->first()) {
            $this->error("Can't find EO field");
            return;
        }

        $sort = 1;

        $section = FormBuilder::createSection('You can proceed without E&O insurance, but you will have limited carrier options. We recommend purchasing E&O from the following link: <a href="https://www.calsurance.com/sfglife" target="_blank">https://www.calsurance.com/sfglife</a>', $page->id, $sort);
        $section->save();
        
        //add conditional display if resident state is NY
        $condition = new FormFieldCondition();
        $condition->form_field_id = $field->id;
        $condition->value = 'NO';
        $condition->action = FormFieldCondition::ACTION_SHOW;
        $condition->form_section_id = $section->id;
        $condition->type = FormFieldCondition::TYPE_EQUALS;
        $condition->save();

        $this->line("[app-content-patch:41:add-eo-purchase-info] Finish");

        return 0;
    }
}
