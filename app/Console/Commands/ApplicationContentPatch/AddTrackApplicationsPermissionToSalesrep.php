<?php

namespace App\Console\Commands\ApplicationContentPatch;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class AddTrackApplicationsPermissionToSalesrep extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:15:add-track-applications-permission-to-salesrep';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 15
        Add the track applications permission to the SalesRep role
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:15:add-track-applications-permission-to-salesrep] Start");
        
            $role = Role::findByName('SalesRep', null);
            $role->givePermissionTo([
                'track applications'
            ]);

        $this->line("[app-content-patch:15:add-track-applications-permission-to-salesrep] Finish");

        return 0;
    }
}
