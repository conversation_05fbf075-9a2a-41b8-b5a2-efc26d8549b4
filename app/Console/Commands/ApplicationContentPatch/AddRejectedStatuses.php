<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class AddRejectedStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:29:add-rejected-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 29
        Add Additional REJECTED Statuses and retroactively update old statuses.
        rejected-ao, rejected-ho
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:29:add-rejected-statuses] Start");

        // Statuses already exist
        $us = UserStatus::whereIn('slug', ['rejected-ao', 'rejected-ho'])->count();
        if($us > 0) {
            $this->warn("This patch has already ran.");
            return;
        }

        // Create new statuses
        $rejected_ao = UserStatus::create([
            'name' => 'Rejected by AO',
            'slug' => 'rejected-ao',
        ]);
        $rejected_ho = UserStatus::create([
            'name' => 'Rejected by Home Office',
            'slug' => 'rejected-ho',
        ]);

        // UPDATE HISTORICAL SUBMITTED TYPES
        //Get all user records with a submitted status record in user_status_history 
        $rejections = DB::select("SELECT
                    ush.id ush_id,
                    u2.name trigger_name
                FROM
                    user_status_history ush
                    JOIN user_statuses us ON us.id = ush.user_status_id
                    JOIN users u2 ON u2.id = ush.trigger_id
                WHERE
                    us.slug = 'rejected'
                ");

        foreach($rejections as $rejection) {
            $ush = UserStatusHistory::find($rejection->ush_id);
            if($rejection->trigger_name == 'SFGAGENCY')
                $ush->user_status_id = $rejected_ho->id;
            else
                $ush->user_status_id = $rejected_ao->id;

            $ush->save();
        }

        $this->line("[app-content-patch:29:add-rejected-statuses] Finish");

        return 0;
    }
}
