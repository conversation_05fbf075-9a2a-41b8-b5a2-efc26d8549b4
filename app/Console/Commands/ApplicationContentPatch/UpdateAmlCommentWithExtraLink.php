<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class UpdateAmlCommentWithExtraLink extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:11:update-aml-comment-extra-link';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 11
        Update comment on aml page with extra link
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:11:update-aml-comment-extra-link] Start");

        $amlPage = FormPage::where('label', 'Anti-Money Laundering')->first();
        if(!$amlPage) {
            $this->error("Target page doesn't exist!");
            return;
        }
        
        $formSection = $amlPage->formSections()->where('label', 'LIKE', 'To submit your contracting application, you must provide proof of%')->first();
        if(!$formSection) {
            $this->error("Target section doesn't exist!");
            return;
        }

        $firstField = $formSection->formFields()->where('sort', 0)->first();
        $finalField = $formSection->formFields()->where('label', 'LIKE', 'After completing the course, please return to this question, change your reply to YES%')->first();
        if(!$firstField || !$finalField) {
            $this->error("Target field doesn't exist!");
            return;
        }
        else if($finalField->sort > 2) {
            $this->warn("This patch has already run.");
            return;
        }

        $finalField->sort = 4;
        $finalField->save();

        $firstField->label = 'In order to be contracted with a carrier, you must provide proof of completing Anti-Money Laundering (AML) training. Please use the link below to access our recommended AML provider. This is a paid course. While you aren’t required to go with this provider, we highly recommend and encourage you to complete it as all carriers will accept this AML.';
        $firstField->save();
        
        $formSection->label = 'To submit your contracting application, you must provide proof of an Anti-Money Laundering (AML) training course. This training needs to have been completed within the last year to be valid.';
        $formSection->save();
        
        FormBuilder::createField("There are free alternatives, but we cannot guarantee that a carrier will accept these. Below is a link to the American Amicable training that is free of charge.", FormField::TYPE_HTML, FormField::WIDTH_FULL, $formSection->id, 2);
        FormBuilder::createField("<a href='https://www.americanamicable.com/internet/aml/amllogon.php?msg=x7' target='_blank'>https://www.americanamicable.com/internet/aml/amllogon.php?msg=x7</a>", FormField::TYPE_HTML, FormField::WIDTH_FULL, $formSection->id, 3);

        $this->line("[app-content-patch:11:update-aml-comment-extra-link] Finish");

        return 0;
    }
}
