<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class AddFlaggedStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:47:add-flagged-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 47
        Add FLAGGED Statuses for NPNs that exist in HQ/B2BHQ.
        flag-exists-hq, flag-exists-b2bhq
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:47:add-flagged-statuses] Start");

        // Statuses already exist
        $us = UserStatus::whereIn('slug', ['flag-exists-hq', 'flag-exists-b2bhq'])->count();
        if($us > 0) {
            $this->warn("This patch has already ran.");
            return;
        }

        // Create new statuses
        $revision_ao = UserStatus::create([
            'name' => 'Exists HQ',
            'slug' => 'flag-exists-hq',
        ]);
        $revision_ho = UserStatus::create([
            'name' => 'Exists B2B HQ',
            'slug' => 'flag-exists-b2bhq',
        ]);

        $this->line("[app-content-patch:47:add-flagged-statuses] Finish");
        return 0;
    }
}