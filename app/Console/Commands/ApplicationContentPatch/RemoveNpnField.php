<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormSection;
use Illuminate\Console\Command;

class RemoveNpnField extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:31:remove-npn-field';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 31
        Remove/disable the NPN Field
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:31:remove-npn-field] Start");

        $fs = FormSection::where('label', 'NPN Number')->first();
        if(!$fs) {
            $this->warn("Can't find active NPN Number Section.");
            return;
        }

        if($fs->status == FormSection::STATUS_DISABLED) {
            $this->warn("This patch has already ran.");
            return;
        }

        $fs->status = FormSection::STATUS_DISABLED;
        $fs->save();

        $this->line("[app-content-patch:31:remove-npn-field] Finish");

        return 0;
    }
}
