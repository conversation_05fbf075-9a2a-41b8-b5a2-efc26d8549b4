<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddLanguagePreferenceCheckboxes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:59:add-language-preference-checkboxes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch
        Adds language preference checkboxes to the personal-information page
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:59:add-language-preference-checkboxes] Start");

        // Check if section already exists
        $exists = FormSection::where('label', 'Interested in leads for clients that speak?')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return 0;
        }

        // Get the personal information page
        $page = FormPage::where('step_ident', 'personal-information')->first();
        if(!$page) {
            $this->error("Can't find Personal Information Page");
            return 1;
        }

        // Get the highest sort order on the page to add our section at the end
        $lastSection = FormSection::where('form_page_id', $page->id)
            ->where('status', 'active')
            ->orderBy('sort', 'desc')
            ->first();
        
        $sort = $lastSection ? $lastSection->sort + 1 : 100;

        // Create the main section
        $languageSection = FormBuilder::createSection('Interested in leads for clients that speak?', $page->id, $sort++);
        
        // Create language checkboxes
        $languages = [
            'Spanish' => 'spanish-language',
            'Russian' => 'russian-language',
            'French' => 'french-language',
            'Mandarin' => 'mandarin-language',
            'Hindi' => 'hindi-language',
            'Other' => 'other-language'
        ];
        
        // Add checkbox for each language
        foreach($languages as $language => $slug) {
            $checkbox = FormBuilder::createField(
                $language, 
                FormField::TYPE_CHECKBOX, 
                FormField::WIDTH_THIRD, 
                $languageSection->id, 
                $sort++, 
                '', 
                0 // Not required
            );
            
            // Create lookup for each language checkbox
            $lookup = new FormLookup();
            $lookup->form_field_id = $checkbox->id;
            $lookup->field_slug = $slug;
            $lookup->save();
            
            // If this is the "Other" checkbox, create a conditional text field section
            if ($language === 'Other') {
                $otherSection = FormBuilder::createSection('Please specify other language(s)', $page->id, $sort++, $checkbox->id, FormField::CHECKBOX_CHECKED);
                
                $otherField = FormBuilder::createField(
                    '', 
                    FormField::TYPE_TEXT, 
                    FormField::WIDTH_HALF, 
                    $otherSection->id, 
                    $sort++,
                    'Enter language(s)',
                    0 // Not required
                );
                
                // Create lookup for the other languages text field
                $otherLookup = new FormLookup();
                $otherLookup->form_field_id = $otherField->id;
                $otherLookup->field_slug = 'other-language-specified';
                $otherLookup->save();
            }
        }
        
        $this->line("[app-content-patch:59:add-language-preference-checkboxes] Finish");

        return 0;
    }
} 