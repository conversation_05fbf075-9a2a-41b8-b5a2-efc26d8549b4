<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
// use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddDivToDivReleaseFormFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:50:add-div-to-div-release-form-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 50
        Adds section and fields to force upload of Div to Div Release form
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:50:add-div-to-div-release-form-fields] Start");

        $exists = FormSection::where('label', 'LIKE', 'Your NPN was identified with an affiliated partner.%')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        $sort = 80; //force to bottom of page

        if(!$section = FormSection::where('label', 'LIKE', 'Do you have a Life Line of Authority on your insurance license?')->where('status', 'active')->first()) {
            $this->error("Can't find LOA section");
            return;
        }

        //Hidden field for 'npn-exists' to trigger div-to-div transfer form info and receipt upload
        $select = FormBuilder::createField('NPN Exists', FormField::TYPE_HIDDEN, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 0, 0, 1, FormField::SELECT_BOOL);

        // New section to require upload of DIV to DIV Release form receipt
        $npn_exists_section = FormBuilder::createSection('Your NPN was identified with an affiliated partner. In order to move forward with your application, an approved release request is required.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
        FormSection::find($npn_exists_section->id)->update(['subline' => 'Please click <a href="https://form.jotform.com/241305600335140" target="_blank">this link</a> to fill out your Release Form. Once you\'ve completed this step, please upload your receipt of form submission below.']);
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $npn_exists_section->id, 0, '', 1);

        $this->line("[app-content-patch:50:add-div-to-div-release-form-fields] Finish");

        return 0;
    }
}
