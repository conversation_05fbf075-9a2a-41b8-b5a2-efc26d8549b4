<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddLifeLoaQuestion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:46:add-life-loa-question';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 46
        Adds Life LOA question
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:46:add-life-loa-question] Start");

        $exists = FormSection::where('label', 'Do you have a Life Line of Authority on your insurance license?')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        $sort = 70; //force to bottom of page

        $section = FormBuilder::createSection('Do you have a Life Line of Authority on your insurance license?', $page->id, $sort);
        $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        // If (NO) show notification
        $subSection = FormBuilder::createSection('Please note: You will need to obtain a license with Life as an LOA in order to get contracted with carriers.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_NO);

        $this->line("[app-content-patch:46:add-life-loa-question] Finish");

        return 0;
    }
}
