<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\FormPage;
use Illuminate\Console\Command;
// use FormBuilder;

class AddFormPageIdToUserStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:19:add-form-page-id-to-user-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 19
        Adds form_page_id to enrolled and unenrolled User Statuses
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:19:add-form-page-id-to-user-statuses] Start");

        // Statuses already updated?
        $us = UserStatus::whereIn('slug', ['unenrolled', 'enrolled'])->whereNull('form_page_id');

        if($us->count() == 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $statuses = $us->get();

        //get form page id we want to add to these statuses
        $page = FormPage::where('step_ident', 'license-information')->first();

        if(!$form_page_id = $page->id) {
            $this->error("Can't find form page with step_ident 'license-information'");
        }
        
        foreach($statuses as $status) {
            $status->form_page_id = $form_page_id;
            $status->save();
        }
        
        $this->line("[app-content-patch:19:add-form-page-id-to-user-statuses] Finish");
        return 0;
    }
}
