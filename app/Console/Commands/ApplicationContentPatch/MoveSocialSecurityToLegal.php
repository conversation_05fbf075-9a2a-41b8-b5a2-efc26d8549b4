<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;

class MoveSocialSecurityToLegal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:01:move-social-security-to-legal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 1
        Move [Social Security Number] from Form [] to Form [Legal Questions] (sort=0)
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:01:move-social-security-to-legal] Start");
        // Get the target section
        $fs = FormSection::where('label', 'Social Security Number')->first();
        if(!$fs) {
            $this->error("Target section [Social Security Number] doesn't exist!");
            return;
        }
        
        // Get the target form page
        $legalPage = FormPage::where('label', 'Legal Questions and Responses/Explanations for Contracting and Appointment Requests')->first();
        if(!$legalPage) {
            $this->error("Target form page [Legal Questions...] doesn't exist!");
            return;
        }

        // Check if is already moved
        if($fs->form_page_id == $legalPage->id) {
            $this->warn("This patch has already run.");
            return;
        }

        // Shift other sections
        $this->line("Shifting other sections from target form");
        $legalSections = FormSection::where('form_page_id', $legalPage->id)->get();
        foreach($legalSections as $s){
            $s->sort = $s->sort + 1;
            $s->save();
        }

        // Move target section to sort=0
        $this->line("Save SSN section to sort=0");
        $fs->form_page_id = $legalPage->id;
        $fs->sort = 0;
        $fs->save();

        $this->line("[app-content-patch:01:move-social-security-to-legal] Finish");

        return 0;
    }
}
