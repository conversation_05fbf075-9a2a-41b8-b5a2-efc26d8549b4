<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class AddRevisionStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:28:add-revision-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 28
        Add REVISION Statuses and retroactively insert records.
        revision-ao, revision-ho
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:28:add-revision-statuses] Start");

        // Statuses already exist
        $us = UserStatus::whereIn('slug', ['revision-ho', 'revision-ao'])->count();
        if($us > 0) {
            $this->warn("This patch has already ran.");
            return;
        }



        // Create new statuses
        $revision_ao = UserStatus::create([
            'name' => 'Revision AO',
            'slug' => 'revision-ao',
        ]);
        $revision_ho = UserStatus::create([
            'name' => 'Revision HO',
            'slug' => 'revision-ho',
        ]);
        

        // Insert a new ush record whenever there is a 'submitted-ao/ho' followed by 'eft-information' record
        // make sure we can manually set the created_at date (might have to use a raw update query?)

        $users = DB::select("SELECT DISTINCT(ush.user_id)
            FROM user_status_history ush
            join user_statuses us on us.id = ush.user_status_id
            where us.slug LIKE 'submitted%'");

        foreach($users as $user) {
            //this user's status history records
            $records = DB::select("SELECT ush.user_id user_id, ush.created_at created_at, us.name status_name, us.slug status_slug, ush.trigger_id 
                FROM user_status_history ush
                join user_statuses us on us.id = ush.user_status_id
                where user_id = ?
                order by ush.created_at", [$user->user_id]);

            //looping through all the user's status records to see where to insert a revision record
            //if a 'submitted' record isn't followed by an 'approved' record, the it was sent back for revisions

            $previous_record = new \stdClass();
            $previous_record->status_slug = '';
            $previous_record->user_id = '';

            foreach($records as $record) {
                if(str_contains($previous_record->status_slug, 'submitted')) {
                    //if this isn't an approval, then insert the revision record
                    if(!str_contains($record->status_slug, 'approved')) {

                        //was submitted to the ao
                        if($previous_record->trigger_id == $previous_record->user_id)
                            $user_status_id = $revision_ao->id;
                        else
                            $user_status_id = $revision_ho->id;

                        $ush = UserStatusHistory::create([
                            'user_id' => $previous_record->user_id,
                            'created_at' => $record->created_at,
                            'user_status_id' => $user_status_id,
                            'trigger_id' => $record->trigger_id
                        ]);
                        $ush->created_at = $record->created_at;
                        $ush->save();     
                        // $this->line('Created revision record '.$ush->id.' '.$previous_record->user_id);                 
                    }
                }

                $previous_record = $record;
            }
            
        }




        $this->line("[app-content-patch:28:add-revision-statuses] Finish");
        return 0;
    }
}