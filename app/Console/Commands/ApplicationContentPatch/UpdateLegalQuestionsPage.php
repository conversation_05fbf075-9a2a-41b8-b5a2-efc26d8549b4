<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormPage;
use App\Models\FormField;
use App\Models\FormSection;
use Illuminate\Console\Command;
use Carbon\Carbon;
use FormBuilder;
use DB;

class UpdateLegalQuestionsPage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:39:update-legal-questions-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 39
        Simplifies Legal Questions Page
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:39:update-legal-questions-page] Start");

        // get the page for the legal questions section
        $page = FormPage::where('step_ident', 'legal-information')->first();
        if(!$page) {
            $this->error("Can't find Legal Information Page");
            return;
        }

        // already ran?
        $updated_section = FormSection::where('label', 'Have you ever been charged or convicted of or plead guilty to any Felony, Federal/State Insurance and/or Securities or Investments regulations and statutes?')
            ->count();
        if($updated_section > 0) {
            $this->warn("This patch has already ran.");
            return;
        }


        // Get all sections starting with numbers 1 - 18
        $sections = DB::select("SELECT * FROM dbo.form_sections 
            where form_page_id = ?
            and status = 'active'
            and active_end IS NULL
            and (
                label like '1.%'
                or label like '2.%'
                or label like '3.%'
                or label like '4.%'
                or label like '5.%'
                or label like '6.%'
                or label like '7.%'
                or label like '8.%'
                or label like '9.%'
                or label like '10.%'
                or label like '11.%'
                or label like '12.%'
                or label like '13.%'
                or label like '14.%'
                or label like '15.%'
                or label like '16.%'
                or label like '17.%'
                or label like '18.%'
                or label like '%Please upload any additional documents regarding your background questions and answers here.%'
            )", [$page->id]);


        // udpate all the above sections with the current active_end date
        foreach($sections as $section) {
            $update_section = FormSection::find($section->id);
            $update_section->active_end = Carbon::today();
            $update_section->save();
        }

        /***************************************
         *
         * Create the new sections/fields
         * 
         ***************************************/

        $sort = 1;
        $tomorrow = Carbon::tomorrow();

        // 1 - top level
        $section = FormBuilder::createSection('Have you ever been charged or convicted of or plead guilty to any Felony, Federal/State Insurance and/or Securities or Investments regulations and statutes?', $page->id, $sort);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            // 1A
            $section = FormBuilder::createSection('Were the charges for any one of the following: aggravated assault, child abuse, domestic violence, sexual assault, intent to distribute, larceny, or fraud?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation', 'attachments'], $page->id, $sort++, $subSelect->id);

        $this->line("[app-content-patch:39:update-legal-questions-page] Finish");

        return 0;
    }

    public static function addConditionalFields($fields, $pageId, $sort, $fieldId, $filename = 'Additional Documents') {
        
        $tomorrow = Carbon::tomorrow();

        // date
        if(in_array('date', $fields)) {
            $date_section = FormBuilder::createSection('Date of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $date_section->active_start = $tomorrow;
            $date_section->save();
            FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $date_section->id, 1);
        }

        // company name
        if(in_array('company', $fields)) {
            $state_section = FormBuilder::createSection('Please list company name.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // fine
        if(in_array('fine', $fields)) {
            $state_section = FormBuilder::createSection('Please list fine amount, if applicable.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // county
        if(in_array('county', $fields)) {
            $county_section = FormBuilder::createSection('County of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $county_section->active_start = $tomorrow;
            $county_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $county_section->id, 1);
        }

        // state
        if(in_array('state', $fields)) {
            $state_section = FormBuilder::createSection('State of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // general explanation
        if(in_array('explanation', $fields)) {
            $details_section = FormBuilder::createSection('Please provide details about the charges and/or offenses as well as any sentencing information.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $details_section->active_start = $tomorrow;
            $details_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $details_section->id, 1)->markAsPii();
        }

        // charges details
        if(in_array('charges', $fields)) {
            $details_section = FormBuilder::createSection('Please detail charges and/or offenses.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $details_section->active_start = $tomorrow;
            $details_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $details_section->id, 1)->markAsPii();
        }

        // sentencing
        if(in_array('sentencing', $fields)) {
            $sentencing_section = FormBuilder::createSection('Please detail any sentencing information.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $sentencing_section->active_start = $tomorrow;
            $sentencing_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $sentencing_section->id, 1)->markAsPii();
        }

        // name
        if(in_array('name', $fields)) {
            $name_section = FormBuilder::createSection('What was your name at time of offense?', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $name_section->active_start = $tomorrow;
            $name_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $name_section->id, 1)->markAsPii();
        }

        // attachments
        if(in_array('attachments', $fields)) {
            $section = FormBuilder::createSection('Please upload any supporting documentation.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, $sort++, false, 1, 0, 255, false, '', $filename)->markAsPii();
        }

        return $sort;

    }
}
