<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddUsCitizenCheckbox extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:42:add-us-citizen-checkbox';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 42
        Adds checkbox to affirm US Citizenship
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:42:add-us-citizen-checkbox] Start");

        $exists = FormSection::where('label', 'LIKE', '%Are you a U.S. Citizen?%')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        if(!$page = FormPage::where('step_ident', 'personal-information')->first()) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        
        $sort = 70; //force to bottom of page
        
        $section = FormBuilder::createSection('Are you a U.S. Citizen?', $page->id, $sort++);
        $section->save();
        

        $checkbox = FormBuilder::createField('Yes, I am a citizen of the United States.', FormField::TYPE_CHECKBOX, FormField::WIDTH_HALF, $section->id, $sort++);

        $this->line("[app-content-patch:42:add-us-citizen-checkbox] Finish");

        return 0;
    }
}
