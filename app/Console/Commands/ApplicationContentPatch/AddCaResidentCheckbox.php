<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddCaResidentCheckbox extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:38:add-ca-resident-checkbox';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 38
        Adds checkbox and PDF for CA residents
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:38:add-ca-resident-checkbox] Start");

        $exists = FormSection::where('label', 'LIKE', '%Privacy Notice for California Contractors%')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        if(!$page = FormPage::where('step_ident', 'personal-information')->first()) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        if(!$lookup = FormLookup::where('field_slug', 'personal-state')->first()){
            $this->error("Can't find State field"); 
            return;
        }

        $sort = 60; //force to bottom of page
        //If resident state is New York
        $section = FormBuilder::createSection('California residents must read and acknowledge the <a href="https://hq.quility.com/api/public/document/179846/view/quility-ca-employee-contractors-privacy-notice-2023-01-19" target="_blank">Privacy Notice for California Contractors</a>.', $page->id, $sort++);
        $section->save();
        
        //add conditional display if resident state is NY
        $condition = new FormFieldCondition();
        $condition->form_field_id = $lookup->form_field_id;
        $condition->value = 'california,ca,c.a.';
        $condition->action = FormFieldCondition::ACTION_SHOW;
        $condition->form_section_id = $section->id;
        $condition->type = FormFieldCondition::TYPE_LIKE;
        $condition->save();

        $checkbox = FormBuilder::createField('I acknowledge receipt of the Privacy Notice for California Contractors', FormField::TYPE_CHECKBOX, FormField::WIDTH_HALF, $section->id, $sort++);

        $this->line("[app-content-patch:38:add-ca-resident-checkbox] Finish");

        return 0;
    }
}
