<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddVisaRecommendedCarriers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:49:visa-carrier-recommendations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 49
        Adds recommended carriers list for non-citizens.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:49:visa-carrier-recommendations] Start");

        $exists = FormSection::where('label', 'LIKE', "%We've noticed that you've selected a work VISA. Below are some top carrier recommendations tailored to your selection.%")->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'personal-information')->first();
        if(!$page) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        $sort = 75; //force to bottom of page

        // Are you a US Citizen
        if(!$section = FormSection::where('label', "Are you a U.S. Citizen?")->where('status', 'active')->first()) {
            $this->error("Can't find US Citizen section.");
        }

        // YES/NO answer field
        if(!$field = FormField::where('form_section_id', $section->id)->first()) {
            $this->error("Can't find Citizenship field.");
            return;
        }

        // If not a US Citizen, show this section.
        $subSection = FormBuilder::createSection("<p>We've noticed that you've selected a work VISA. Below are some top carrier recommendations tailored to your selection.</p><ul><li>Assurity</li><li>Athene</li><li>F&G</li><li>Foresters</li><li>Lincoln Financial</li><li>Mutual Trust</li><li>NLG & LSW</li><li>TransAmerica</li></ul>", $page->id, $sort++, $field->id, FormField::SELECT_BOOL_NO);

        $this->line("[app-content-patch:49:visa-carrier-recommendations] Finish");

        return 0;
    }
}
