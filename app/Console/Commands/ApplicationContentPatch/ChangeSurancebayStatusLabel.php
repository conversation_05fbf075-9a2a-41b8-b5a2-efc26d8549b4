<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use Illuminate\Console\Command;

class ChangeSurancebayStatusLabel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:22:change-surancebay-status-label';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 22
        Change Surancebay Status to FCRA
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:22:change-surancebay-status-label] Start");

        $us = UserStatus::where('slug', 'surance-bay-agreement')->first();
        if(!$us) {
            $this->warn("This patch has already run.");
            return;
        }

        $us->name = 'FCRA Agreement';
        $us->slug = 'fcra-agreement';
        $us->save();

        $this->line("[app-content-patch:22:change-surancebay-status-label] Finish");
        return 0;
    }
}
