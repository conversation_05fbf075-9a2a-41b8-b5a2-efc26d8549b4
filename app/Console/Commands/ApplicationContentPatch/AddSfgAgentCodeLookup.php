<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormPage;
use App\Models\FormField;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use Carbon\Carbon;
use FormBuilder;
use DB;

class AddSfgAgentCodeLookup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:40:add-sfg-agent-code-lookup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 40
        Adds form lookup for SFG Agent Code for returning agents.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:40:add-sfg-agent-code-lookup] Start");

        // already ran?
        $form_lookup = FormLookup::where('field_slug', FormLookup::RETURNING_SFG_AGENT_CODE)
            ->count();
        if($form_lookup > 0) {
            $this->warn("This patch has already ran.");
            return;
        }

        if(!$form_section = FormSection::where('label', 'LIKE', '%Please enter your SFG Agent Code, if available.%')->first()){
            $this->error('Form section not found.');
        }

        if(!$form_field = FormField::where('form_section_id', $form_section->id)->first()) {
            $this->error('Form field not found.');
        }

        $lookup = new FormLookup;
        $lookup->form_field_id = $form_field->id;
        $lookup->field_slug = FormLookup::RETURNING_SFG_AGENT_CODE;
        $lookup->save();

        $this->line("[app-content-patch:40:add-sfg-agent-code-lookup] Finish");

        return 0;
    }

}
