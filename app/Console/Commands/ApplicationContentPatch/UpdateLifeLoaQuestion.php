<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;
use Carbon\Carbon;


class UpdateLifeLoaQuestion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:51:update-life-loa-question';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 51
        Updates Life LOA question
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:51:update-life-loa-question] Start");

        // Check if the old section exists and is active
        $exists = FormSection::where('label', 'Do you have life on your health insurance license?')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch already ran.");
            return;
        }

        $section = FormSection::where('label', 'Do you have a Life Line of Authority on your insurance license?')->where('status', 'active')->first();
        if(!$section) {
            $this->error("Can't find section");
            return;
        }
        $section->label = 'Do you have life on your health insurance license?';
        $section->save();

        $currentDate = Carbon::now()->toDateString();
        $field = FormField::where('form_section_id', $section->id)
            ->where(function ($query) use ($currentDate) {
                $query->where('active_start', '<=', $currentDate)
                      ->orWhereNull('active_start');
            })
            ->where(function ($query) use ($currentDate) {
                $query->where('active_end', '>=', $currentDate)
                      ->orWhereNull('active_end');
            })
            ->where('label','')
            ->first();

        // Find the License Information Page
        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        $sort = 71; // force to bottom of page

        // If (Yes), add the follow-up question
        $followUpSection = FormBuilder::createSection('Do you have Health on your insurance license?', $page->id, $sort++, $field->id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $followUpSection->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        $this->line("[app-content-patch:51:update-life-loa-question] Finish");

        return 0;
    }
}
