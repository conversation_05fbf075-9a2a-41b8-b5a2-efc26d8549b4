<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormOption;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class UpdateUsCitizenToSelect extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:43:update-us-citizen-to-select';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 43
        Changes US Citizenship question to a select (YES/NO) field
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:43:update-us-citizen-to-select] Start");

        $exists = FormSection::where('label', 'LIKE', 'Please upload your green card documentation.')->count();

        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        if(!$page = FormPage::where('step_ident', 'personal-information')->first()) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        $sort = 71; //force to bottom of page

        //get section id
        if(!$section = FormSection::where('label', 'LIKE', '%Are you a U.S. Citizen?%')->first()) {
            $this->error("Can't find section");
            return;
        }

        //update the old question settings
        FormField::where('form_section_id', $section->id)
            ->where('type', 'checkbox')
            ->update([
                'type' => 'select', 
                'placeholder' => 'Please Select', 
                'max_length' => 1,
                'is_required' => 1,
                'width' => 'third_width',
                'sort' => $sort++
            ]);

        // getting the newly udpated field
        $field = FormField::where('form_section_id', $section->id)
                    ->where('type', 'select')
                    ->first();
        
        // add a lookup for this field for easy reference where needed
        FormBuilder::addFormLookup($field->id, FormLookup::IS_CITIZEN_BOOL);
        
        //add the new select yes/no options
        $selectOptions = array('YES' => 'YES', 'NO' => 'NO');
        $sortOption = 0;

        foreach ($selectOptions as $value => $label) {
            $option = new FormOption;
            $option->form_field_id = $field->id;
            $option->label = $label;
            $option->value = $value;
            $option->sort = $sortOption;
            $option->save();
            $sortOption ++;
        }
        
        //greencard documentation
        $greencard_section = FormBuilder::createSection('Please upload your green card documentation.', $page->id, $sort++, $field->id, FormField::SELECT_BOOL_NO);
        $greencard_section->save();
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $greencard_section->id, $sort++, false, 1, 0, 255, false, '', 'Greencard documentation')->markAsPii();


        //Update SSN section label
        if(!$ssn_section = FormSection::where('label', 'LIKE', 'Social Security Number')->first()) {
            $this->error("Can't find ssn section");
            return;
        }
        $ssn_section->label = 'Social Security Number (or ITIN if not a U.S. citizen)';
        $ssn_section->save();

        $this->line("[app-content-patch:43:update-us-citizen-to-select] Finish");

        return 0;
    }
}
