<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;

class UpdateSuranceBayAgreementPageIdent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:24:update-surance-bay-agreement-page-indent';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 24
        Update SuranceBay page slug to match the new FCRA content
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:24:update-surance-bay-agreement-page-ident] Start");

        $page = FormPage::where('step_ident', 'surance-bay-agreement')->first();
        
        if(!$page) {
            $this->warn("This patch has already ran");
            return;
        }
        
        $page->step_ident = 'fcra-agreement';
        $page->save();

        $this->line("[app-content-patch:24:update-surance-bay-agreement-page-ident] Finish");

        return 0;
    }
}
