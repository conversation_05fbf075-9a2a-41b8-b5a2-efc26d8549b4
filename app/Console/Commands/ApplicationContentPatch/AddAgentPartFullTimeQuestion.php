<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddAgentPartFullTimeQuestion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:45:add-agent-part-full-time-question';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 45
        Adds question about part time/full time status
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:45:add-agent-part-full-time-question] Start");

        $exists = FormSection::where('label', 'Is your goal to:')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'demographic-information')->first();
        if(!$page) {
            $this->error("Can't find Demographic Information Page");
            return;
        }

        $sort = 10; //force to bottom of page

        $section = FormBuilder::createSection('Is your goal to:', $page->id, $sort);
        $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort, 'Please Select', 1, 0, 1, FormField::SELECT_GOAL);

        $this->line("[app-content-patch:45:add-agent-part-full-time-question] Finish");

        return 0;
    }
}
