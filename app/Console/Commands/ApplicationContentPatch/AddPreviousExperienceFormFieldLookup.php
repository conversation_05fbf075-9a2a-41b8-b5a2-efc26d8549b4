<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;

class AddPreviousExperienceFormFieldLookup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:56:add-previous-experience-form-field-lookup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 56
        Adds form lookup for previous experience field
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:56:add-previous-experience-form-field-lookup] Start");

        // Check if lookup already exists
        $exists = FormLookup::where('field_slug', 'previous-experience')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return 0;
        }

        // Find the section containing "previous experience"
        if(!$section = FormSection::where('label', 'LIKE', '%previous experience%')->first()) {
            $this->error("Can't find 'previous experience' section");
            return 1;
        }

        // Get the form field from this section
        if(!$field = FormField::where('form_section_id', $section->id)->first()) {
            $this->error("Can't find form field in the previous experience section");
            return 1;
        }

        // Create the new lookup
        $lookup = new FormLookup();
        $lookup->form_field_id = $field->id;
        $lookup->field_slug = 'previous-experience';
        $lookup->save();

        $this->line("[app-content-patch:56:add-previous-experience-form-field-lookup] Finish");

        return 0;
    }
} 