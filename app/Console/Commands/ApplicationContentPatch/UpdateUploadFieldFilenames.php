<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class UpdateUploadFieldFilenames extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:17:update-upload-field-filenames';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 17
        Updates the various file upload fields with a filename to use when pushing them to HQ.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:17:update-upload-field-filenames] Start");

        $sections = FormSection::all();
        foreach($sections as $section) {
            $fields = FormField::where('form_section_id', $section->id)->where('type', FormField::TYPE_UPLOAD)->get();
            foreach($fields as $field) {
                $this->line("update filename for section:{$section->label}\n");

                if(stripos($section->label, "AML") !== false) {
                    $field->filename = 'Anti-Money Laundering AML';
                    $field->save();
                } elseif(stripos($section->label, "lawsuit") !== false) {
                    $field->filename = 'Lawsuit-Court-Resolution';
                    $field->save();
                } elseif(stripos($section->label, "additional documents regarding your background")  !== false) {
                    $field->filename = 'Additional Background';
                    $field->save();
                } elseif(stripos($section->label, "Notice of Hearing") !== false) {
                    $field->filename = 'Notice of Hearing';
                    $field->save();
                } elseif(stripos($section->label, "state exam") !== false) {
                    $field->filename = 'Proof-Pass-Exam';
                    $field->save();
                } elseif(stripos($section->label, "voided check") !== false) {
                    $field->filename = 'Banking Authorization';
                    $field->save();
                } elseif(stripos($section->label, "payment plan") !== false) {
                    $field->filename = 'Payment Plan';
                    $field->save();
                } elseif(stripos($section->label, "pre-licensing course") !== false) {
                    $field->filename = 'Pre-Licensing Course';
                    $field->save();
                } elseif(stripos($section->label, "bankruptcy") !== false) {
                    $field->filename = 'Bankruptcy Documents';
                    $field->save();
                } elseif(stripos($section->label, "resolution of the charges") !== false) {
                    $field->filename = 'Resolution of Charges';
                    $field->save();
                } elseif(stripos($section->label, "Articles of Incorporation") !== false) {
                    $field->filename = 'Articles of Incorporation';
                    $field->save();
                } elseif(stripos($section->label, "supporting documentation") !== false) {
                    $field->filename = 'Supporting Documentation';
                    $field->save();
                } elseif(stripos($section->label, "E&O certificate") !== false) {
                    $field->filename = 'E&O Certificate';
                    $field->save();
                }
            }
        }

        $this->line("[app-content-patch:17:update-upload-field-filenames] Finish");

        return 0;
    }
}
