<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;

class UpdateSuranceBayAgreementFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:20:update-surance-bay-agreement-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 20
        Update SuranceBay content with new content.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:20:update-surance-bay-agreement-fields] Start");

        $page = FormPage::where('step_ident', 'surance-bay-agreement')->first();
        $section = FormSection::where('label', 'SuranceBay Signature Agreement')->first();
        $field = FormField::where('label', 'like', '%I hereby authorize Surancebay, LLC%')->first();

        if(!$page || !$section || !$field) {
            $this->warn("Target page, section, or field doesn't exist or has already been updated.");
            return;
        }
        
        $page->label = 'FAIR CREDIT REPORTING ACT';
        $page->save();

        $section->label = 'DISCLOSURE AND CONSENT FORM';
        $section->save();

        $field->label = 'This disclosure is being provided to you by Quility pursuant to the Fair Credit Reporting Act (“FCRA”). As used herein, “Quility” means Quility Holdings LLC and its direct and indirect subsidiaries, affiliates, officers, employees, agents and representatives. In connection with determining your eligibility: (i) to be appointed or sponsored as a licensed insurance agent of Quility, (ii) to be certified or authorized to produce or place business with Quility, including with respect to certain products or programs, and/or (iii) to maintain such appointments, certifications, or authorizations in one or more states, Quility may from time to time order reports through various vendors that validate or provide information on your producer licensing status, including insurance regulatory matters, and reports relating to your financial/credit and/or criminal background. These reports may include “consumer reports” from a “consumer reporting agency” (CRA), as such terms are defined under the FCRA.
        <br /><br />
        <strong>AUTHORIZATION TO ORDER CONSUMER REPORTS</strong>
        <br /><br />
        By signing below, I hereby voluntarily authorize Quility to obtain consumer reports relating to my producer licensing status, insurance regulatory, financial/credit, and criminal background, and to use those reports in connection with any insurance agent or producer license, appointment, certification, or other authorization, as described above, or which I may seek, whether now or in the future, in any jurisdiction. I hereby further voluntarily authorize Quility to disclose information in such reports, as well as information I may provide, as permitted by applicable law. This is a continuing authorization.
        <br /><br />
        For California, Oklahoma, and Minnesota Resident Producers: You have the right to request a copy of any consumer report we may order. Please contact us by mail at 104 Whitson Avenue, Swannanoa, NC to request any such copy. 
        <br /><br />
        <strong>For California Residents Only: ADDITIONAL DISCLOURES FOR CALIFORNIA RESIDENT PRODUCERS</strong>
        <br /><br />
        The consumer reports ordered, for the purposes described previously, may include what California law defines as an "investigative consumer report," which may contain information relating to your character, general reputation, personal characteristics, and mode of living. In addition to the right to receive a copy of any consumer report, the order, as described previously, you also have the right to visually inspect the files concerning you that are maintained by the consumer reporting agency, provided the inspection is during normal business hours and upon reasonable notice. The inspection can be done in person if you appear in person and furnish proper identification. "Proper identification" as used in this paragraph means information generally deemed sufficient to identify a person, which includes documents such as a valid driver\'s license, social security account number, military identification card and credit cards. If you are unable to reasonably identify yourself with the information described above, you may be required to provide additional information concerning your employment and personal or family history in order to verify your identity. You are entitled to be accompanied by one person of your choosing, who shall furnish reasonable identification. You may be required to furnish a written statement granting the consumer reporting agency permission to discuss your file in the presence of such person. You are entitled to a copy of the file for a fee not to exceed the actual costs of duplication services. The inspection can also be done via certified mail if you make a written request, with proper identification, for copies to be sent to a specified addressee. You can also request a summary of the information to be provided by telephone if you make a written request, with proper identification for telephone disclosure, and the toll charge, if any, for the telephone call is prepaid by or directly charged to you. The consumer reporting agency shall provide trained personnel to explain to you any of the information furnished to you, as well as a written explanation of any coded information contained in files maintained on you.';
        $field->save();

        $this->line("[app-content-patch:20:update-surance-bay-agreement-fields] Finish");

        return 0;
    }
}
