<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddNameSuffixField extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:30:add-name-suffix-field';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 30
        Add name suffix field
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:30:add-name-suffix-field] Start");

        //suffix field already there?
        $lookup = FormLookup::where('field_slug', 'personal-name-suffix')->first();
        $suffix_field = $lookup ? FormField::find($lookup->form_field_id) : null;
        if($suffix_field) {
            $this->warn("This patch has already run.");
            return;
        }

        $fp = FormPage::where('step_ident', 'personal-information')->first();
        if(!$fp) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        //get the last name field
        $last_name_field = FormField::find(FormLookup::where('field_slug', 'personal-last-name')->first()->form_field_id);
        if(!$last_name_field) {
            $this->error("Can't find Last Name Field");
            return;
        }

        // add field and lookup
        $suffix_field = FormBuilder::createField('Suffix', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $last_name_field->form_section_id, 3, '', 0, 1, 255, FormField::SELECT_NAME_SUFFIX);
        
        FormBuilder::addFormLookup($suffix_field->id, 'personal-name-suffix');

        $this->line("[app-content-patch:30:add-name-suffix-field] Finish");

        return 0;
    }
}
