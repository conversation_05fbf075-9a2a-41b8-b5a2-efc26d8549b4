<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormLookup;
use App\Models\FormOption;
use Illuminate\Console\Command;
use DB;

class RestoreNpnStateDC extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:08:restore-npn-state-dc';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 8
        Restore District of Columbia for Npn State form option";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:08:restore-npn-state-dc] Start");

        $stateValue = 'DC';
        $stateLabel = 'DISTRICT OF COLUMBIA';

        $fl = FormLookup::where('field_slug', FormLookup::LICENSE_STATE)->first();
        if(!$fl) {
            $this->error("Can't find " . FormLookup::LICENSE_STATE);
            return;
        }

        $fo = FormOption::where('form_field_id', $fl->form_field_id)->where('value', $stateValue)->first();
        if($fo) {
            $this->warn("This patch has already run.");
        } else {
            $firstOption = $fl->formField->formOptions->last();
            if($firstOption)
                $sortOption = $firstOption->sort + 1;
            else
                $sortOption = 1;

            $option = new FormOption;
            $option->form_field_id = $fl->form_field_id;
            $option->label = $stateLabel;
            $option->value = $stateValue;
            $option->sort = $sortOption;
            $option->save();

            $this->info("New FormOption [npn-state/DC] is added. Sort: " . $sortOption);
        }

        $this->line("[app-content-patch:08:restore-npn-state-dc] Finish");

        return 0;
    }
}
