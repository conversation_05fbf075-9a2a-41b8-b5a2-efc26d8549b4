<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;

class MakeCurrentAddressToNotRequired extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:06:make-current-address-to-not-required';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 6
        Current Adress / To should not be required
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:06:make-current-address-to-not-required] Start");
        // Get the target section
        $fs = FormSection::where('label', 'Confirm Your Current Address')->first();
        if(!$fs) {
            $this->error("Target section [Confirm Your Current Address] doesn't exist!");
            return;
        }
        
        // Get the target field
        $ff = $fs->formFields()->where('label', 'To')->first();
        if(!$ff) {
            $this->error("Target form field [To] doesn't exist!");
            return;
        } else if(!$ff->is_required) {
            $this->warn("This patch has already run.");
            return;
        }

        // Check if is already moved
        $ff->is_required = 0;
        $ff->save();

        $this->line("[app-content-patch:06:make-current-address-to-not-required] Finish");

        return 0;
    }
}
