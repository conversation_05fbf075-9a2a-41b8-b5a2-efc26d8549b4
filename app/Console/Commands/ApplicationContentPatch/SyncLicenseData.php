<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\Agent;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\UserEntry;
use Illuminate\Console\Command;
use FormBuilder;
use DB;

class SyncLicenseData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:36:sync-license-data-from-hq';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 36
        Syncs license data from HQ back into the OBP
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:36:sync-license-data-from-hq] Start");

        $users = DB::select("select 
                u.name
                , u.created_at
                , u.id
                , u.email
                , a.AgentCode
                , u.approved_by_ao
                , a.NPN AS HQNPN
                , npn.value AS NPN 
                , npn.updated_at as NPN_Updated
                , a.ContractStartDt
                , lic.value as Licensed
                , lic.updated_at as DateLicensed
            from users u
            join TempAgentsFromHQ a
                on a.AgentCode = u.agent_code
            LEFT JOIN user_entries npn 
                ON npn.user_id = u.id 
                AND npn.form_field_id = '02F817FC-7352-4DF4-B2D9-4AD084DB5877'
            LEFT JOIN user_entries lic 
                ON lic.user_id = u.id 
                AND lic.form_field_id = '6305EBB8-9FDE-4657-909C-BC0958AA6846'
            where a.NPN IS NOT NULL
            -- 	and npn.value IS NULL
             	and (lic.value IS NULL or lic.value = 'NO')");
        $count = count($users);
        $i = 0;
        foreach($users as $user) {
            // $this->line($agent)
            print_r($user);
            
            $licensed_field = FormLookup::where('field_slug', FormLookup::LICENSE_IS_LICENSED_BOOL)->first();
            $entry = UserEntry::where('user_id', $user->id)->where('form_field_id', $licensed_field->form_field_id)->first();

            if($user->Licensed == NULL && !isset($entry->value)) {
                // NULL would indicate that the Licensed question is unanswered
                // create a new entry with the updated date matching the contract start date
                $entry = UserEntry::create([
                    'user_id' => $user->id,
                    'form_field_id' => $licensed_field->form_field_id,
                    'value' => 'YES',
                    'sort' => 0
                ]);
                echo "\nentry: ".$entry->id;

                // manually override create and update dates
                $entry->updated_at = $user->ContractStartDt;
                $entry->created_at = $user->created_at; //using this so that it's before the updated_at date
                $entry->save();

            } elseif ($user->Licensed == 'NO') {
                echo "\nentry: ".$entry->id;

                // update the entry from NO to YES and the updated_at date as the contract start date
                $entry->value = 'YES';
                $entry->updated_at = $user->ContractStartDt;
                if($entry->created_at > $entry->updated_at)
                    $entry->created_at = $user->created_at;
                $entry->save();
            }

            // save the npn field
            $npn_field = FormLookup::where('field_slug', FormLookup::LICENSE_NPN)->first();
            $entry = UserEntry::updateOrCreate([
                'user_id' => $user->id,
                'form_field_id' => $npn_field->form_field_id,
                'value' => $user->HQNPN,
                'sort' => 0
            ]);
            echo "\nentry: ".$entry->id;
            $entry->updated_at = $user->ContractStartDt;
            $entry->created_at = $user->ContractStartDt;
            $entry->save();

            $i++;
            echo "\nDone $i of $count";
            echo "\n=================================\n";
        }

        $this->line("[app-content-patch:36:sync-license-data-from-hq] Finish");

        return 0;
    }
}
