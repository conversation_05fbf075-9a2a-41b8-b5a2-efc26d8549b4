<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use Carbon\Carbon;
use FormBuilder;

class ReorganizeLicensingQuestions extends Command
{
    protected $signature = 'app-content-patch:58:reorganize-licensing-questions';
    
    protected $description = 'Reorganizes licensing questions and adds pathway indicators';

    public function handle()
    {
        $this->line("[app-content-patch:58:reorganize-licensing-questions] Start");

        // TODO: Check if this has already been run


        // Get the license information page
        $page = FormPage::where('step_ident', 'license-information')->first();
        if (!$page) {
            $this->error("Cannot find License Information page");
            return 1;
        }

        // get is_licensed_boolean field id
        $isLicensedBooleanFieldId = FormLookup::where('field_slug', 'is-licensed-boolean')->first()->form_field_id;

        // PREVIOUS EXPERIENCE SECTION
        if ($experienceSection = FormSection::where('label', 'Do you have previous experience in life insurance sales?')->where('status', 'active')->first()) {
            // see if condition already exists
            $existingCondition = FormFieldCondition::where('form_field_id', $isLicensedBooleanFieldId)
                ->where('form_section_id', $experienceSection->id)
                ->first();

            // create a new conditional display for the previous experience section
            if (!$existingCondition) {
                $condition = new FormFieldCondition;
                $condition->form_field_id = $isLicensedBooleanFieldId;
                $condition->form_section_id = $experienceSection->id;
                $condition->value = FormField::SELECT_BOOL_YES;
                $condition->action = FormFieldCondition::ACTION_SHOW;
                $condition->type = FormFieldCondition::TYPE_EQUALS;
                $condition->save();
            }

            // MOVE THE LOA SECTION UP
            if ($loaSection = FormSection::where('label', 'What lines of authority do you or will you have on your insurance license? (Check all that apply)')->where('status', 'active')->first()) {
                $loaSection->sort = $experienceSection->sort - 1;
                $loaSection->save();
            }

            $contractedSection = FormSection::where('label', 'Were you previously contracted with Symmetry or another BGA/IMO?')->where('status', 'active')->first();
            if(!$contractedSection) {
                // GET PREVIOUS EXPERIENCE FORM FIELD
                $previousExperienceField = FormField::where('form_section_id', $experienceSection->id)->first();



                // CREATE NEW SECTION "PREVIOUS CONTRACTS"
                $previousContractsSection = FormBuilder::createSection('Were you previously contracted with Symmetry or another BGA/IMO?', $page->id, $experienceSection->sort + 1, $previousExperienceField->id, FormField::SELECT_BOOL_YES);
                
                // CREATE SELECT FIELD
                $selectField = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $previousContractsSection->id, $experienceSection->sort + 2, 'Please Select', 1, 0, 255, FormField::SELECT_ACTIVE_CONTRACTS);
                
                // CREATE LOOKUP FOR PREVIOUSLY CONTRACTED
                $previously_contracted_lookup = new FormLookup();
                $previously_contracted_lookup->form_field_id = $selectField->id;
                $previously_contracted_lookup->field_slug = FormLookup::Q2A_PREVIOUSLY_CONTRACTED;
                $previously_contracted_lookup->save();

                // CREATE FIRST SUB SECTION
                $subSection1 = FormBuilder::createSection('Do any of those contracts remain active?', $page->id, $previousContractsSection->sort + 3, $selectField->id, 'symmetry');
                $subField1 =FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $subSection1->id, $previousContractsSection->sort + 4, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
                
                // CREATE LOOKUP FOR ACTIVE SYMMETRY CONTRACTS
                $lookup1 = new FormLookup();
                $lookup1->form_field_id = $subField1->id;
                $lookup1->field_slug = FormLookup::Q2A_ACTIVE_SYMMETRY_CONTRACTS;
                $lookup1->save();



                // CREATE SECOND SUB SECTION
                $subSection2 = FormBuilder::createSection('Do any of these contracts remain active?', $page->id, $previousContractsSection->sort + 5, $selectField->id, 'other');
                $subField2 = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $subSection2->id, $previousContractsSection->sort + 6, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                // CREATE LOOKUP FOR ACTIVE OTHER CONTRACTS
                $lookup2 = new FormLookup();
                $lookup2->form_field_id = $subField2->id;
                $lookup2->field_slug = FormLookup::Q2A_ACTIVE_OTHER_CONTRACTS;
                $lookup2->save();

            }

            // CHECK IF ADVANCED MARKET SECTION ALREADY EXISTS
            if (!FormSection::where('label', 'Do you plan to offer Advanced Market Products (IUL, IBC, Annuities)?')->where('status', 'active')->first()) {
                
                // GET THE EXPERIENCE SECTION QUESTION
                $experienceSectionQuestion = FormField::where('form_section_id', $experienceSection->id)->first();
                
                // CREATE NEW SECTION FOR ADVANCED MARKET PRODUCTS
                $advancedMarketSection = FormBuilder::createSection(
                    'Do you plan to offer Advanced Market Products (IUL, IBC, Annuities)?',
                    $page->id,
                    200,
                    $experienceSectionQuestion->id,
                    FormField::SELECT_BOOL_YES
                );

                // CREATE SELECT FIELD FOR ADVANCED MARKET PRODUCTS
                $amField = FormBuilder::createField(
                    '',
                    FormField::TYPE_SELECT,
                    FormField::WIDTH_THIRD,
                    $advancedMarketSection->id,
                    210,
                    'Please Select',
                    1,
                    0,
                    255,
                    FormField::SELECT_BOOL_YES_NO_MAYBE
                );

                // CREATE LOOKUP FOR ADVANCED MARKETS
                $lookup3 = new FormLookup();
                $lookup3->form_field_id = $amField->id;
                $lookup3->field_slug = FormLookup::Q2A_ADVANCED_MARKETS;
                $lookup3->save();
            }

        }

        // DEACTIVATE THE TRANSFERRING SECTION
        if ($transferringSection = FormSection::where('label', 'Are you transferring from another IMO or insurance agency?')->where('status', 'active')->first()) {
            $transferringSection->status = 'inactive';
            $transferringSection->save();
        }

        // DEACTIVATE THE RETURNING SECTION 
        if ($returningSection = FormSection::where('label', 'Are you returning to Symmetry?')->where('status', 'active')->first()) {
            $returningSection->status = 'inactive';
            $returningSection->save();
        }
        
        // DEACTIVATE THE "Do you have life on your health insurance license?" SECTION
        if ($lifeOnHealthInsuranceSection = FormSection::where('label', 'Do you have life on your health insurance license?')->where('status', 'active')->first()) {
            $lifeOnHealthInsuranceSection->status = 'inactive';
            $lifeOnHealthInsuranceSection->save();
        }
        
        // DEACTIVATE THE "Within the last 6 months, have you written business or signed a contract with any of the following carriers?" SECTION
        // if ($writtenBusinessSection = FormSection::where('label', 'LIKE', 'Within the last 6 months, have you written business or signed a contract with any of the following carriers?%')->where('status', 'active')->first()) {
        //     $writtenBusinessSection->status = 'inactive';
        //     $writtenBusinessSection->save();
        // }
        
        
        $this->line("[app-content-patch:58:reorganize-licensing-questions] Finish");
        return 0;
    }

    
}