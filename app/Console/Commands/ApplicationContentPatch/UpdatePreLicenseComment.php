<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class UpdatePreLicenseComment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:09:update-pre-license-comment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 9
        Only show pre-licensing comment when the answer is YES.
        Update contact message when answer is NO.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:09:update-pre-license-comment] Start");

        $licensePage = FormPage::where('label', 'Insurance License Information')->first();
        $lookupPrelicenseCourseSelect = FormLookup::where('field_slug', FormLookup::PRE_LICENSE_STATUS)->first();
        if(!$licensePage || !$lookupPrelicenseCourseSelect) {
            $this->error("Target page or field doesn't exist!");
            return;
        }
        
        // Update contact message when answer is NO.
        $ff = FormField::where('label', 'LIKE', '%Your application progress up to this point will be saved.')->first();
        if(!$ff) {
            $this->error("Target field [Pre-licensing => NO / Contact Message] doesn't exist!");
            return;
        }

        $newText = "<b>It's time to enroll in a pre-licensing course! Please reach out to your AO (emphasis mine). Your application progress up to this point will be saved.</b>";
        if($newText == $ff->label) {
            $this->warn("This patch has already run.");
            return;
        }

        $ff->label = $newText;
        $ff->save();
        
        // Only show pre-licensing comment when the answer is YES.
        $ffPreLicensingYesComment = FormField::where('label', 'LIKE', "%Once you've passed your state exam, please come back to this page and complete your application.</p>")->first();
        if($ffPreLicensingYesComment) {
            $ffPreLicensingYesComment->delete();
        }

        foreach($licensePage->formSections()->where('sort', '>=', 24) as $section) {
            $section->sort = $section->sort + 1;
            $section->save();
        }

        $section = FormBuilder::createSection('', $licensePage->id, 24, $lookupPrelicenseCourseSelect->form_field_id, FormField::SELECT_BOOL_YES);
        FormBuilder::createField(
            "<p class='mb-0'>Once you have completed your pre-licensing course, your next step is to register and complete your state exam.</p>
            <a target='_blank' href='https://www.examfx.com/insurance-prelicensing-training/view-insurance-state-requirements'>Click on the link for requirements in your state</a>
            <p class='mt-2'>Once you've passed your state exam, please come back to this page and complete your application.</p>", 
            FormField::TYPE_HTML, 
            FormField::WIDTH_FULL, 
            $section->id
        );

        $this->line("[app-content-patch:09:update-pre-license-comment] Finish");

        return 0;
    }
}
