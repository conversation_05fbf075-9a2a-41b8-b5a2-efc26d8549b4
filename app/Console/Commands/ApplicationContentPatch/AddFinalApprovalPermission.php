<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class AddFinalApprovalPermission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:05:add-final-approval-permission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 5
        Add new permission [final approve applications] to [Staff, AgencyOwner]
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:05:add-final-approval-permission] Start");
        
        Permission::firstOrCreate(['name' => 'final approve applications']);

        $role = Role::where(['name' => User::ROLE_TYPE_STAFF])->first();
        if(!$role) {
            $this->error("Can't find target role [Staff]");
            return;
        }

        $role->givePermissionTo([
            'final approve applications',
        ]);

        $role = Role::where(['name' => User::ROLE_TYPE_SUPER_ADMIN])->first();
        if(!$role) {
            $this->error("Can't find target role [SupeAdmin]");
            return;
        }

        $role->givePermissionTo([
            'final approve applications',
        ]);

        $this->line("[app-content-patch:05:add-final-approval-permission] Finish");

        return 0;
    }
}
