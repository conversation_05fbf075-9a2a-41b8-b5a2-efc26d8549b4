<?php

// namespace App\Console\Commands\ApplicationContentPatch;

// use App\Models\FormField;
// use App\Models\FormPage;
// use App\Models\FormSection;
// use App\Models\FormLookup;
// use Illuminate\Console\Command;
// use FormBuilder;

// class AddMoreReturningAgentQuestionsNew extends Command
// {
//     /**
//      * The name and signature of the console command.
//      *
//      * @var string
//      */
//     protected $signature = 'app-content-patch:35:add-more-returning-questions-new';

//     /**
//      * The console command description.
//      *
//      * @var string
//      */
//     protected $description = "
//         Application Content Patch - No 35
//         Adds questions for returning Symmetry agents
//     ";

//     /**
//      * Create a new command instance.
//      *
//      * @return void
//      */
//     public function __construct()
//     {
//         parent::__construct();
//     }

//     /**
//      * Execute the console command.
//      *
//      * @return int
//      */
//     public function handle()
//     {
//         $this->line("[app-content-patch:35:add-more-returning-questions] Start");

//         $exists = FormSection::where('label', 'Are you returning to Symmetry?')->where('status', 'active')->count();
//         if($exists > 0) {
//             $this->warn("This patch has already run.");
//             return;
//         }

//         $page = FormPage::where('step_ident', 'license-information')->first();
//         if(!$page) {
//             $this->error("Can't find License Information Page");
//             return;
//         }

//         $sort = 60; //force to bottom of page

//         $section = FormBuilder::createSection('Are you returning to Symmetry?', $page->id, $sort);
//         $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

//             // If (YES) returning to Symmetry
//             $subSection = FormBuilder::createSection('Please enter your SFG Agent Code, if available.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
//             $text_field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $subSection->id, $sort++, 'SFG Agent Code', 0);

//         $this->line("[app-content-patch:35:add-more-returning-questions] Finish");

//         return 0;
//     }
// }
