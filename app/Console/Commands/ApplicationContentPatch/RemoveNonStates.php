<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormLookup;
use App\Models\FormOption;
use Illuminate\Console\Command;

class RemoveNonStates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:03:remove-non-states';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 3
        Remove Non States
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:03:remove-non-states] Start");

        $removeStates = ['AMERICAN SAMOA', 'ARMED FORCES AFRICA \ CANADA \ EUROPE \ MIDDLE EAST', 'ARMED FORCES AMERICA (EXCEPT CANADA)',
            'ARMED FORCES PACIFIC', 'FEDERATED STATES OF MICRONESIA', 'GUAM GU', 'MARSHALL ISLANDS', 'NORTHERN MARIANA ISLANDS',
            'PALAU', 'VIRGIN ISLANDS'];

        $fl = FormLookup::where('field_slug', FormLookup::LICENSE_STATE)->first();
        if(!$fl) {
            $this->error("Can't find " . FormLookup::LICENSE_STATE);
            return;
        }

        $fo = FormOption::where('form_field_id', $fl->form_field_id)->get();
        foreach($fo as $o){
            if(in_array($o->label, $removeStates)){
                $o->delete();
            }
        }

        $this->line("[app-content-patch:03:remove-non-states] Finish");

        return 0;
    }
}
