<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;
use FormBuilder;

class AddCarrierContractQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:48:add-carrier-contract-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 48
        Adds carrier contract questions
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:48:add-carrier-contract-questions] Start");

        $exists = FormSection::where('label', 'LIKE', 'Within the last 6 months, have you written business or signed a contract with any of the following carriers?%')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        if(! $section = FormSection::where('label', 'Do you have previous experience in life insurance sales?')->first()){
            $this->error("Can't find 'previous experience' section");
            return;
        }

        // The answer/select field regarding having previous experience selling life insurance
        $field = FormField::where('form_section_id', $section->id)
            ->where(function ($query) {
                $query->whereNull('active_start')
                    ->orWhere('active_start', '<=', date('Y-m-d'));
            })
            ->where(function ($query) {
                $query->whereNull('active_end')
                    ->orWhere('active_end', '>', date('Y-m-d'));
            })
            ->first();

        

        $sort = 100; //force to bottom of page

        $carriers = [
            'American Amicable/Occidental',
            'AIG',
            'Americo',
            'Banner/LGA',
            'F&G',
            'Foresters',
            'Mutual of Omaha',
            'SBLI',
            'UHL'
        ];

        $subSection = FormBuilder::createSection('Within the last 6 months, have you written business or signed a contract with any of the following carriers? '.implode(", ", $carriers), $page->id, $sort++, $field->id, FormField::SELECT_BOOL_YES);
        $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $subSection->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        $subSection2 = FormBuilder::createSection('Select the appropriate carriers and enter the required dates.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
        
        foreach($carriers as $carrier) {
            $carrierSection = FormBuilder::createSection('', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
            $checkbox = FormBuilder::createField($carrier, FormField::TYPE_CHECKBOX, FormField::WIDTH_HALF, $carrierSection->id, $sort++, 0, 0);
            $dateSection = FormBuilder::createSection('', $page->id, $sort++, $checkbox->id, FormField::CHECKBOX_CHECKED);
            FormBuilder::createField('When was your signed contract date?', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $dateSection->id, $sort++);
            FormBuilder::createField('When was the last piece of business written?', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $dateSection->id, $sort++);
        }
        
        $this->line("[app-content-patch:48:add-carrier-contract-questions] Finish");

        return 0;
    }
}
