<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class FixIncorrectApprovalStatuses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:27:fix-incorrect-approval-statuses';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 27
        Fix 'ho-approved' statuses that should be 'ao-approved'
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:27:fix-incorrect-approval-statuses] Start");
        
        //The ao_approved status that we want to use to correct the old data with
        $ao_approved_status = UserStatus::where('slug', 'ao-approved')->first();
        
        // These are the records that were updated to 'ho-approved' when they should actually be 'ao-approved'
        // If the agency_owner on the app is the trigger_id on the approval record, then it must be an 'ao-approved' record, not 'ho-approved'
        $update_history_records = DB::select("SELECT ush.id ush_id, ush.created_at, u.agency_owner_code, u2.agent_code, u2.name, u2.email, u.name as recruit_name, u.id as recruit_id
            FROM user_status_history ush
            JOIN users u on u.id = ush.user_id
            JOIN user_statuses us on us.id = ush.user_status_id
            JOIN users u2 on u2.id = ush.trigger_id
            WHERE us.slug = 'ho-approved'
            and u.agency_owner_code = u2.agent_code
            ORDER BY ush.created_at DESC");

        $count = count($update_history_records);
        $this->line("Updating {$count} records");

        //update each record individually
        foreach($update_history_records as $record) {
            $ush = UserStatusHistory::find($record->ush_id);
            $ush->user_status_id = $ao_approved_status->id;
            $ush->save();
        }

        $this->line("[app-content-patch:27:fix-incorrect-approval-statuses] Finish");
        
        return 0;

    }
}
