<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class AddNewUserRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:12:add-new-userrole';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 12
        Add new user role - UnlicensedAgent
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:12:add-new-userrole] Start");

        if(Role::where('name', User::ROLE_TYPE_UNLICENSED_AGENT)->first()) {
            $this->warn("This patch has already run.");
            return;
        }

        // create roles and set perms
        $role = Role::create(['name' => User::ROLE_TYPE_UNLICENSED_AGENT]);
        $role->givePermissionTo([
            'fill applications',
            'fill fields',
            'license guide',
            'company information',
        ]);

        $this->line("[app-content-patch:12:add-new-userrole] Finish");

        return 0;
    }
}
