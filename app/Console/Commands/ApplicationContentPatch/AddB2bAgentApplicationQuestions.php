<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormLookup;
use App\Models\UserStatus;
use Illuminate\Console\Command;
use FormBuilder;

class AddB2bAgentApplicationQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:55:add-b2b-agent-application-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 55
        Adds b2b agent application questions
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:55:add-b2b-agent-application-questions] Start");

        // Check if the page already exists
        $exists = FormPage::where('application_slug', 'b2b-agent')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        // Add the new page
        $page = FormPage::create([
            'label' => 'Agent Application',
            'status' => 'active', 
            'step_ident' => 'b2b-agent-information',
            'subline' => '',
            'sort' => 1,
            'application_slug' => 'b2b-agent'
        ]);

        // create page status record
        $status = UserStatus::create([
            'name' => 'Agent Information',
            'slug' => 'b2b-agent-info',
            'form_page_id' => $page->id
        ]);

        $sort = 1;

        $section = FormBuilder::createSection('Name', $page->id, $sort);
        $first_name_field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, $sort++, 'First Name');
        $last_name_field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, $sort++, 'Last Name');

        // add name lookups
        FormBuilder::addFormLookup($first_name_field->id, FormLookup::B2B_AGENT_FIRST_NAME);
        FormBuilder::addFormLookup($last_name_field->id, FormLookup::B2B_AGENT_LAST_NAME);

        $section = FormBuilder::createSection('Date of Birth', $page->id, $sort++);
        $dob_field = FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $section->id, $sort++);

        // add lookup for b2b agent dob
        FormBuilder::addFormLookup($dob_field->id, FormLookup::B2B_AGENT_DOB);

        $section = FormBuilder::createSection('State of Residence', $page->id, $sort++);
        $state_field =FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, false, 1, 0, 100, FormField::SELECT_STATE);

        // add lookup for state
        FormBuilder::addFormLookup($state_field->id, FormLookup::B2B_AGENT_STATE);

        $section = FormBuilder::createSection('SSN', $page->id, $sort++);
        $ssn_field = FormBuilder::createField('', FormField::TYPE_SSN, FormField::WIDTH_THIRD, $section->id, $sort++, false, 1, 1, 20);

        // add lookup for ssn
        FormBuilder::addFormLookup($ssn_field->id, FormLookup::B2B_AGENT_SSN);

        $section = FormBuilder::createSection('Preferred Email', $page->id, $sort++);
        $email_field = FormBuilder::createField('', FormField::TYPE_EMAIL, FormField::WIDTH_THIRD, $section->id, $sort++);

        // add lookup for EMAIL
        FormBuilder::addFormLookup($email_field->id, FormLookup::B2B_AGENT_EMAIL);

        $section = FormBuilder::createSection('Entity Name', $page->id, $sort);
        $entity_name_field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++, false, 0);
        FormBuilder::addFormLookup($entity_name_field->id, FormLookup::B2B_AGENT_ENTITY_NAME);
        
        $section = FormBuilder::createSection('EIN', $page->id, $sort);
        $ein_field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++, false, 0);

        // add lookup for EIN
        FormBuilder::addFormLookup($ein_field->id, FormLookup::B2B_AGENT_EIN);
        
        // $section = FormBuilder::createSection('Group Name', $page->id, $sort);
        // FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++);
        
        // // downline page
        // $page = FormPage::create([
        //     'label' => 'Downline',
        //     'status' => 'active', 
        //     'step_ident' => 'principal-downline',
        //     'subline' => 'Please provide information on your downline.',
        //     'sort' => 2,
        //     'application_slug' => 'b2b-principal'
        // ]);

        // // create page status record
        // $status = UserStatus::create([
        //     'name' => 'Principal Downline',
        //     'slug' => 'principal-downline',
        //     'form_page_id' => $page->id
        // ]);

        // $section = FormBuilder::createSection('Will you have a downline?', $page->id, $sort);
        // $has_downline_field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        //     // If (YES) to having a downline (above)
        //     $section = FormBuilder::createSection('Number of agents', $page->id, $sort++, $has_downline_field->id, FormField::SELECT_BOOL_YES);
        //     FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++);
            
        //     $section = FormBuilder::createSection('Do you have agents on "advancing" or "as earned"?', $page->id, $sort++, $has_downline_field->id, FormField::SELECT_BOOL_YES);
        //     FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 255, ['Advancing' => 'Advancing', 'As Earned' => 'As Earned']);

        //     $section = FormBuilder::createSection('Do you want a vector check on all agents?', $page->id, $sort++, $has_downline_field->id, FormField::SELECT_BOOL_YES);
        //     $vector_field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        //     // add lookup for EIN
        //     FormBuilder::addFormLookup($vector_field->id, FormLookup::B2B_AGENT_VECTOR_CHECK);

        // B2b Agent Contracts page
        $page = FormPage::create([
            'label' => 'Contracts',
            'status' => 'active', 
            'step_ident' => 'b2b-agent-contracts',
            'subline' => 'Please provide information on contract statuses.',
            'sort' => 3,
            'application_slug' => 'b2b-agent'
        ]);

        // create page status record
        $status = UserStatus::create([
            'name' => 'B2b Agent Contracts',
            'slug' => 'b2b-agent-contracts',
            'form_page_id' => $page->id
        ]);

        $section = FormBuilder::createSection('In the past 2 years, have you been contracted with another IMO or Agency?', $page->id, $sort++);
        $contracted_field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            $section = FormBuilder::createSection('Do you have any active contracts affiliated with your previous IMO?', $page->id, $sort++, $contracted_field->id, FormField::SELECT_BOOL_YES);
            $active_contracts_field = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                $carriers = [
                    'American Amicable/Occidental',
                    'AIG',
                    'Americo',
                    'Banner/LGA',
                    'F&G',
                    'Foresters',
                    'Mutual of Omaha',
                    'SBLI',
                    'UHL'
                ];
        
                $subSection = FormBuilder::createSection('Within the last 6 months, have you written business or signed a contract with any of the following carriers? '.implode(", ", $carriers), $page->id, $sort++, $active_contracts_field->id, FormField::SELECT_BOOL_YES);
                $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $subSection->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
                $subSection2 = FormBuilder::createSection('Select the appropriate carriers and enter the required dates.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
                
                foreach($carriers as $carrier) {
                    $carrierSection = FormBuilder::createSection('', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
                    
                    $checkbox = FormBuilder::createField(
                        $carrier, 
                        FormField::TYPE_CHECKBOX, 
                        FormField::WIDTH_HALF, 
                        $carrierSection->id, 
                        $sort++, 
                        0,
                        0
                    );
                    
                    $dateSection = FormBuilder::createSection('', $page->id, $sort++, $checkbox->id, FormField::CHECKBOX_CHECKED);
                    
                    FormBuilder::createField(
                        'Enter the date the last policy was submitted.', 
                        FormField::TYPE_DATE, 
                        FormField::WIDTH_HALF, 
                        $dateSection->id, 
                        $sort++,
                        0,
                        0
                    );
                }


                $uploadSection = FormBuilder::createSection('Please upload your release form.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
                    
                FormBuilder::createField(
                    '', 
                    FormField::TYPE_UPLOAD, 
                    FormField::WIDTH_HALF, 
                    $uploadSection->id, 
                    $sort++
                );
                
        $this->line("[app-content-patch:55:add-b2b-agent-application-questions] Finish");

        return 0;
    }
}
