<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class UpdateAmlComment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:10:update-aml-comment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 10
        Update comment on aml page
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:10:update-aml-comment] Start");

        $amlPage = FormPage::where('label', 'Anti-Money Laundering')->first();
        if(!$amlPage) {
            $this->error("Target page doesn't exist!");
            return;
        }
        
        // Update message
        $ff = FormField::where('label', 'LIKE', 'In order to be contracted with a carrier, you must provide proof of completing Anti-Money Laundering (AML) training.%')->first();
        if(!$ff) {
            $this->error("Target field doesn't exist!");
            return;
        }

        $newText = "In order to be contracted with a carrier, you must provide proof of completing Anti-Money Laundering (AML) training. Please see the below link to our recommended AML provider. While you aren't required to go with this provider, we highly recommend and encourage it as all carriers will accept this AML. There are free options to go with, but we cannot guarantee that a carrier will accept it.";
        if($newText == $ff->label) {
            $this->warn("This patch has already run.");
            return;
        }

        $ff->label = $newText;
        $ff->save();

        $this->line("[app-content-patch:10:update-aml-comment] Finish");

        return 0;
    }
}
