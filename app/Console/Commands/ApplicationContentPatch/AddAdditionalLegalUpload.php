<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class AddAdditionalLegalUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:14:add-additional-legal-upload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 14
        Add Additional Legal Upload Document
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:14:add-additional-legal-upload] Start");

        $fp = FormPage::where('step_ident', 'legal-information')->first();
        if(!$fp) {
            $this->error("Can't find Legal Information Page");
            return;
        }

        // Upload Section already there?
        $fs = $fp->formSections()->where('label', 'Please upload any additional documents regarding your background questions and answers here.')->first();
        if($fs) {
            $this->warn("This patch has already run.");
            return;
        }

        // Shift first field of target section
        $fsToShift = $fp->formSections()->where('label', 'Please read and agree to the terms below.')->first();
        $sortShiftingFrom = $fsToShift->sort;

        foreach( $fp->formSections()->where('sort', '>=', $sortShiftingFrom)->get() as $_fs ) {
            $_fs->sort = $_fs->sort + 1;
            $_fs->save();
        }

        // Create Upload Section
        $section = FormBuilder::createSection('Please upload any additional documents regarding your background questions and answers here.', $fp->id, $sortShiftingFrom);
        FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, 0, '', 0)->markAsPii();

        $this->line("[app-content-patch:14:add-additional-legal-upload] Finish");

        return 0;
    }
}
