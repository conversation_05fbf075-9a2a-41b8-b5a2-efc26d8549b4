<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class RemoveLicenseCommentTypo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:13:remove-license-comment-typo';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 13
        Remove Wrong typo from License page
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:13:remove-license-comment-typo] Start");

        $ff = FormField::where('label', 'LIKE', '%(emphasis mine)%')->first();
        if(!$ff) {
            $this->error("Target field doesn't exist!");
            return;
        }
        
        $ff->label = "<b>It's time to enroll in a pre-licensing course! Please reach out to your AO. Your application progress up to this point will be saved.</b>";
        $ff->save();

        $this->line("[app-content-patch:13:remove-license-comment-typo] Finish");

        return 0;
    }
}
