<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormPage;
use App\Models\FormField;
use App\Models\FormSection;
use Illuminate\Console\Command;
use Carbon\Carbon;
use FormBuilder;
use DB;

class AddNewLegalQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:25:add-new-legal-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 25
        Add New Legal Questions
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:25:add-new-legal-questions] Start");

        // get the page for the legal questions section
        $page = FormPage::where('step_ident', 'legal-information')->first();
        if(!$page) {
            $this->error("Can't find Legal Information Page");
            return;
        }

        // already ran?
        $updated_sections = FormSection::whereNotNull('active_end')->count();
        if($updated_sections > 0) {
            $this->warn("This patch has already ran.");
            return;
        }


        
        
        // get the section id for the section containing the ssn, since we don't want to change that one
        $ssn_section_id = DB::select("SELECT ff.form_section_id FROM form_lookups fl
            join form_fields ff ON ff.id = fl.form_field_id 
            where fl.field_slug like '%ssn%';")[0]->form_section_id;

        // set the start date for the section in the past to ensure it's active for everyone
        $ssn_section = FormSection::find($ssn_section_id);
        $ssn_section->active_start = '2022-01-01';
        $ssn_section->save();

        // no change to the agreement section either
        $agree_section_id = DB::select("SELECT * FROM form_fields ff
            join form_sections fs on fs.id = ff.form_section_id
            where ff.label like '%I agree%' 
            and fs.form_page_id = ?", [$page->id])[0]->form_section_id;

        // set the start date for the section in the past to ensure it's active for everyone
        $agree_section = FormSection::find($agree_section_id);
        $agree_section->active_start = '2022-01-01';
        $agree_section->save();


        // get all sections attached to the legal-information page
        $sections = FormSection::where('form_page_id', $page->id)->get();

        // update previous sections (except for ssn) with new start/end dates
        // set end date to today
        foreach($sections as $section) {
            if($section->id != $ssn_section_id && $section->id != $agree_section_id) {
                $section->active_end = Carbon::today();
                $section->save();
            }
        }



        /***************************************
         *
         * Create the new sections/fields
         * 
         ***************************************/

        $sort = 1;
        $tomorrow = Carbon::tomorrow();

        // 0 - ssn section - no change

        // 1 - top level
        $section = FormBuilder::createSection('1. Have you ever been charged, convicted of, plead guilty or no contest, or have any current charges pending to any felony, Misdemeanor, federal/state insurance and/or securities or investments regulations and statutes, or have you ever been on probation?', $page->id, $sort);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            // 1A
            $section = FormBuilder::createSection('1A. Have you ever been charged with any felony?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'sentencing', 'name'], $page->id, $sort++, $subSelect->id);

            // 1B
            $section = FormBuilder::createSection('1B. Have you ever been convicted of or pled guilty or no contest to any felony?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'sentencing', 'name'], $page->id, $sort++, $subSelect->id);

            // 1C
            $section = FormBuilder::createSection('1C. Did you file a 1033 form in any state due to felony charges covered by 18USC 1033?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                // upload waiver form
                $waiver_form_section = FormBuilder::createSection('Please upload a copy of your waiver form.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $waiver_form_section->active_start = $tomorrow;
                $waiver_form_section->save();
                FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $waiver_form_section->id, $sort++, false, 1, 0, 255, false, '', '1033 Waiver Form')->markAsPii();

            // 1D
            $section = FormBuilder::createSection('1D. Have you ever been on probation for any felony?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                // Explanation
                $explanation_section = FormBuilder::createSection('Please provide an explanation.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $explanation_section->active_start = $tomorrow;
                $explanation_section->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $explanation_section->id, $sort++)->markAsPii();

            // 1E
            $section = FormBuilder::createSection('1E. Do you have any pending felony charges?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'name'], $page->id, $sort++, $subSelect->id);

            // 1F
            $section = FormBuilder::createSection('1F. Have you ever been charged with any misdemeanor?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'sentencing', 'name'], $page->id, $sort++, $subSelect->id);
            
            // 1G
            $section = FormBuilder::createSection('1G. Have you ever been convicted of or pled guilty or no contest to any misdemeanor?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'sentencing', 'name'], $page->id, $sort++, $subSelect->id);

            // 1H
            $section = FormBuilder::createSection('1H. Have you ever been on probation for any misdemeanor?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                // Probation terms
                $probation_terms_section = FormBuilder::createSection('Please detail probation terms.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $probation_terms_section->active_start = $tomorrow;
                $probation_terms_section->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $probation_terms_section->id, $sort++)->markAsPii();
                
                // Probation explanation
                $probation_explanation_section = FormBuilder::createSection('Please provide an explanation', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $probation_explanation_section->active_start = $tomorrow;
                $probation_explanation_section->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $probation_explanation_section->id, $sort++)->markAsPii();

            // 1I
            $section = FormBuilder::createSection('1I. Do you have any pending misdemeanor charges?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'name'], $page->id, $sort++, $subSelect->id);

            // 1J
            $section = FormBuilder::createSection('1J. Have you ever been convicted of or plead guilty or no contest to any violations of federal or state securities or investment regulations?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'sentencing', 'name'], $page->id, $sort++, $subSelect->id);

            // 1K
            $section = FormBuilder::createSection('1K. Do you have any pending violations of federal or state securities or investment regulations?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'name'], $page->id, $sort++, $subSelect->id);

            // 1L
            $section = FormBuilder::createSection('1L. Do you have any pending violations of state insurance department regulations or statutes?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'charges', 'name'], $page->id, $sort++, $subSelect->id);

        // 2 - top level
        $section = FormBuilder::createSection('2. Have you ever been arrested for any crime not mentioned above?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

        // 3 - top level
        $section = FormBuilder::createSection('3. Have you ever been alleged or found to have been engaged in any fraud or has any foreign government, court, regulatory agency ever entered an order against you?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            // 3A
            $section = FormBuilder::createSection('3A. Have you ever been alleged to have engaged in any fraud?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            // 3B
            $section = FormBuilder::createSection('3B. Have you ever been found to have engaged in any fraud?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            // 3C
            $section = FormBuilder::createSection('3C. Has any foreign government, court, regulatory agency, or exchange ever entered an order against you related to investments or fraud?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

        // 4 - top level
        $section = FormBuilder::createSection('4. Has any state or federal regulatory agency found you to have made a false statement or omission or been dishonest, unfair, or unethical? Have you ever been cautioned or disciplined for violating any code of ethics for any organization?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //4A
            $section = FormBuilder::createSection('4A. Has any state or federal regulatory agency found you to have made a false statement or omission or been dishonest, unfair, or unethical?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation'], $page->id, $sort++, $subSelect->id);

            //4B
            $section = FormBuilder::createSection('4B. Have you ever been cautioned or disciplined for violating any code of ethics for any organization?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation'], $page->id, $sort++, $subSelect->id);

        // 5 - top level
        $section = FormBuilder::createSection('5. Has any state, federal or self-regulatory agency or any professional organization filed a complaint against you, fined, sanctioned, censored, penalized, or otherwise disciplined you? Have you ever been the subject of a consumer initiated complaint?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //5A
            $section = FormBuilder::createSection('5A. Has any state, federal or self-regulatory agency ever filed a complaint against you?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'explanation'], $page->id, $sort++, $subSelect->id);

            //5B
            $section = FormBuilder::createSection('5B. Has any state, federal or self-regulatory agency ever fined, sanctioned, censored, penalized, or otherwise disciplined you?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'fine', 'county', 'state', 'explanation'], $page->id, $sort++, $subSelect->id);

            //5C
            $section = FormBuilder::createSection('5C. Have you ever been the subject of a consumer initiated complaint?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'county', 'state', 'explanation'], $page->id, $sort++, $subSelect->id);

        // 6 - top level
        $section = FormBuilder::createSection('6. Have you ever been or are you currently being investigated, have any indictments, lawsuits, civil judgements, other legal proceedings or going through any litigation with any legal or regulatory authority or insurance company?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //6A
            $section = FormBuilder::createSection('6A. Are you currently or have you ever been under investigation by any legal or regulatory authority?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //6B
            $section = FormBuilder::createSection('6B. Are you currently or have you ever been under investigation by any insurance company?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //6C
            $section = FormBuilder::createSection('6C. Are you currently or have you ever been involved with any indictments, lawsuits, civil judgements, or other legal proceedings or going through any litigation with any legal or regulatory authority or insurance company? (You may omit family court)', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //6D
            $section = FormBuilder::createSection('6D. Are you currently or have you ever been named as a defendant or co-defendant in a lawsuit, or have you ever sued or been sued by an insurance company?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

        // 7 - top level
        $section = FormBuilder::createSection('7. Has any insurance or financial insurance company, or broker dealer terminated your contract or appointment or permitted you to resigned for any reason other than lack of sales? Were you terminated or denied to contract for cause?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //7A
            $section = FormBuilder::createSection('7A. Were you terminated/ resigned because you were accused of violating insurance or investment related statutes, regulations, rules or industry standards of conduct?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'company', 'explanation'], $page->id, $sort++, $subSelect->id);

            //7B
            $section = FormBuilder::createSection('7B. Were you terminated/ resigned for a failure to supervise in connection with violating insurance or investment related statutes, regulations, rules or industry standards of conduct?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'company', 'explanation'], $page->id, $sort++, $subSelect->id);

            //7C
            $section = FormBuilder::createSection('7C. Were you terminated/ resigned because you were accused of fraud or wrongful taking of property?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'company', 'explanation'], $page->id, $sort++, $subSelect->id);

            //7D
            $section = FormBuilder::createSection('7D. Were you terminated or denied to contract for cause?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'company', 'explanation'], $page->id, $sort++, $subSelect->id);
        
        // 8 - top level
        $section = FormBuilder::createSection('8. Has any lawsuit or claim ever been made against your surety company, or E&O insurer, arising out of your sales or practices or have you been refused surety bonding, or E&O coverage?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //8A
            $section = FormBuilder::createSection('8A. Have you ever had a claim filed against your surety company?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //8B
            $section = FormBuilder::createSection('8B. Has a bonding or surety company ever denied, paid on, or revoked a bond for you?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //8C
            $section = FormBuilder::createSection('8C. Have you ever had a claim filed against your E&O carrier?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

            //8D
            $section = FormBuilder::createSection('8D. Has any E&O carrier ever denied, paid claims on or canceled your coverage?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['date', 'explanation'], $page->id, $sort++, $subSelect->id);

        // 9 - top level
        $section = FormBuilder::createSection('9. Have you ever voluntarily surrendered or had an insurance or securities license denied, suspended, cancelled or revoked?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //9A
            $section = FormBuilder::createSection('9A. Have you ever voluntarily surrendered your insurance or securities license?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation'], $page->id, $sort++, $subSelect->id);

            //9B
            $section = FormBuilder::createSection('9B. Have you ever had an insurance or securities license denied, suspended, cancelled or revoked?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation'], $page->id, $sort++, $subSelect->id);

        // 10 - top level
        $section = FormBuilder::createSection('10. Have you had any interruptions in licensing?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        $sort = self::addConditionalFields(['explanation'], $page->id, $sort++, $subSelect->id);

        // 11 - top level
        $section = FormBuilder::createSection('11. Have you personally or has any insurance or securities brokerage firm with whom you have been associated filed a bankruptcy petition or declared bankruptcy?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //11A
            $section = FormBuilder::createSection('11A. Have you personally filed a bankruptcy petition?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                // bankruptcy date
                $bankruptcy_date = FormBuilder::createSection('Please provide the date of Bankruptcy Petition Filing.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_date->active_start = $tomorrow;
                $bankruptcy_date->save();
                FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $bankruptcy_date->id, $sort++);
                
                // bankruptcy explanation
                $bankruptcy_explanation = FormBuilder::createSection('Please provide an explanation', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_explanation->active_start = $tomorrow;
                $bankruptcy_explanation->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $bankruptcy_explanation->id, $sort++)->markAsPii();

                // bankruptcy documents
                $bankruptcy_documents = FormBuilder::createSection('Please attach any additional legal documentation that you may have.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_documents->active_start = $tomorrow;
                $bankruptcy_documents->save();
                FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $bankruptcy_documents->id, $sort++, false, 1, 0, 255, false, '', 'Bankruptcy Petition Documents')->markAsPii();

            //11B
            $section = FormBuilder::createSection('11B. Have you personally declared bankruptcy?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                // bankruptcy date
                $bankruptcy_date = FormBuilder::createSection('Please provide the date of the bankruptcy filing.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_date->active_start = $tomorrow;
                $bankruptcy_date->save();
                FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $bankruptcy_date->id, $sort++);
                
                // bankruptcy discharge date
                $discharge_date = FormBuilder::createSection('Please provide the date of discharge.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $discharge_date->active_start = $tomorrow;
                $discharge_date->save();
                FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $discharge_date->id, $sort++);
                
                // bankruptcy explanation
                $bankruptcy_explanation = FormBuilder::createSection('Please provide an explanation.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_explanation->active_start = $tomorrow;
                $bankruptcy_explanation->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $bankruptcy_explanation->id, $sort++)->markAsPii();

                // bankruptcy documents
                $bankruptcy_documents = FormBuilder::createSection('Please attach any additional legal documentation that you may have.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $bankruptcy_documents->active_start = $tomorrow;
                $bankruptcy_documents->save();
                FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $bankruptcy_documents->id, $sort++, false, 1, 0, 255, false, '', 'Personal Bankruptcy Documents')->markAsPii();

            //11C
            $section = FormBuilder::createSection('11C. Has any insurance or securities brokerage firm with whom you have been associated filed a bankruptcy petition or been declared bankrupt either during your association or within five years after terminations of such association?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                // company name
                $company_name = FormBuilder::createSection('Please list company name.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $company_name->active_start = $tomorrow;
                $company_name->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $company_name->id, $sort++);

                // company relationship
                $company_relationship = FormBuilder::createSection('Please provide your relationship to the company.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $company_relationship->active_start = $tomorrow;
                $company_relationship->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $company_relationship->id, $sort++);

                // explanation
                $explanation = FormBuilder::createSection('Please provide an explanation.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $explanation->active_start = $tomorrow;
                $explanation->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $explanation->id, $sort++)->markAsPii();

        //12
        $section = FormBuilder::createSection('12. Do you have any debt collection matters or any indebtedness current or pending against you?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //12A
            $section = FormBuilder::createSection('12A. Is this debt collection matters or any indebtedness as a result of any commission chargeback or other insurance transactions of business?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                //to whom
                $to_whom = FormBuilder::createSection('Please list company name.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $to_whom->active_start = $tomorrow;
                $to_whom->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $to_whom->id, $sort++);
                
                //debt amount
                $amount = FormBuilder::createSection('Please list the debt amount.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $amount->active_start = $tomorrow;
                $amount->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $amount->id, $sort++);
                
                //repaying?
                $section = FormBuilder::createSection('Are you repaying this debt?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                //financed?
                $financed = FormBuilder::createSection('Is the debt financed?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $financed->active_start = $tomorrow;
                $financed->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $financed->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                //finance details
                $terms = FormBuilder::createSection('Please detail the finance terms.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $terms->active_start = $tomorrow;
                $terms->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $terms->id, $sort++)->markAsPii();

                // explanation
                $explanation = FormBuilder::createSection('Please provide an explanation.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $explanation->active_start = $tomorrow;
                $explanation->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $explanation->id, $sort++)->markAsPii();

                //Vector One?
                $section = FormBuilder::createSection('Has this debt been reported to Vector One?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                //Charged off?
                $section = FormBuilder::createSection('Has this debt been charged off?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

            //12B
            $section = FormBuilder::createSection('12B. Do you have any non-insurance related debt in collections or arrears?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            
                //company name
                $section = FormBuilder::createSection('Please list company name.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++);

                //debt amount
                $section = FormBuilder::createSection('Please list the debt amount.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $section->id, $sort++);
                
                //repaying?
                $section = FormBuilder::createSection('Are you repaying this debt?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $section->active_start = $tomorrow;
                $section->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                //financed?
                $financed = FormBuilder::createSection('Is the debt financed?', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $financed->active_start = $tomorrow;
                $financed->save();
                FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_THIRD, $financed->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

                //finance details
                $terms = FormBuilder::createSection('Please detail the finance terms.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $terms->active_start = $tomorrow;
                $terms->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $terms->id, $sort++)->markAsPii();

                // explanation
                $explanation = FormBuilder::createSection('Please provide an explanation.', $page->id, $sort++, $subSelect->id, FormField::SELECT_BOOL_YES);
                $explanation->active_start = $tomorrow;
                $explanation->save();
                FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $explanation->id, $sort++)->markAsPii();

        //13
        $section = FormBuilder::createSection('13. Have you ever or do you currently have any judgments, garnishments, or liens against you?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        
            //13A
            $section = FormBuilder::createSection('13A. Do you currently have any judgements?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Current Judgements Documentation');
            
            //13B
            $section = FormBuilder::createSection('13B. Have you ever had any judgements?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Past Judgements Documentation');
            
            //13C
            $section = FormBuilder::createSection('13C. Do you currently have any garnishments?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Current Garnishments Documentation');
            
            //13D
            $section = FormBuilder::createSection('13D. Have you ever had any garnishments?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Past Garnishments Documentation');
            
            //13E
            $section = FormBuilder::createSection('13E. Do you currently have any liens?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Current Liens Documentation');
            
            //13F
            $section = FormBuilder::createSection('13F. Have you ever had any liens?', $page->id, $sort++, $infractionSelect->id, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            $subSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
            $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $subSelect->id, 'Past Liens Documentation');
            
        //14
        $section = FormBuilder::createSection('14. Do you have any unresolved matters pending with the Internal Revenue Service or other taxing authority?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $infractionSelect->id, 'IRS Matters Documentation');
        
        //15
        $section = FormBuilder::createSection('15. Do you have any other information, pending or current, related to criminal, insurance-related complaints, credit, debt, etc., that was not covered by the above questions, that would result in a yes answer?', $page->id, $sort++);
        $section->active_start = $tomorrow;
        $section->save();
        $infractionSelect = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, 0, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);
        $sort = self::addConditionalFields(['explanation', 'attachments'], $page->id, $sort++, $infractionSelect->id, 'Other Matters Documentation');
        


        $this->line("[app-content-patch:25:add-new-legal-questions] Finish");

        return 0;
    }

    public static function addConditionalFields($fields, $pageId, $sort, $fieldId, $filename = 'Additional Documents') {
        
        $tomorrow = Carbon::tomorrow();

        // date
        if(in_array('date', $fields)) {
            $date_section = FormBuilder::createSection('Date of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $date_section->active_start = $tomorrow;
            $date_section->save();
            FormBuilder::createField('', FormField::TYPE_DATE, FormField::WIDTH_THIRD, $date_section->id, 1);
        }

        // company name
        if(in_array('company', $fields)) {
            $state_section = FormBuilder::createSection('Please list company name.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // fine
        if(in_array('fine', $fields)) {
            $state_section = FormBuilder::createSection('Please list fine amount, if applicable.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // county
        if(in_array('county', $fields)) {
            $county_section = FormBuilder::createSection('County of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $county_section->active_start = $tomorrow;
            $county_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $county_section->id, 1);
        }

        // state
        if(in_array('state', $fields)) {
            $state_section = FormBuilder::createSection('State of Incident', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $state_section->active_start = $tomorrow;
            $state_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $state_section->id, 1);
        }

        // general explanation
        if(in_array('explanation', $fields)) {
            $details_section = FormBuilder::createSection('Please provide an explanation.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $details_section->active_start = $tomorrow;
            $details_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $details_section->id, 1)->markAsPii();
        }

        // charges details
        if(in_array('charges', $fields)) {
            $details_section = FormBuilder::createSection('Please detail charges and/or offenses.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $details_section->active_start = $tomorrow;
            $details_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $details_section->id, 1)->markAsPii();
        }

        // sentencing
        if(in_array('sentencing', $fields)) {
            $sentencing_section = FormBuilder::createSection('Please detail any sentencing information.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $sentencing_section->active_start = $tomorrow;
            $sentencing_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXTAREA, FormField::WIDTH_FULL, $sentencing_section->id, 1)->markAsPii();
        }

        // name
        if(in_array('name', $fields)) {
            $name_section = FormBuilder::createSection('What was your name at time of offense?', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $name_section->active_start = $tomorrow;
            $name_section->save();
            FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_THIRD, $name_section->id, 1)->markAsPii();
        }

        // attachments
        if(in_array('attachments', $fields)) {
            $section = FormBuilder::createSection('Please attach any additional legal documentation that you may have.', $pageId, $sort++, $fieldId, FormField::SELECT_BOOL_YES);
            $section->active_start = $tomorrow;
            $section->save();
            FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $section->id, $sort++, false, 1, 0, 255, false, '', $filename)->markAsPii();
        }

        return $sort;

    }
}
