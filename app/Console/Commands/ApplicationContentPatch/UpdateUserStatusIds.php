<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Illuminate\Console\Command;
use DB;

class UpdateUserStatusIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:23:update-user-status-ids';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 23
        Update user records with the new approval status ID for either AO Approved or HO Approved
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:23:update-user-status-ids] Start");

        $approved = UserStatus::where('slug', 'approved')->first();
        $ao_approved = UserStatus::where('slug', 'ao-approved')->first();
        $ho_approved = UserStatus::where('slug', 'ho-approved')->first();

        // Get all users with a current user_status_id for the old 'approved' status
        $users = User::where('user_status_id', $approved->id)->get();

        if(count($users) == 0) {
            $this->warn("This patch has already run.");
            return;
        }

        // update each user user_status_id with the new ao_approved or ho_approved status id
        foreach($users as $user) {
            //see if this user has an ho_approved user status history record
            $ho_approved_count = UserStatusHistory::where('user_id', $user->id)->where('user_status_id', $ho_approved->id)->count();
            if($ho_approved_count > 0) {
                // if this record exists then we know the user_status_id is for ho_approved
                $user->user_status_id = $ho_approved->id;
            } else {
                // no ho_approved record, so must be ao_approved
                $user->user_status_id = $ao_approved->id;
            }
            $user->save();
        }
        $this->line("[app-content-patch:23:update-user-status-ids] Finish");
        return 0;
    }
}
