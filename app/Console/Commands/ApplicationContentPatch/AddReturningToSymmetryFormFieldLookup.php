<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormSection;
use App\Models\FormLookup;
use Illuminate\Console\Command;

class AddReturningToSymmetryFormFieldLookup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:57:add-returning-to-symmetry-form-field-lookup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 57
        Adds form lookup for 'returning to symmetry' field
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:57:add-returning-to-symmetry-form-field-lookup] Start");

        // Check if lookup already exists
        $exists = FormLookup::where('field_slug', 'returning-to-symmetry')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return 0;
        }

        // Find the section containing "returning to symmetry"
        if(!$section = FormSection::where('label', 'Are you returning to Symmetry?')->first()) {
            $this->error("Can't find 'Are you returning to Symmetry?' section");
            return 1;
        }

        // Get the form field from this section
        if(!$field = FormField::where('form_section_id', $section->id)->first()) {
            $this->error("Can't find form field in the 'Are you returning to Symmetry?' section");
            return 1;
        }

        // Create the new lookup
        $lookup = new FormLookup();
        $lookup->form_field_id = $field->id;
        $lookup->field_slug = 'returning-to-symmetry';
        $lookup->save();

        $this->line("[app-content-patch:57:add-returning-to-symmetry-form-field-lookup] Finish");

        return 0;
    }
} 