<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use Illuminate\Console\Command;

class ChangePreLicensingDescription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:07:change-pre-licensing-description';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 7
        Change Description of Pre-licensing
        [You can talk to your AO to get a discount code.] => [You can talk to your Agency Owner/Manager/Interviewer for a discount code.]
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:07:change-pre-licensing-description] Start");

        $ff = FormField::where('label', 'LIKE', "It's time to enroll in a pre-licensing course! You can talk to your AO to get a discount code.%")->first();
        if(!$ff) {
            $this->error("Can't find First Field.");
            return;
        }

        $ff->label = str_replace("You can talk to your AO to get a discount code.", "You can talk to your Agency Owner/Manager/Interviewer for a discount code.", $ff->label);
        $ff->save();

        $this->line("[app-content-patch:07:change-pre-licensing-description] Finish");
        return 0;
    }
}
