<?php

namespace App\Console\Commands\ApplicationContentPatch;

use Artisan;
use Illuminate\Console\Command;

class _Run extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:run';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run all patch commands. Please add new command to run in below array $registeredPatches';

    protected $registeredPatches = [
        // 'app-content-patch:01:move-social-security-to-legal',
        // 'app-content-patch:02:add-storage-permission',
        // 'app-content-patch:03:remove-non-states',
        // 'app-content-patch:04:add-no-licensing-prompt',
        // 'app-content-patch:05:add-final-approval-permission',
        // 'app-content-patch:06:make-current-address-to-not-required',
        // 'app-content-patch:07:change-pre-licensing-description',
        // 'app-content-patch:08:restore-npn-state-dc',
        // 'app-content-patch:09:update-pre-license-comment',
        // 'app-content-patch:10:update-aml-comment',
        // 'app-content-patch:11:update-aml-comment-extra-link',
        // 'app-content-patch:12:add-new-userrole',
        // 'app-content-patch:13:remove-license-comment-typo',
        // 'app-content-patch:14:add-additional-legal-upload',
        // 'app-content-patch:15:add-track-applications-permission-to-salesrep',
        // 'app-content-patch:16:update-aml-comment-text',
        // 'app-content-patch:17:update-upload-field-filenames',
        // 'app-content-patch:18:add-user-statuses',
        // 'app-content-patch:19:add-form-page-id-to-user-statuses',
        // 'app-content-patch:20:update-surance-bay-agreement-fields',
        // 'app-content-patch:21:add-approval-statuses',
        // 'app-content-patch:22:change-surancebay-status-label',
        // 'app-content-patch:23:update-user-status-ids',
        // 'app-content-patch:24:update-surance-bay-agreement-page-indent',
        // 'app-content-patch:25:add-new-legal-questions',
        // 'app-content-patch:26:add-submitted-statuses',
        // 'app-content-patch:27:fix-incorrect-approval-statuses',
        // 'app-content-patch:28:add-revision-statuses',
        // 'app-content-patch:29:add-rejected-statuses',
        // 'app-content-patch:30:add-name-suffix-field',
        // 'app-content-patch:31:remove-npn-field',
        // 'app-content-patch:32:add-returning-questions',
        // disable and run manually
        // 'app-content-patch:33:populate-user-columns',
        // 'app-content-patch:34:add-ny-resident-questions',
        // 'app-content-patch:35:add-more-returning-questions',
        // diable and run manually
        // 'app-content-patch:36:sync-license-data-from-hq',
        // 'app-content-patch:37:update-user-is-returning-field',
        // 'app-content-patch:38:add-ca-resident-checkbox',
        // 'app-content-patch:39:update-legal-questions-page',
        // 'app-content-patch:40:add-sfg-agent-code-lookup',
        // 'app-content-patch:41:add-eo-purchase-info',
        // 'app-content-patch:42:add-us-citizen-checkbox',
        // 'app-content-patch:43:update-us-citizen-to-select',
        // 'app-content-patch:44:add-transferring-agent-form-upload',
        // 'app-content-patch:45:add-agent-part-full-time-question',
        // 'app-content-patch:46:add-life-loa-question',
        // 'app-content-patch:47:add-flagged-statuses',
        // 'app-content-patch:48:add-carrier-contract-questions',
        // 'app-content-patch:49:visa-carrier-recommendations',
        // 'app-content-patch:50:update-transferring-questions',
        // 'app-content-patch:51:update-life-loa-question',
        // 'app-content-patch:53:add-principal-application-questions',
        // 'app-content-patch:55:add-b2b-agent-application-questions',
        'app-content-patch:56:add-previous-experience-form-field-lookup',
        'app-content-patch:57:add-returning-to-symmetry-form-field-lookup',
        'app-content-patch:58:reorganize-licensing-questions',
        'app-content-patch:59:add-language-preference-checkboxes',
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        foreach($this->registeredPatches as $cmd) {
            Artisan::call($cmd, [], $this->getOutput());
        }

        return 0;
    }
}
