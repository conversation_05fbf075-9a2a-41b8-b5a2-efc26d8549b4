<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddTransferringAgentFormUpload extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:44:add-transferring-agent-form-upload';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 44
        Add transferring agent optional form upload
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:44:add-transferring-agent-form-upload] Start");

        $exists = FormSection::where('label', 'LIKE', 'You can upload the completed Agent Transfer Form below.')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        $page = FormPage::where('step_ident', 'license-information')->first();
        if(!$page) {
            $this->error("Can't find License Information Page");
            return;
        }

        $transferring_section = FormSection::where('label', 'LIKE', 'Are you transferring from another IMO or insurance agency?')->first();
        $transferring_field = FormField::where('form_section_id', $transferring_section->id)->first();

        $sort = 55; //force to bottom of page

        $upload_section = FormBuilder::createSection('You can upload the completed Agent Transfer Form below.', $page->id, $sort);
        $upload_field = FormBuilder::createField('', FormField::TYPE_UPLOAD, FormField::WIDTH_FULL, $upload_section->id, $sort++, false, 0, 0, 255, false, '', 'Agent Transfer Form');


        $condition = new FormFieldCondition();
        $condition->form_field_id = $transferring_field->id;
        $condition->value = 'YES';
        $condition->action = FormFieldCondition::ACTION_SHOW;
        $condition->form_section_id = $upload_section->id;
        $condition->type = FormFieldCondition::TYPE_EQUALS;
        $condition->save();

        $this->line("[app-content-patch:44:add-transferring-agent-form-upload] Finished");

        return 0;
    }
}
