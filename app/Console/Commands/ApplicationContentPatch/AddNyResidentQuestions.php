<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormLookup;
use App\Models\FormFieldCondition;
use Illuminate\Console\Command;
use FormBuilder;

class AddNyResidentQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:34:add-ny-resident-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 34
        Adds questions for NY residents
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:34:add-ny-resident-questions] Start");

        $exists = FormSection::where('label', 'Do you have a non-resident license in another state?')->where('status', 'active')->count();
        if($exists > 0) {
            $this->warn("This patch has already run.");
            return;
        }

        if(!$page = FormPage::where('step_ident', 'personal-information')->first()) {
            $this->error("Can't find Personal Information Page");
            return;
        }

        if(!$lookup = FormLookup::where('field_slug', 'personal-state')->first()){
            $this->error("Can't find State field"); 
            return;
        }

        $sort = 50; //force to bottom of page
        //If resident state is New York
        $section = FormBuilder::createSection('Do you have a non-resident license in another state?', $page->id, $sort++);
        $section->subline = "Due to certain limitations in NY, most of our carriers require a non-resident license in another state for contracting.";
        $section->save();
        
        //add conditional display if resident state is NY
        $condition = new FormFieldCondition();
        $condition->form_field_id = $lookup->form_field_id;
        $condition->value = 'new york,ny,n.y.';
        $condition->action = FormFieldCondition::ACTION_SHOW;
        $condition->form_section_id = $section->id;
        $condition->type = FormFieldCondition::TYPE_LIKE;
        $condition->save();

        $select = FormBuilder::createField('', FormField::TYPE_SELECT, FormField::WIDTH_HALF, $section->id, $sort++, 'Please Select', 1, 0, 1, FormField::SELECT_BOOL);

        // if yes - non-resident license
        $section = FormBuilder::createSection('Please enter the states in which you have a non-resident license.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_YES);
        $field = FormBuilder::createField('', FormField::TYPE_TEXT, FormField::WIDTH_HALF, $section->id, $sort++, 'Non-resident license states');

        //if no non-resident license
        $section = FormBuilder::createSection('We are happy to process your Symmetry contract and give you access to training content while you apply for and await issuance of a non-resident license. Please be advised, no carrier contracting will be sent for NY state.', $page->id, $sort++, $select->id, FormField::SELECT_BOOL_NO);

        $this->line("[app-content-patch:34:add-ny-resident-questions] Finish");

        return 0;
    }
}
