<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use Illuminate\Console\Command;
use FormBuilder;

class UpdateTransferringQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:50:update-transferring-questions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 50
        Modifies questions related to transferring from another IMO.
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:50:update-transferring-questions] Start");

        if($exists = FormField::where('label', 'LIKE', '%What IMO or insurance agency are you coming from?%')) {
            $this->warn("This patch has already run.");
            return;
        }

        // find and modify the content of this field
        $field = FormField::where('label', 'LIKE', '%If you are transferring carriers from a previous company%')->first();
        if(!$field) {
            $this->error("Target field 'If you are transferring carriers from a previous company...' not found!");
            return;
        }
        $field->label = '<p style="font-size:1.5em;">What IMO or insurance agency are you coming from?</p>';
        $field->type = 'text';
        $field->placeholder = '';
        $field->width = 'full-width';
        $field->save();


        // find and update the label for the file upload field
        $section = FormSection::where('label', 'LIKE', '%You can upload the completed Agent Transfer Form below%')
            ->where('status', 'active')
            ->first();
        
            if($section) {
            $this->error("Target section 'You can upload the completed Agent Transfer Form below' not found!");
            return;
        }

        $section->label = 'Please upload any release documentation to support your transfer.';
        $section->save();

        $this->line("[app-content-patch:50:update-transferring-questions] Finish");

        return 0;
    }
}
