<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormSection;
use App\Models\FormField;
use App\Models\UserEntry;
use App\Models\User;
use Illuminate\Console\Command;
use DB;

class UpdateUserIsReturningField extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:37:update-user-is-returning-field';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch - No 37
        Update user records is_returning field based on their answer to the related question
    ";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:37:update-user-is-returning-field] Start");

        $returning_users = User::where('is_returning', 1)->count();
        if($returning_users > 0) {
            $this->warn('This patch has already ran.');
            return;
        }

        
        // select * from dbo.form_sections fs where label = 'Are you returning to Symmetry?'
        if(!$form_section = FormSection::where('label', 'Are you returning to Symmetry?')->first()) {
            $this->error("Form section not found.");
            return;
        }

        if(!$form_field = FormField::where('form_section_id', $form_section->id)->first()) {
            $this->error("Form field not found.");
            return;
        }

        //getting all user entries where they indicate they are returning to symmetry
        $entries = UserEntry::where('form_field_id', $form_field->id)->where('value', 'YES')->get();

        foreach($entries as $entry) {
            $user = User::find($entry->user_id);
            $user->is_returning = 1;
            $user->save();
            $this->line('Updated user:'. $user->id);
        }

        $this->line("[app-content-patch:37:update-user-is-returning-field] Finish");
        return 0;
    }
}
