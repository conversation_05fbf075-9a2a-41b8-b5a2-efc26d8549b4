<?php

namespace App\Console\Commands\ApplicationContentPatch;

use App\Models\FormField;
use App\Models\FormLookup;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\User;
use App\Models\UserApplications;
use App\Models\Agent;
use Illuminate\Console\Command;
use FormBuilder;

class PopulateUserColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app-content-patch:33:populate-user-columns {limit=0}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "
        Application Content Patch = No 33
        Populates new user collumns with data from apps.
    ";

    protected $agents = [];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line("[app-content-patch:33:populate-user-columns] Start");

        $limit = $this->argument('limit');
            

        $users = User::whereNull('recruiter_name');
        if($limit > 0)
            $users = $users->limit($limit);
        $count = $users->count();
        $this->line("Updating $count records.");
        $users = $users->get();

        //populate an array with all apps
        $app_records = UserApplications::all();
        $apps = [];
        foreach($app_records as $record)
            $apps[$record->user_id] = $record;
        
        //populate array with all active agents
        $agents = Agent::active()->get();
        foreach($agents as $agent)
            $this->agents[$agent->AgentCode] = $agent;
        $agent_array_keys = array_keys($this->agents);


        $i = 0;
        foreach($users as $user) {
            $i++;

            if(!isset($apps[$user->id]))
                continue;

            //$upline_agent = $user->uplineAgent();
            $upline_agent = null;
            if($user->upline_agent_id) {
                // $upline_agent_code = array_search($user->upline_agent_id, array_column($this->agents, 'AgentID'));
                $upline_agent_code = array_search($user->upline_agent_id, array_filter( array_combine( $agent_array_keys, array_column( $this->agents, 'AgentID' ) ) ));
                $upline_agent = isset($this->agents[$upline_agent_code]) ? $this->agents[$upline_agent_code] : null;
            }
            $upline_agent_name = $upline_agent ? $upline_agent->AgentName : null;
            $upline_agent_code = $upline_agent ? $upline_agent->AgentCode : null;

            // $agency_owner = $this->getAgencyOwner($user->agency_owner_code);
            $agency_owner = null;
            if(isset($this->agents[$user->agency_owner_code]))
                $agency_owner = $this->agents[$user->agency_owner_code];
            $agency_owner_name = $agency_owner ? $agency_owner->AgentName : null;

            $this->line("Updating ".$i." ".$user->id." AO: $agency_owner_name");

            $user->work_email = $apps[$user->id]['work_email'];
            $user->licensed = $apps[$user->id]['licensed'];
            $user->first_ao_submission = $apps[$user->id]['first_ao_submission_on'];
            $user->first_homeoffice_submission = $apps[$user->id]['first_homeoffice_submission_on'];
            $user->homeoffice_submission = $apps[$user->id]['homeoffice_submission_on'];
            $user->last_approved = $apps[$user->id]['last_approved_on'];
            $user->last_revision = $apps[$user->id]['last_revision_requested_on'];
            $user->passed_exam = $apps[$user->id]['passed_state_exam'];
            $user->recruiter_name = $apps[$user->id]['recruiter_name'];
            $user->upline_agent_name = $upline_agent_name;
            $user->upline_agent_code = $upline_agent_code;
            $user->agency_owner_name = $agency_owner_name;
            $user->save();
        }

        $this->line("[app-content-patch:33:populate-user-columns] Finish");

        return 0;
    }

    // this isn't needed, but leaving it here for reference later
    // Uses an array of all active agents to search for upline AO rather than multiple queries to the HQ db
    public function getAgencyOwner($AgentCode = null) {
        if(!$AgentCode)
            return null;

        $agent = $this->agents[$AgentCode];
        if($agent){
            if($agent->IsAgencyOwner == 1)
                return $agent;

            // $upline = Agent::where('AgentCode', $agent->AgentCode)->first();
            $upline = $this->agents[$agent->AgentCode];
            if($upline)
            {
                do{
                    if($upline->IsAgencyOwner == 1)
                        return $upline;
                } while($upline = $this->agents[$upline->AgentCode]);
            }
        }
        return null;
    }
}
