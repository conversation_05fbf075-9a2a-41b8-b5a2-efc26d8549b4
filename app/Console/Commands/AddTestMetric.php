<?php

namespace App\Console\Commands;

use App\Models\FormOption;
use App\Models\Metric;
use App\Models\UserStatus;
use Illuminate\Console\Command;

class AddTestMetric extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:test-metric';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $test_form_option = FormOption::where('label', 'like', '%Bachelor%')->first();
        if($test_form_option && $test_form_option->formField) {
            $form_field = $test_form_option->formField;
            $form_section = $form_field->formSection;

            $metric = Metric::create([
                'name' => 'Metric-FormField-' . $form_section->label . '-' . $form_field->label . "-InProgress",
                'description' => '',
                'application_type' => Metric::APPLICATION_TYPE_IN_PROGRESS,
                'type' => '',
                'model_type' => Metric::MODEL_TYPE_FORM_FIELD,
                'model_id' => $form_field->id,
                'model_value' => '',
                'breakdown_by_upline' => true,
            ]);
            $this->line("Metric added: " . $metric->id);

            $metric = Metric::create([
                'name' => 'Metric-FormField-' . $form_section->label . '-' . $form_field->label . "-Completed",
                'description' => '',
                'application_type' => Metric::APPLICATION_TYPE_COMPLETED,
                'type' => '',
                'model_type' => Metric::MODEL_TYPE_FORM_FIELD,
                'model_id' => $form_field->id,
                'model_value' => '',
                'breakdown_by_upline' => true,
            ]);
            $this->line("Metric added: " . $metric->id);
        }

        $test_user_status = UserStatus::where('slug', 'registration')->first();
        if($test_user_status) {
            $metric = Metric::create([
                'name' => 'Metric-UserStatus-' . $test_user_status->name . "-InProgress",
                'description' => '',
                'application_type' => Metric::APPLICATION_TYPE_IN_PROGRESS,
                'type' => '',
                'model_type' => Metric::MODEL_TYPE_USER_STATUS,
                'model_id' => $test_user_status->id,
                'model_value' => ''
            ]);
            $this->line("Metric added: " . $metric->id);

            $metric = Metric::create([
                'name' => 'Metric-UserStatus-' . $test_user_status->name . "-Completed",
                'description' => '',
                'application_type' => Metric::APPLICATION_TYPE_COMPLETED,
                'type' => '',
                'model_type' => Metric::MODEL_TYPE_USER_STATUS,
                'model_id' => $test_user_status->id,
                'model_value' => ''
            ]);
            $this->line("Metric added: " . $metric->id);
        }

        Metric::create([
            'name' => 'Metric-UserInvite-Blocked',
            'description' => '',
            'application_type' => '',
            'type' => '',
            'model_type' => Metric::MODEL_TYPE_USER_INVITE,
            'model_id' => '',
            'model_value' => ''
        ]);

        return 0;
    }
}
