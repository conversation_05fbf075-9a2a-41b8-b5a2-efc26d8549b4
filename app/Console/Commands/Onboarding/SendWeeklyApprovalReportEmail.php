<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\DailyApplication;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use League\Csv\Writer;
use Illuminate\Support\Facades\Storage;
use App\Mail\ApplicationsReportMail;
use Carbon\Carbon;

class SendWeeklyApprovalReportEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report:weekly-approvals {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the weekly app approvals list email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        //if no date is given, it will use today's date.
        $date_arg = $this->argument('date') ?? Carbon::now();
        $date = Carbon::parse($date_arg);

        // Find the previous Saturday
        $start_date = $date->previous(Carbon::SATURDAY);

        // Calculate the end date by adding 6 days to the start date
        $end_date = $start_date->copy()->addDays(6);

        // Check if the end date is in the future
        // We want to get the previous week's approvals
        if ($end_date->isFuture()) {
            // If the end date is in the future, move both dates back by 7 days
            $start_date = $start_date->subDays(7);
            $end_date = $end_date->subDays(7);
        }

        // Format the dates as needed
        // echo "Start Date: " . $start_date->format('Y-m-d') . PHP_EOL;
        // echo "End Date: " . $end_date->format('Y-m-d') . PHP_EOL;

        // return;

        $start_date = $start_date->format('Y-m-d');
        $end_date = $end_date->format('Y-m-d');

        // Fetch records from the database
        $records = DailyApplication::where('status_slug', 'ho-approved')
            ->where('last_approved_on', '>=', $start_date)
            ->where('last_approved_on', '<=', $end_date)
            ->get();

        if ($records->isEmpty()) {
            // No records found, stop the process
            $this->info('No records found.');
            return;
        }

        // Create a CSV file
        $csv = Writer::createFromString('');
        $csv->insertOne(['Name', 'Email', 'Upline Agent', 'Agency Owner', 'E&O Insurance', 'State', 'Experience', 'Advanced Markets', 'Licensed', 'Transferring', 'Carrier Selection']); // Replace with your column names

        foreach ($records as $record) {
            $user = User::find($record->user_id);
            $carriers = $user->getMeta('carriers', '');
            $csv->insertOne([
                $record->user_name, 
                $record->email,
                $record->upline_agent_name,
                $record->agency_owner_name,
                $record->eo_insurance == 'YES' ? 'YES' : 'NO',
                $record->state,
                $record->experience != null ? $record->experience : '',
                $record->advanced_markets == true ? 'YES' : 'NO',
                $record->licensed != null ? $record->licensed : '',
                $record->transferring == 'YES' ? 'YES' : 'NO',
                $carriers,
            ]);
        }

        // Save the CSV file to the local storage
        $filename = "WeeklyApplications-{$start_date}-to-{$end_date}.csv";
        Storage::put("exports/{$filename}", (string) $csv);

        // Send the email with the CSV attachment
        $emailRecipients = env('DAILY_REPORT_EMAIL_RECIPIENTS', '<EMAIL>,<EMAIL>,<EMAIL>');
        $emails = explode(',', $emailRecipients);
        $file_path = storage_path("app/exports/{$filename}");

        Mail::to($emails)->send(
            new ApplicationsReportMail($file_path, "Weekly Applications Report for $start_date to $end_date")
        );

        // Delete the file from local storage after sending the email
        Storage::delete("exports/{$filename}");

        $this->info('Email sent with CSV attachment.');
    }

}
