<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\UserInvite;
use App\Mail\UnenrolledMail;
use Illuminate\Support\Facades\Mail;

class SendUnenrolledEmailToAO extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onboarding:send-unenrolled-email-to-ao {email} {name} {recruit_name} {recruiter_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the unenrolled status email to the AO';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->argument('name');
        $recruit_name = $this->argument('recruit_name');
        $recruiter_name = $this->argument('recruiter_name');

        // send email
        Mail::to($email)->send(
            new UnenrolledMail(
                $email,
                $name, 
                $recruit_name,
                $recruiter_name
            )
        );

        $this->info('Unenrolled Status Message Sent');
    }
}
