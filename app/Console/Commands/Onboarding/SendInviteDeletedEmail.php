<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\InviteDeletedMail;

class SendInviteDeletedEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onboarding:send-invite-deleted-email {email} {name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the invite email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->argument('name');

        // send email
        Mail::to($email)->send(
            new InviteDeletedMail(
                $email,
                $name
            )
        );

        $this->info('Invite Deleted Message Sent');
    }
}
