<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\UserInvite;

class ExpireOldInvites extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onboarding:expire-old-invites';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mark old invite as expired';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $user_invites = UserInvite::where('updated_at', '<', now()->subDays(config('system.invite-expiration-days', 10)))->update(
            [
                'invite_expired' => 1
            ]
        );

        $this->info($user_invites . ' Expired');
        return true;
    }
}
