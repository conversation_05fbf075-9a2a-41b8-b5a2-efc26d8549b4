<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\DailyApplication;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use League\Csv\Writer;
use Illuminate\Support\Facades\Storage;
use App\Mail\AmTransferringReportMail;
use Carbon\Carbon;

class SendDailyAmTransferringReportEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report:daily-am-transferring {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the daily am-transferring list email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $date = $this->argument('date') ?? Carbon::now()->subHours(24);

        // Fetch records from the database
        $records = DailyApplication::where('status_slug', 'ho-approved')
            ->where('last_approved_on', '>=', $date)
            ->where(function($query) {
                $query->where('transferring', 'YES')
                    ->orWhere('advanced_markets', 1);
            })
            ->get();

        // $query = DailyApplication::where('status_slug', 'ho-approved')
        //     ->where('last_approved_on', '>=', $date)
        //     ->where(function($query) {
        //         $query->where('transferring', 'YES')
        //             ->orWhere('advanced_markets', 1);
        //     });
        // $sql = $query->toSql();
        // $bindings = $query->getBindings();
        // dd($sql, $bindings);

        if ($records->isEmpty()) {
            // No records found, stop the process
            $this->info('No records found.');
            return;
        }

        // Create a CSV file
        $csv = Writer::createFromString('');
        $csv->insertOne(['Name', 'Email', 'Upline Agent', 'Agency Owner', 'E&O Insurance', 'State', 'Experience', 'Advanced Markets', 'Transferring', 'Carrier Selection']); // Replace with your column names

        foreach ($records as $record) {
            $user = User::find($record->user_id);
            $carriers = $user->getMeta('carriers', '');
            $csv->insertOne([
                $record->user_name, 
                $record->email,
                $record->upline_agent_name,
                $record->agency_owner_name,
                $record->eo_insurance == 'YES' ? 'YES' : 'NO',
                $record->state,
                $record->experience != null ? $record->experience : '',
                $record->advanced_markets == true ? 'YES' : 'NO',
                $record->transferring == 'YES' ? 'YES' : 'NO',
                $carriers
            ]);
        }

        // Save the CSV file to the local storage
        $filename = 'AmTransferring-' . now()->format('Y-m-d-H-i-s') . '.csv';
        Storage::put("exports/{$filename}", (string) $csv);

        // Send the email with the CSV attachment
        $emailRecipients = env('DAILY_AM_TRANSFERRING_REPORT_EMAIL_RECIPIENTS', '<EMAIL>,<EMAIL>');
        $emails = explode(',', $emailRecipients);
        $file_path = storage_path("app/exports/{$filename}");

        Mail::to($emails)->send(
            new AmTransferringReportMail($file_path)
        );

        // Delete the file from local storage after sending the email
        Storage::delete("exports/{$filename}");

        $this->info('Email sent with CSV attachment.');
    }

}
