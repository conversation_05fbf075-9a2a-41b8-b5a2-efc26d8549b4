<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\UserInvite;
use App\Mail\InviteAcceptedNotification;
use Illuminate\Support\Facades\Mail;

class SendInviteAcceptedNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onboarding:send-invite-accepted-notification {email} {name} {recruit_name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the invite blocked notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $name = $this->argument('name');
        $recruit_name = $this->argument('recruit_name');

        // send email
        Mail::to($email)->send(
            new InviteAcceptedNotification(
                $email,
                $name, 
                $recruit_name
            )
        );

        $this->info('Invite Accepted Notification Message Sent');
    }
}
