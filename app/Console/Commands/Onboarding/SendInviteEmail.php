<?php

namespace App\Console\Commands\Onboarding;

use Illuminate\Console\Command;
use App\Models\UserInvite;
use App\Models\User;
use App\Models\Agent;
use App\Mail\OnboardingMail;
use Illuminate\Support\Facades\Mail;
use App\Mail\PrincipalOnboardingMail;
use App\Mail\B2BAgentOnboardingMail;



class SendInviteEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'onboarding:send-invite-emails {id} {--force}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send the invite email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $invite_id = $this->argument('id');
        $force_send = $this->option('force');
        $invite = UserInvite::where('id', $invite_id)->first();
        if($invite) {

            $ao_signature_line = '';
            $user = User::find($invite->user_id);
            if($agent = Agent::where('AgentCode', $user->agent_code)->first()) {
                if($ao = Agent::where('AgentID', $agent->BaseShopOwnerAgentID)->first()){
                    $agency_owner_name = $ao->PreferredName ? $ao->PreferredName : $ao->AgentName;
                    $ao_signature_line = "Agency Owner: $agency_owner_name";
                }
            }

            // send email
            if(!$invite->invite_sent || $force_send) {
                $mailable = match ($invite->type) {
                    'b2b-principal' => new PrincipalOnboardingMail(
                        $invite->name, 
                        $invite->code, 
                        [
                            'name' => $invite->user->name,
                            'email' => $invite->user->email
                        ],
                        $invite->person_message,
                        $ao_signature_line,
                    ),
                    'b2b-agent' => new B2BAgentOnboardingMail(
                    // 'b2b-agent' => new OnboardingMail(
                        $invite->name, 
                        $invite->code, 
                        [
                            'name' => $invite->user->name,
                            'email' => $invite->user->email
                        ],
                        $invite->person_message,
                        $ao_signature_line,
                    ),
                    default => new OnboardingMail(
                        $invite->name, 
                        $invite->code, 
                        [
                            'name' => $invite->user->name,
                            'email' => $invite->user->email
                        ],
                        $invite->person_message,
                        $ao_signature_line,
                    ),
                };
                
                $mail_result = Mail::to($invite->email)->send($mailable);
                $invite->update([
                    'invite_sent' => 1,
                ]);
                $this->info('Invite Sent');
            } else {
                $this->info('Invite Already Sent');
                // dump('Invite Already Sent');
            }
        } else {
            $this->info('No Invite With That ID Found');
        }
    }
}
