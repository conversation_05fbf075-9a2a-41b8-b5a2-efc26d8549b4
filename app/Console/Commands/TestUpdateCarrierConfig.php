<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserInvite;
use GuzzleHttp\Client;
use HQAccounts;
use Log;

class TestUpdateCarrierConfig extends Command
{
    protected $signature = 'test:carrier-config {agent_code}';
    protected $description = 'Test retrieving and updating carrier configuration for an agent';

    public function handle()
    {
        $agentCode = $this->argument('agent_code');
        
        // Find user by agent_code
        $user = User::where('agent_code', $agentCode)->first();
        if (!$user) {
            $this->error("No user found with agent_code: {$agentCode}");
            return 1;
        }

        $this->info("Found user: {$user->email}");

        // Get the latest invite
        $userInvite = UserInvite::where('id', $user->user_invite_id)->latest()->first();
        if (!$userInvite) {
            $this->error("No invite found for user");
            return 1;
        }

        $this->info("Found invite with carriers: " . $userInvite->carrier);

        // First, get current config
        try {
            $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $agentCode . '/config';
            // $requestPath = 'https://dev-dashboard.quility.com/api/trusted/agents/' . $agentCode . '/config';
            $accessToken = HQAccounts::generateAccessToken(true);

            $client = new Client();
            $response = $client->request('GET', $requestPath, [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
            ]);

            $configs = json_decode($response->getBody(), true);
            
            $this->info("\nCurrent Configurations:");
            $foundCarrierConfig = false;
            
            if (empty($configs) || !isset($configs['data']['AgentConfig']) || count($configs['data']['AgentConfig']) == 0) {
                $this->info("No existing configurations found");
            } else {
                foreach ($configs['data']['AgentConfig'] as $config) {
                    if ($config['ConfigName'] === 'selected_carriers') {
                        $foundCarrierConfig = true;
                        $currentValue = $config['ConfigValue'] ?? 'null';
                        $this->info("Current selected_carriers value: " . $currentValue);
                    }
                }
                
                if (!$foundCarrierConfig) {
                    $this->info("No existing selected_carriers configuration found");
                }
            }

            // Now update the config
            $this->info("\nUpdating configuration...");
            
            $response = $client->request('POST', $requestPath, [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
                'json' => [
                    'config_type' => 'Agent Config',
                    'config' => [
                        'ConfigName' => 'selected_carriers',
                        'ConfigValue' => $userInvite->carrier,
                        'DataType' => 'string'
                    ]
                ]
            ]);

            if ($response->getStatusCode() === 200) {
                $this->info("Successfully updated carrier configuration");
                return 0;
            }

            $this->error("Failed to update configuration. Status code: " . $response->getStatusCode());
            return 1;

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            Log::error("Error in carrier config test: " . $e->getMessage());
            return 1;
        }
    }
}
