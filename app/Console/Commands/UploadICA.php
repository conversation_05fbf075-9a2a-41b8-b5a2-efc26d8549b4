<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserRemoteAccount;
use App\Models\UserSignature;
use App\Models\User;
use File;
use App\CustomProviders\HQAccountServiceProvider;

class UploadICA extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'documents:upload-ica {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload ICA to HQ for Agent';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $agentSyncApi = resolve(AgentSyncApiService::class);
        // $user = resolve(User::class);
        
        
        $user_id = $this->argument('user_id');

        if(!$agent = UserRemoteAccount::where('user_id', $user_id)->first()) {
            $this->error("FILE UPLOAD ERROR (ICA) Couldn't find agent for user_id: $user_id");
            return;
        }

        if(!$user = User::find($user_id)) {
            $this->error("FILE UPLOAD ERROR (ICA) Couldn't find user_id: $user_id");
            return;
        }

        $email = str_replace("-stale", "", $user->email);
        $signature = UserSignature::where('user_email', $email)
                            ->where('event_type', UserSignature::EVENT_DOWNLOADABLE)
                            ->first();

        if($signature) {
            $relativePath = "uploads/signatures/{$signature->file_name}";
            $completePath = storage_path("app/{$relativePath}");

            if (!File::exists($completePath)) {
                //TODO: refactor to HelloSignService
                $HelloSignController = resolve('App\Http\Controllers\HelloSignController');
                $HelloSignController->downloadFiles($signature->signature_request_id);
            }
            // $hq = resolve('App\Htt')
            $hq = new HQAccountServiceProvider();
            $uploaded = $hq->sendFileRequest($completePath, $agent->agent_code, "ICA.zip");
            if(!!$uploaded) {
                $user->setMeta('ica_uploaded', true);
                $this->line('FILE UPLOAD SUCCESS (ICA) FOR '.$user_id.' '.print_r($uploaded, true));
            } else {
                $this->error("FILE UPLOAD ERROR (ICA) COULDN'T BE UPLOADED TO HQ FOR ".$user_id);
            }
        } else {
            $this->error("FILE UPLOAD ERROR (ICA) COULDN'T FIND SIGNATURE RECORD FOR ".$user_id);
        }
        return 0;
    }
}
