<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class AddTempAuth0DataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sfg:modify-temp-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add sample Auth0 data to test users';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // sales rep user
        $user = User::where('email', '<EMAIL>')->first();
        $user->sub = 'auth0|5ecc0f6b810cdd0cdb1f65ef';
        $user->auth0_metadata = '{"sub":"auth0|5ecc0f6b810cdd0cdb1f65ef","nickname":"testagent","name":"<PERSON>","picture":"https:\/\/s.gravatar.com\/avatar\/a3dddf5539e03d2facfd1b2de9ccba7b?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fte.png","updated_at":"2021-11-16T19:03:45.670Z","email":"<EMAIL>","email_verified":true,"http:\/\/quility.com\/roles":["SalesRep"],"http:\/\/quility.com\/meta_data":{"AgentCode":"SFG0015047","AgentsVUEID":"37","OptID":"CRAIGW4","Status":"Active","BaseShopOwnerOptID":"","KeyLeaderAgentCode":"","KeyLeaderOptID":"","LeadershipLevel":"Sales Representative","SFGDirectAgentCode":"","SFGDirectOptID":"","UplineAgentCode":"","UplineOptID":"","BaseShopOwnerAgentCode":"","ContractStartDate":"2017-10-18","FastTrack":0},"http:\/\/quility.com\/user_data":{"email":"<EMAIL>","name":"Craig Widner"}}';
        $user->agent_code = 'SFG0015047';
        $user->save();

        // agency owner user
        $user = User::where('email', '<EMAIL>')->first();
        $user->sub = 'auth0|5e835e124edbbf0d73e6fc2d';
        $user->auth0_metadata = '{"sub":"auth0|5e835e124edbbf0d73e6fc2d","nickname":"testowner","name":"Jake Davis","picture":"https:\/\/s.gravatar.com\/avatar\/29781c3264a8fd32dedda52bab8a7bca?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fte.png","updated_at":"2021-11-16T19:13:02.082Z","email":"<EMAIL>","email_verified":true,"http:\/\/quility.com\/roles":["AgencyOwner"],"http:\/\/quility.com\/meta_data":{"AgentCode":"SFG0000078","OptID":"JAKED2","Status":"Active","LeadershipLevel":"Agency Owner","BaseShopOwnerAgentCode":"SFG0000078","BaseShopOwnerOptID":"EDWARDP","KeyLeaderAgentCode":"","KeyLeaderOptID":"","SFGDirectAgentCode":"SFG0000078","SFGDirectOptID":"EDWARDP","UplineAgentCode":"SFG0000001","UplineOptID":"SFGAGENCY"},"http:\/\/quility.com\/user_data":{"email":"<EMAIL>","name":"Jake Davis"}}';
        $user->agent_code = 'SFG0000078';
        $user->save();

        // staff user
        $user = User::where('email', '<EMAIL>')->first();
        $user->sub = 'auth0|5f6b3d7ff34b74007864dbc0';
        $user->auth0_metadata = '{"sub":"auth0|5f6b3d7ff34b74007864dbc0","nickname":"teststaff","name":"<EMAIL>","picture":"https:\/\/s.gravatar.com\/avatar\/2ccd4bc979753c6d5a7101ecfc2a8486?s=480&r=pg&d=https%3A%2F%2Fcdn.auth0.com%2Favatars%2Fte.png","updated_at":"2021-11-16T19:15:29.240Z","email":"<EMAIL>","email_verified":true,"http:\/\/quility.com\/roles":["Staff"],"http:\/\/quility.com\/meta_data":{"AgentCode":"SFG0000001"},"http:\/\/quility.com\/user_data":{"email":"<EMAIL>","name":"<EMAIL>"}}';
        $user->agent_code = 'SFG0000001';
        $user->save();

        $this->info("Test user accounts updated.");

        return 0;
    }
}
