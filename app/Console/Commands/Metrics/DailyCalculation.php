<?php

namespace App\Console\Commands\Metrics;

use Illuminate\Console\Command;
use App\Models\Metric;
use App\Models\MetricValue;
use App\Models\UserEntry;
use App\Models\FormOption;
use App\Models\User;
use App\Models\UserInvite;
use App\Models\UserStatus;
use App\Models\UserStatusHistory;
use Carbon\Carbon;

class DailyCalculation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metrics:daily-calculations {date=now} {days=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Calculate metric data based on start date and range';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {   
        $days = $this->argument('days');
        $date = $this->argument('date');

        if($date == 'now') {
            $today = now();
        } else {
            $today = Carbon::createFromFormat('Y-m-d', $date)->setTime(0,0,0);
        }

        for ($i=0; $i < $days; $i++) {
            Metric::where('active', 1)->chunk('10', function ($metrics) use($today) {
                $today = $today->format('Y-m-d');

                foreach ($metrics as $metric) {  

                    if($metric->model_type == Metric::MODEL_TYPE_FORM_FIELD || $metric->model_type == Metric::MODEL_TYPE_USER_STATUS) {
                    
                        // need to updated to check based on "application_type" ie: All, Completed, In Progress, Abandoned
                        $users = User::whereDate('updated_at', $today)
                            ->where('upline_agent_id', '!=', null)
                            ->select('id', 'upline_agent_id');
                            // ->get();

                        if($metric->application_type === Metric::APPLICATION_TYPE_COMPLETED) {
                            $users->where('email', 'like', '%@completed.quility.com');
                        } elseif($metric->application_type === Metric::APPLICATION_TYPE_ABANDONED) {
                            $users->where('email', 'like', '%@purged.quility.com');
                        } elseif($metric->application_type === Metric::APPLICATION_TYPE_IN_PROGRESS) {
                            $users->where('email', 'not like', '%@purged.quility.com')
                                ->where('email', 'not like', '%@completed.quility.com');;
                        }

                        // chunk this
                        $uplines = User::where('upline_agent_id', '!=', null)
                            ->select('upline_agent_id')->distinct()
                            ->orderBy('upline_agent_id');
                            // ->get()->pluck('upline_agent_id')
                        
                        switch ($metric->model_type) {
                            case Metric::MODEL_TYPE_FORM_FIELD:
                                $this->modelFormField($users, $uplines, $metric, $today);
                                break;    
                            case Metric::MODEL_TYPE_USER_STATUS:
                                $this->modelUserStatus($users, $uplines, $metric, $today);
                                break;
                            default:
                                break;
                        }

                    } else if($metric->model_type == Metric::MODEL_TYPE_USER_INVITE) {
                        $user_invites = UserInvite::whereDate('updated_at', $today)
                            ->where('upline_agent_id', '!=', null);

                        // chunk this
                        $uplines = User::where('upline_agent_id', '!=', null)
                            ->select('upline_agent_id')->distinct()
                            ->orderBy('upline_agent_id');
                            // ->get()->pluck('upline_agent_id')

                        $this->modelUserInvite($user_invites, $uplines, $metric, $today);
                    }
                }
            });

            $today->addDay();
        }
        return 0;
    }

    private function modelFormField($users, $uplines, $metric, $today)
    {   
        $data = [
            'aggregate_totals' => [],
        ];
        
        $form_options = null;

        $form_options = FormOption::where('form_field_id', $metric->model_id)->get();
        if($form_options) {
            $form_options = $form_options->pluck('value');
        }

        $aggregate_totals_form_options = $form_options;
                    
        // set values as 0 for each value
        foreach($aggregate_totals_form_options as $form_option) {
            $data['aggregate_totals'][$form_option] = 0;
        }

        $users->chunk(1000, function ($us) use($form_options, &$data, $metric, $today) {
            
            $user_entries = null;
            if($metric->model_id) {
                $user_entries = UserEntry::where('form_field_id', $metric->model_id)
                    ->whereIn('user_id', $us->pluck('id'))
                    ->select('user_id', 'value')
                    ->orderBy('user_id')
                    ->get();
            }

            // loop over all user enties grouped by value
            foreach ($user_entries->groupBy('value') as $group_value => $group) {

                // if needed to check for values not in $form_options
                if(isset($data['aggregate_totals'][$group_value])) {
                    $data['aggregate_totals'][$group_value] += $group->count();
                } else {
                    $data['aggregate_totals'][$group_value] = $group->count();
                }
            }

            if($metric->breakdown_by_upline) {
                if(!isset($data['upline_totals'])) {
                    $data['upline_totals'] = [];
                }

                foreach($us->groupBy('upline_agent_id') as $upline_id => $upline_users) {

                    // set default values for each upline_id
                    foreach($form_options as $form_option) {
                        if(!isset($data['upline_totals'][$upline_id])) {
                            $data['upline_totals'][$upline_id][$form_option] = 0;
                        }
                    }

                    // loop over all enties by upline agent id grouped by value (same as above in aggregate_totals but filtered down more)
                    $e = $user_entries->whereIn('user_id', $upline_users->pluck('id'))->groupBy('value');
                    foreach ($e as $group_value => $group) {

                        // if needed to check for values not in $form_options
                        if(isset($data['upline_totals'][$upline_id][$group_value])) {
                            $data['upline_totals'][$upline_id][$group_value] += $group->count();
                        } else {
                            $data['upline_totals'][$upline_id][$group_value] = $group->count();
                        }  
                    }
                }
            }
        });

        if($metric->breakdown_by_upline) {
            $uplines->chunk(1000, function($ups) use($form_options, &$data) {
                foreach($ups->pluck('upline_agent_id') as $upline_id)  {
                    if(!isset($data['upline_totals'][$upline_id])) {
                        foreach($form_options as $form_option) {
                            $data['upline_totals'][$upline_id][$form_option] = 0;
                        }
                    }
                }
            });
        }

        $metric_value = MetricValue::updateOrCreate(
            [
                'date' => $today,
                'metric_id' => $metric->id,
            ],
            [
                'value' => json_encode($data),
            ]);

        $this->info('Metric Value Added For: ' . $metric->name . ' - ' . $today);     
    }

    private function modelUserStatus($users, $uplines, $metric, $today)
    {
        $user_statuses = UserStatusHistory::whereDate('updated_at', $today);

        if($metric->model_id) {
            $user_statuses->where('user_status_id', $metric->model_id);
            $status_options = UserStatus::where('id', $metric->model_id)->get();
        } else {
            $status_options = UserStatus::get();
        }

        $data = [
            'aggregate_totals' => [],
        ];

        // set values as 0 for each value
        foreach($status_options as $option) {
            $data['aggregate_totals'][$option->name] = 0;
        }

        $user_statuses->with(['user', 'userStatus'])->chunk(1000, function($us) use(&$data, $metric) {
            foreach ($us->groupBy('user_status_id') as $group_value => $group) {
                $user_status = $us->first()->userStatus;
                
                if(isset($data['aggregate_totals'][$user_status->name])) {
                    $data['aggregate_totals'][$user_status->name] += $group->count();
                } else {
                    $data['aggregate_totals'][$user_status->name] = $group->count();
                }

                if($metric->breakdown_by_upline) {
                    foreach($us as $u) {
                        if($u->user && $u->user->upline_agent_id) {
                            $upline_id = $u->user->upline_agent_id;
                            if(isset($data['upline_totals'][$upline_id][$u->userStatus->name])) {
                                $data['upline_totals'][$upline_id][$u->userStatus->name] += 1;
                            } else {
                                $data['upline_totals'][$upline_id][$u->userStatus->name] = 1;
                            }
                        }
                    }
                }
            }
        });

        if($metric->breakdown_by_upline) {
            $uplines->chunk(1000, function($ups) use($status_options, &$data) {
                foreach($ups->pluck('upline_agent_id') as $upline_id)  {
                    foreach($status_options as $option) {
                        if(!isset($data['upline_totals'][$upline_id]) || !isset($data['upline_totals'][$upline_id][$option->name])) {
                            $data['upline_totals'][$upline_id][$option->name] = 0;
                        }
                    }
                }
            });
        }

        $metric_value = MetricValue::updateOrCreate(
            [
                'date' => $today,
                'metric_id' => $metric->id,
            ],
            [
                'value' => json_encode($data),
            ]);

        $this->info('Metric Value Added For: ' . $metric->name . ' - ' . $today);   
    }

    private function modelUserInvite($user_invites, $uplines, $metric, $today)
    {
        $invite_statuses = [Metric::MODEL_VALUE_USER_INVITE_BLOCKED, Metric::MODEL_VALUE_USER_INVITE_SENT, Metric::MODEL_VALUE_USER_INVITE_ACCEPTED];

        $data = [
            'aggregate_totals' => [],
        ];

        // set values as 0 for each value
        foreach($invite_statuses as $option) {
            $data['aggregate_totals'][$option] = 0;
        }

        $user_invites->chunk(1000, function($uis) use(&$data, $invite_statuses, $metric) {
            foreach($uis as $ui) {
                $addBlocked = 0;
                $addSent = 0;
                $addAccepted = 0;

                if($ui->invite_accepted) {
                    $addAccepted = 1;
                } else if($ui->invite_sent) {
                    $addSent = 1;
                } else if($ui->blocked) {
                    $addBlocked = 1;
                }

                $data['aggregate_totals'][Metric::MODEL_VALUE_USER_INVITE_BLOCKED]  += $addBlocked;
                $data['aggregate_totals'][Metric::MODEL_VALUE_USER_INVITE_SENT]     += $addSent;
                $data['aggregate_totals'][Metric::MODEL_VALUE_USER_INVITE_ACCEPTED] += $addAccepted;

                if($metric->breakdown_by_upline) {
                    $upline_id = $ui->upline_agent_id;

                    if(isset($data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_BLOCKED])) {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_BLOCKED] += $addBlocked;
                    } else {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_BLOCKED] = $addBlocked;
                    }
                    
                    if(isset($data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_SENT])) {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_SENT] += $addSent;
                    } else {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_SENT] = $addSent;
                    }

                    if(isset($data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_ACCEPTED])) {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_ACCEPTED] += $addAccepted;
                    } else {
                        $data['upline_totals'][$upline_id][Metric::MODEL_VALUE_USER_INVITE_ACCEPTED] = $addAccepted;
                    }
                }
            }
        });

        if($metric->breakdown_by_upline) {
            $uplines->chunk(1000, function($ups) use($invite_statuses, &$data) {
                foreach($ups->pluck('upline_agent_id') as $upline_id)  {
                    foreach($invite_statuses as $option) {
                        if(!isset($data['upline_totals'][$upline_id]) || !isset($data['upline_totals'][$upline_id][$option])) {
                            $data['upline_totals'][$upline_id][$option] = 0;
                        }
                    }
                }
            });
        }

        $metric_value = MetricValue::updateOrCreate(
            [
                'date' => $today,
                'metric_id' => $metric->id,
            ],
            [
                'value' => json_encode($data),
            ]);

        $this->info('Metric Value Added For: ' . $metric->name . ' - ' . $today);   
    }
}
