<?php

namespace App\Console\Commands;

use App\Http\Requests\BaseAgentRequest;
use App\Models\Base\BaseAgent;
use App\Services\NiprSoapService;
use Illuminate\Console\Command;

class NiprNpnLookup extends Command
{
    protected NiprSoapService $niprSyncApi;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nipr:npn_lookup {first_name} {last_name} {ssn_last_4}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get NPN for specific';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->niprSyncApi = resolve(NiprSoapService::class);

        $first_name = $this->argument('first_name');
        $last_name = $this->argument('last_name');
        $ssn_last_4 = $this->argument('ssn_last_4');

        $foundNpn = $this->niprSyncApi->NPNLookupWithSSN($ssn_last_4, $first_name, $last_name);
        $this->info("NPN Number: " . $foundNpn);
        $foundNpn = $this->niprSyncApi->NPNLookupWithLicenseNumber('W827392', 'FL');
        $this->info("NPN Number: " . $foundNpn);
    }
}
