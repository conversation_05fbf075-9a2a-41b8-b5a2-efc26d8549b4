<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\FormLookup;
use FormBuilder;

class PushUserToSureLC extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'surelc:push-agent {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Push agent to SureLC';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        $sureLcService = resolve('App\Services\SureLcService');

        if(!$user = User::find($this->argument('user_id'))) {
            $this->error('User not found');
            return 1;
        }
        
        if($user->type == 'b2b-principal') {
            $ssn = FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_SSN);
            $ssn = str_replace('-', '', $ssn);
            $data = [
                'last_name' => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_LAST_NAME),
                'email' => FormBuilder::fetchUserEntry($user, FormLookup::PRINCIPAL_EMAIL),
                'ssn' => $ssn,
                'noEmails' => true,
            ];
            $result =$sureLcService->pushProducer($data);
            $this->info('Principal pushed to SureLC');
        } else if($user->type == 'b2b-agent') {
            $ssn = FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_SSN);
            $ssn = str_replace('-', '', $ssn);
            $agencyOwner = $user->agencyOwner();
            $agencyOwnerUser = User::where('agent_code', $agencyOwner->AgentCode)->first();
            $npn = $agencyOwnerUser->getMeta('npn');
            // look up agency owner branchCode in sureLc
            $branchCode = $sureLcService->getProducerBranchCodeByNPN($npn);

            $data = [
                'lastName' => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_LAST_NAME),
                'email' => FormBuilder::fetchUserEntry($user, FormLookup::B2B_AGENT_EMAIL),
                'ssn' => $ssn,
                'noEmails' => true,
                'branchCode' => $branchCode['branchCode']
            ];
            $result = $sureLcService->pushProducer($data);
            $this->info('Agent pushed to SureLC');
        }
        $this->info('SureLC result: '.print_r($result, true)."\n".print_r($data, true)); 
        return 0;
    }
}
