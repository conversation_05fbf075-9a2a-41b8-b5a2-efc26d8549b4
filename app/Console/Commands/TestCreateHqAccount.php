<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Jobs\CreateHQAccount;

class TestCreateHqAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:create-hq-account {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Using this to test/debug the hq account creation processes. You will need a user email address to run the test for that user.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();
        echo "Testing with ${email} $user->id";

        CreateHQAccount::dispatch($user->id);
        return 0;
    }
}
