<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\CustomProviders\HQAccountServiceProvider;
use Log;

class UploadUserFilesToHQ extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:upload-files-to-hq {userId : The ID of the user whose files to upload}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload a user\'s files to HQ using HQAccountServiceProvider';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $userId = $this->argument('userId');
        
        if (!$userId) {
            $this->error('Invalid user ID provided');
            return 1;
        }

        try {
            $this->info("Starting upload of files to HQ for user ID: {$userId}");
            
            // Initialize HQAccountServiceProvider
            $hqService = new HQAccountServiceProvider();
            
            // Generate access token before uploading files
            $token = $hqService->generateAccessToken(true);
            if (!$token) {
                $this->error('Failed to generate access token');
                return 1;
            }
            
            // Upload files to HQ
            $uploadCount = $hqService->uploadFilesToHq($userId);
            
            $this->info("Successfully uploaded {$uploadCount} files to HQ for user ID: {$userId}");
            return 0;
            
        } catch (\Exception $e) {
            $this->error("Error uploading files to HQ: " . $e->getMessage());
            Log::error("Error in UploadUserFilesToHQ command: " . $e->getMessage(), [
                'userId' => $userId,
                'exception' => $e
            ]);
            return 1;
        }
    }
} 