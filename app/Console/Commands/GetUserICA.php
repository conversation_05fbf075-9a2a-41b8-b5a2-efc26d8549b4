<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserSignature;
use AzureStore;
use HelloSign;
use File;


class GetUserICA extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:get-user-ica {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get the ICA for a user from DropboxSign';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $email = $this->argument('email');
        // if(!$signature_request = UserSignature::where('user_email', $email)->whereNotNull('signature_request_id')->first()) {
        //     $this->error('Signature record not found');
        //     return;
        // }
        // $signatureRequestId = $signature_request->signature_request_id;
        $signatureRequestId = 'bfe4e293f2b492d565eafe274234734349bfb8f7';
        $this->line("signature_request_id: $signatureRequestId");

        $fileName = "{$signatureRequestId}.".HelloSign\SignatureRequest::FILE_TYPE_ZIP;
        $storagePath = storage_path(config('hellosign.localPath', null));
        $completePath = "{$storagePath}/{$fileName}";

        // check for folder, create if not exists
        if (!File::isDirectory($storagePath)) {
            File::makeDirectory($storagePath, 0777, true, true);
        }

        // send download request to client
        $client = new HelloSign\Client(config('hellosign.keys.api_key', null));
        if (!$client->getFiles($signatureRequestId, $completePath, HelloSign\SignatureRequest::FILE_TYPE_ZIP)) {
            $this->error("HELLO SIGN ERROR: Unable to download files for signatureRequestId {$signatureRequestId}.");
            return false;
        }

        // attempt to move file to azure blof storage
        $remotePath = config('hellosign.remotePath');
        if (!AzureStore::moveLocalFileToStorage($remotePath, $completePath)) {
            $this->error("HELLO SIGN ERROR: Unable to push local file {$completePath} to remote path ".AzureStore::buildPath($remotePath));
            return false;   
        }
        
        $this->line($fileName);
        return;
    }
}
