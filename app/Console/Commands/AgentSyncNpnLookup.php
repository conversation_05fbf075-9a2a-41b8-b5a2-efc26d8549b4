<?php

namespace App\Console\Commands;

use App\Http\Requests\BaseAgentRequest;
use App\Models\Base\BaseAgent;
use App\Services\AgentSyncApiService;
use Illuminate\Console\Command;

class AgentSyncNpnLookup extends Command
{
    protected AgentSyncApiService $agentSyncApi;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agentsync:npn_lookup {first_name} {last_name} {ssn_last_4}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check specific agent for an NPN using the AgentSync API.';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        \Log::channel('tasks')->info('Starting unlicensed NPN lookup.');

        $this->agentSyncApi = resolve(AgentSyncApiService::class);

        $first_name = $this->argument('first_name');
        $last_name = $this->argument('last_name');
        $ssn_last_4 = $this->argument('ssn_last_4');


        $foundNpn = $this->agentSyncApi->searchNpnBySsn($first_name, $last_name, $ssn_last_4);
        echo "\nNPN: $foundNpn\n";
                    
        \Log::channel('tasks')->info('Unlicensed NPN lookup complete.');
    }
}
