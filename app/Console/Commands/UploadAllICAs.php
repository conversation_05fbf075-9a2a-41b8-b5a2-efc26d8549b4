<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\UserRemoteAccount;
use App\Models\UserSignature;
use App\Models\User;
use App\CustomProviders\HQAccountServiceProvider;
use Artisan;
use File;
use DB;

class UploadAllICAs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'documents:upload-all-icas';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'One-time script to upload all ICAs to HQ for all agents in the Onboarding Portal';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->line('Begin uploading all ICAs');

        // get all users that have an agent_code and downloadable signature file
        $query = DB::table('users as u')
            ->join('user_signatures as us', 'us.user_email', '=', 'u.email')
            ->whereNotNull('u.agent_code')
            ->where('us.event_type', 'signature_request_downloadable')
            ->select('u.*');
        
        $count = $query->count();
        $this->line("Count: ".$count);
        
        $agents = $query->get();
        foreach($agents as $agent) {
            $user = User::find($agent->id);
            if(!$ica_uploaded = $user->getMeta('ica_uploaded', null)) {
                Artisan::call('documents:upload-ica '.$user->id);
                $this->line($count--.' Uploaded '.$user->id.' '.$user->agent_code);
            } else {
                $this->line($count--.' Skipped '.$user->id.' '.$user->agent_code);
            }
        }

        return;
        
    }
}
