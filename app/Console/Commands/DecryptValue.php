<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Defuse\Crypto\Crypto;
use Defuse\Crypto\Key;

class DecryptValue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'decrypt:value {value}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Decrypt any value. Be sure to use the correct SFG_INTERNAL_SECURE_KEY env var setting';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $value = $this->argument('value');

        $secureKey = config('system.securekey', null);
        $key = Key::loadFromAsciiSafeString($secureKey);
        $decryptedValue = Crypto::decrypt($value, $key);
        echo "\n".$decryptedValue;
        return 0;
    }
}
