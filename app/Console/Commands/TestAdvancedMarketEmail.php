<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;
use App\Mail\AdvancedMarketMail;

class TestAdvancedMarketEmail extends Command
{
    protected $signature = 'mail:test-advanced-market';
    protected $description = 'Preview the Advanced Market email';

    public function handle()
    {
        $this->info('Sending test Advanced Market email...');
        
        try {
            Mail::to('<EMAIL>')->send(new AdvancedMarketMail('Test Agent'));
            $this->info('✓ Advanced Market email sent <NAME_EMAIL>!');
        } catch (\Exception $e) {
            $this->error('✗ Failed to send email: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
} 