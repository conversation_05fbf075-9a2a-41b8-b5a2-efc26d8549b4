<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\AgentSyncApiService;

class VerifyNPN extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:verify-npn {first_name} {last_name} {last4}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Verify NPN in AgentSync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $agentSyncApi = resolve(AgentSyncApiService::class);
        
        $first_name = $this->argument('first_name');
        $last_name = $this->argument('last_name');
        $ssn_last_4 = $this->argument('last4');

        $foundNpn = "";

        $foundNpn = $agentSyncApi->searchNpnBySsn($first_name, $last_name, $ssn_last_4);

        // $foundNpn = $agentSyncApi->searchByLicenseNumber('W827392', 'FL');

        return $foundNpn;
        return 0;
    }
}
