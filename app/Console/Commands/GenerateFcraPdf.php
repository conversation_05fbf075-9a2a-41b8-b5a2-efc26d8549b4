<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
// use Illuminate\Support\Facades\Http;
use App\Models\User;
use App\Models\FormPage;
use App\Models\FormSection;
use App\Models\FormField;
use App\Models\UserEntry;
// use FormBuilder;
use PDF;
// use Barryvdh\DomPDF\Facade\Pdf as Pdf;
// use App;
use Storage;

class GenerateFcraPdf extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'document:generate-fcra-pdf {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Create a recruit's signed FCRA PDF file.";

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $user_id = $this->argument('user_id');

        $pdf = resolve('App\Services\GenerateFCRAService');
        return !!$pdf->makePDF($user_id);
    }
}
