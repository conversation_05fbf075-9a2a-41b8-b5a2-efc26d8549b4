<?php

namespace App\Console\Commands\Testing;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use HQAccounts;

class TestServiceLevelAttribute extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:service-level {agent_code=BTB0095147 : The agent code to test}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the getServiceLevelAttribute function for a specific user';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $agentCode = $this->argument('agent_code');
        $this->info("Testing service level attribute for agent code: {$agentCode}");

        // Find the user by agent_code
        $user = User::where('agent_code', $agentCode)->first();

        if (!$user) {
            $this->error("User with agent code {$agentCode} not found.");
            return 1;
        }

        $this->info("Found user: {$user->name} (ID: {$user->id})");

        try {
            // First, let's make a direct API call to see the raw response
            $this->info("\n=== RAW API RESPONSE DEBUG ===");
            $this->debugApiResponse($user);

            // Get the service level
            $startTime = microtime(true);
            $serviceLevel = $user->service_level;
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

            if ($serviceLevel) {
                $this->info("Service Level: {$serviceLevel}");
                $this->info("Execution time: {$executionTime}ms");
            } else {
                $this->warn("Service Level is null or empty");
            }

            // Test again to verify caching
            $this->info("\nTesting cached response:");
            $startTime = microtime(true);
            $cachedServiceLevel = $user->service_level;
            $endTime = microtime(true);
            $cachedExecutionTime = round(($endTime - $startTime) * 1000, 2); // in milliseconds

            $this->info("Service Level (cached): {$cachedServiceLevel}");
            $this->info("Execution time (cached): {$cachedExecutionTime}ms");

            // Show improvement from caching
            if ($cachedExecutionTime < $executionTime) {
                $improvement = round((($executionTime - $cachedExecutionTime) / $executionTime) * 100, 2);
                $this->info("Cache improved response time by {$improvement}%");
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("Error getting service level: " . $e->getMessage());
            $this->error("Stack trace:");
            $this->error($e->getTraceAsString());
            return 1;
        }
    }

    /**
     * Debug the raw API response from the trusted agents config endpoint
     *
     * @param User $user
     * @return void
     */
    protected function debugApiResponse(User $user)
    {
        $agencyOwner = $user->agencyOwner();

        if (!$agencyOwner || !$agencyOwner->AgentCode) {
            $this->warn("No agency owner found for user - cannot make API call");
            return;
        }

        $this->info("Agency Owner Agent Code: {$agencyOwner->AgentCode}");

        try {
            $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $agencyOwner->AgentCode . '/config';
            $this->info("API Endpoint: {$requestPath}");

            $accessToken = HQAccounts::generateAccessToken(true);
            $this->info("Access Token Generated: " . substr($accessToken, 0, 20) . "...");

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET', $requestPath, [
                'headers' => [
                    'Authorization' => "Bearer {$accessToken}",
                    'Accept' => 'application/json',
                ],
            ]);

            // Display response status and headers
            $this->info("\n--- HTTP RESPONSE STATUS ---");
            $this->info("Status Code: " . $response->getStatusCode());
            $this->info("Reason Phrase: " . $response->getReasonPhrase());

            $this->info("\n--- HTTP RESPONSE HEADERS ---");
            foreach ($response->getHeaders() as $name => $values) {
                $this->info("{$name}: " . implode(', ', $values));
            }

            // Display raw response body
            $rawBody = $response->getBody()->getContents();
            $this->info("\n--- RAW RESPONSE BODY ---");
            $this->info("Response Body Length: " . strlen($rawBody) . " bytes");
            $this->line($rawBody);

            // Display parsed JSON structure
            $this->info("\n--- PARSED JSON STRUCTURE ---");
            $configs = json_decode($rawBody, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error("JSON Parse Error: " . json_last_error_msg());
                return;
            }

            $this->info("JSON Structure:");
            $this->line(json_encode($configs, JSON_PRETTY_PRINT));

            // Analyze the AgentConfig data specifically
            if (isset($configs['data']['AgentConfig'])) {
                $this->info("\n--- AGENT CONFIG ANALYSIS ---");
                $this->info("Number of config items: " . count($configs['data']['AgentConfig']));

                foreach ($configs['data']['AgentConfig'] as $index => $config) {
                    $this->info("Config #{$index}:");
                    $this->info("  - ConfigName: " . ($config['ConfigName'] ?? 'N/A'));
                    $this->info("  - ConfigValue: " . ($config['ConfigValue'] ?? 'N/A'));
                    $this->info("  - DataType: " . ($config['DataType'] ?? 'N/A'));

                    if ($config['ConfigName'] === 'ServiceLevel') {
                        $this->info("  *** THIS IS THE SERVICE LEVEL CONFIG ***");
                    }
                }
            } else {
                $this->warn("No 'data.AgentConfig' found in response");
            }

        } catch (\Exception $e) {
            $this->error("\n--- API CALL ERROR ---");
            $this->error("Error Type: " . get_class($e));
            $this->error("Error Message: " . $e->getMessage());
            $this->error("Error Code: " . $e->getCode());

            if ($e instanceof \GuzzleHttp\Exception\RequestException && $e->hasResponse()) {
                $errorResponse = $e->getResponse();
                $this->error("HTTP Status: " . $errorResponse->getStatusCode());
                $this->error("Error Response Body: " . $errorResponse->getBody()->getContents());
            }

            $this->error("Stack Trace:");
            $this->error($e->getTraceAsString());
        }

        $this->info("\n=== END RAW API RESPONSE DEBUG ===\n");
    }
}