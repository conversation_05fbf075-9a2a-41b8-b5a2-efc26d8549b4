<?php

namespace App\Console\Commands\Testing;

use Illuminate\Console\Command;
use App\Models\FormOption;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\UserEntry;
use App\Models\UserInvite;
use App\Models\UserStatusHistory;
use Str;

class AddTestMetricData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'testing:test-metric-data {loop=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {   
        $loop = $this->argument('loop');
        $form_option = FormOption::where('label', 'like', '%Bachelor%')->first();
        $form_options = FormOption::where('form_field_id', $form_option->form_field_id)->get();

        $uplines = collect();
        $uplines->push(
            rand(10000, 99999), rand(10000, 99999), 
            rand(10000, 99999), rand(10000, 99999), 
            rand(10000, 99999), rand(10000, 99999),
            rand(10000, 99999), rand(10000, 99999), 
            rand(10000, 99999), rand(10000, 99999), 
            rand(10000, 99999), rand(10000, 99999),
        );

        $inviter = User::where('email', '<EMAIL>')->first();

        // users w/ sample form field
        $user_status = UserStatus::where('slug', 'registration')->first()->id;
        for ($i=0; $i < $loop; $i++) { 
            $user = User::factory()->create(
                [
                    'user_status_id' => $user_status,
                    'upline_agent_id' => $uplines->random(),
                    'updated_at' => now()->subDays(rand(0, 15)),
                ]
            );

            $this->info('User Point Added: ' . $user->id);  

            UserStatusHistory::create([
                'user_id' => $user->id, 
                'user_status_id' => $user_status,
            ]);

            $entry = UserEntry::create([
                'user_id' => $user->id,
                'form_field_id' => $form_option->form_field_id,
                'value' =>  $form_options->random()->value
            ]);

            $this->info('Data Point Added: ' . $entry->id);  

            // Add inviter record for this user ::: METRIC-TESTING PURPOSE - Not asserted values
            srand(time());

            $accepted = rand(0, 1);
            $blocked = $accepted ? 0 : rand(0, 1);
            $block_reason = $blocked ? '' : 'user-invite-blocked';
            $expired = ($accepted || $blocked) ? 1 : 0;
            $sent = $accepted ? 1 : rand(0, 1);

            $ui = UserInvite::create([
                'user_id'           => $inviter->id,
                'email'             => $user->email,
                'name'              => $user->name,
                'phone'             => Str::random(10),
                'invite_expired'    => $expired,
                'blocked'           => $blocked,
                'block_reason'      => $block_reason,
                'invite_sent'       => $sent,
                'invite_accepted'   => $accepted,
                'code'              => (string) Str::uuid(),
                'contract_level'    => rand(15, 20) * 5,
                'upline_agent_id'   => $uplines->random()
            ]);

            $this->info('Invite Added: ' . $ui->id);  
        }

        return 0;
    }
}
