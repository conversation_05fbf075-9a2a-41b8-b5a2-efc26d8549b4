<?php

namespace App\Console\Commands\CleanUp;

use Illuminate\Console\Command;
use App\Models\UserEntry;
use App\Models\UserUpload;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\FormField;
use Carbon\Carbon;
use Storage;

class DeleteApplication extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:delete-application {user_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Deletes a user and all their application data.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        if(!$user = User::find($this->argument('user_id'))){
            $this->warn('user not found');
            return 0;
        }
        
        // delete signature images
        $folder_signature_imgs = "signature/{$user->id}";
        if(Storage::disk('public')->exists($folder_signature_imgs)) {
            Storage::disk('public')->deleteDirectory($folder_signature_imgs);
        }
        
        //delete user_uploads
        UserUpload::where('user_id', $user->id)->delete();

        // delete user_entries    
        UserEntry::where('user_id', $user->id)->delete();

        $user->update([
            'name' => '*****',
            'email' => $user->id . '@completed.quility.com',
        ]);

        $user->delete();

        $this->info('user : ' . $user->id . ' : application deleted');
        return 0;
        
    }
}
