<?php

namespace App\Console\Commands\CleanUp;

use Illuminate\Console\Command;
use App\Models\UserStatusHistory;
use App\Models\User;
use DB;
use Carbon\Carbon;
use App\Mail\CleanUp\SendPendingDeletionMail;
use Illuminate\Support\Facades\Mail;

class SendPendingDeletionEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:send-pending-deletion-email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send an email warning about pending application deletion';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {   
        User::where('type', 'default')
            ->where('updated_at', '<', Carbon::now()->subDays(53))
            ->where('updated_at', '>', Carbon::now()->subDays(54))
            ->chunk(100, function($users) {
                foreach ($users as $user) {
                    Mail::to($user->email)->send(new SendPendingDeletionMail($user));
                    $this->info('Email send to : ' . $user->email);
                }
            });
        return 0;
    }
}
