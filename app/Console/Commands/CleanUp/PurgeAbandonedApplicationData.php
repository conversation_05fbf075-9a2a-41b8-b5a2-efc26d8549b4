<?php

namespace App\Console\Commands\CleanUp;

use Illuminate\Console\Command;
// use App\Models\UserEntry;
// use App\Models\FormField;
use App\Models\User;
use Carbon\Carbon;
// use Storage;

class PurgeAbandonedApplicationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:purge-abandoned-applications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove PII from all user_entries for abandoned applications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {   

        // Modified to simply rename but not destroy the original LOGIN email address by appending '-OLD'
        // This is so we can keep the data and allow the user to return at a future data and start a new application

        // $fields_to_delete = FormField::where('pii', 1)->select('id')->get()->toArray();
        $fields_to_delete = null;

        User::where('updated_at', '<', Carbon::now()->subDays(60))
            ->chunk(100, function($users) use($fields_to_delete) {
                foreach ($users as $user) {
                    // // delete signature images
                    // $folder_signature_imgs = "signature/{$user->id}";
                    // if(Storage::disk('public')->exists($folder_signature_imgs)) {
                    //     Storage::disk('public')->deleteDirectory($folder_signature_imgs);
                    // }

                    // $this->info('user : ' . $user->id . ' : signature image folder purged');  

                    // // delete PII information (SSN, Names, Documents, etc) from user_entries    
                    // UserEntry::where('user_id', $user->id)
                    //     ->whereIn('form_field_id', $fields_to_delete)
                    //     ->delete();

                    $user->update([
                        // 'name' => '*****',
                        // 'email' => $user->id . '@purged.quility.com',
                        'email' => $user->email . '-STALE',
                    ]);

                    // $user->delete();

                    $this->info('user : ' . $user->id . ' ' . $user->email . ' : STALE application status updated');  
                }
        });
        
        return 0; 
    }
}

// http://sfgonboarding.test/storage/signature/F64E0F04-2BFF-4DAC-B523-77B3EDBCE6B8/CBA2DEC6-7447-4826-84F8-9AFD0FD892E0.png