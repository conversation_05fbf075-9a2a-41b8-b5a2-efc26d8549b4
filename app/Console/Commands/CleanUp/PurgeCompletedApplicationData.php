<?php

namespace App\Console\Commands\CleanUp;

use Illuminate\Console\Command;
use App\Models\UserEntry;
use App\Models\User;
use App\Models\UserStatus;
use App\Models\FormField;
use Carbon\Carbon;
use Storage;

class PurgeCompletedApplicationData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'clean-up:purge-completed-applications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove PII from all user_entries for completed applications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $fields_to_delete = FormField::where('pii', 1)->select('id')->get()->toArray();
        $sent_to_hq_status = UserStatus::where('name', 'Sent To HQ Quility')->first();

        User::where('user_status_id', $sent_to_hq_status->id)
            ->chunk(100, function($users) use($fields_to_delete) {
                foreach ($users as $user) {
                    // delete signature images
                    $folder_signature_imgs = "signature/{$user->id}";
                    if(Storage::disk('public')->exists($folder_signature_imgs)) {
                        Storage::disk('public')->deleteDirectory($folder_signature_imgs);
                    }
                    
                    $this->info('user : ' . $user->id . ' : signature image folder purged');  

                    // delete PII information (SSN, Names, Documents, etc) from user_entries    
                    UserEntry::where('user_id', $user->id)
                        ->whereIn('form_field_id', $fields_to_delete)
                        ->delete();

                    $user->update([
                        'name' => '*****',
                        'email' => $user->id . '@completed.quility.com',
                    ]);

                    $user->delete();

                    $this->info('user : ' . $user->id . ' : application purged');  
                }
        });
        
        return 0;
    }
}
