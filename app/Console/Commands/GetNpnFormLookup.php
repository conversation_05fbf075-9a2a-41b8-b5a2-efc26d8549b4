<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Jobs\CreateHQAccount;

class GetNpnFormLookup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:get-npn {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Lookup NPN from user meta';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();
        $npn = $user->getMeta('npn', null);
        echo "NPN: $npn";
        return 0;
    }
}
