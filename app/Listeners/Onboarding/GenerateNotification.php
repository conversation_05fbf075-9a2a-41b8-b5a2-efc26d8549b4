<?php

namespace App\Listeners\Onboarding;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Auth\Events\Registered;
use App\Models\User;
use App\Models\UserTask;

use App\Events\Review\AO\Submitted as AOSubmitted;
use App\Events\Review\AO\Rejected as AORejected;
use App\Events\Review\AO\Approved as AOApproved;
use App\Events\Review\HO\Submitted as HOSubmitted;
use App\Events\Review\HO\Rejected as HORejected;
use App\Events\Review\HO\Approved as HOApproved;
use App\Events\Status\Unenrolled as Unenrolled;
use App\Events\Status\Enrolled as Enrolled;

use App\Events\Review\RevisionRequested;

use App\Events\Onboarding\UnlicensedAccountCreated;
use App\Events\Onboarding\AssignedToDept;

use UserNotifications;
// use UserTasks;
use Agents;
use DB;

class GenerateNotification implements ShouldQueue
{
    private $logger;
    private $log_prefix;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger.notification-generator');
    }
    
    /**
     * Handle the event.
     *
     * @param  object  $event
     */
    public function handle($event)
    {
        if(!$event || (!isset($event->recruit) && !isset($event->user))) {
            $this->log("Invalid event", "error");
            return;
        }

        $recruit = isset($event->recruit) ? $event->recruit : $event->user;
        $agencyOwner = $recruit->agencyOwner();
        if(!$agencyOwner || !$ao_user = User::where('agent_code', $agencyOwner->AgentCode)->first()) {
            $this->log("AO user not found for this candidate", "error");
        }

        //we have an $upline_user if the AO is not the recruiter
        $upline_user = false;
        if($recruit->agency_owner_code != $recruit->upline_agent_code) {
            if($uplineAgent = $recruit->uplineAgent()->first()){
                if(!$upline_user = User::where('agent_code', $uplineAgent->AgentCode)->first()) {
                    $this->log("Upline user not found for this candidate", "error");
                }
            } else {
                $this->log("Upline agent not found");
            }
        }

        $this->log_prefix = "[#" . $recruit->id . "-" . get_class($event) . "] ";
        
        //TODO: NPN verified?

        DB::beginTransaction();

        try {
            if ($event instanceof Registered) {
                $message = $recruit->name." just registered";
                try {
                    UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                } catch (\Throwable $th) {  
                    $this->log("Error creating notification for AO: " . $th->getMessage(), "error");
                }
                try {
                    if($upline_user)
                        UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
                } catch (\Throwable $th) {
                    $this->log("Error creating notification for upline: " . $th->getMessage(), "error");
                }
            } elseif ($event instanceof RevisionRequested) {
                //by who? HO/AO
                $message = "Revision requested for ".$recruit->name;
                UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof AOSubmitted) {
                $message = "App submitted for ".$recruit->name;
                UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof AOApproved) {
                $message = "AO approved app for ".$recruit->name;
                UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof AORejected) {
                $message = "AO rejected app for ".$recruit->name;
                UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof HOApproved) {
                $message = "Home Office approved app for ".$recruit->name;
                UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof HORejected) {
                $message = "Home Office rejected app for ".$recruit->name;
                try {
                    UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                } catch (\Throwable $th) {
                    $this->log("Error creating notification for recruit: " . $th->getMessage(), "error");
                }
                try {
                    UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                } catch (\Throwable $th) {
                    $this->log("Error creating notification for AO: " . $th->getMessage(), "error");
                }
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof HOSubmitted) {
                $message = "App for ".$recruit->name." has been submitted to the Home Office";
                UserNotifications::create($message)->for($recruit)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof Unenrolled) {
                $message = $recruit->name." is not enrolled in a prelicensing course.";
                UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } elseif ($event instanceof Enrolled) {
                $message = $recruit->name." has enrolled in a prelicensing course.";
                UserNotifications::create($message)->for($ao_user)->target($recruit->id)->save();
                if($upline_user)
                    UserNotifications::create($message)->for($upline_user)->target($recruit->id)->save();
            } else {
                $this->log("No matching event, nothing happened", "warning");
            }
        
            DB::commit();
        } catch(\Exception $e) {
            DB::rollback();
            $this->log("Database transaction failed: " . $e->getMessage(), "error");
        }
    }

    private function log($message, $level = "info")
    {
        switch($level) {
            case "debug":
                $this->logger->debug($this->log_prefix . $message);
                break;
            case "error":
                $this->logger->error($this->log_prefix . $message);
                break;
            case "warning":
                $this->logger->warning($this->log_prefix . $message);
                break;
            default:
                $this->logger->info($this->log_prefix . $message);
        }
    }
}
