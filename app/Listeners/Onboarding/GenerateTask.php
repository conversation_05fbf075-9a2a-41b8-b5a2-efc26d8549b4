<?php

namespace App\Listeners\Onboarding;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Auth\Events\Registered;
use App\Models\User;
use App\Models\UserTask;

use App\Events\Review\AO\Submitted as AOSubmitted;
use App\Events\Review\AO\Rejected as AORejected;
use App\Events\Review\AO\Approved as AOApproved;
use App\Events\Review\HO\Submitted as HOSubmitted;
use App\Events\Review\HO\Rejected as HORejected;
use App\Events\Review\HO\Approved as HOApproved;

use App\Events\Review\RevisionRequested;

use App\Events\Onboarding\UnlicensedAccountCreated;
use App\Events\Onboarding\AssignedToDept;

use UserTasks;
use Agents;
use DB;

class GenerateTask implements ShouldQueue
{
    private $logger;
    private $log_prefix;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger.task-generator');
    }
    
    /**
     * Handle the event.
     *
     * @param  object  $event
     */
    public function handle($event)
    {
        if(!$event || (!isset($event->recruit) && !isset($event->user))) {
            $this->log("Invalid event", "error");
            return;
        }

        $recruit = isset($event->recruit) ? $event->recruit : $event->user;

        $staff = Agents::getOneWithRole(User::ROLE_TYPE_STAFF);

        $this->log_prefix = "[#" . $recruit->id . "-" . get_class($event) . "]";

        DB::beginTransaction();

        try{
            if ($event instanceof Registered) {

                if(!$recruit->can('fill applications'))
                    $this->log("Failed to create a fill application task for Candidate: User does not have permission", "error");
                else if(UserTasks::create(UserTask::TYPE_FILL_APPLICATION)->for($recruit)->target($recruit->id)->save())
                    $this->log("Created a fill application task for Candidate");
                else
                    $this->log("Failed to create a fill application task for Candidate", "error");

            }
            else if ($event instanceof RevisionRequested) {

                if(!$recruit->can('fill applications'))
                    $this->log("Failed to create a update application task for Candidate: User does not have permission", "error");
                else if(UserTasks::create(UserTask::TYPE_UPDATE_APPLICATION)->for($recruit)->target($recruit->id)->save())
                    $this->log("Created a update application task for Candidate");
                else
                    $this->log("Failed to create a update application task for Candidate", "error");

            }
            else if ($event instanceof AOSubmitted) {
        
                $agent = null;
                $aoRecruit = $recruit->agencyOwner();
                if($aoRecruit) 
                {
                    if(!$agent = User::where('agent_code', $aoRecruit->AgentCode)->first())
                    {
                        $this->log("Candidate's AO [Code: {$aoRecruit->AgentCode}] is not registered yet. Creating a placeholder account for him now.");
                        $agent = Agents::createPlaceholderUserForAgent($aoRecruit);
                    }
                }else{
                    $this->log("Candidate doesn't have valid upline AO", "warning");
                }

                if(!$agent)
                    $this->log("Failed to create a review task for AO: Invalid upline agent for this candidate!", "error");
                else if(!$agent->can('review applications'))
                    $this->log("Failed to create a review task for AO: User does not have permission", "error");
                
                // Disable: AO task is created in UserApplicationController
                // else if(UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($agent)->target($recruit->id)->save())
                //     $this->log("Created a review task for AO");
                // else
                //     $this->log("Failed to create a review task for AO", "error");

            }
            // [DISABLE]: no need to create review task for HO, any HO can review any application
            // else if ($event instanceof AOApproved) {

            //     if(!$staff->can('review applications'))
            //         $this->log("Failed to create a review task for HO: User does not have permission", "error");
            //     else if(UserTasks::create(UserTask::TYPE_APPROVE_APPLICATION)->for($staff)->target($recruit->id)->save()){
            //         $this->log("Created a review task for HO");
            //     }
            //     else
            //         $this->log("Failed to create a review task for HO", "error");

            // }
            // [DISABLE]: no need to create CUA task for HO, CreateFullHQAccount is called on HOApproved
            // else if ($event instanceof HOApproved) {

            //     if(!$staff)
            //         $this->log("Found no staff member. Can't verify this approval.", "error");
            //     else if(!$staff->can('create hqaccount'))
            //         $this->log("Failed to create a CUA task for HO: User does not have permission", "error");
            //     else if(UserTasks::create(UserTask::TYPE_CREATE_UNLICENSED_ACCOUNT)->for($staff)->target($recruit->id)->save())
            //         $this->log("Created a CUA task for HO");
            //     else
            //         $this->log("Failed to create a CUA task for HO", "eror");

            // }
            else if ($event instanceof UnlicensedAccountCreated) {

                if(!$staff)
                    $this->log("Found no staff member. Can't verify this event.", "error");
                if(!$staff->can('assign to onboarding dept'))
                    $this->log("Failed to create a ATO task for HO: User does not have permission", "error");
                else if(UserTasks::create(UserTask::TYPE_ASSIGN_TO_ONBOARDING)->for($staff)->target($recruit->id)->save())
                    $this->log("Created a ATO task for HO");
                else
                    $this->log("Failed to create a ATO task for HO", "error");

            }else {
                $this->log("No matching event, nothing happened", "warning");
            }

            DB::commit();
        }catch(\Exception $e) {
            DB::rollback();
            $this->log("Database transaction failed: " . $e->getMessage(), "error");
        }
    }

    private function log($mesasge, $level = "info")
    {
        switch($level) {
            case "debug":
                $this->logger->debug($this->log_prefix . $mesasge);
                break;
            case "error":
                $this->logger->error($this->log_prefix . $mesasge);
                break;
            case "warning":
                $this->logger->warning($this->log_prefix . $mesasge);
                break;
            default:
                $this->logger->info($this->log_prefix . $mesasge);
        }
    }
}
