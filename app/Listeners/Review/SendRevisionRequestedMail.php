<?php

namespace App\Listeners\Review;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\Review\RevisionRequestedMail;
use Illuminate\Support\Facades\Mail;

class SendRevisionRequestedMail implements ShouldQueue
{
    private $logger;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger');
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        if($event->recruit) {
            if(!empty($event->recruit->email)) {
                Mail::to($event->recruit->email)->send(
                    new RevisionRequestedMail($event->recruit, $event->note ?? '')
                );
            } else {
                $this->logger->error("[SendRevisionRequestedMail] Cannot send email to candidate, email address is empty. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
            }
        } else {
            $this->logger->error("[SendRevisionRequestedMail] Cannot send email to candidate, null candidate given");
        }
    }
}
