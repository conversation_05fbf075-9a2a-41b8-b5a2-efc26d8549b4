<?php

namespace App\Listeners\Review\AO;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\Review\AO\ApprovedApplicationMail;
use Illuminate\Support\Facades\Mail;

class SendApprovedEmail implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        // Mail::to($event->recruit->email)->send(
        //     new ApprovedApplicationMail($event->recruit)
        // );
    }
}
