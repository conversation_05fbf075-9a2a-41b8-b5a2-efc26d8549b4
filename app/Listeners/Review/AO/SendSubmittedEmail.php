<?php

namespace App\Listeners\Review\AO;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\Review\AO\SubmittedApplicationToRecruitMail;
use App\Mail\Review\AO\SubmittedApplicationToRecruiterMail;
use App\Mail\Review\AO\SubmittedApplicationToAoMail;
use Illuminate\Support\Facades\Mail;
use App\Models\Agent;

class SendSubmittedEmail implements ShouldQueue
{
    private $logger;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger');
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $recruiter = $event->recruit->invitedBy;

        if($event->recruit) {
            if(!empty($event->recruit->email)) {
                Mail::to($event->recruit->email)->send(
                    new SubmittedApplicationToRecruitMail($event->recruit)
                );

                // if recruiter is not the AO, send email notification to the AO
                if($event->recruit->agency_owner_code != $recruiter->agent_code) {
                    if($agency_owner = Agent::where('AgentCode', $event->recruit->agency_owner_code)->first()){
                        Mail::to($agency_owner->AgentEmail)->send(
                            new SubmittedApplicationToAoMail($event->recruit, $agency_owner)
                        );
                    }
                }

                if($recruiter) {
                    Mail::to($recruiter->email)->send(
                        new SubmittedApplicationToRecruiterMail($event->recruit)
                    );
                } else {
                    $this->logger->error("[AO::SendSubmittedEmail] Cannot send submission notify email to recruiter, inviter doesn't exist. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
                }
            } else {
                $this->logger->error("[AO::SendSubmittedEmail] Cannot send email to candidate, email address is empty. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
            }
        } else {
            $this->logger->error("[AO::SendSubmittedEmail] Cannot send email to candidate, null candidate given");
        }
    }
}
