<?php

namespace App\Listeners\Review\HO;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\Review\AO\SubmittedApplicationUnlicensedToRecruitMail;
use App\Mail\Review\AO\SubmittedApplicationUnlicensedToRecruiterMail;
use Illuminate\Support\Facades\Mail;
use HQAccounts;
use App\Mail\Review\HO\SubmittedPrincipalApplication;

class SendSubmittedEmail implements ShouldQueue
{
    private $logger;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger');
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $recruiter = $event->recruit->invitedBy;

        // Get home office email addresses from environment variable
        $B2BhomeOfficeEmails = explode(',', env('B2B_HOME_OFFICE_EMAILS'));

        if($event->recruit) {

            if ($event->recruit->type == 'principal') {
                foreach ($B2BhomeOfficeEmails as $email) {
                    Mail::to(trim($email))->send(
                        new SubmittedPrincipalApplicationMail($event->recruit)
                    );
                }
            }

            if(!empty($event->recruit->email)) {
                if(!HQAccounts::isFullyLicensed($event->recruit)) {
                    Mail::to($event->recruit->email)->send(
                        new SubmittedApplicationUnlicensedToRecruitMail($event->recruit)
                    );
            
                    // Disabling per ONBST2-507
                    // if($recruiter) {
                    //     if(!empty($recruiter->email)) {
                    //         Mail::to($recruiter->email)->send(
                    //             new SubmittedApplicationUnlicensedToRecruiterMail($event->recruit)
                    //         );
                    //     } else {
                    //         $this->logger->error("[HO::SendSubmittedEmail] Cannot send unlicensed notify email to recruiter, email address is empty." . 
                    //                  " Recruit: " . $event->recruit->name . " #" . $event->recruit->id . 
                    //                  " Inviter: " . $recruiter->name . " #" . $recruiter->id);
                    //     }
                    // } else {
                    //     $this->logger->error("[HO::SendSubmittedEmail] Cannot send unlicensed notify email to recruiter, inviter doesn't exist. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
                    // }
                }
            } else {
                $this->logger->error("[HO::SendSubmittedEmail] Cannot send email to candidate, email address is empty. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
            }
        } else {
            $this->logger->error("[HO::SendSubmittedEmail] Cannot send email to candidate, null candidate given");
        }
    }
}
