<?php

namespace App\Listeners\Review\HO;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Mail\Review\HO\ApprovedApplicationToRecruitMail;
use App\Mail\Review\HO\ApprovedApplicationToRecruitQ2BMail;
use App\Mail\Review\HO\ApprovedApplicationToRecruiterMail;
use Illuminate\Support\Facades\Mail;

class SendApprovedEmail implements ShouldQueue
{
    private $logger;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        $this->logger = app('logger');
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $recruiter = $event->recruit->invitedBy;

        if($event->recruit) {
            if(!empty($event->recruit->email)) {
                // Use different email class based on tenant
                if(config('app.tenant') == 'Q2B') {
                    Mail::to($event->recruit->email)->send(
                        new ApprovedApplicationToRecruitQ2BMail($event->recruit)
                    );
                } else {
                    Mail::to($event->recruit->email)->send(
                        new ApprovedApplicationToRecruitMail($event->recruit)
                    );
                }
            } else {
                $this->logger->error("[SendApprovedEmail] Cannot send email to candidate, email address is empty. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
            }
        } else {
            $this->logger->error("[SendApprovedEmail] Cannot send email to candidate, null candidate given");
        }

        if($recruiter) {
            if(!empty($recruiter->email)) {
                Mail::to($recruiter->email)->send(
                    new ApprovedApplicationToRecruiterMail($event->recruit)
                );
            } else { 
                $this->logger->error("[SendApprovedEmail] Cannot send email to recruiter, email address is empty." . 
                                     " Candidate: " . $event->recruit->name . " #" . $event->recruit->id . 
                                     " Inviter: " . $recruiter->name . " #" . $recruiter->id);
            }
        } else {
            $this->logger->error("[SendApprovedEmail] Cannot send email to recruiter, inviter doesn't exist. Candidate: " . $event->recruit->name . " #" . $event->recruit->id);
        }
    }
}
