<?php

namespace App\Listeners\UserInvite;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Artisan;

class SendBlockedEmail implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $email = $event->email;
        $name = $event->name;
        $recruit_name = $event->recruit_name;

        $emailSecond = $event->emailSecond;
        $nameSecond = $event->nameSecond;

        if(!empty($emailSecond) && !empty($nameSecond))
            Artisan::call("onboarding:send-invite-blocked-email '{$emailSecond}' '{$nameSecond}' '{$recruit_name}'");
        Artisan::call("onboarding:send-invite-blocked-notification '{$email}' '{$name}' '{$recruit_name}'");
    }
}
