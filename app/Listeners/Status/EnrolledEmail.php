<?php

namespace App\Listeners\Status;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Artisan;

class EnrolledEmail implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        $email = $event->ao->AgentEmail;
        $name = $event->ao->AgentName;
        $recruit_name = $event->recruit->name;
        $recruiter_name = $event->recruit->invitedBy->name;

        Artisan::call("onboarding:send-enrolled-email-to-ao '{$email}' '{$name}' '{$recruit_name}' '{$recruiter_name}'");
    }
}
