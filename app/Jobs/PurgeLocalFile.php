<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\UserEntry;
use App\Models\FormField;
use AzureStore;
use File;
use Log;

class PurgeLocalFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userEntryId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userEntryId)
    {
        $this->userEntryId = $userEntryId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!$entry = UserEntry::find($this->userEntryId)) {
            Log::error("PURGE FILE JOB FAILED: Unable to find User Entry with id of {$this->userEntryId}");
            return false;
        }

        $fullLocalPath = public_path("/uploads/{$entry->user_id}/{$entry->form_field_id}.{$entry->value}");
        
        if (!File::exists($fullLocalPath)) {
            Log::error("PURGE FILE JOB FAILED: File at {$fullLocalPath} does not exist.");
            return false;
        }

        File::delete($fullLocalPath);

        return true;
    }
}
