<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Models\UserRemoteAccount;
use App\Models\FormLookup;
use App\Models\FormField;
use FormBuilder;
use HQAccounts;
use AgentSync;
use DB;

class CreateAgentSyncRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {    
        if (!$user = User::find($this->userId)) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Could not find user with id of {$this->userId}");
            return false;
        }
        
        if (!$remoteAccount = UserRemoteAccount::where('user_id', $this->userId)->first()) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Could not find remote account information for user with id of {$this->userId}");
            return false;
        }

        $parentAccountId = false;
        $accountId = false;

        $agentCode = $remoteAccount->agent_code;
        $optId = $remoteAccount->opt_id;

        if (is_null($agentCode)) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: User with id of {$this->userId} does not have an AgentCode set.");
            return false;
        }

        if (is_null($optId)) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: User with id of {$this->userId} does not have an OptId set.");
            return false;
        }

        // lookup 130 user that this recruit is underneath
        if ($uplineAgentCode = $this->findTopLevelAgentCode($agentCode)) {
            if ($uplineAccountId = $this->findAccountIdByAgentCode($uplineAgentCode)) {
                $parentAccountId = $uplineAccountId;
            }
        }

        // check if we correctly retrieved an upline account id -- if not, find and use base symmetry one
        if (!$parentAccountId) {
            if (!$parentAccountId = $this->fetchSymmetryAccountId()) {
                $parentAccountId = '';
            }
        }

        // check if this user is filing as a business
        $isBusiness = (FormBuilder::fetchUserEntry($user, FormLookup::FIELD_IS_BUSINESS_BOOL) == FormField::SELECT_BUSINESS_TYPE_BUSINESS);

        if ($isBusiness) {
            if ($accountObject = $this->createAccount($user, $agentCode, $optId, $parentAccountId)) {
                $accountId = $accountObject['id'];
            }
        }

        // if account object is not made, set Account to parent account determined above
        if (!$accountId) {
            $accountId = $parentAccountId;
        }

        // build user data
        if (!$contactObject = $this->createContact($user, $agentCode, $optId, $accountId)) {
            Log::error("AGENT SYNC CREATE CONTACT ERROR: Unable to make contact object for user with id of {$this->userId}.");
            return false;
        }

        return true;
    }

    /**
     * Build Contact object.
     *
     * @return array
     */
    public function createContact($user, $agentCode, $optId, $accountId)
    { 
        $contactObject = AgentSync::create('Contact')->addFields([
            'AccountId'         => $accountId,
            'AgentCode'         => $agentCode,
            'BirthDate'         => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BIRTHDATE),
            'FirstName'         => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_FIRST_NAME),
            'LastName'          => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_LAST_NAME),
            'NPN'               => FormBuilder::fetchUserEntry($user, FormLookup::LICENSE_NPN, true),
            'SSN'               => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_SSN, true),
            'Phone'             => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_PHONE, true),
            'Email'             => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_WORK_EMAIL),
            'AgentAddress1'     => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STREET1),
            'AgentCity'         => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_CITY),
            'AgentState'        => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_STATE),
            'AgentZip'          => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_ZIP),
            'OPTID'             => $optId,
            'QuilityStatus'     => 'Active',
            'CommissionLevel'   => $user->contract_level ?? '',
        ])->save();

        if ($contactObject == false || (is_array($contactObject) && !isset($contactObject['id']))) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Unable to create contact in AgentSync for user {$user->id}");
            return false;
        }

        return $contactObject;
    }    

    /**
     * Build Account object.
     *
     * @return array
     */
    public function createAccount($user, $agentCode, $optId, $parentAccountId)
    { 
        $accountObject = AgentSync::create('Account')->addFields([
            'Name'              => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_NAME), 
            'NPN'               => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_NPN, true),
            'Phone'             => FormBuilder::fetchUserEntry($user, FormLookup::FIELD_BUSINESS_PHONE),
            'PrincipalAgentNPN' => FormBuilder::fetchUserEntry($user, FormLookup::LICENSE_NPN, true),
            'AgentCode'         => $agentCode,
            'ParentId'          => $parentAccountId,
            'OPTID'             => $optId,
            'QuilityStatus'     => 'Active',
        ])->save();

        if ($accountObject == false || (is_array($accountObject) && !isset($accountObject['id']))) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Unable to create account in AgentSync for user {$user->id}");
            return false;
        }

        return $accountObject;
    }

    /**
     * Attempt to find 130 in external database to parent to current record.
     *
     * @return string|bool
     */
    public function findTopLevelAgentCode($agentCode)
    { 
        $results = DB::connection('sqlsrv')->select(
            DB::raw("SELECT * FROM dbo.AgentHierarchy WHERE DownlineAgentCode = :agentCode AND UplineAgentCode <> 'SFG0000001' ORDER BY DownlineLevel DESC"),
            array('agentCode' => $agentCode)
        );

        if (!$results || !is_array($results) || (is_array($results) && sizeof($results) == 0)) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Unable to fetch top level agent code from remote DB with agent code of {$agentCode}");
            return false;
        }

        // NOTE: If there is more than one record, go down one record from the top
        // typically the top level is one of the "Five OGs" and we want to parent
        // to a 130 if at all possible. If there is only one record, use that instead.

        $agentRecord = (sizeof($results) > 1 ? $results[1] : $results[0]);

        if (!property_exists($agentRecord, 'UplineAgentCode')) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Agent heirarchy record does not contain UplineAgentCode for {$agentCode}");
            return false;
        }

        return $agentRecord->UplineAgentCode;
    }

    /**
     * Attempt to find 130 in external database to parent to current record.
     *
     * @return string|bool
     */
    public function findAccountIdByAgentCode($agentCode)
    { 
        if (!$account = AgentSync::find('Account')->whereAgentCode($agentCode)->get()) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Could not find Account in AgentSync for AgentCode {$agentCode}");
            return false;
        }

        if (!isset($account['Id'])) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Account in AgentSync for AgentCode {$agentCode} did not return Id field.");
            return false;
        }

        return $account['Id'];
    }

    /**
     * Search for base Symmetry account and return it's id.
     *
     * @return string|bool
     */
    public function fetchSymmetryAccountId()
    { 
        if (!$result = AgentSync::query("SELECT Id FROM Account WHERE name = 'Symmetry' LIMIT 1")) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: Could not find Account in AgentSync by the name 'Symmetry'");
            return false;
        }

        if (!isset($result['records']) || sizeof($result['records']) < 1) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: AgentSync Symmetry search did not return records.");
            return false;
        }

        if (!isset($result['records'][0]['Id'])) {
            Log::error("AGENT SYNC CREATE RECORD ERROR: AgentSync Symmetry search did not return records with an Id field.");
            return false;
        }

        return $result['records'][0]['Id'];
    }    
}
