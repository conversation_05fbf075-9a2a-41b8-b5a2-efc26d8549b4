<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\UserEntry;
use App\Models\UserUpload;
use App\Models\UserRemoteAccount;
use App\Models\FormField;
use HQAccounts;
use AzureStore;
use Log;

class UploadRemoteFile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userUploadId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userUploadId)
    {
        $this->userUploadId = $userUploadId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!$upload = UserUpload::where('id', $this->userUploadId)->where('hq_uploaded', 0)->first()) {
            Log::error("AGENT FILE UPLOAD JOB FAILED: Unable to find local only User Upload with id of {$this->userUploadId}");
            return false;
        }

        $fullLocalPath = $upload->fullLocalPath();

        if (!$userRemoteAccount = UserRemoteAccount::where('user_id', $upload->user_id)->whereNotNull('agent_code')->first()) {
            Log::error("AGENT FILE UPLOAD JOB FAILED: Unable to find User Remote Account with agent code for id of {$upload->user_id}");
            return false;
        }

        $agentCode = $userRemoteAccount->agent_code;

        if (!$response = HQAccounts::sendFileReqest($fullLocalPath, $agentCode)) {
            Log::error("FILE DOWNLOAD JOB FAILED: Unable to copy User Entry with id of {$this->userEntryId} to local storage");
            return false;
        }

        if ($response->success) {
            $upload->hq_uploaded = 1;
            $upload->save();
        }

        return true;
    }
}
