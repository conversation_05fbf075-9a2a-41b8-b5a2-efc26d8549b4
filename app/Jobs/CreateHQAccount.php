<?php

namespace App\Jobs;

use App\Mail\admin\CreateHqLiteAccountEmail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Defuse\Crypto\Crypto;
use App\Models\User;
use App\Models\UserRemoteAccount;
use App\Models\UserUpload;
use Defuse\Crypto\Key;
use Exception;
use HQAccounts;
use Log;
use Mail;
use Str;
use App\Models\UserInvite;

class CreateHQAccount implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $userId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $user = User::find($this->userId);
        if (!$user) {
            Log::error("HQ ACCOUNT CREATION ERROR: Could not find user with id of {$this->userId}");
            return false;
        }

        // Get the user's invite to access carrier selections
        $userInvite = UserInvite::where('id', $user->user_invite_id)->latest()->first();
        if (!$userInvite) {
            Log::error("HQ ACCOUNT CREATION ERROR: Could not find invite for user with id of {$this->userId}");
            return false;
        }

        if (!$response = HQAccounts::sendAgentRequest($this->userId)) {
            Log::error("HQ ACCOUNT CREATION ERROR: Could not create HQ account for user with id of {$this->userId}");
            return false;
        }

        $remoteAccountDetails = [
            'type'          => (HQAccounts::isFullyLicensed($this->userId) ? UserRemoteAccount::TYPE_LICENSED : UserRemoteAccount::TYPE_UNLICENSED),
            'status'        => UserRemoteAccount::STATUS_FAILED,
            'agent_code'    => null,
            'opt_id'        => null,
            'opt_pass'      => null,
            'hq_metadata'   => null,
        ];


        $cryptKey = Key::loadFromAsciiSafeString(config('system.securekey', null));
        if (isset($response['data'])) {
            $remoteAccountDetails['status'] = UserRemoteAccount::STATUS_CREATED;
            $remoteAccountDetails['agent_code'] = $response['data']['agent']['AgentCode'];
            $remoteAccountDetails['opt_id'] = $response['data']['agent']['OptID'];
            $remoteAccountDetails['opt_pass'] = Crypto::encrypt($response['data']['OptPassword'], $cryptKey);
            $remoteAccountDetails['hq_metadata'] = json_encode($response['data']['agent']);
        }
        if (!HQAccounts::hasExistingLiteAccount($this->userId)) {
            $remoteAccountDetails['user_id']= $this->userId;
            $remoteAccountDetails['id']= Str::uuid()->toString();
            $remoteUser = UserRemoteAccount::create($remoteAccountDetails);
            //Mail::to($response['data']['Email'])->send(new CreateHqLiteAccountEmail($response['data']['Email'], $response['data']['FirstName'].' '.$response['data']['LastName'] , Crypto::Decrypt($remoteUser->opt_pass, $cryptKey)));
        }else{
            UserRemoteAccount::where('user_id', $this->userId)->update($remoteAccountDetails);
        }

        $user->agent_code = $response['data']['agent']['AgentCode'];
        $user->save();

        // Store carrier selections as AgentConfig
        if (config('app.tenant') == 'Q2B') {
            try {
                $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $user->agent_code . '/config';
                $accessToken = HQAccounts::generateAccessToken(true);

                if($userInvite->carrier) {
                    $response = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Authorization' => "Bearer {$accessToken}",
                            'Accept' => 'application/json',
                        ],
                        'json' => [
                            'config_type' => 'Agent Config',
                            'config' => [
                                'ConfigName' => 'selected_carriers',
                                'ConfigValue' => $userInvite->carrier,
                                'DataType' => 'string'
                            ]
                        ]
                    ]);

                    if ($response->getStatusCode() !== 200) {
                        Log::error("Failed to store carrier selections for agent {$user->agent_code}");
                    }
                }
                
                // Send commissions config if available
                if (!empty($userInvite->commissions)) {
                    $commissionsResponse = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Authorization' => "Bearer {$accessToken}",
                            'Accept' => 'application/json',
                        ],
                        'json' => [
                            'config_type' => 'Agent Config',
                            'config' => [
                                'ConfigName' => 'commissions',
                                'ConfigValue' => $userInvite->commissions,
                                'DataType' => 'string'
                            ]
                        ]
                    ]);

                    if ($commissionsResponse->getStatusCode() !== 200) {
                        Log::error("Failed to store commissions for agent {$user->agent_code}");
                    }
                }

                // Send service level config if available
                if ($user->type == 'b2b-principal' && !empty($userInvite->service_level)) {
                    $serviceLevelResponse = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Authorization' => "Bearer {$accessToken}",
                            'Accept' => 'application/json',
                        ],
                        'json' => [
                            'config_type' => 'Agent Config',
                            'config' => [
                                'ConfigName' => 'ServiceLevel',
                                'ConfigValue' => $userInvite->service_level,
                                'DataType' => 'string'
                            ]
                        ]
                    ]);

                    if ($serviceLevelResponse->getStatusCode() !== 200) {
                        Log::error("Failed to store service level for agent {$user->agent_code}");
                    }
                }

                // Send accepted agency agreement timestamp config
                $agreementResponse = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                    'headers' => [
                        'Content-Type' => 'application/json',
                        'Authorization' => "Bearer {$accessToken}",
                        'Accept' => 'application/json',
                    ],
                    'json' => [
                        'config_type' => 'User Config',
                        'config' => [
                            'ConfigName' => 'AcceptedAgencyAgreement V1.',
                            'ConfigValue' => now()->toDateTimeString(),
                            'DataType' => 'string'
                        ]
                    ]
                ]);
                if ($agreementResponse->getStatusCode() !== 200) {
                    Log::error("Failed to store accepted agency agreement timestamp for agent {$user->agent_code}");
                }


            } catch (\Exception $e) {
                Log::error("Error storing carrier selections: " . $e->getMessage());
            }
        }

        // Send language preference config if available
        if (config('app.tenant') == 'Q2A' && $user->agent_code) {
            try {
                $requestPath = config('quilityaccounts.apiPath') . 'trusted/agents/' . $user->agent_code . '/config';
                $accessToken = HQAccounts::generateAccessToken(true);
                
                // Get language preferences from form lookups
                $languages = [];
                $languageSlugs = [
                    'spanish-language',
                    'russian-language',
                    'french-language',
                    'mandarin-language',
                    'hindi-language'
                ];
                
                foreach ($languageSlugs as $slug) {
                    $lookupField = \App\Models\FormLookup::where('field_slug', $slug)->first();
                    if ($lookupField) {
                        $userEntry = \App\Models\UserEntry::where('form_field_id', $lookupField->form_field_id)
                            ->where('user_id', $user->id)
                            ->where('value', 'checked')
                            ->first();
                            
                        if ($userEntry) {
                            // Add the language to our array, formatting as Language-speaking
                            $language = ucfirst(str_replace('-language', '', $slug));
                            $languages[] = $language . "-speaking";
                        }
                    }
                }
                
                // If we have language preferences, send them to HQ
                if (!empty($languages)) {
                    $languagesString = implode(',', $languages);
                    
                    $response = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                        'headers' => [
                            'Content-Type' => 'application/json',
                            'Authorization' => "Bearer {$accessToken}",
                            'Accept' => 'application/json',
                        ],
                        'json' => [
                            'config_type' => 'Agent Config',
                            'config' => [
                                'ConfigName' => 'LeadLanguagePreferences',
                                'ConfigValue' => $languagesString,
                                'DataType' => 'string'
                            ]
                        ]
                    ]);

                    if ($response->getStatusCode() !== 200) {
                        Log::error("Failed to store language preferences for agent {$user->agent_code}");
                    }
                }
                
                // Check for "Other" language preference
                $otherLookup = \App\Models\FormLookup::where('field_slug', 'other-language')->first();
                if ($otherLookup) {
                    $otherChecked = \App\Models\UserEntry::where('form_field_id', $otherLookup->form_field_id)
                        ->where('user_id', $user->id)
                        ->where('value', 'checked')
                        ->first();
                        
                    if ($otherChecked) {
                        // Look for the specified other language
                        $otherSpecifiedLookup = \App\Models\FormLookup::where('field_slug', 'other-language-specified')->first();
                        if ($otherSpecifiedLookup) {
                            $otherValue = \App\Models\UserEntry::where('form_field_id', $otherSpecifiedLookup->form_field_id)
                                ->where('user_id', $user->id)
                                ->first();
                                
                            if ($otherValue && !empty($otherValue->value)) {
                                // If the field is secure, decrypt it
                                if ($otherSpecifiedLookup->formField->isSecure()) {
                                    $otherLanguage = Crypto::decrypt($otherValue->value, $cryptKey);
                                } else {
                                    $otherLanguage = $otherValue->value;
                                }
                                
                                // Send the other language to HQ
                                $otherResponse = (new \GuzzleHttp\Client)->request('POST', $requestPath, [
                                    'headers' => [
                                        'Content-Type' => 'application/json',
                                        'Authorization' => "Bearer {$accessToken}",
                                        'Accept' => 'application/json',
                                    ],
                                    'json' => [
                                        'config_type' => 'Agent Config',
                                        'config' => [
                                            'ConfigName' => 'LeadLanguageOther',
                                            'ConfigValue' => $otherLanguage,
                                            'DataType' => 'string'
                                        ]
                                    ]
                                ]);

                                if ($otherResponse->getStatusCode() !== 200) {
                                    Log::error("Failed to store other language preference for agent {$user->agent_code}");
                                }
                            }
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error("Error storing language preferences: " . $e->getMessage());
            }
        }

        //Now that we have our agent_code in the user_remote_accounts table, we can upload any files attached to this user
        $file_count = HQAccounts::uploadFilesToHq($this->userId);
        Log::info("Final file upload count on new account:".$file_count);

        /*
        Remove for now.  This is WIP
        if (!$uploads = UserUpload::where('user_id', $this->userId)->where('hq_uploaded', 0)->get()) {
            Log::error("HQ ACCOUNT CREATION ERROR: No file uploads found for user with id of {$this->userId}");
            // NOTE: no return false here as it could be the second pass of an account getting upserted
            return true;
        }

        if (sizeof($uploads) > 0) {
            foreach ($uploads as $upload) {
                UploadRemoteFile::dispatch($upload->id);
            }
        }
        */
    
        return true;
    }
}
