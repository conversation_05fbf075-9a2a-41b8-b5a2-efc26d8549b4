<template>
  <v-flex>
    <v-checkbox
      outlined
      :name="name"
      :label="label"
      :messages="comment"
      hide-details="auto"
      v-model="checked"
      @change="update()"
    ></v-checkbox>
    <span class="text-h6 error--text" v-for="(err, index) in errors" :key="index">{{ err }}</span>
  </v-flex>
</template>

<script>
export default {
  props: {
    label: String,
    name: String,
    comment: [String, Array],
    value: [Boolean, String, Number],
    errors: [String, Array],
  },
  data() {
    return {
      checked: 0,
    }
  },
  mounted() {
    if (this.value === 'checked' || this.value == 1) this.checked = 1
    else this.checked = 0
  },
  methods: {
    update() {
      this.$emit('input', this.checked ? 'checked' : '')
    },
    setValue(newVal) {
      if (newVal === 'checked' || this.value == 1) this.checked = 1
      else this.checked = 0
    },
  },
  watch: {
    value(newVal, oldVal) {
      this.setValue(newVal)
    }
  }
}
</script>

<style lang="sass" scope></style>
