<template>
  <v-dialog v-if="shown" v-model="showDialog" width="500">
    <v-card>
      <!-- <v-card-title class="text-h5 lighten-2"> Confirm </v-card-title> -->

      <v-card-text>
        <div v-html="content"></div>
      </v-card-text>

      <v-divider></v-divider>

      <v-card-actions>
        <v-btn color="primary" text @click="showDialog = false">Continue Editing</v-btn>
        <v-spacer></v-spacer>
        <v-btn v-if="value != 'confirmed'" color="primary" text @click="update('confirmed')">
            Save Progress
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
/**
 * This is the beginning of implementing a confirmation dialog to display when the field type is 'confirm'
 * It was started for the licensing page but the database updates required are a bit complex and I just need to get something
 * done. So, we'll come back to this later.
 */
export default {
  props: {
    content: String,
    value: [Boolean, String, Number],
    shown: Boolean
  },
  data() {
    return {
      showDialog: true,
    }
  },
  mounted() {
    console.log("confirmation dialog", this.value)
  },
  methods: {
    update(val) {
      this.$emit('input', val)
    },
  },
}
</script>

<style lang="sass" scope></style>
