<template>
  <v-flex>
    <span v-if="!noLabel" class="text-h5">{{ label || '&nbsp;' }}</span>
    <v-autocomplete
      v-if="autocomplete"
      :id="id"
      :items="items"
      :placeholder="placeholder"
      v-model="selected"
      :messages="comment"
      :error-messages="errors"
      hide-details="auto"
      :item-text="itemText ? itemText : 'label'"
      :item-value="itemValue ? itemValue : 'value'"
      :outlined="!solo"
      :solo="solo"
      :disabled="disabled"
      :hint="hint"
      :persistent-hint="persistentHint"
    ></v-autocomplete>
    <v-select
      v-else
      :id="id"
      :items="items"
      :placeholder="placeholder"
      v-model="selected"
      :messages="comment"
      :error-messages="errors"
      hide-details="auto"
      :item-text="itemText ? itemText : 'label'"
      :item-value="itemValue ? itemValue : 'value'"
      :outlined="!solo"
      :solo="solo"
      :disabled="disabled"
      :multiple="multiple"
      :hint="hint"
      :persistent-hint="persistentHint"
    ></v-select>
  </v-flex>
</template>

<script>
export default {
  props: {
    id: String,
    noLabel: Boolean,
    label: String,
    itemText: String,
    itemValue: String,
    placeholder: String,
    comment: [String, Array],
    errors: [String, Array],
    items: Array,
    default: [String, Number],
    solo: Boolean,
    disabled: Boolean,
    autocomplete: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
      },
    hint: String,
    persistentHint: Boolean,
  },
  data() {
    return {
      selected: this.default || '',
    }
  },
  watch: {
    default(val) {
      this.selected = val
    },

    selected(val) {
      if(typeof val == 'object' && this.itemValue)
        this.$emit('input', val[this.itemValue])
      else
        this.$emit('input', val)
    },
  },
  methods: {
    setValue(newVal) {
      if(this.itemValue) {
        this.selected = this.items.find(it => it[this.itemValue] == newVal)
      }
      else
        this.selected = newVal
    },
  },
}
</script>

<style lang="sass" scope></style>
