<template>
  <div>
    <editor
      model-events="change keydown blur focus paste"
      :id="idtiny"
      :api-key="api_key"
      v-model="textVal"
      :initial-value="textVal"
      :init="{
        branding: false,
        height: 300,
        menubar: false,
        plugins: [
          'advlist autolink lists link image charmap print preview anchor',
          'searchreplace visualblocks code fullscreen',
          'insertdatetime media table paste code help wordcount',
        ],
        toolbar: toolbar,
      }"
    />
  </div>
</template>

<script>
import Editor from '@tinymce/tinymce-vue'
export default {
  components: {
    editor: Editor,
  },
  props: {
    initVal: String,
    idtiny: String,
    toolbar: String,
  },

  data() {
    return {
      api_key: process.env.MIX_TINYMCE_APIKEY,
    }
  },

  computed: {
    textVal: {
      get() {
        return this.initVal
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
}
</script>
<style lang="sass" scope></style>
