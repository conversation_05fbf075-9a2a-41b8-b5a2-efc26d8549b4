<template>
  <v-dialog ref="dialog" v-model="modal" :return-value.sync="date" persistent width="290px">
    <template v-slot:activator="{ on, attrs }">
      <v-container class="pa-0">
        <span v-if="!noLabel" class="text-h5">{{ label || '&nbsp;' }}</span>
        <v-text-field
          v-model="date"
          :messages="comment"
          hide-details="auto"
          :prepend-icon="noIcon ? '' : 'mdi-calendar'"
          :error-messages="errors"
          readonly
          outlined
          v-bind="attrs"
          v-on="on"
        ></v-text-field>
      </v-container>
    </template>
    <v-date-picker v-model="date" scrollable>
      <v-spacer></v-spacer>
      <v-btn text color="primary" @click="modal = false"> Cancel </v-btn>
      <v-btn text color="primary" @click="$refs.dialog.save(date)"> OK </v-btn>
    </v-date-picker>
  </v-dialog>
</template>

<script>
export default {
  props: {
    noLabel: Boolean,
    noIcon: Boolean,
    label: String,
    comment: [String, Array],
    errors: [String, Array],
    default: String,
  },
  data() {
    return {
      date: this.default || null,
      menu: false,
      modal: false,
      menu2: false,
    }
  },
  watch: {
    date(val) {
      this.$emit('input', val)
    },
  },
  methods: {
    setValue(newVal) {
      this.value = newVal
    },
  },
}
</script>

<style lang="sass" scope></style>
