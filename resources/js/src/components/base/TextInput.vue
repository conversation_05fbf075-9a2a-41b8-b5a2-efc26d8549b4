<template>
  <v-flex>
    <span v-if="!noLabel" class="text-h5" v-html="label || '&nbsp;'" role="input-label"></span>
    <v-text-field
      outlined
      :name="name"
      ref="inputItem"
      :placeholder="placeholder"
      :append-outer-icon="checked ? 'mdi-check' : ''"
      :type="getInputType"
      :maxlength="maxlength"
      autocomplete="on"
      :messages="comment"
      :error-messages="errors"
      @keyup="inputTrigger"
      hide-details="auto"
      v-model="value"
      v-mask="mask"
      :disabled="disabled"
    >
    <v-icon
      v-if="append_inner_icon"
      slot="append"
      :color="append_inner_icon_color"
    >
      {{ append_inner_icon }}
    </v-icon>
  </v-text-field>
  </v-flex>
</template>

<script>
import { FORM_FIELD_TYPES } from '@/utils/const'

export default {
  props: {
    noLabel: <PERSON><PERSON><PERSON>,
    label: String,
    name: String,
    placeholder: String,
    comment: [String, Array],
    errors: [String, Array],
    checked: <PERSON><PERSON><PERSON>,
    password: <PERSON><PERSON><PERSON>,
    extra: String,
    default: String,
    disabled: <PERSON><PERSON><PERSON>,
    append_inner_icon: null,
    append_inner_icon_color: null,
    maxlength: {
      type: [Number, String],
      default: ''
    },
  },
  data() {
    return {
      value: '' || (this.default === process.env.MIX_SYSTEM_SECURE_PLACEHOLDER ? this.placeholder : this.default),
    }
  },
  updated() {
    this.$root.$on('check_validation', () => {
      this.checkFocus()
    })
  },
  computed: {
    mask() {
      if (this.extra === FORM_FIELD_TYPES.TYPE_SSN) {
        return 'SSS-SS-SSSS'
      }
      if (this.extra === FORM_FIELD_TYPES.TYPE_PHONE) {
        return '(###) ###-####'
      }
      if (this.extra === FORM_FIELD_TYPES.TYPE_ROUTING_NUMBER) {
        return 'SSSSSSSSS'
      }
      if (this.extra === FORM_FIELD_TYPES.TYPE_EIN) {
        return '##-#######'
      }
      return ''
    },
    getInputType() {
      if (this.password) {
        return 'password'
      }
      if (this.extra === FORM_FIELD_TYPES.TYPE_EMAIL) {
        return 'email'
      }
      return 'text'
    }
  },
  watch: {
    value(val) {
      this.$emit('input', val)
    },
  },

  methods: {
    setValue(newVal) {
      this.value = newVal
    },
    inputTrigger(e) {
      if (e.key !== 'Enter' && (this.value === process.env.MIX_SYSTEM_SECURE_PLACEHOLDER || this?.value?.substring(0, 2) === '**')) {
        this.value = ''
      }
    },
    checkFocus() {
      if (this.errors.length > 0) {
        this.$refs.inputItem.focus()
      }
    },
  },
}
</script>

<style lang="sass" scope>
.optional-field-input-label
  color: #888
  font-size: .75rem
  letter-spacing: .025em
  text-transform: uppercase
</style>
