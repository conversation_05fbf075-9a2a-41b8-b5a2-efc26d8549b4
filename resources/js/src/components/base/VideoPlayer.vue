<template>
  <v-flex>
    <span v-if="!noLabel" class="text-h5">{{ label || '&nbsp;' }}</span>
    <LazyYoutube v-if="isYoutube" :src="url" />
    <LazyVimeo v-else-if="isVimeo" :src="url" />
    <vue-core-video-player v-else :src="url"></vue-core-video-player>
  </v-flex>
</template>

<script>
export default {
  props: {
    noLabel: Boolean,
    label: String,
    url: String,
  },
  data() {
    return {}
  },
  computed: {
    isYoutube() {
      return this.url.includes('youtube.com')
    },
    isVimeo() {
      return this.url.includes('vimeo.com')
    },
  },
}
</script>

<style lang="sass" scope></style>
