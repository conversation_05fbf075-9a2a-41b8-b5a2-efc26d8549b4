<template>
  <v-tooltip left>
    <template v-slot:activator="{ on, attrs }">
      <span 
        x-small 
        v-bind="attrs"
        v-on="on"
        class="license-status-indicator"
        style="white-space:nowrap;"
      >
        {{licenseStatus}}
      </span>
    </template>
    <span>{{tooltipText}}</span>
  </v-tooltip>
  
</template>

<script>
export default {
  props: {
    item: [Array, Object]
  },
  data() {
    return {}
  },
  computed: {
    licenseStatus() {
      let status = ''
      if(this.item.is_returning)
        status = 'R, '
      if(this.item.transferring == 'YES')
        status += 'T, '
      if(this.item.advanced_markets)
        status += 'AM, '
      if(this.item.is_fully_licensed)
        return status+"L"
      if(this.item.has_passed_exam)
        return status+"UP"
      return status+"U"
    },
    tooltipText() {
      if(this.item.is_fully_licensed)
        return "Licensed"
      if(this.item.has_passed_exam)
        return "Passed exam, waiting for license."
      return "Unlicensed"
    }
  },
}
</script>

<style scoped>
.license-status-indicator {
  cursor: pointer;
}
</style>