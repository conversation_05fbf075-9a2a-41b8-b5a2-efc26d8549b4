<template>
  <v-flex class="signature-container d-flex flex-column align-center">
    <VueSignaturePad
      class="signature-canvas grey lighten-2 rounded"
      width="100%"
      height="200px"
      ref="signaturePad"
      :options="{ onBegin, onEnd }"
    />
    <span class="text-h6 error--text" v-for="(err, index) in errors" :key="index">{{ err }}</span>
    <div>
      <v-btn class="ma-2" @click="clear">Clear</v-btn>
      <v-btn class="ma-2" @click="undo">Undo</v-btn>
    </div>
  </v-flex>
</template>

<script>
export default {
  props: {
    default: String,
    errors: [String, Array],
  },
  mounted() {
    this.default && this.$refs.signaturePad.fromDataURL(this.default)
  },
  methods: {
    undo() {
      this.$refs.signaturePad.undoSignature()
    },
    clear() {
      this.$refs.signaturePad.clearSignature()
      this.$emit('input', '')
    },
    onBegin() {},
    onEnd() {
      const { data } = this.$refs.signaturePad.saveSignature()
      if (data) {
        this.$emit('input', data)
      }
    },
  },
}
</script>

<style lang="sass" scope></style>
