<template>
  <v-flex>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      loading-text="Loading... Please wait"
      :sort-by="sortBy"
      hide-default-footer
      :items-per-page="itemsPerPage"
      @page-count="pageCount = $event"
      :page.sync="page"
    >
      <template v-slot:no-data v-if="!$slots['no-data']">
        <h4>No data</h4>
      </template>
      <slot v-for="(_, name) in $slots" :name="name" :slot="name" />
      <template v-for="(_, name) in $scopedSlots" :slot="name" slot-scope="slotData"
        ><slot :name="name" v-bind="slotData"
      /></template>
    </v-data-table>
    <div class="d-flex flex-row align-center justify-end pt-2">
      <v-pagination v-model="page" :length="pageCount" :total-visible="7"></v-pagination>
      <select-box
        noLabel
        solo
        v-model="itemsPerPage"
        :default="itemsPerPage"
        :items="itemsSelect"
        class="select-items-range"
      ></select-box>
    </div>
  </v-flex>
</template>

<script>
import SelectBox from './SelectBox.vue'
export default {
  components: { SelectBox },
  props: {
    headers: Array,
    items: Array,
    loading: Boolean,
    sortBy: String,
  },
  created() {},
  data: () => ({
    page: 1,
    pageCount: 0,
    itemsPerPage: 10,
    itemsSelect: [
      { label: '10', value: 10 },
      { label: '25', value: 25 },
      { label: '50', value: 50 },
      { label: 'All', value: -1 },
    ],
  }),
  methods: {},
}
</script>

<style lang="sass" scope>
.select-items-range
  max-width: 10rem
</style>
