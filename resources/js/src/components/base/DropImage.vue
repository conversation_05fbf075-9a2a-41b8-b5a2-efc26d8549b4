<template>
  <v-flex>
    <vue-dropzone
      :id="`customdropzone_${id}`"
      :destroyDropzone="true"
      class="customdropzone"
      :options="dropzoneOptions"
      ref="myVueDropzone"
      :useCustomSlot="true"
      @vdropzone-sending="sendingEvent"
      @vdropzone-error="handleError"
      @vdropzone-success="handleSuccess"
      @vdropzone-max-files-exceeded="handleMaxFiles"
      @vdropzone-drop="handleDrop"
      @vdropzone-removed-file="handleRemove"
    >
      <div class="dropzone-custom-content">
        <h3 class="dropzone-custom-title">Drag and drop to upload content!</h3>
        <div class="subtitle grey--text">...or click to select a file from your computer</div>
      </div>
    </vue-dropzone>
    <div class="d-flex flex-column">
      <span v-for="(error, index) in errors" :key="index" class="text-red">{{ error }}</span>
      <span v-for="(error, index) in formErrors" :key="index" class="text-red">{{ error }}</span>
    </div>
    <div class="d-flex flex-column" v-if="exceeded">
      <span class="text-red">{{ exceeded }}</span>
    </div>

    <!-- Add modal for image preview -->
    <v-dialog 
      v-model="showPreview" 
      max-width="90vw" 
      overlay-opacity="0"
      overlay-color="transparent"
    >
      <v-card dark>
        <!-- <v-card-title class="headline">
          <span>{{ previewFile ? previewFile.name : '' }}</span>
          <v-spacer></v-spacer>
          <v-btn icon @click="showPreview = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title> -->
        <v-card-text>
          <img :src="previewFile ? previewFile.url : ''" class="preview-image" v-if="previewFile && isImageFile(previewFile.name)">
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-flex>
</template>

<script>
import vue2Dropzone from 'vue2-dropzone'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'

import { FILE_UPLOAD_FILEBIN, FILE_UPLOAD_CHUNKSIZE } from '@/utils/const'
import { getXSRFToken } from '@/utils/helper'
import http from '@/plugins/http'
import { mapActions } from 'vuex'

export default {
  props: {
    id: String,
    noLabel: Boolean,
    label: String,
    comment: [String, Array],
    maxFiles: Number,
    formErrors: [String, Array],
  },
  components: {
    vueDropzone: vue2Dropzone,
  },

  mounted() {
    this.getFile()
    // Add click handler for preview
    if (this.$refs.myVueDropzone) {
      this.$refs.myVueDropzone.dropzone.on('addedfile', file => {
        file.previewElement.addEventListener('click', () => this.handlePreview(file));
      });
    }
  },
  data() {
    return {
      dropzoneOptions: {
        paramName: 'file',
        url: process.env.MIX_API_FILE_UPLOAD_URL || FILE_UPLOAD_FILEBIN,
        thumbnailWidth: 200,
        addRemoveLinks: true,
        chunking: true,
        chunkSize: process.env.MIX_FILE_UPLOAD_CHUNKSIZE || FILE_UPLOAD_CHUNKSIZE,
        withCredentials: true,
        addRemoveLinks: true,
        maxFilesize: 40,
        maxFiles: this.maxFiles ?? null,
      },
      errors: [],
      exceeded: "",
      isContentLoaded: false,
      permanentRemove: true,
      showPreview: false,
      previewFile: null,
    }
  },
  methods: {
    ...mapActions({
      getImage: 'application/getImage',
      deleteImage: 'application/deleteImage',
      setSnackbar: 'snackbar/set',
    }),
    sendingEvent(file, xhr, formData) {
      const token = getXSRFToken()
      xhr.setRequestHeader('X-XSRF-TOKEN', token)
      formData.append('fieldId', this.$props.id)
    },
    handleSuccess(file, response) {
      this.isContentLoaded = true;
      this.checkIfExceeded();

      // Add URL to file object for preview
      if (response && response.url) {
        file.url = response.url;
      } else {
        // Construct URL from file name if response doesn't include it
        const userId = this.$store.state.auth.user.id; // Assuming you have access to user ID
        file.url = `${process.env.MIX_APP_URL}/storage/uploads/files/${userId}/${file.name}`;
      }

      const isHeicFile = (name) => {
        const ext = (name || '').split(".").slice(-1)[0];
        return ['heic', 'heif'].includes(ext);
      }
      
      if(this.$refs.myVueDropzone && isHeicFile(file.name)) {
        this.permanentRemove = false;
        this.$refs.myVueDropzone.removeAllFiles()
        this.getFile();
      }

      this.setSnackbar({
        status: true,
        text: 'File saved!',
        color: 'success',
      })
    },
    handleError(file, message, xhr){
      try {
        if(message.errors){
          const { file } = message.errors;
          this.errors = file;
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleRemove(file, error, xhr) {
      if(!this.permanentRemove)
        return;

      setTimeout(() => {
        // Delay 500ms to prevent from file-remove following object destory
        // Remove file only when it is shown
        if(this.$refs.myVueDropzone && this.$refs.myVueDropzone.dropzone) {
          this.isContentLoaded = false;
          this.deleteFile();
        }
      }, 500)
    },
    handleMaxFiles(file){
      this.checkIfExceeded();
    },
    handleDrop(){
      this.checkIfExceeded();
    },
    checkIfExceeded(){
      if(this.maxFiles){
        if(this.$refs.myVueDropzone.dropzone.files.length > this.maxFiles){
          this.exceeded = `You have exceeded the number of files that can be uploaded. The maximum number of files is: ${this.maxFiles}`;
          return false;
        }else{
          this.exceeded = '';
        }
      }
      if(this.$refs.myVueDropzone.dropzone.files.length === 0){
        this.errors = [];
      }
      return true;
    },
    async deleteFile(){
      await this.deleteImage(this.$props.id)
        .then(response => {
          this.checkIfExceeded();
          // this.$emit('input', 'file-activity')
        })
        .catch(err => console.error(err))
    },

    async getFile() {
      await this.getImage(this.$props.id)
        .then(response => {
          if (response.data === 400) {
            console.log(response.data)
          } else if(!response.data[0] || !response.data[0].name || !response.data[0].url) {
            console.log(response.data)
          } else {
            for (const e of response.data) {
              const file = { size: 123, name: e.name, url: e.url }
              const url = `${e.url}`
              this.$refs.myVueDropzone.manuallyAddFile(file, url)
            }
            this.isContentLoaded = true
          }
        })
        .catch(err => console.error("error",err))
        .finally(() => this.permanentRemove = true)
    },
    template: function () {
      return `
            <div class="dz-preview dz-file-preview" style="cursor: pointer;">
                <div class="dz-image" data-dz-preview>
                    <div data-dz-thumbnail-bg></div>
                </div>
                <div class="dz-details">
                    <div class="dz-size"><span data-dz-size></span></div>
                    <div class="dz-filename">
                        <span data-dz-name class="clickable-filename">
                            <v-icon small class="preview-icon">mdi-eye</v-icon>
                            <span class="filename-text"></span>
                        </span>
                    </div>
                </div>
                <div class="dz-progress"><span class="dz-upload" data-dz-uploadprogress></span></div>
                <div class="dz-error-message"><span data-dz-errormessage></span></div>
                <div class="dz-success-mark"><i class="fa fa-check"></i></div>
                <div class="dz-error-mark"><i class="fa fa-close"></i></div>
            </div>
        `
    },
    thumbnail: function (file, dataUrl) {
      var ref
      if (file.previewElement) {
        file.previewElement.classList.remove('dz-file-preview')
        ref = file.previewElement.querySelectorAll('[data-dz-thumbnail-bg]')
        ref &&
          ref.map(thumbnailElement => {
            thumbnailElement.alt = file.name
            thumbnailElement.style.backgroundImage = 'url("' + dataUrl + '")'
          })
        return setTimeout(
          (function () {
            return function () {
              return file.previewElement.classList.add('dz-image-preview')
            }
          })(this),
          1,
        )
      }
    },
    contentLoaded() {
      return this.isContentLoaded;
    },
    isImageFile(filename) {
      if (!filename) return false;
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      const ext = filename.split('.').pop().toLowerCase();
      return imageExtensions.includes(ext);
    },
    handlePreview(file) {
      if (this.isImageFile(file.name)) {
        this.previewFile = file;
        this.showPreview = true;
      } else {
        // Open non-image files in new tab
        window.open(file.url, '_blank');
      }
    },
  },
}
</script>

<style lang="sass" scope>
.customdropzone
  position: relative

  .dropzone-custom-content
    position: absolute
    top: 50%
    left: 50%
    transform: translate(-50%, -50%)
    text-align: center

  .dropzone-custom-title
    margin-top: 0
    color: #00b782

  .dz-image
    img
      border-radius: 100%
      object-fit: contain

  .dz-preview.dz-complete.dz-image-preview
    border: 1px solid #ccc

.text-red
  color: #ff5252
  margin-top: 5px

.preview-image
  width: 100%
  height: auto
  max-height: 80vh
  object-fit: contain

.dz-preview
  cursor: pointer
  transition: transform 0.2s ease

  &:hover
    transform: scale(1.05)

.clickable-filename
  color: #1976d2
  text-decoration: underline
  display: flex
  align-items: center
  gap: 4px
  
  &:hover
    color: #1565c0

.preview-icon
  font-size: 14px
  margin-right: 4px

.v-dialog
  background: transparent !important
  box-shadow: none

  .v-card
    background: rgba(0, 0, 0, 0.7) !important
</style>

