<template>
  <v-flex class="heading d-flex flex-column align-center justify-center">
    <h2 class="text-h2 ma-0 mt-10 mb-2">{{ title }}</h2>
    <hr class="secondary" />
    <span v-html="subline" class="ma-3 text-center"></span>
  </v-flex>
</template>

<script>
export default {
  props: {
    title: String,
    subline: String,
  },
  data() {
    return {}
  },
}
</script>

<style lang="sass" scope>
.heading
  hr
    width: 40px
</style>
