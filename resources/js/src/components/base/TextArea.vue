<template>
  <v-flex>
    <span v-if="!noLabel" class="text-h5">{{ label || '&nbsp;' }}</span>
    <p v-if="!!subline">{{ subline }}</p>
    <v-textarea
      outlined
      :name="name"
      :placeholder="placeholder"
      :append-outer-icon="checked ? 'mdi-check' : ''"
      :messages="comment"
      :error-messages="errors"
      hide-details="auto"
      v-model="value"
      :v-mask="mask"
      counter="1000"
      maxlength="1000"
    ></v-textarea>
  </v-flex>
</template>

<script>
export default {
  props: {
    noLabel: Boolean,
    label: String,
    subline: String,
    name: String,
    placeholder: String,
    comment: [String, Array],
    errors: [String, Array],
    checked: Boolean,
    mask: String, // (###) ####-###
    default: String,
  },
  data() {
    return {
      value: '' || this.default,
    }
  },
  watch: {
    value(val) {
      this.$emit('input', val)
    },
  },
  methods: {
    setValue(newVal) {
      this.value = newVal
    },
  },
}
</script>

<style lang="sass" scope></style>
