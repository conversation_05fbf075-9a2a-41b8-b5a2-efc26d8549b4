<template>
  <div class="popup-menu">
    <v-menu offset-y>
      <template v-slot:activator="{ on, attrs }">
        <v-btn text small>
          <v-icon large class="white--text ma-1" v-bind="attrs" v-on="on"> mdi-apps </v-icon>
        </v-btn>
      </template>
      <v-container>
        <v-row class="white darken-2 pa-2 popup-menu-box">
          <v-col cols="4" class="pa-1" v-for="(item, index) in menus" :key="index">
            <div
              :class="`d-flex flex-column ma-0 pa-2 pt-4 text-center white--text caption popup-menu-item ${item.color}`"
            >
              <v-icon large class="white--text ma-2">
                {{ item.icon }}
              </v-icon>
              {{ item.text }}
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-menu>
  </div>
</template>

<script>
import * as _ from 'lodash'

export default {
  props: {
    items: Array,
  },
  data() {
    return {
      showMenu: false,
      menus: [],
    }
  },
  watch: {
    items(newItems) {
      console.log(newItems)
      this.menus = newItems.map(item => {
        item.color = this.getRandomColor()
        return item
      })
    },
  },
  methods: {
    clicked(menuId) {
      this.$emit('menuClicked', menuId)
    },
    getRandomColor() {
      return _.sample(['red', 'purple', 'pink', 'indigo', 'cyan', 'teal', 'green'])
    },
  },
}
</script>

<style lang="sass" scope>
.popup-menu
  & .v-btn
    width: 3rem !important
    height: 3rem !important

.popup-menu-box
  width: 27rem !important

.popup-menu-item
  width: 8rem !important
  height: 8rem !important
  cursor: pointer

  &:hover
    filter: brightness(1.2)
</style>
