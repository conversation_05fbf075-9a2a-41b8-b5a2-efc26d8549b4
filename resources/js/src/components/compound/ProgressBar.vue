<template>
  <v-flex>
    <span class="grey--text pl-1" v-if="target_step"> Step {{ target_step }} of {{ total }} - {{ steps[target_step - 1].label }} </span>
    <span class="grey--text pl-1" v-else> Step {{ value }} of {{ total }} - {{ description }} </span>
    <div v-if="false &&tenant != 'Q2B'" class="progressbar-canvas mt-1 primary lighten-1 pa-1 white--text" id="">
      <div class="bar white--text overflow-hidden grey lighten-2" id="steps_bar">
        <div
          class="bar-fill d-flex align-center justify-center text-center white--text text-h5 primary text-center"
          :style="{ width: getWidth() }"
          v-if="isInteractive"
          @mousemove="getPosition"
          @mouseleave="mouseX = null"
          id="progress_bar"
        >
          {{ temp_label != '' ? temp_label : (Math.min((value / total) * 100, 99)).toFixed(0)+'%' }}
        </div>
        <div
          v-else
          class="bar-fill d-flex align-center justify-center text-center white--text text-h5 primary text-center"
          :style="{ width: getWidth() }"
          id="progress_bar"
        >
          {{ (Math.min((value / total) * 100, 99)).toFixed(0)+'%' }}
        </div>
      </div>
    </div>
    <div
      v-if="tenant != 'Q2B' && isInteractive"
      class="progressbar-canvas mt-1 step-indicators"
      @mousemove="getPosition"
      @mouseleave="mouseX = null;temp_label='';target_step=null;"
      :style="getBarStyle()"
      @click="goToStep()"
    >
    </div>
  </v-flex>
</template>

<script>
import { APPLICATION_TYPES } from '@/utils/const'
export default {
  props: {
    value: Number,
    total: Number,
    description: String,
    steps: Array,
    user_history_slugs: Array,
    type: String,
    isInteractive: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      current_step: 1,
      mouseX: null,
      temp_label: '',
      target_step: null,
      images: {
        step_indicator_bg: require('@resources/images/ProgressBarStepIndicators.png').default,
      },
    }
  },
  mounted() {
    // console.log("type:",this.type)
    // console.log("tenant:",this.tenant)
    console.log("steps:",this.steps)
  },
  computed: {
    tenant() {
      return process.env.MIX_APP_TENANT
    },
  },
  methods: {
    getBackgroundSize() {
      return window.innerWidth > 1400 ? 'cover' : 'contain'
    },
    getBarStyle() {
      return `margin-top: -46px !important; background-image:url('${this.images.step_indicator_bg}');background-size: ${this.getBackgroundSize()}`
    },
    getPosition(event) {
      let per = Math.ceil((event.offsetX / event.target.offsetWidth) * 100)
      let mouseX = this.roundToNearestStepPercent(per, this.steps.length)
      mouseX = mouseX > 0 ? mouseX : null

      let step = Math.ceil(mouseX / (100 / this.steps.length))
      if(!this.stepIsAvailable(step-1))
        return

      this.mouseX = mouseX

      if(step > 0) {
        this.temp_label = `${step}/${this.steps.length}`
        this.target_step = step
      } else {
        this.temp_label = "1/${this.steps.length}"
        this.target_step = 1
        this.mouseX = 100 / this.steps.length
      }
    },

    getWidth() {
      if(this.isInteractive && this.mouseX !== null)
        return this.mouseX+"%"
      return Math.min((this.value / this.total) * 100, 99)+"%"
    },
    getNearestStep() {

    },
    roundToNearestStepPercent(num, total_steps) {
      let nearest_step_percent_ceil = Math.ceil(num / total_steps) * total_steps
      let nearest_step_percent_floor = Math.floor(num / total_steps) * total_steps
      let diff1 = Math.abs(num - nearest_step_percent_ceil)
      let diff2 = Math.abs(num - nearest_step_percent_floor)

      if(diff1 <= diff2)
        return nearest_step_percent_ceil
      return nearest_step_percent_floor
    },
    stepIsAvailable(step_index) {
      //check status history to see if the user has been to this step before
      if(step_index < 0)
        return false
      let step_ident = this.steps[step_index].step_ident
      if(step_ident == 'license-information')
        step_ident = 'enrolled'
      return this.user_history_slugs.includes(step_ident)
    },
    goToStep() {
      if(this.stepIsAvailable(this.target_step-1))
        this.$emit('goToStep', this.target_step-1)
    }
  }

}
</script>

<style lang="sass" scope>

.progressbar-canvas
  height: 45px
  border-radius: 23px

  & .bar
    border-radius: 20px
    width: 100%
    height: 100%

    & .bar-fill
      height: 100%
      padding: 0 .75rem
      white-space: nowrap

.step-indicators
  position: relative
  cursor: pointer
  opacity:0
  background-position-y: center

.step-indicators:hover
  opacity: 0
</style>
