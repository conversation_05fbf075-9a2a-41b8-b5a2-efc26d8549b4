<template>
  <v-flex class="flip-card-item" @click="flipped = !flipped" v-bind:class="{ 'flip-card-item-flipped': flipped }">
    <div class="flip-card-front d-flex align-center justify-center text-center pa-5 card">
      <h3 class="primary--text text--darken-2 text-h3">{{ title }}</h3>
    </div>
    <div class="flip-card-back d-flex align-center justify-center text-center pa-5 card primary white--text">
      <div>
        <strong class="white--text">{{ title }}</strong>
        <div v-html="text"></div>
      </div>
    </div>
  </v-flex>
</template>

<script>
export default {
  props: {
    noLabel: Boolean,
    label: String,
    title: String,
    text: String,
  },
  data() {
    return {
      flipped: false,
    }
  },
  methods: {
    flipCard() {
      this.flipped = true
    },
    unFlipCard() {
      this.flipped = false
    },
  },
}
</script>

<style lang="sass" scope>
.flip-card-item
  position: relative
  transition: transform 1s
  transform-style: preserve-3d

  &.flip-card-item-flipped
    transform: rotateY(180deg)
    transition: transform 0.5s

.flip-card-front,
.flip-card-back
   backface-visibility: hidden

.flip-card-back
  left: 0
  transform: rotateY(180deg)
  position: absolute
  top: 0

.card, .card-flipped
  min-height: 200px
  border-radius: 0
  cursor: pointer
  -webkit-box-shadow: 9px 10px 22px -8px rgba(209,193,209,.5)
  -moz-box-shadow: 9px 10px 22px -8px rgba(209,193,209,.5)
  box-shadow: 9px 10px 22px -8px rgba(209,193,209,.5)
</style>
