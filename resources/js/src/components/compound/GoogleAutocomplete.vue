<template>
  <vue-google-autocomplete
    :id="id"
    class="google-autocomplete pa-3 w-100 gray--text lighten-1 form-control"
    placeholder="Type to search your location via google..."
    country="us"
    v-on:placechanged="getAddressData"
  >
  </vue-google-autocomplete>
</template>

<script>
import { trim } from 'lodash';
import VueGoogleAutocomplete from 'vue-google-autocomplete'

export default {
  components: {
    VueGoogleAutocomplete,
  },
  props: {
    id: String,
  },
  data() {
    return {}
  },
  watch: {},
  methods: {
    /**
     * When the location found
     * @param {Object} addressData Data of the found location
     * @param {Object} placeResultData PlaceResult object
     * @param {String} id Input container ID
     */
    getAddressData: function (addressData, placeResultData) {
      let newAddress = {
        street: '',
        addr2: '',
        city: '',
        state: '',
        zip: '',
        country: '',
      }

      let street_number = ''
      let street_name = ''

      if (placeResultData && Array.isArray(placeResultData.address_components)) {
        placeResultData.address_components.map(comp => {
          if (!comp.types || !comp.long_name || !Array.isArray(comp.types)) return

          if (comp.types.includes('street_number')) {
            street_number = trim(comp.long_name)
          } else if (comp.types.includes('route')) {
            street_name = trim(comp.long_name)
          } else if (comp.types.includes('locality')) {
            newAddress.city = trim(comp.long_name)
          } else if (comp.types.includes('administrative_area_level_2') && !newAddress.city) {
            newAddress.city = trim(comp.long_name)
          } else if (comp.types.includes('administrative_area_level_1')) {
            newAddress.state = trim(comp.long_name)
          } else if (comp.types.includes('postal_code')) {
            newAddress.zip = trim(comp.long_name)
          } else if (comp.types.includes('country')) {
            newAddress.country = trim(comp.short_name)
          }
        })

        newAddress.street = trim(street_number + ' ' + street_name)
      }

      this.$emit('input', newAddress)
    },
  },
}
</script>

<style lang="sass" scope>
.google-autocomplete
  font-size: 16px
  width: 100%
  border: 1px solid gray
  border-radius: 5px
</style>
