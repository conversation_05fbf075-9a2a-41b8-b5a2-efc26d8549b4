<template>
  <v-flex class="plain-accordion">
    <span v-if="!noLabel" class="text-h5">{{ label || '&nbsp;' }}</span>
    <v-expansion-panels accordion multiple>
      <v-expansion-panel v-for="(item, i) in items" :key="i">
        <v-expansion-panel-header class="primary white--text">
          <v-row align="center" justify="start">
            <span class="pa-2" v-text="item.title"></span>
          </v-row>
          <template v-slot:actions>
            <v-icon color="white">
              <!-- {{ $expand ? 'mdi-plus' : 'mdi-minus' }} -->
              $expand
            </v-icon>
          </template>
        </v-expansion-panel-header>
        <v-expansion-panel-content class="pa-1">
          <span v-html="item.content"></span>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-flex>
</template>

<script>
export default {
  props: {
    noLabel: <PERSON><PERSON><PERSON>,
    label: String,
    content: String,
  },
  data() {
    return {
      defaultItems: [
        {
          content: '<b>Project</b> Alpha',
          title: 'PA',
        },
        {
          content: 'Project Beta',
          title: 'PB',
        },
        {
          content: 'Project Lorem ipsum dolor sit amet',
          title: 'PL',
        },
      ],
    }
  },
  computed: {
    items() {
      try {
        return JSON.parse(this.content)
      } catch (e) {
        console.error('Parsing accordion items json error!')
      }
      return this.defaultItems
    },
  },
}
</script>

<style lang="sass" scope>
.plain-accordion
  .v-expansion-panel-content__wrap
    padding: 10px !important
    text-align: left

    .theme--light.v-list,
    .theme--light.v-sheet
      background: transparent

  .v-expansion-panel-header
    font-size: 15px !important
    font-weight: 600

    padding: 10px !important
    border-radius: 0 !important
    width: 100%

    @media(max-width: 500px)
      padding: 16px 12px

    .task-list-button
      @media(min-width: 600px)
        display: none

    .row
      margin: 0

      span
        max-width: 40vw
        overflow: hidden
        text-overflow: ellipsis
        white-space: nowrap

      .v-size--small
        opacity: 0

  .v-expansion-panel
    border-radius: 0 !important

    &:before
      box-shadow: none !important

    .v-list-item.v-list-item--link
      border-top: 1px solid rgba(0,0,0,.12)

    .v-expansion-panel-header--active .v-size--small
      opacity: 1

    @media(max-width: 600px)
      .v-btn__content i
        margin-right: 0 !important
</style>
