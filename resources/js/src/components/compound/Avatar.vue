<template>
  <v-flex class="d-flex flex-column align-center justify-center">
    <v-img v-if="img" :src="img" width="120" height="120" contain class="avatar-img" />
    <h3 class="text-h4 mt-3 avatar-name">{{ name || '' }}</h3>
    <h4 class="text-h5 mt-1">{{ position || '' }}</h4>
  </v-flex>
</template>

<script>
export default {
  props: {
    // name: '<PERSON><PERSON>',
    // position: 'Midfilder',
    // img: require("@resources/images/sfg-logo.png").default,
  },
  data() {
    return {}
  },
}
</script>

<style lang="sass" scope>
.avatar-img
  border-radius: 100%
  border: 2px solid black

.avatar-name
  font-weight: 700 !important
</style>
