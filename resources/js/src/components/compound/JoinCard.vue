<template>
  <v-card class="pa-3" max-width="300px">
    <v-card-title class="d-flex">
      <div class="d-flex pa-4 pl-10 primary darken-2 flex-grow-1 flex-column white--text">
        <h3 class="text-h3">{{ event.day }}</h3>
        <h5 class="text-h5">{{ event.name }}</h5>
      </div>
    </v-card-title>

    <v-card-text>
      <v-container>
        <div class="text-h5">
          {{ event.time }}
        </div>
        <div class="mt-5">
          <h2 class="text-h3">Join by Phone</h2>
          <a class="green--text text-decoration-underline">{{ event.phone }}</a>
        </div>
        <div class="mt-5">
          <h2 class="text-h3">Webinar ID</h2>
          <span>{{ event.webinarId }}</span>
        </div>
        <div class="mt-5">
          <h2 class="text-h3">Recurring Calendar Event Creation</h2>
          <a class="green--text text-decoration-underline">Google</a> or
          <a class="green--text text-decoration-underline">Outlook</a>
        </div>
      </v-container>
    </v-card-text>

    <v-card-actions class="d-flex justify-center">
      <v-btn color="primary white--text"> Join Link </v-btn>
      <v-btn color="text--black" text> Copy Link </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
export default {
  props: {},
  data() {
    return {
      event: {
        day: 'Wednesday',
        name: 'National Call',
        time: '1pm ET',
        phone: '(*************',
        webinarId: '571-684-240',
      },
    }
  },
}
</script>

<style lang="sass" scope></style>
