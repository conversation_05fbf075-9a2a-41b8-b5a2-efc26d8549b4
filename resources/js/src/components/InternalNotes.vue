<template>
  <v-card>
    <v-card-title>
      <div class="font-weight-bold">INTERNAL NOTES</div>
    </v-card-title>
    <v-card-text>
      <v-divider></v-divider>
      <v-container>
        <div v-for="note in notes" :key="note.timestamp" class="my-2" style="font-size:0.8em;">
          <div><span class="font-weight-bold">{{note.user_name}}</span> <span class="text--disabled">({{noteDate(note.timestamp)}}):</span></div>
          <span v-html="note.note.replace(/\n/g, '<br />')"></span>
        </div>
      </v-container>
      <v-container>
        <text-area class="mt-2" ref="new_note" label="Comment" v-model="new_note" />
        <v-btn primary @click="save()" :loading="saving">Save Note</v-btn>
      </v-container>
    </v-card-text>
  </v-card>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import * as moment from 'moment'
import TextArea from '@/components/base/TextArea'

export default {
  name: 'InternalNotes',
  components: {
    TextArea
  },
  props: {
    internal_notes: [String, Array],
    app_id: [String]
  },
  mounted: function() {
    this.notes = this.internal_notes
    console.log(this.internal_notes, this.app_id)
  },
  computed: {
    ...mapState({
      user: state => state.auth.user,
    }),
  },
  data: function() {
    return {
      new_note: "",
      notes: [],
      saving: false
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      saveInternalNote: 'application/saveInternalNote',
      getInternalNotes: 'application/getInternalNotes',
    }),
    noteDate: function(timestamp) {
      return moment.unix(timestamp).format("MMMM Do YYYY, h:mm a");
    },
    save: function() {
      this.saving = true
      this.saveInternalNote({ app_id: this.app_id, note: this.new_note }).then(response => {
        this.setSnackbar({
          status: true,
          text: 'Note saved',
          color: 'success',
        })
        this.$refs.new_note.setValue('')
        this.new_note = ""
        this.load()
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to save note.',
          color: 'warning',
        })
      })
      .finally(() => this.saving = false)

    },
    load: function() {
      this.getInternalNotes(this.app_id).then(response => {
        console.log(response.data)
        this.notes = response.data
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to retrieve notes.',
          color: 'warning',
        })
      })
      .finally(() => this.saving = false)
    }
  },
}
</script>
