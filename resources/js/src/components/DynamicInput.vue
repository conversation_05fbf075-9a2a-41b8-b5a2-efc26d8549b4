<template>
  <div class="mt-3">
    <v-btn color="primary" elevation="2" @click="addItem">New FAQ</v-btn>
    <v-row class="mt-2">
      <v-col><h1>Question</h1></v-col>
      <v-col><h1>Answer</h1></v-col>
    </v-row>
    <div v-for="item in faqs" :key="item.id">
      <v-row>
        <v-col>
          <v-text-field v-model="item.question" v-on:input="setData" outlined dense></v-text-field>
        </v-col>
        <v-col>
          <v-text-field v-model="item.answer" v-on:input="setData" outlined dense></v-text-field>
        </v-col>
      </v-row>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      id: 1,
      faqs: [{ question: '', answer: '' }],
    }
  },
  methods: {
    addItem() {
      console.log(this.faqs)
      this.id += 1
      this.faqs.push({
        question: '',
        answer: '',
      })
    },
    setData() {
      this.$emit('getData', this.faqs)
    },
  },
}
</script>

<style scoped></style>
