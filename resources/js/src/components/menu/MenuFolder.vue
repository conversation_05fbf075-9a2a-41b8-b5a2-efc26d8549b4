<template>
  <v-flex>
    <v-flex
      class="menu-entry d-flex flex-row align-center justify-space-between pl-6 pr-2 pt-2 pb-2 text-h5"
      @click="onClick"
    >
      <v-icon v-if="icon" left large color="primary">
        {{ icon }}
      </v-icon>
      <span class="flex-grow-1">
        {{ text }}
      </span>
      <v-icon v-if="subLinks">
        {{ !expanded ? 'mdi-chevron-down' : 'mdi-chevron-up' }}
      </v-icon>
    </v-flex>
    <v-expand-transition>
      <v-container :class="`pa-0 sub-items ${!subLinks && 'd-none'}`" v-show="expanded">
        <div v-for="item in subLinks" :key="item.link" :class="`pa-0 ma-0 ${item.hide && 'd-none'}`">
          <menu-link :text="item.text" :icon="item.icon" :link="item.link" sub> </menu-link>
        </div>
      </v-container>
    </v-expand-transition>
  </v-flex>
</template>

<script>
import MenuLink from './MenuLink.vue'

export default {
  props: {
    icon: String,
    text: String,
    link: String,
    subLinks: Array,
  },
  components: {
    MenuLink,
  },
  data() {
    return {
      expanded: false,
    }
  },
  methods: {
    onClick() {
      this.expanded = !this.expanded
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
  },
}
</script>

<style lang="sass" scope>
.menu-entry
  cursor: pointer
  border-bottom: 1px solid #E9EEE3

.sub-items
  transition: all .5s ease-in-out
</style>
