<template>
  <hr v-if="divider" />
  <v-flex
    v-else
    :class="`menu-entry d-flex flex-row align-center justify-space-between pr-2 pt-2 pb-2 ${
      sub ? 'primary lighten-5 pl-9' : 'pl-6'
    } ${selected && 'selected'}`"
    @click="onClick"
  >
    <v-icon v-if="icon" left :large="!sub" color="primary">
      {{ icon }}
    </v-icon>
    <span :class="`flex-grow-1 ${sub ? 'text-h6' : 'text-h5'}`">
      {{ text }}
    </span>
  </v-flex>
</template>

<script>
export default {
  props: {
    icon: String,
    text: String,
    link: String,
    sub: Boolean,
    divider: Boolean,
  },
  data() {
    return {
      selected: null,
    }
  },
  created() {
    this.selected = this.link === this.$route.name
  },
  watch: {
    '$route.name'() {
      this.selected = this.link === this.$route.name
    },
  },
  methods: {
    onClick() {
      if (this.link) this.goTo(this.link)
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
  },
}
</script>

<style lang="sass" scope>
.menu-entry
  cursor: pointer
  border-bottom: 1px solid #E9EEE3

  &:hover, &.selected
    color: #56C3F2
</style>
