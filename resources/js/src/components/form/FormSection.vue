<template>
  <div v-if="showSection">
    <v-row>
      <v-col cols="12" key="label" class="s-form-section-label">
        <h3 class="text-h3" v-html="data.label"></h3>
        <p v-if="!!data.subline" class="text-h4" v-html="data.subline"></p>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6">
        <v-alert 
          v-if="note && note.content" 
          color="red lighten-2" 
          border="top"
          class="mt-2 mb-0"
          dark
        >
          
          <v-row align="center">
            <v-col class="grow">
              {{ note.content }}
            </v-col>
            <v-col class="shrink">
              <v-btn v-if="!note.fixed" :loading="resolving" @click="resolveNote(note, 1)">Mark as Fixed</v-btn>
              <v-btn v-else :loading="resolving" @click="resolveNote(note, 0)">Undo</v-btn>
            </v-col>
          </v-row>
        </v-alert>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" key="autofill" class="s-form-autofill" v-if="toRenderGoogleAutocomplete">
        <google-autocomplete :id="sectionId" @input="onGoogleAddressChange" />
      </v-col>
      <v-col cols="12" :sm="getFieldSize(field.width)" v-for="field in data.fields" v-bind:key="field.id">
        <form-field
          :ref="field.alias_autofill || `field-${field.id}`"
          :data="field"
          :options="options[field.id] || []"
          :index="index"
          :showValidationErrors="showValidationErrors"
          @input="onFieldValueChange"
          :shown="shown"
        >
        </form-field>
      </v-col>
    </v-row>
  </div>
</template>
<script>
import FormField from '@/components/form/FormField'
import GoogleAutocomplete from '@/components/compound/GoogleAutocomplete'
import { FORM_SECTION_FILL_MODES, FORM_FIELD_SIZES } from '@/utils/const'
import { mapActions } from 'vuex'

export default {
  name: 'SFormSection',
  components: {
    FormField,
    GoogleAutocomplete,
  },
  props: {
    data: Object,
    shown: Boolean,
    options: Object,
    index: Number,
    showValidationErrors: Boolean,
    pageStep: String,
    note: Object,
    hasNotes: Number
  },
  data() {
    return {
      resolving: false
    }
  },
  computed: {
    toRenderGoogleAutocomplete() {
      return this.data.fill_mode === FORM_SECTION_FILL_MODES.FILLMODE_GOOGLE_AUTOCOMPLETE
    },
    formFieldSizes() {
      return FORM_FIELD_SIZES
    },
    sectionId() {
      return `${this.index}_` + (this.data.label || '').toLowerCase().replace(' ', '-')
    },
    showSection() {
      // returning true now. We ran into an issue where if the candidate leaves a field blank, 
      // the HO can't see the field to leave a note and that field isn't then visible to the candidate to add an answer
      return true
      if(!this.hasNotes)
        return true
      if(this.hasNotes && this?.note?.content)
        return true
      return false
    }
  },
  methods: {
    ...mapActions({
      resolveApplicationReviewNote: 'review/resolveNote',
      setSnackbar: 'snackbar/set',
    }),
    resolveNote(item, fixed) {
      this.resolving = true
      let g = this
      this.resolveApplicationReviewNote({ appId: item.id, fixed })
        .then(() => {
          g.resolving = false
          item.fixed = fixed
          g.setSnackbar({
            status: true,
            text: 'Resolving note success!',
            color: 'success',
          })
        })
        .catch(() => {
          g.resolving = false
          g.setSnackbar({
            status: true,
            text: 'Resolving note failed!',
            color: 'warning',
          })
        })
    },
    getFieldSize(sizeValue) {
      if (sizeValue === FORM_FIELD_SIZES.WIDTH_HALF) return 6
      if (sizeValue === FORM_FIELD_SIZES.WIDTH_THIRD) return 4
      if (sizeValue === FORM_FIELD_SIZES.WIDTH_FOURTH) return 3

      return 12
    },
    async onFieldValueChange(payload) {

      this.$emit('input', { ...payload, index: this.index })

    },
    onGoogleAddressChange(newAddress) {
      if (newAddress && newAddress.country && newAddress.street && newAddress.state) {
        Object.keys(newAddress).map(alias => {
          if (this.$refs[alias] && this.$refs[alias][0]) this.$refs[alias][0].setValue(newAddress[alias])
        })
      }
    },
    setAddressHistoryTo(id, value) {
      const refKey = `field-${id}`
      if (this.$refs[refKey] && this.$refs[refKey][0]) this.$refs[refKey][0].setValue(value)
    },
    hasErrors() {
      let ret = false

      if (!this.shown) {
        return false
      }

      Object.keys(this.$refs).every(refField => {
        const errsField = this.$refs[refField][0]?.errors
        ret ||= errsField && errsField.length > 0
        return !ret
      })

      return ret
    },
  },
}
</script>

<style lang="sass" scope>
.s-form-section-label
  padding-bottom: 0
</style>
