<template>
  <v-container>
    <h2 class="text-h2">{{ data.label }}</h2>
    <p v-if="!!data.subline" class="text-h4">{{ data.subline }}</p>
    <hr />
    <v-container
      v-for="(section, index) in data.sections"
      :key="`${index}-${getKeyConstraint(section)}`"
      :style="{ display: `${canShowSection(section) ? 'flex' : 'none'}` }"
      class="pt-7 flex-column"
    >
      <form-section
        v-for="repeatIndex in getRepeatRange(section)"
        :key="repeatIndex"
        :data="section"
        :pageStep="data.step_ident"
        :options="options"
        :index="repeatIndex"
        :showValidationErrors="showValidationErrors"
        :ref="`section_${index}_${repeatIndex}`"
        :id="`section_${section.id}_${repeatIndex}`"
        :shown="canShowSection(section)"
        class="s-form-section"
        @input="onFieldValueChange"
        :note="sectionNote(section.id)"
        :hasNotes="hasNotes"
      />
    </v-container>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
import FormSection from './FormSection'
import { FORM_FIELD_TYPES, FORM_FIELD_AUTOSAVE_DURATION, FORM_FIELD_CONDITION_ACTION_TYPES } from '@/utils/const'
import { compare, isValueIdentical, extractObjectToArray } from '@/utils/helper'
import { setWithExpiry, getWithExpiry } from '@/utils/storage'
import { FORM_OPTION_TTL } from '@/utils/const'

export default {
  components: {
    FormSection,
  },
  props: {
    data: Object,
    showValidationErrors: Boolean,
    notes: Object,
  },
  data() {
    return {
      values: {}, // current form values
      dumpValues: {}, // saved form values

      options: {}, // option array for selectbox within the page

      timerIdAutoSaver: null,
      timerIdAutoSaverPrev: null,

      edgeCaseValues: {}
    }
  },
  computed: {
    hasNotes() {
      return Object.keys(this.notes)?.length
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(newData) {
        this.edgeCaseValues = {};

        if (!newData || !newData.sections) {
          this.values = {}
          return
        }

        const arraySelectFieldIds = []

        // Check Values and Options
        newData.sections.map(section => {
          if (!section.fields || !Array.isArray(section.fields)) return
          this.values = section.fields.reduce((obj, field) => {
            if (field.type === FORM_FIELD_TYPES.TYPE_SELECT) {
              if (!arraySelectFieldIds.includes(field.id)) arraySelectFieldIds.push(field.id)
            }

            if (field.value)
              return {
                ...obj,
                [field.id]: extractObjectToArray(field.value),
              }

            return obj
          }, this.values)
        })

        this.dumpValues = JSON.parse(JSON.stringify(this.values))
        this.loadOptions(arraySelectFieldIds)

        // Collect Edge case Form Field Ids
        //   PREVIOUS_ADDRESS_FROM/PREVIOUS_ADDRESS_TO: Automatically fill-in "To" as Previous "From" value
        newData.sections.map((section, index) => {
          if (section.label !== "Please Add Your Previous Address") return
          if (!section.fields || !Array.isArray(section.fields)) return

          this.edgeCaseValues.ADDRESS_HISTORY = {
            FORM_SECTION_ID: section.id,
            FORM_SECTION_INDEX: index,
            FORM_FIELD_ID_FROM: null,
            FORM_FIELD_ID_TO  : null,
          }

          this.values = section.fields.map(field => {
            if(field.label === "From")
              this.edgeCaseValues.ADDRESS_HISTORY.FORM_FIELD_ID_FROM = field.id
            if(field.label === "To")
              this.edgeCaseValues.ADDRESS_HISTORY.FORM_FIELD_ID_TO   = field.id
          })
        })

        // Form Saver
        this.timerIdAutoSaver = setTimeout(this.saveValues, FORM_FIELD_AUTOSAVE_DURATION)
        this.$emit('setPreventNextEvent', this.values)
      },
    },
  },
  beforeDestroy() {
    this.saveValues(true)
    if (this.timerIdAutoSaver) clearTimeout(this.timerIdAutoSaver)
    if (this.timerIdAutoSaverPrev) clearTimeout(this.timerIdAutoSaverPrev)
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getOptions: 'form/getOptions',
      saveField: 'form/saveField',
      saveFieldBulk: 'form/saveFieldBulk',
    }),
    sectionNote(sectionId) {
      const resultKey = Object.keys(this.notes).find(key => this.notes[key].sectionId === sectionId);
      const note = resultKey ? this.notes[resultKey] : null;
      return note
    },
    loadOptions(arraySelectFieldIds) {
      const key = JSON.stringify(arraySelectFieldIds)
      const savedData = getWithExpiry(key)

      if (savedData) {
        this.options = savedData
      } else {
        this.getOptions({
          id: arraySelectFieldIds,
        })
          .then(data => {
            if (data) {
              this.options = data
              setWithExpiry(key, data, FORM_OPTION_TTL)
            }
          })
          .catch(() => {
            this.options = {}
            setWithExpiry(key, {}, FORM_OPTION_TTL)
          })
      }
    },
    getRepeatRange(section) {
      if (!section) return []

      const conditionsToRepeat =
        section.conditions &&
        section.conditions.filter(c => c.action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_REPEAT_DATE)
      if (conditionsToRepeat && conditionsToRepeat[0]) {
        // only consider first repeat condition
        let counter = 0
        let ret = [0]

        const { field, type, value } = conditionsToRepeat[0]
        const targetValueArray = Array.isArray(this.values[field]) ? this.values[field] : [this.values[field]]

        while (targetValueArray.length > counter) {
          if (compare(targetValueArray[counter], type, value)) ret.push(++counter)
          else break
        }

        return ret
      }

      // no conditon => default single section
      return [0]
    },
    canShowSection(section, deepStack = []) {
      if (!section) return false
      if(deepStack.includes(section.id)) {
        return true;
      }

      if (section.conditions) {
        const indexConditionRefused = section.conditions.findIndex(condition => {
          const { action, field, type, value } = condition
          const res = compare(
            Array.isArray(this.values[field]) ? this.values[field][0] : this.values[field],
            type,
            value,
          )

          const canShowParentSection = this.canShowSection(this.findSectionContainingField(field),
          [
            ...deepStack,
            section.id
          ]);

          return (
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !res) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !canShowParentSection) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_HIDE && res)
          )
        })

        return indexConditionRefused === -1
      }

      return true
    },
    findSectionContainingField(fieldId) {
      const index = this.data.sections.findIndex(s => {
        return s.fields.findIndex(f => f.id == fieldId) !== -1;
      });

      if(index === -1)
        return null;
      return this.data.sections[index];
    },
    getKeyConstraint(section) {
      let key = 'x'

      if (section.conditions) {
        section.conditions.map(condition => (key = key + condition.field))
      }

      return key
    },
    onFieldValueChange(payload) {
      if (!payload || !payload.id) return

      if(payload.value == 'file-activity') {
        this.$emit('reloadFormPage', this.values)
        return
      }


      const fieldVal = this.values[payload.id]
      const previousValue = Array.isArray(fieldVal) ? fieldVal[payload.index] : fieldVal

      if (previousValue === payload.value) return

      if (payload.index) {
        if (Array.isArray(fieldVal)) fieldVal[payload.index] = payload.value
        else {
          this.values[payload.id] = [fieldVal]
          this.values[payload.id][payload.index] = payload.value
        }
      } else {
        if (Array.isArray(fieldVal)) fieldVal[payload.index] = payload.value
        else this.values[payload.id] = payload.value
      }
      this.$forceUpdate()

      if(this.edgeCaseValues.ADDRESS_HISTORY && this.edgeCaseValues.ADDRESS_HISTORY.FORM_SECTION_ID && payload.id === this.edgeCaseValues.ADDRESS_HISTORY.FORM_FIELD_ID_FROM) {
        // should wait for next section to show up
        setTimeout(() => {
          const refSection = this.$refs[`section_${this.edgeCaseValues.ADDRESS_HISTORY.FORM_SECTION_INDEX}_${payload.index+1}`]

          if(refSection && refSection[0]) {
            refSection[0].setAddressHistoryTo(this.edgeCaseValues.ADDRESS_HISTORY.FORM_FIELD_ID_TO, payload.value);
          }
        }, 1000)
      }

      this.$emit('setPreventNextEvent', this.values)
    },
    saveValues(all_fields = false) {
      let fieldsToSave = []
      Object.keys(this.values).map(id => {
        if (all_fields || !isValueIdentical(this.dumpValues[id], this.values[id])) {
          fieldsToSave.push({ id, value: this.values[id] })
        }
      })

      const resumeSaving = () => {
        if (this.timerIdAutoSaverPrev) clearTimeout(this.timerIdAutoSaverPrev)
        this.timerIdAutoSaverPrev = this.timerIdAutoSaver
        this.timerIdAutoSaver = setTimeout(this.saveValues, FORM_FIELD_AUTOSAVE_DURATION)
      }

      if (!fieldsToSave.length) {
        resumeSaving()
        return
      }

      // === BULK SAVE MODE
      this.saveFieldBulk(fieldsToSave)
        .then(res => {
          const resultFieldIds = (res.data && Object.keys(res.data)) || []
          let successFieldsCount = 0

          fieldsToSave.map(fs => {
            if(!resultFieldIds.includes(fs.id) || !res.data[fs.id].success)
              return

            if (Array.isArray(fs.value)) this.dumpValues[fs.id] = extractObjectToArray(Object.assign({}, fs.value))
            else this.dumpValues[fs.id] = fs.value

            successFieldsCount++
          })

          if (successFieldsCount == fieldsToSave.length) {
            this.setSnackbar({
              text: 'Saving success',
              status: true,
              color: 'success',
            })
          } else {
            this.setSnackbar({
              text: 'Saving not completed',
              status: true,
              color: 'warning',
            })
            console.log(`Only saved ${successFieldsCount} out of ${fieldsToSave.length} fields.`)
          }
        })
        .catch(err => {
          console.error(err)
          this.setSnackbar({
            text: 'Saving error',
            status: true,
            color: 'warning',
          })
        })
        .finally(() => {
          resumeSaving()
        })


      // === SINGLE SAVE MODE
      /*
      let saveCounter = 0
      let saveSuccess = true

      fieldsToSave.map(f => {
        this.saveField(f)
          .then(() => {
            if (Array.isArray(f.value)) this.dumpValues[f.id] = extractObjectToArray(Object.assign({}, f.value))
            else this.dumpValues[f.id] = f.value
          })
          .catch(() => {
            saveSuccess = false
          })
          .finally(() => {
            saveCounter = saveCounter + 1

            if (saveCounter === fieldsToSave.length) {
              resumeSaving()

              if (saveSuccess) {
                this.setSnackbar({
                  text: 'Saving success',
                  status: true,
                  color: 'success',
                })
              } else {
                this.setSnackbar({
                  text: 'Saving error',
                  status: true,
                  color: 'warning',
                })
              }
            }
          })
      })
        */
    },
    hasErrors() {
      let ret = false

      Object.keys(this.$refs)
        .filter(ref => ref.startsWith('section_'))
        .every(refSection => {
          if (this.$refs[refSection][0] === undefined || this.$refs[refSection][0].length == 0) {
            setTimeout(function(){
              ret ||= this.$refs[refSection][0].hasErrors()
              return !ret
            }, 1000);
          }else{
            ret ||= this.$refs[refSection][0].hasErrors()
            return !ret
          }
        })

      return ret
    },
    goToSection(id, repeat = 0) {
      try {
        document.getElementById(id).scrollIntoView()
      } catch (e) {
        if (repeat < 5) setTimeout(() => this.goToSection(id, repeat + 1), 200)
      }
    },
  },
}
</script>

<style lang="sass" scope></style>
