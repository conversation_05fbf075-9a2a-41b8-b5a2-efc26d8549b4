<template>
  <div>
    <div v-if="data.type === formFieldTypes.TYPE_EMAIL_CONFIRMATION">
      <text-input
        ref="emailInput"
        :label="label"
        :noLabel="!data.label"
        :placeholder="data.placeholder"
        v-model="value"
        :extra="formFieldTypes.TYPE_EMAIL"
        :default="defaultValue"
        :errors="showValidationErrors ? errors : []"
      />
      <text-input
        ref="emailConfirmInput"
        :label="label + ' (Confirm)'"
        :noLabel="!data.label"
        :placeholder="'Confirm ' + data.placeholder"
        v-model="confirmValue"
        :extra="formFieldTypes.TYPE_EMAIL"
        :errors="showValidationErrors ? confirmErrors : []"
      />
    </div>
    <text-input
      v-else-if="
        data.type === formFieldTypes.TYPE_TEXT ||
        data.type === formFieldTypes.TYPE_EMAIL ||
        data.type === formFieldTypes.TYPE_SSN ||
        data.type === formFieldTypes.TYPE_EIN ||
        data.type === formFieldTypes.TYPE_PHONE ||
        data.type === formFieldTypes.TYPE_ROUTING_NUMBER"
      ref="item"
      :label="label"
      :noLabel="!data.label"
      :placeholder="data.placeholder"
      v-model="value"
      :extra="data.type"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />
    <text-input
      v-else-if="data.type === formFieldTypes.TYPE_PASSWORD"
      password
      ref="item"
      :label="label"
      :noLabel="!data.label"
      :placeholder="data.placeholder"
      v-model="value"
      :extra="data.type"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />

    <template v-else-if="data.type === formFieldTypes.TYPE_DATE">
      <label>{{ data.placeholder }} <span class="optional-field-input-label">(MM/DD/YYYY)</span></label>
      <div class="cont-datepicker">
        <date-pick
          type="date"
          value-type="date"
          v-mask="'##/##/####'"
          ref="item"
          v-model="value"
          :format="'YYYY-MM-DD'"
          :displayFormat="'MM/DD/YYYY'"
          :label="label"
          :noLabel="!data.label"
          placeholder="MM/DD/YYYY"
          :errors="showValidationErrors ? errors : []"
          :startWeekOnSunday="true"
          :style="showValidationErrors && errors.length > 0 ? 'border: 1px solid #ff5252; border-radius: 5px;' : ''"
          :isDateDisabled="isDateDisabled"
          :selectableYearRange="{from: new Date().getFullYear() - 100, to: new Date().getFullYear()}"
        />
      </div>
      <label class="text-error" role="alert" v-for="(error, index) in showValidationErrors ? errors : []" :key="index">
        {{ error }}
      </label>
    </template>

    <template v-else-if="data.type === formFieldTypes.TYPE_DOB">
      <label>{{ data.placeholder }} <span class="optional-field-input-label">(MM/DD/YYYY)</span></label>
      <div class="cont-datepicker">
        <date-pick
          type="date"
          value-type="date"
          v-mask="'##/##/####'"
          ref="item"
          v-model="value"
          :format="'YYYY-MM-DD'"
          :displayFormat="'MM/DD/YYYY'"
          :label="label"
          :noLabel="!data.label"
          placeholder="MM/DD/YYYY"
          :errors="showValidationErrors ? errors : []"
          :startWeekOnSunday="true"
          :style="showValidationErrors && errors.length > 0 ? 'border: 1px solid #ff5252; border-radius: 5px;' : ''"
          :isDateDisabled="isDateDisabled"
          :selectableYearRange="{from: new Date().getFullYear() - 100, to: new Date().getFullYear()}"
        />
      </div>
      <label class="text-error" role="alert" v-for="(error, index) in showValidationErrors ? errors : []" :key="index">
        {{ error }}
      </label>
    </template>

    <select-box
      v-else-if="data.type === formFieldTypes.TYPE_SELECT"
      ref="item"
      :id="data.id"
      :label="label"
      :noLabel="!data.label"
      :placeholder="data.placeholder"
      :items="options"
      v-model="value"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />

    <text-area
      v-else-if="data.type === formFieldTypes.TYPE_TEXTAREA"
      ref="item"
      :label="label"
      :noLabel="!data.label"
      :placeholder="data.placeholder"
      v-model="value"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />
    <p class="mb-0" v-else-if="data.type === formFieldTypes.TYPE_HTML" ref="item" v-html="data.label"></p>

    <drop-image
      :id="data.id"
      v-else-if="data.type === formFieldTypes.TYPE_UPLOAD"
      ref="item"
      :label="label"
      v-model="value"
      :max-files="5"
      :form-errors="showValidationErrors ? errors : []"
    />

    <check-box
      v-else-if="data.type === formFieldTypes.TYPE_CHECKBOX"
      ref="item"
      :label="label"
      v-model="value"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />

    <signature
      v-else-if="data.type === formFieldTypes.TYPE_SIGNATURE"
      :label="label"
      v-model="value"
      :extra="data.type"
      :default="defaultValue"
      :errors="showValidationErrors ? errors : []"
    />

    <confirmation-dialog
      v-else-if="data.type === formFieldTypes.TYPE_CONFIRM"
      :content="data.label"
      v-model="value"
      :shown="shown"
    />

    <!-- <video-player
      v-else
      url="https://player.vimeo.com/video/64654583"
    /> -->
    <!-- <flip-card
      v-else
      title="How Do I Approve Applications?"
      text="I don't know"
    /> -->
    <!-- <plain-accordion
      v-else
    /> -->
    <p v-else />
  </div>
</template>
<script>
import TextInput from '@/components/base/TextInput'
import TextArea from '@/components/base/TextArea'
import SelectBox from '@/components/base/SelectBox'
import DropImage from '@/components/base/DropImage'
import Signature from '@/components/base/Signature'
import CheckBox from '@/components/base/CheckBox'
import ConfirmationDialog from '@/components/base/ConfirmationDialog'

import DatePick from 'vue-date-pick'
import 'vue-date-pick/dist/vueDatePick.css'
import VueMask from 'v-mask'
import moment from 'moment'

import { FORM_FIELD_TYPES } from '@/utils/const'
import { extractObjectToArray, validateEmail, validateSSN, validatePhone } from '@/utils/helper'

export default {
  components: {
    TextInput,
    TextArea,
    SelectBox,
    DatePick,
    DropImage,
    CheckBox,
    Signature,
    ConfirmationDialog,
  },
  props: {
    data: Object,
    index: Number,
    options: Array,
    showValidationErrors: Boolean,
    shown: Boolean,
  },
  data() {
    return {
      value: '',
      confirmValue: '', // Add this for email confirmation
    }
  },
  mounted: function() {
    if(this.data.type === this.formFieldTypes.TYPE_CHECKBOX) {
      if(this.value == '' || this.value == 0) {
        this.$set(this, 'value', false)
      }
    }

    // Add this block to prevent paste in email confirmation field
    if(this.data.type === this.formFieldTypes.TYPE_EMAIL_CONFIRMATION) {
      this.$nextTick(() => {
        this.disablePasteOnEmailConfirm();
      });
    }
  },
  computed: {
    label() {
      if (!this.data.is_required) return `${this.data.label}    (optional) ` ;
      return this.data.label
    },
    defaultValue() {
      const val = extractObjectToArray(this.data.value)

      let ret = null;

      // non-repeat section => field
      if(!this.index)
        ret = Array.isArray(val) ? val[0] : val

      // repeat-section => field
      else if(Array.isArray(val) && this.index < val.length)
        ret = val[this.index]

      return ret == null ? '' : ret;
    },
    errors() {
      const errs = []

      const { is_required, type, max_length } = this.data

      if (type === FORM_FIELD_TYPES.TYPE_HTML) return []
      // if (type === FORM_FIELD_TYPES.CONFIRM) return []

      if (type === FORM_FIELD_TYPES.TYPE_UPLOAD && (is_required === 1 || is_required === "1")) {
        if (!this.$refs.item.contentLoaded()) {
          errs.push('This field is required.')
        }
        return errs
      }

      if ((is_required === 1 || is_required === "1") && !this.value) errs.push('This field is required.')

      if (
        [
          FORM_FIELD_TYPES.TYPE_TEXT,
          FORM_FIELD_TYPES.TYPE_EMAIL,
          FORM_FIELD_TYPES.TYPE_PASSWORD,
          FORM_FIELD_TYPES.TYPE_PHONE,
          FORM_FIELD_TYPES.TYPE_SSN,
          FORM_FIELD_TYPES.TYPE_TEXTAREA,
        ].includes(type) &&
        max_length &&
        this.value.length > max_length
      )
        errs.push(`The length must be ${max_length} characters or fewer.`)

      if (type === FORM_FIELD_TYPES.TYPE_EMAIL && !validateEmail(this.value))
        errs.push('The email address is not valid.')

      if (type === FORM_FIELD_TYPES.TYPE_SSN && !validateSSN(this.value)) errs.push('The ssn number is not valid.')

      if (type === FORM_FIELD_TYPES.TYPE_PHONE && !validatePhone(this.value))
        errs.push('The phone number is not valid.')

      if (type === FORM_FIELD_TYPES.TYPE_DATE && this.value && !moment(this.value, 'YYYY-MM-DD', true).isValid())
        errs.push('The date is not valid.')

      // Add age validation for DOB
      if (type === FORM_FIELD_TYPES.TYPE_DOB && this.value) {
        const birthDate = moment(this.value, 'YYYY-MM-DD');
        const age = moment().diff(birthDate, 'years');
        if (age < 18) {
          errs.push('You must be at least 18 years old.')
        }
        if (age > 100) {
          errs.push('You must be less than 100 years old.')
        }

        // Validate date format for DOB
        if (!this.isValidDOBFormat(this.value)) {
          errs.push('Date must be in MM/DD/YYYY format.')
        }
      }

      if (this.data.type === FORM_FIELD_TYPES.TYPE_EMAIL_CONFIRMATION) {
        if (this.value && !validateEmail(this.value)) {
          errs.push('The email address is not valid.')
        }
        if (this.value !== this.confirmValue) {
          errs.push('Email addresses do not match.')
        }
      }

      // Log form fields with error
      // if(errs.length > 0)
      //   console.log(this.data, errs);

      return errs
    },
    confirmErrors() {
      const errs = []
      if (this.data.type === FORM_FIELD_TYPES.TYPE_EMAIL_CONFIRMATION && this.value !== this.confirmValue) {
        errs.push('Email addresses do not match.')
      }
      return errs
    },
    formFieldTypes() {
      return FORM_FIELD_TYPES
    },
  },
  watch: {
    value(val) {
      if (this.data.type === FORM_FIELD_TYPES.TYPE_EMAIL_CONFIRMATION) {
        this.$emit('input', {
          id: this.data.id,
          value: val,
          isValid: val === this.confirmValue && validateEmail(val)
        })
      } else {
        this.$emit('input', { id: this.data.id, value: val })
      }
    },
    confirmValue(val) {
      if (this.data.type === FORM_FIELD_TYPES.TYPE_EMAIL_CONFIRMATION) {
        this.$emit('input', {
          id: this.data.id,
          value: this.value,
          isValid: this.value === val && validateEmail(this.value)
        })
      }
    },
  },
  created() {
    this.value = this.defaultValue
  },
  methods: {
    setValue(newVal) {
      if (this.data.type === FORM_FIELD_TYPES.TYPE_DATE)
        this.value = newVal
      else if (this.$refs.item) this.$refs.item.setValue(newVal)
    },
    isDateDisabled(date) {
      const currentDate = new Date();
      return date > currentDate;
    },
    disablePasteOnEmailConfirm() {
      if (this.$refs.emailConfirmInput) {
        // Find the actual input element inside the component
        const inputEl = this.$refs.emailConfirmInput.$el.querySelector('input');
        if (inputEl) {
          inputEl.addEventListener('paste', e => {
            e.preventDefault();
          });
        }
      }
    },
    isValidDOBFormat(dateStr) {
      // First check if it's a valid moment date
      if (!moment(dateStr, 'YYYY-MM-DD', true).isValid()) {
        return false;
      }

      // Then check if the input matches the expected format
      const datePattern = /^(0[1-9]|1[0-2])\/(0[1-9]|[12][0-9]|3[01])\/\d{4}$/;
      const formattedDate = moment(dateStr, 'YYYY-MM-DD').format('MM/DD/YYYY');
      return datePattern.test(formattedDate);
    },
  },
}
</script>

<style scope>
.vdpComponent {
  border: 1px solid #9e9e9e;
  border-radius: 5px;
}
.vdpComponent.vdpWithInput > input {
  border-radius: 5px;
  height: 50px;
  font-size: 16px;
  padding: 5px 10px;
  max-width: 260px;
  width: 100%;
}
.cont-datepicker {
  position: relative;
  width: 100%;
}
.text-error {
  display: block;
  margin-top: 8px;
  margin-bottom: 0px;
  margin-left: 12px;
  font-size: 12px;
  line-height: 12px;
  word-break: break-word;
  overflow-wrap: break-word;
  word-wrap: break-word;
  hyphens: auto;
  color: #ff5252;
}
</style>
