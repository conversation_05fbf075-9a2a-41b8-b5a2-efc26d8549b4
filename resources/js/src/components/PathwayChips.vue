<template>
  <div class="d-flex">
    <v-chip
      v-if="firstPart"
      class="mr-1"
      small
      :color="getFirstPartColor"
      text-color="white"
    >
      {{ firstPart }}
    </v-chip>
    <v-chip
      v-if="secondPart"
      small
      color="primary"
      text-color="white"
    >
      {{ secondPart }}
    </v-chip>
  </div>
</template>

<script>
export default {
  name: 'PathwayChips',
  props: {
    pathway: {
      type: String,
      default: ''
    }
  },
  computed: {
    parts() {
      return this.pathway ? this.pathway.split('/') : [];
    },
    firstPart() {
      return this.parts.length > 0 ? this.parts[0] : '';
    },
    secondPart() {
      return this.parts.length > 1 ? this.parts[1] : '';
    },
    getFirstPartColor() {
      switch(this.firstPart) {
        case 'U':
          return 'grey';
        case 'L':
          return 'success';
        case 'R':
          return 'warning';
        case 'T':
          return 'error';
        default:
          return 'grey';
      }
    }
  }
}
</script>
