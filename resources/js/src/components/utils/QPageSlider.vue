<template>
    <v-hover v-slot:default="{ hover }">
        <v-carousel :key="slider_key" :cycle="!hover" continuous :height="height" interval="800000" hide-delimiter-background show-arrows-on-hover class="cms">
            <v-carousel-item v-if="loading" class="text-center">
                <v-progress-linear indeterminate></v-progress-linear>
            </v-carousel-item>
            <v-carousel-item v-else v-for="(block, key) in blocks" :key="key" @click="maybeOpenLink(block)" :class="isClickable(block) ? 'clickable' : ''">
                <v-responsive :aspect-ratio="14/9" class="d-none d-md-flex justify-center">
                    <div :class="blockClasses(block, 'my-7')" style="height:100%;">
                        <div :is="block.type" :content-data="block" style="height:100%;"></div>
                    </div>
                </v-responsive>
                <!-- <v-responsive :aspect-ratio="4/3" class="d-md-none justify-center">
                    <div :class="blockClasses(block, 'my-7')">
                        <div :is="block.type" :content-data="block"></div>
                    </div>
                </v-responsive> -->
            </v-carousel-item>
        </v-carousel>
    </v-hover>
</template>
<script>
// import Page from '@/store/Models/Page'
// import QuilityAPI from '@/store/API/QuilityAPI.js'
import http from '@/plugins/http'
// import CallToAction from '@/CMS/views/ContentBlocks/CallToAction.vue';
// import FullWidthText from '@/CMS/views/ContentBlocks/FullWidthText.vue';
// import ZendeskText from '@/CMS/views/ContentBlocks/ZendeskText.vue';
// import TwoCol5050 from '@/CMS/views/ContentBlocks/TwoCol5050.vue';
// import TwoCol2575 from '@/CMS/views/ContentBlocks/TwoCol2575.vue';
// import TwoCol7525 from '@/CMS/views/ContentBlocks/TwoCol7525.vue';
// import ThreeCol255025 from '@/CMS/views/ContentBlocks/ThreeCol255025.vue';
// import ThreeCol333333 from '@/CMS/views/ContentBlocks/ThreeCol333333.vue';
// import FourCol25252525 from '@/CMS/views/ContentBlocks/FourCol25252525.vue';
// import CardStyles from '@/CMS/views/ContentBlocks/CardStyles.vue';
// import ButtonGroup from '@/CMS/views/ContentBlocks/ButtonGroup.vue';
// import CardGroup from '@/CMS/views/ContentBlocks/CardGroup.vue';
// import IframeGroup from '@/CMS/views/ContentBlocks/IframeGroup.vue';
import Banner from '@/CMS/views/ContentBlocks/Banner.vue';
// import Leaderboard from '@/CMS/views/ContentBlocks/Leaderboard.vue';
import moment from 'moment'

export default {
    props: {
        slug: { type: String },
        height: { type: [String, Number], default: '100%' },
        login_tab: { type: Number, default: 0 }
    },
    data: function() {
        return {
            blocks: [],
            slider_key: Math.random()
        }
    },
    mounted: function() {
        this.getContent()
    },
    computed: {
        'loading': function() {
            return false
        },

    },
    methods: {
        async getContent(){
            let id = this.login_tab == 1 ? process.env.MIX_LOGIN_SLIDER_AGENT_PAGE_ID : process.env.MIX_LOGIN_SLIDER_RECRUIT_PAGE_ID
            let request = await http.get(`/api/page/` + id)
            if(request.data.data.status != 'publish') {
                this.blocks = []
            } else {
                this.blocks = request.data.data.blocks.filter(block => {
                    if (block.status === 'draft')
                        return false

                    if (block.startDate !== null && block.startDate !== undefined && block.startDate !== "") {
                        if (!moment(block.startDate).isSameOrBefore(moment(), 'day'))
                            return false
                    }

                    if (block.endDate !== null && block.endDate !== undefined && block.endDate !== "") {
                        if (!moment(block.endDate).isSameOrAfter(moment(), 'day'))
                            return false
                    }

                    return true
                })
            }
            //using this to force a re-render
            this.slider_key = this.login_tab
        },
        blockClasses(block, other) {
            if (typeof block.styles != 'object') {
                this.$set(block, 'styles', [])
            }
            var classes = [
                ...block.styles,
                block.type,
                other
            ]
            return classes.join(' ')
        },
        isClickable: function(block) {
            if (block.link != null && block.link != '') {
                return true
            }
            return false
        },
        maybeOpenLink: function(block) {
            if (block.link != null && block.link != '') {
                if (tblock.link.indexOf('http') > -1) {
                    window.open(block.link, "_blank")
                } else {
                    this.$router.push(block.link)
                }
            }
        }
    },
    watch: {
        login_tab: function(tab) {
            this.getContent()
        },
        blocks: function(newV, oldV) {
            
        }
        // 'slider_content': function(newV) {
        //     //tests for empty object
        //     if (Object.keys(newV).length === 0) {
        //         QuilityAPI.getPageWithSlug(this.slug).then(function() {})
        //     }
        // }
    },
    components: {
        // CallToAction,
        // FullWidthText,
        // TwoCol5050,
        // TwoCol2575,
        // TwoCol7525,
        // ThreeCol333333,
        // ThreeCol255025,
        // FourCol25252525,
        // CardStyles,
        // ButtonGroup,
        // CardGroup,
        // IframeGroup,
        Banner,
        // ZendeskText,
        // Leaderboard
    }
}

</script>
