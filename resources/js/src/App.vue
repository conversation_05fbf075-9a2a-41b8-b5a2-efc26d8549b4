<template>
  <router-view />
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { startSocket, closeSocket, doSend } from '@/utils/websocket'
import { USER_ROLES_TYPES } from '@/utils/const'
import axios from 'axios'
import User from '@/utils/User'

export default {
  name: 'App',
  created() {
    this.validate()
  },
  computed: {
    ...mapGetters({
      user: 'auth/user',
      hasUserRole: 'auth/hasUserRole',
    }),
  },
  watch: {
    user(newUser) {
      if (!newUser.id || this.hasUserRole(USER_ROLES_TYPES.RECRUIT) || this.hasUserRole(USER_ROLES_TYPES.UNLICENSED_AGENT)) {
        closeSocket()
      } else {
        startSocket(process.env.MIX_LARAVEL_WEBSOCKETS_PATH, {
          cbOpen: this.onOpenReviewSocket,
          cbMessage: this.onMessageReviewSocket,
        })
        // Fetch Auth0 user metadata when user is available
        // Disabled for now, because we have to simply log out and back in if impersonating
        // this.fetchAuth0UserMetadata()
      }
    },
  },
  methods: {
    ...mapActions('auth', ['getCookie', 'getUser']),
    ...mapActions('review', ['setPageList', 'lockPage', 'unlockPage']),
    async validate() {
      await this.getCookie()
      // TODO: consider current location
      if (this.$route.path != "/auth/register" && this.$route.path != "/auth/password/reset") {
        this.getUser().catch(() => this.$router.push({ name: 'login' }))
      }
    },
    onOpenReviewSocket() {
      if (this.user.id) doSend(JSON.stringify({ event: 'login', data: this.user.id }))
    },
    onMessageReviewSocket(obj) {
      // if (obj.action == "loggedin") {
      // }
      if (obj.action == 'pagelist') {
        // list of all locked pages
        this.setPageList(obj.data)
      }
      if (obj.action == 'pagelock') {
        // new single page locked, update row
        this.lockPage({
          pageId: obj.data.target,
          data: obj.data,
        })
      }
      if (obj.action == 'pageunlock') {
        // single page has been unlocked
        this.unlockPage({
          pageId: obj.data,
        })
      }
    },
    // New method to fetch Auth0 user metadata
    async fetchAuth0UserMetadata() {
      try {
        if (!this.user || !this.user.id) {
          console.log('User not authenticated yet, skipping Auth0 metadata fetch')
          return
        }

        console.log('Fetching Auth0 user metadata...')
        const response = await axios.get('/api/user/auth0-metadata')

        if (response.data) {
          // Log the Auth0 user metadata to the console
          console.log('Auth0 User Metadata:', response.data)

          // Log specific metadata sections if available
          if (response.data.app_metadata) {
            console.log('App Metadata:', response.data.app_metadata)
          }

          if (response.data.user_metadata) {
            console.log('User Metadata:', response.data.user_metadata)
          }

          if (response.data.quility_metadata) {
            console.log('Quility Metadata:', response.data.quility_metadata)
          }

          if (response.data.quility_roles) {
            console.log('Quility Roles:', response.data.quility_roles)
          }

          // Check if reload_required flag is present and true
          if (response.data.reload_required && response.data.new_user_id) {
            console.log('Agent code changed, switching to user ID:', response.data.new_user_id)

            // Redirect to the switchUser endpoint, which will handle logout/login
            window.location.href = `/api/user/switch-user/${response.data.new_user_id}`
          }

          // You could also store this in Vuex if needed
          // this.$store.commit('auth/setAuth0UserMetadata', response.data)
        }
      } catch (error) {
        console.error('Error fetching Auth0 user metadata:', error.response ? error.response.data : error.message)
      }
    },
  },
}
</script>
<style lang="scss"></style>
