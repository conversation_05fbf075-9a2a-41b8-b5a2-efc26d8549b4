import { DefineBlock } from './../block';
import { SummitBlock } from './block';

export type SummitVideo = SummitBlock & {
  video_id: string,
  title?: string,
  resources?: string

  // TODO: Not sure if we still need these fields, but they are used somewhere
  buttonText?: string,
  buttonHref?: string,
  newWindow?: boolean,
}

export type LightSpeedVideo = SummitVideo & { token: string }

export type WistiaVideo = SummitVideo

export type SummitWistiaVideoBlock = DefineBlock<'wistia-video-group', {
  Videos: WistiaVideo[],
  NumColumns: number,
}>
export type SummitLightSpeedVideoBlock = DefineBlock<'light-speed-video-group', {
  Videos: LightSpeedVideo[]
}>
