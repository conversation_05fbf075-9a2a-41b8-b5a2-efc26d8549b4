.cms {
    .quility_green {


        a,
        a:link {
            color: $q_green_1;
            text-decoration: underline;
        }

        a:hover {
            text-decoration: none;
        }

        h1 {
            color: #000;
        }

        /*
        h1::after {
            display: block;
            width: 46px;
            height: 5px;
            content: " ";
            background-color: $q_green_1;
            margin: 12px 0;
        }
        */

        h1.q-title::after {
            display: block;
            width: 37px;
            height: 7px;
            content: " ";
            background-color: $q_green_1;
            margin: 4px auto;
            margin-top: 7px;
            margin-bottom: 20px
        }

        h2.q-title::after {
            display: block;
            width: 46px;
            height: 5px;
            content: " ";
            background-color: $q_green_1;
            margin: 4px auto;
        }

        h3.q-title::after {
            display: block;
            width: 46px;
            height: 5px;
            content: " ";
            background-color: $q_green_1;
            margin: 4px auto;
        }

        h4.q-title::after {
            display: block;
            width: 46px;
            height: 3px;
            content: " ";
            background-color: $q_green_1;
            margin: 12px 0;
        }

        h4.q-title::after {
            margin: 12px auto;
        }

        /****** short rules *****/
        h1 .first-with-short-rule::after,
        h2 .first-with-short-rule::after,
        h3 .first-with-short-rule::after,
        h4 .first-with-short-rule::after,
        h5 .first-with-short-rule::after {
            display: block;
            content: " ";
            background-color: $q_green_1 !important;
            margin: 12px 0;
        }



        h1 .first-with-short-rule::after,
        h1.text-center .first-with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 50px;
            height: 9px;
            margin-top: 7px !important;
            margin-bottom: 20px !important;
        }

        h2 .first-with-short-rule::after,
        h2.text-center .first-with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 45px;
            height: 7px;
            margin-top: 7px !important;
            margin-bottom: 20px !important;
        }

        h3 .first-with-short-rule::after,
        h3.text-center .first-with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 35px;
            height: 5px;
            margin-top: 6px !important;
            margin-bottom: 20px !important;
        }

        h4 .first-with-short-rule::after,
        h4.text-center .first-with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 30px;
            height: 4px;
            margin-top: 5px !important;
            margin-bottom: 20px !important;
        }

        h5 .first-with-short-rule::after,
        h5.text-center .first-with-short-rule::after {
            /*
            padding: .3em;
            */
            width: 25px;
            height: 3px;
            margin-top: 4px !important;
            margin-bottom: 20px !important;
        }




        h1.text-center .first-with-short-rule::after,
        h2.text-center .first-with-short-rule::after,
        h3.text-center .first-with-short-rule::after,
        h4.text-center .first-with-short-rule::after,
        h5.text-center .first-with-short-rule::after {
            padding: .2em;
            display: block;
            content: " ";
            background-color: $q_green_1 !important;
            margin: 0 auto !important;
        }



        h1 .with-short-rule::after,
        h2 .with-short-rule::after,
        h3 .with-short-rule::after,
        h4 .with-short-rule::after,
        h5 .with-short-rule::after {
            display: block;
            content: " ";
            background-color: $q_green_4 !important;
            margin: 12px 0;
        }



        h1 .with-short-rule::after,
        h1.text-center .with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 50px;
            height: 9px;
            margin-top: 7px;
            margin-bottom: 20px;
        }

        h2 .with-short-rule::after,
        h2.text-center .with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 45px;
            height: 7px;
            margin-top: 7px !important;
            margin-bottom: 20px !important;
        }

        h3 .with-short-rule::after,
        h3.text-center .with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 35px;
            height: 5px;
            margin-top: 6px !important;
            margin-bottom: 20px !important;
        }

        h4 .with-short-rule::after,
        h4.text-center .with-short-rule::after,
            {
            /*
            padding: .3em;
            */
            width: 30px;
            height: 4px;
            margin-top: 5px !important;
            margin-bottom: 20px !important;
        }

        h5 .with-short-rule::after,
        h5.text-center .with-short-rule::after {
            /*
            padding: .3em;
            */
            width: 25px;
            height: 3px;
            margin-top: 4px !important;
            margin-bottom: 20px !important;
        }




        h1.text-center .with-short-rule::after,
        h2.text-center .with-short-rule::after,
        h3.text-center .with-short-rule::after,
        h4.text-center .with-short-rule::after,
        h5.text-center .with-short-rule::after {
            display: block;
            content: " ";
            background-color: $q_green_4 !important;
            margin: 0 auto !important;
        }

        ul {
            list-style-position: outside;
            text-indent: -0.5em;
            padding-left: 0.9em;
        }

        ul li::before {
            content: "â€¢";
            position: relative;
            left: -9px;
            color: $q_green_1;
        }

        ul li {
            margin-bottom: 0.6em;
        }

        .q-title {
            /*display: block;*/
            margin: 0 auto;
            padding: 20px 0 10px 0;
        }

        .title-left::after {
            padding: .3em;
            display: block;
            width: 5%;
            height: 3px;
            content: " ";
            background-color: $q_green_5;
            margin: 12px 0;
        }

        .title-center::after {
            padding: .3em;
            display: block;
            width: 5%;
            height: 3px;
            content: " ";
            background-color: $q_green_5;
            margin: 0 auto;
            margin-top: 12px;
            margin-bottom: 12px;
        }

        /****** buttons ******/
        .button-1 {
            color: white;
            font-weight: bold;
            display: inline-block;
            background: $q_green_1;
            padding: 20px 40px;
            /* border-radius: 5px; */
            margin-top: 10px;
            margin-right: 10px;
            -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            opacity: 1;
            transition: 0.3s;
        }

        .theme--dark.v-btn:not(.v-btn--flat):not(.v-btn--text):not(.v-btn--outlined),
        .v-btn {
            margin-top: 0;
            border-radius: 0;
            background: $q_green_1;
        }

        .button-2 {
            color: white;
            display: inline-block;
            background: $q_green_3;
            padding: 10px 20px;
            border-radius: 0;
            margin-top: 10px;
            margin-right: 10px;
            -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            opacity: 1;
            transition: 0.3s;
        }

        .button-3 {
            color: white;
            display: inline-block;
            background: $q-med-grey;
            padding: 10px 20px;
            border-radius: 0;
            margin-top: 10px;
            margin-right: 10px;
            -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            opacity: 1;
            transition: 0.3s;
        }

        .button-invert {
            color: $q_green_1;
            font-weight: bold;
            display: inline-block;
            background: transparent;
            padding: 20px 40px;
            border-radius: 5px;
            border: $q_green_1 2px solid;
            margin-right: 10px;
            -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
            opacity: 1;
            transition: 0.3s;
        }

        a.button-1,
        .button-1 a:link,
        .button-1 a,
        a.button-2,
        .button-2 a:link,
        .button-2 a,
        a.button-3,
        .button-3 a:link,
        .button-3 a,
        .theme--dark.v-btn:not(.v-btn--flat):not(.v-btn--text):not(.v-btn--outlined) {
            text-decoration: none;
            color: white;
        }

        a.button-invert,
        .button-invert a:link,
        .button-invert a {
            color: $q_green_1;
        }

        .button-1 a:hover,
        .button-2 a:hover,
        .button-3 a:hover {
            color: rgba(251, 251, 251, 0.8);
        }

        .button-invert a:hover {
            color: $q_green_3;
        }

        .button-1:hover,
        .button-2:hover,
        .button-3:hover,
        .button-invert:hover {
            opacity: 0.8;
        }
    }

}


/*****/
