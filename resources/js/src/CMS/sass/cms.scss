@import "./page_styles.scss";

.cms-editor {

    /***** Don't make this sticky when we are using the cms editor ****/
    .q-sticky {
        position: absolute;
    }
}

/***** CMS Editor *****/
.editor__content {
    min-height: 1.15em;
    border: #eee thin solid;
    padding: 15px !important;
    ;
}

.cms {
    body {
        color: #000000;
        font-size: calc(14px + (18 - 14) * ((100vw - 300px) / (1600 - 300)));
        line-height: 1.3em;
    }

    p2,
    p {
        font-size: calc(14px + (18 - 14) * ((100vw - 300px) / (1600 - 300)));
        display: block;
        margin-bottom: 1.3em;
    }

    p,
    ul li {
        font-size: calc(14px + (18 - 14) * ((100vw - 300px) / (1600 - 300)));
        line-height: 1.3em;
    }


    /***** h tags are edited in general.scss ****/
    /* 
    h1 {
        font-weight: 900;
        font-size: 3.25em;
        line-height: 1.3em;
        margin-top: 9px;
        margin-bottom: 22px;
        
}*/

    /* h2 {
        font-size: 2.5em;
        font-weight: 900;
        line-height: 1.3em;
        color: #000;
        margin-bottom: 18px !important;
}*/

    /* h3 {
        font-size: 2em;
        letter-spacing: -.015em;
        color: #000;
        margin-bottom: 37px;
}*/

    /* 
    h4 {
        font-weight: 600;
    }
    

    h2 .with-short-rule::after,
    h3 .with-short-rule::after,
    h4 .with-short-rule::after,
    h5 .with-short-rule::after {
        display: block;
        width: 46px;
        height: .2em;
        content: " ";
        background-color: $q_blue_2;
        margin: 12px 0;
    }

    h2.text-center .with-short-rule::after,
    h3.text-center .with-short-rule::after,
    h4.text-center .with-short-rule::after,
    h5.text-center .with-short-rule::after {
        margin: 12px auto;
    }

    h2.text-right .with-short-rule::after,
    h3.text-right .with-short-rule::after,
    h4.text-right .with-short-rule::after,
    h5.text-right .with-short-rule::after {
        margin: 12px 0 0 auto;
    }
    */

    img {
        display: block;
        margin: 0 auto;
        max-width: 100%;
    }


    .site-width {
        max-width: 1200px;
        margin: 0 auto;
    }

    .v-card__title {
        word-break: normal !important;
    }

    .v-card__text {
        margin-bottom: 25px;
    }

    .content-block-title {
        background-color: $q_green_5;
        padding: 10px 20px;
        display: inline-block;
    }

    /*
    .v-card__actions {
        height: 35px;
    }
    */
    iframe {
        border: none !important;
    }

    .cms img {
        max-width: 100%;
    }

    .border-l-white {
        border-left: #fff thin solid;
        padding-left: 25px;
        color: #fff;

    }

    .border-l-gray {
        border-left: #333 thin solid;
        padding-left: 25px;
        color: #fff;

    }


    /******** Wysiwyg Styles *******/
    .quote {
        font-size: 1.2em;
        padding-left: 5%;
        margin: 35px 10% 35px 5%;
        border-left: 2px solid #999999;
        line-height: 1.5em;
    }

    /****** buttons ******/
    .button-1 {
        color: white;
        font-weight: bold;
        display: inline-block;
        background: #000;
        padding: 20px 40px;
        border-radius: 0;
        margin-top: 10px;
        margin-right: 10px;
        -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        opacity: 1;
        transition: 0.3s;
    }

    .button-2 {
        color: white;
        display: inline-block;
        background: $primary;
        padding: 10px 20px;
        border-radius: 0;
        margin-top: 10px;
        margin-right: 10px;
        -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        opacity: 0.6;
        transition: 0.3s;
    }

    .button-3 {
        color: white;
        display: inline-block;
        background: #333;
        padding: 10px 20px;
        border-radius: 0;
        margin-top: 10px;
        margin-right: 10px;
        -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        opacity: 0.6;
        transition: 0.3s;
    }

    .button-invert {
        color: #000;
        font-weight: bold;
        display: inline-block;
        background: transparent;
        padding: 20px 40px;
        border-radius: 0;
        border: #000 2px solid;
        margin-top: 10px;
        margin-right: 10px;
        -webkit-box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
        opacity: 1;
        transition: 0.3s;
    }

    a.button-1,
    .button-1 a:link,
    .button-1 a,
    a.button-2,
    .button-2 a:link,
    .button-2 a,
    a.button-3,
    .button-3 a:link,
    .button-3 a {
        text-decoration: none;
        color: white;
    }

    a.button-invert,
    .button-invert a:link,
    .button-invert a {
        color: #000;
    }

    .button-1 a:hover,
    .button-2 a:hover,
    .button-3 a:hover {
        color: rgba(251, 251, 251, 0.8);
    }

    .button-invert a:hover {
        color: #999;
    }

    .button-1:hover,
    .button-2:hover,
    .button-3:hover,
    .button-invert:hover {
        opacity: 0.8;
    }

    /***** card style container - not single card *****/

    .q-card {
        border-radius: 4px;
        margin-top: 28px !important;
        margin-bottom: 28px !important;
        padding: 0 20px;
        display: block;
        max-width: 100%;
        outline: none;
        text-decoration: none;
        transition-property: box-shadow, opacity, -webkit-box-shadow;
        overflow-wrap: break-word;
        position: relative;
        white-space: normal;
        transition: box-shadow 0.28s cubic-bezier(0.4, 0, 0.2, 1), -webkit-box-shadow 0.28s cubic-bezier(0.4, 0, 0.2, 1);
        will-change: box-shadow;
        box-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
    }



    /******** Banner Block *******/
    .banner h1 {
        font-size: calc(28px + (80 - 28) * ((100vw - 300px) / (1600 - 300)));

    }

    .banner .banner-image__block {
        height: 400px;
        background-size: contain;
        background-position: center center;
    }

    .light-on-dark {
        color: #eeeeee;

        h1,
        h2,
        h3,
        h4 {
            color: #ffffff !important;
        }
    }

    .dark-on-light {}

    .banner,
    .call-to-action {
        overflow: hidden;
    }

    .v-carousel__item .banner {
        height: 100%;
    }

    .v-carousel__item .banner .banner-image__block {
        height: 100%;
    }

    .v-carousel__item .v-responsive__content>div {
        height: 100%;
    }

    .v-carousel__item .banner.my-7 {
        margin-top: 0px !important;
        margin-bottom: 0px !important;
    }


    /******** CTA Block Styles *******/

    .cta-blue {}

    .cta-green {
        background-color: $q-green-1;
        color: #000000;

        h1,
        h2,
        h3,
        h4 {
            color: #ffffff !important;
        }

    }

    /******** Block Styles *******/

    .grey-block {
        background-color: #f3f3f3;
        padding-top: 40px;
        padding-bottom: 40px
    }

    .white-block {
        background-color: #ffffff;
        padding-top: 40px;
        padding-bottom: 40px
    }



    .sfg_medium_green-block,
    .sfg_medium_green {
        background-color: $sfg_neon_green;
    }


    .sfg_royal_blue-block,
    .sfg_royal_blue {
        background-color: $sfg_neon_green !important;
    }

    .sfg_midnight_blue-block,
    .sfg_midnight_blue {
        background-color: $sfg_neon_green !important;
    }

    .sfg_yellow-block,
    .sfg_yellow {
        background-color: $sfg_neon_green !important;
    }


    .sfg_neon_green-block,
    .sfg_neon_green {
        background-color: $sfg_neon_green !important;
    }

    .sfg_light_grey-block,
    .sfg_light_grey {
        background-color: $sfg_light_grey !important;
    }


    .sfg_grey-block,
    .sfg_grey {
        background-color: $sfg_grey !important;
    }

    .sfg_sky_blue-block,
    .sfg_sky_blue {
        background-color: $sfg_sky_blue !important;
    }

    .grass-green-block,
    .grass-green {
        background-color: $q_agency_1 !important;
    }

    .water-green-block,
    .water-green {
        background-color: $q_green_1 !important;
    }

    .green-block {
        background-color: $q_green_1 !important;
    }

    .blue-block,
    .indigo {
        background-color: $q_blue_1 !important;
    }

    /*.grass-green-block p,
    .grass-green-block h1,
    .grass-green-block h2,
    .grass-green-block h3,
    .grass-green-block h4,
    .grass-green-block h5,
    .grass-green-block h6,
    .water-green-block p,
    .water-green-block h1,
    .water-green-block h2,
    .water-green-block h3,
    .water-green-block h4,
    .water-green-block h5,
    .water-green-block h6,
    .grass-green-block p,
    .grass-green-block h1,
    .grass-green-block h2,
    .grass-green-block h3,
    .grass-green-block h4,
    .grass-green-block h5,
    .grass-green-block h6,
    .green-block p,
    .green-block h1,
    .green-block h2,
    .green-block h3,
    .green-block h4,
    .green-block h5,
    .green-block h6 {
        color: #fff !important;
    }

    .blue-block p,
    .blue-block h1,
    .blue-block h2,
    .blue-block h3,
    .blue-block h4,
    .blue-block h5,
    .blue-block h6 {
        color: #fff important;
    }
*/



    .iframe-container {
        position: relative;
        overflow: hidden;
        width: 100%;
        padding-top: 56.25%;
        /* 16:9 Aspect Ratio (divide 9 by 16 = 0.5625) */
    }

    /* Then style the iframe to fit in the container div with full height and width */
    .responsive-iframe {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        width: 100%;
        height: 100%;
    }

    /***** Breakpoints *****/

    @media only screen and (max-width: 600px) {

        .border-l-white,
        .border-l-gray {
            border-left: none;
            padding-left: 25px;

        }

    }

    .agent-titles {
        text-align: center;
        padding-top: 15px;
        font-size: .8em;
    }

    .agent_profile_photo {
        height: 150px;
        width: 150px;
        border-radius: 50% !important;
        border: 2px solid #000000;
        overflow: hidden;
        text-align: center;
        /*display: inline-block;*/
        position: relative;
        margin: 0 auto;
        background-color: white;
    }

    .agent_profile_photo img {
        width: auto;
        height: 150px;
        position: absolute;
        margin: 0 auto;
        bottom: 0;
        right: 0;

    }

}
