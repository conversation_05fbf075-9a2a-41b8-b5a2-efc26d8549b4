<template>
    <QSummitVideoPlayerWrapper :video="video">
        <iframe 
            width="100%" height="100%"
            :src="`https://vt.lightspeedvt.com/share/embed.cfm?cmpid=${video.video_id}&token=${video.token}&UniqueId=${agentCode}`"
            frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
            scrolling="no" allowfullscreen 
        />
    </QSummitVideoPlayerWrapper>
</template>

<script lang="ts">
import QSummitVideoPlayerWrapper from './QSummitVideoPlayerWrapper.vue';
import { defineComponent, PropType } from 'vue'
import { LightSpeedVideo } from '@/CMS/types/blocks';

export default defineComponent({
    name: "QSummitLightSpeedPlayer",

    components: { QSummitVideoPlayerWrapper },

    props: {
        video: {
            type: Object as PropType<LightSpeedVideo>,
            required: true
        }
    },

    computed: {
        agentCode() { return (this.user as any).AgentCode; },
    }
})
</script>

<style lang="scss" scoped>
  .q-summit-wistia-player__resources {
    background: #18438a;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    color: rgb(192, 192, 192);

    &-icon {
        padding-right: 12px;
    }

    &-heading {
        font-weight: 700;
        padding-right: 12px;
        color: white;
    }

    p {
        margin: 0;
    }

    a {
        color: #0083EB;
        &:hover {
            color: #0965b0;
        }
    }
  }
</style>

<style lang="scss" scoped>
    .wistia_responsive_padding {
        padding:56.25% 0 0 0;
        position:relative;
        .wistia_responsive_wrapper {
            height:100%;
            left:0;
            position:absolute;
            top:0;
            width:100%;
        }        
    }
</style>
