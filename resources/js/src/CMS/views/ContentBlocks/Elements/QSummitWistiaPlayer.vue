<template>
    <QSummitVideoPlayerWrapper :video="video">
        <iframe :src="defaultWistiaVideo" :title="title" allow="autoplay; fullscreen" allowtransparency="true" frameborder="0" scrolling="no" class="wistia_embed" name="wistia_embed" msallowfullscreen width="100%" height="100%" />
    </QSummitVideoPlayerWrapper>
</template>

<script lang="ts">
import QSummitVideoPlayerWrapper from './QSummitVideoPlayerWrapper.vue';
import QuilityAPI from '@/store/API/QuilityAPI.js'
import { defineComponent, PropType } from 'vue'
import { WistiaVideo } from '@/CMS/types/blocks';

declare global {
  interface Window {
    /* Some Wistia global for tracking */
    _wq: {
      id: any,
      onReady: (...args: any[]) => void
    }[];
  }
}

/* WistiaPlayer with built-in Summit tracking and without title and text */
export default defineComponent({
    name: "QWistiaPlayer",

    components: { 
        QSummitVideoPlayerWrapper
    },

    props: {
        video: {
            type: Object as PropType<WistiaVideo>,
            required: true
        }
    },

    data: () => ({
        percentage_watched: 0
    }),

    mounted() {
        setTimeout(this.startTrackingVideo, 2000);
    },

    computed: {
        video_id() {
            return this.video.video_id;
        },
        defaultWistiaVideo() {
            return "https://fast.wistia.net/embed/iframe/" + this.video_id + "?videoFoam=true";
        },
        title() {
            return this.video.title;
        },
        getAlignment: function() {
            return "position:absolute; left:0; bottom:0; padding:20px; text-align:right; width:100%"
        }
    },
    methods: {
        //adds event watchers
        startTrackingVideo() {
            window._wq.push({
                id: this.video_id,
                onReady: (video: any) => {
                    video.bind('play', () => {
                        this.logPlayingStarted()
                    });
                    video.bind('end', () => {
                        this.logCompletedVideo()
                    });

                    video.bind('percentwatchedchanged', (p: number) => {
                        this.logPercentWatched(p)
                    });
                }
            });
        },
        logPlayingStarted() {
            QuilityAPI.logWistiaEvent("started", this.video_id)
        },
        logPercentWatched(percent: number) {
            var p = Math.round(percent * 10)
            if (p > this.percentage_watched) {
                console.log('watched' + p + "0%")
                this.percentage_watched = p;
                QuilityAPI.logWistiaEvent("watched " + Math.round(percent * 100) + "%", this.video_id)
            }
        },
        logCompletedVideo() {
            console.log('Video finished!')
            QuilityAPI.logWistiaEvent("finished", this.video_id)
        },
    }
})
</script>

<style lang="scss" scoped>
  .q-summit-wistia-player__resources {
    background: #18438a;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    color: rgb(192, 192, 192);

    &-icon {
        padding-right: 12px;
    }

    &-heading {
        font-weight: 700;
        padding-right: 12px;
        color: white;
    }

    p {
        margin: 0;
    }

    a {
        color: #0083EB;
        &:hover {
            color: #0965b0;
        }
    }
  }
</style>

<style lang="scss" scoped>
    .wistia_responsive_padding {
        padding:56.25% 0 0 0;
        position:relative;
        .wistia_responsive_wrapper {
            height:100%;
            left:0;
            position:absolute;
            top:0;
            width:100%;
        }        
    }
</style>
