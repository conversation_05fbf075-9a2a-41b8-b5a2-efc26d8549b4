<template>
    <v-card tile height="100%" outlined color="transparent">
        <v-responsive :aspect-ratio="16/9">
            <slot />
        </v-responsive>
        <div v-if="!noText(video.resources)" class="q-summit-wistia-player__resources">
            <div class="q-summit-wistia-player__resources-icon">
                <svg xmlns="http://www.w3.org/2000/svg" width="34.3" height="34.38"><g data-name="Group 20050"><path data-name="Rectangle 5145" fill="none" stroke="#0083eb" stroke-linecap="round" stroke-linejoin="round" d="m19.2 13.13 2.04 2.05L2.76 33.67.71 31.6z"/></g><path data-name="Rectangle 5147" fill="none" stroke="#0083eb" stroke-linecap="round" stroke-linejoin="round" d="m23.99 6.28 4.1 4.11-5.81 5.82-4.11-4.1z"/><path data-name="Path 74988" d="m13.87 2.15 8.66 5.78-2.89 2.88Z" fill="none" stroke="#0083eb" stroke-linecap="round" stroke-linejoin="round"/><path data-name="Path 74989" d="m32.15 20.44-5.77-8.66-2.89 2.88Z" fill="none" stroke="#0083eb" stroke-linecap="round" stroke-linejoin="round"/><path data-name="Rectangle 5148" fill="none" stroke="#0083eb" stroke-linecap="round" stroke-linejoin="round" d="m26.73 5.6 2.05 2.05-1.71 1.71-2.05-2.05z"/></svg>
            </div>
            <span class="q-summit-wistia-player__resources-heading">Resources:</span>
            <div class="q-summit-wistia-player__resources-text" v-html="video.resources" />
        </div>
    </v-card>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { SummitVideo } from '@/CMS/types/blocks/';

export default defineComponent({
    name: "QSummitVideoPlayerWrapper",

    props: {
        video: { type: Object as PropType<SummitVideo>, required: true },
    },

    methods: {
        noText(text: string | null | undefined) {
            return  text == null || text == "" || text == "<p></p>";
        }
    }
})
</script>

<style lang="scss" scoped>
  .q-summit-wistia-player__resources {
    background: #18438a;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    color: rgb(192, 192, 192);

    &-icon {
        padding-right: 12px;
    }

    &-heading {
        font-weight: 700;
        padding-right: 12px;
        color: white;
    }

    p {
        margin: 0;
    }

    a {
        color: #0083EB;
        &:hover {
            color: #0965b0;
        }
    }
  }
</style>

<style lang="scss" scoped>
    .wistia_responsive_padding {
        padding:56.25% 0 0 0;
        position:relative;
        .wistia_responsive_wrapper {
            height:100%;
            left:0;
            position:absolute;
            top:0;
            width:100%;
        }        
    }
</style>
