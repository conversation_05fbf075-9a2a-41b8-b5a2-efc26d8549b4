<template>
    <v-container fluid class="grey lighten-5 my-0 px-0  py-0">
        <q-benefits-submenu></q-benefits-submenu>
        <div class="q-sticky-buffer">
            <v-container fluid class="px-5 grey lighten-5 site-width">
                <slot></slot>
            </v-container>
        </div>
    </v-container>
</template>
<script>
import QBenefitsSubmenu from '@/components/navigation/Submenus/QBenefitsSubmenu.vue';
export default {
    props: [],
    data: () => ({}),
    components: {
        QBenefitsSubmenu,
    },
}
</script>