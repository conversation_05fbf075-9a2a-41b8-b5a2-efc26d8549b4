<template>
    <div>
        <div class="px-8 pb-8">
            <v-btn large color="primary" @click="addVideo">Add Video</v-btn>
        </div>
        <Draggable tag="v-list" v-model="itemsComputed" handle=".handle">
            <v-hover #default="{ hover }" v-for="(item, index) in itemsComputed" :key="item[keyBy]">
                <v-list-item class="pa-4">
                    <LightSpeedVideoEditor :video="item" />

                    <v-btn class="handle" v-if="hover" dark color="gray" small fab absolute top left>
                        <v-icon small>fas fa-arrows-alt</v-icon>
                    </v-btn>
                    <v-btn @click="deleteVideo(index)" v-if="hover" fab small absolute top right color="red">
                        <v-icon small>fas fa-trash</v-icon>
                    </v-btn>
                    <v-divider />
                </v-list-item>
            </v-hover>
        </Draggable>
    </div>
</template>

<script lang="ts">
import Draggable from "vuedraggable";
import { defineComponent, PropType } from 'vue'
import { SummitLightSpeedVideoBlock, LightSpeedVideo } from "@/CMS/types/blocks";
import LightSpeedVideoEditor from './LightSpeedVideoEditor.vue'

export default defineComponent({
    components: { 
        Draggable,
        LightSpeedVideoEditor,
    },

    props: {
        value: { type: Object as PropType<SummitLightSpeedVideoBlock['block']>, required: true },
        keyBy: { type: String as PropType<keyof LightSpeedVideo>, default: 'id' },
        editor: { type: Boolean, default: false }
    },

    emits: { 'input': (p: SummitLightSpeedVideoBlock['block']) => !!p },

    computed: {
        itemsComputed: {
            get() {
                return this.value.Videos
            },
            set(items: LightSpeedVideo[]) {
                this.$emit('input', {
                    Videos: items,
                })
            }
        }
    },

    methods: {
        deleteVideo(key: number) {
            this.itemsComputed = this.itemsComputed.filter((_, i: number) => i !== key)
        },
        addVideo() {
            this.itemsComputed = [...this.itemsComputed, {
                text: '',
                title: '',
                token: '',
                video_id: '',
            }]
        }
    }
})
</script>