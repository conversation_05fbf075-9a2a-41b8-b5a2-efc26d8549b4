<template>
    <call-to-action>
        <template v-slot:headline>
            <v-text-field class="headline" v-model="value.headline" label="Headline"></v-text-field>
        </template>
        <template v-slot:text>
            <text-editor v-model="value.text"></text-editor>
        </template>
        <template v-slot:meta>
            <v-text-field v-model="value.buttonText" label="Button Text"></v-text-field>
            <v-text-field v-model="value.buttonHref" label="Button Link"></v-text-field>
        </template>
        <template v-slot:buttonText>
            <span>{{ value.buttonText }}</span>
        </template>
    </call-to-action>
</template>
<script>
import TextEditor from '../TextEditor.vue'
import CallToAction from '@/CMS/views/ContentBlocks/CallToAction.vue'
export default {
    props: ['value'],
    created: function() {
        this.reset();
    },
    components: {
        TextEditor,
        CallToAction
    },
    methods: {
        reset: function() {
            if (typeof this.value.headline == 'undefined') {
                this.$set(this.value, 'headline', "Sed nec diam non eros lobortis tempor et eget sapien.")
            }
            if (typeof this.value.headlineColor == 'undefined') {
                this.$set(this.value, 'headlineColor', "white")
            }
            if (typeof this.value.text == 'undefined') {
                this.$set(this.value, 'text', "Sed rutrum orci eget ligula mattis, vel vestibulum ligula feugiat. Cras et velit ut ipsum fringilla vulputate.")
            }
            if (typeof this.value.buttonText == 'undefined') {
                this.$set(this.value, 'buttonText', "Click Here!")
            }
            if (typeof this.value.buttonHref == 'undefined') {
                this.$set(this.value, 'buttonHref', "https://www.quility.com")
            }
            this.$emit('input', this.value)
        },
    },
    watch: {
        'value.type': function(newV) {
            var g = this;
            this.$nextTick(g.reset())
        },
    }
}

</script>
