<template>
    <v-card tile height="100%" width="100%" outlined color="transparent">
        <v-card-title>
            <h5>LightSpeed Video</h5>
        </v-card-title>
        <v-card-text>
            <v-text-field class="mt-5" label="Title" v-model="video.title"></v-text-field>
            <v-text-field class="mt-5" label="Video ID" v-model="video.video_id"></v-text-field>
            <v-text-field class="mt-5" label="Token" v-model="video.token"></v-text-field>
            <h6>Right column text</h6>
            <text-editor class="cms" label="Text" v-model="video.text"></text-editor>
            <h6>Resources</h6>
            <text-editor class="cms" label="Text" v-model="video.resources"></text-editor>
        </v-card-text>
    </v-card>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { LightSpeedVideo } from '@/CMS/types/blocks/';
import TextEditor from '@/CMS/editor/components/TextEditor.vue'

export default defineComponent({
    name: "LightSpeedVideoEditor",

    components: {
        TextEditor,
    },

    props: {
        video: {
            type: Object as PropType<LightSpeedVideo>,
            required: true,
        }
    },
})
</script>
