import { USER_ROLES_TYPES } from '@/utils/const'

export const navAdmin = (i18n, admin) => [
  {
    icon: 'mdi-view-dashboard',
    text: i18n.t('menu.dashboard'),
    link: 'dashboard',
  },
  { divider: true },
  admin.getResourceLink('users'),
  admin.getResourceLink('books'),
]

export const navSymmetry = i18n => [
  {
    icon: 'mdi-blank',
    text: 'Index',
    link: 'index',
    hide: true,
  },
  {
    icon: 'mdi-calendar-check',
    text: i18n.t('menu.my_task'),
    link: 'my_task',
    permission: '',
  },
  {
    icon: 'mdi-square-edit-outline',
    text: i18n.t('menu.onboarding_application'),
    link: 'onboard',
    permission: 'fill applications',
  },
  {
    icon: 'mdi-account-plus-outline',
    text: i18n.t('menu.send_invite'),
    link: 'send_invitation',
    permission: 'create invites',
    role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.AGENCY_OWNER, USER_ROLES_TYPES.SALES_REP],
  },
  {
    icon: 'mdi-email-fast-outline',
    text: i18n.t('menu.view_my_invites'),
    link: 'view_my_invites',
    permission: 'create invites',
    role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.AGENCY_OWNER, USER_ROLES_TYPES.SALES_REP],
  },
  {
    icon: 'mdi-clipboard-text-search-outline',
    text: i18n.t('menu.applications'),
    link: 'folder-applications',
    permission: 'review applications',
    role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF],
    subLinks: [
      {
        icon: 'mdi-view-list-outline',
        text: i18n.t('menu.all_applications'),
        link: 'all_applications',
        role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF],
      },
      {
        icon: 'mdi-clock-outline',
        text: i18n.t('menu.pending_applications'),
        link: 'pending_applications',
        role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF],
      },
      {
        icon: 'mdi-check-circle-outline',
        text: i18n.t('menu.completed_applications'),
        link: 'completed_applications',
        role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF],
      },
      {
        icon: 'mdi-account-group-outline',
        text: i18n.t('menu.group_invitations'),
        link: 'group_invitations',
        role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF],
      },
      {
        icon: 'mdi-text-box-search',
        text: i18n.t('menu.preview_application_form'),
        link: 'preview_application_form',
        role: [USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.AGENCY_OWNER],
        show: process.env.MIX_PREVIEW_APPLICATION_FORM ?? false
      },
    ],
  },
  {
    icon: 'mdi-text-box-search',
    text: i18n.t('menu.preview_application_form'),
    link: 'preview_application_form',
    permission: 'review applications',
    role: [USER_ROLES_TYPES.AGENCY_OWNER],
    show: process.env.MIX_PREVIEW_APPLICATION_FORM ?? false
  },
  {
    icon: 'mdi-trackpad',
    text: i18n.t('menu.track_applications'),
    link: 'track_applications',
    permission: 'track applications',
    role: [USER_ROLES_TYPES.AGENCY_OWNER, USER_ROLES_TYPES.SALES_REP],
  },
  {
    icon: "mdi-domain",
    text: i18n.t("menu.company_information"),
    link: "company_information",
    permission: "company information",
    role: [
      USER_ROLES_TYPES.RECRUIT,
      USER_ROLES_TYPES.UNLICENSED_AGENT,
      USER_ROLES_TYPES.AGENCY_OWNER,
      USER_ROLES_TYPES.STAFF,
      USER_ROLES_TYPES.SUPER_ADMIN
    ],
    tenants: ['Q2A']
  },
  {
    icon: 'mdi-square-edit-outline',
    text: i18n.t('menu.content_management'),
    link: 'content_management',
    permission: 'agency content',
    role:  USER_ROLES_TYPES.AGENCY_OWNER,
    show: false,
  },
  {
    icon: 'mdi-square-edit-outline',
    text: i18n.t('menu.content_management'),
    link: 'content_list',
    permission: '',
    role: USER_ROLES_TYPES.SUPER_ADMIN,
  },
  {
    icon: 'mdi-information-outline',
    text: i18n.t('menu.help_center'),
    link: 'help_center',
    permission: '',
  },
  // {
  //   icon: 'mdi-note-text',
  //   text: i18n.t('menu.guide'),
  //   link: 'guide',
  //   permission: 'license guide',
  //   role: USER_ROLES_TYPES.RECRUIT,
  // },
  {
    icon: 'mdi-email-fast-outline',
    text: "Test Emails", 
    link: "emails",
    permission: "",
    role: [
      USER_ROLES_TYPES.STAFF,
      USER_ROLES_TYPES.SUPER_ADMIN
    ],
    // show: true, // process.env.NODE_ENV !== 'production'
  },
]
