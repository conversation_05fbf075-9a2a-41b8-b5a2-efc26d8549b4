import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from 'vuex-persistedstate'

import auth from './modules/auth'
import layout from './modules/layout'
import snackbar from './modules/snackbar'
import user_invite from './modules/user_invite'
import form from './modules/form'
import hellosign from './modules/hellosign'
import application from './modules/application'
import review from './modules/review'
import task from './modules/task'
import form_field_editor from './modules/form_field_editor'
import content_management from './modules/content_management'
import company_information from './modules/company_information'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {},
  mutations: {},
  actions: {},
  plugins: [
    createPersistedState({
      paths: ['auth', 'content_management.states'],
    }),
  ],
  modules: {
    auth,
    layout,
    snackbar,
    form,
    hellosign,
    application,
    review,
    user_invite,
    task,
    form_field_editor,
    content_management,
    company_information,
  },
})
