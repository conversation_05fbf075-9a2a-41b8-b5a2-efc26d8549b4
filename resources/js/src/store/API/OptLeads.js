import OptLead from '@/store/Models/OptLead'


export default {
    getOptLeads(agent_code, filters, options) {
        OptLead.commit((state) => {
            state.loading = true
        })
        var params = {
            ...options,
            ...filters
        }
        this.cancelQRequest("getOptLeadsQ");
        return this.getRequest('/api/private/opt_leads/agent/' + agent_code, params, "getOptLeadsQ").then(function(json) {
            //OptLead.create({ data: json.data })
            if (typeof json.meta == 'undefined') {
                return json;
            }
            OptLead.commit((state) => {
                state.loading = false
                state.total = json.meta.total
                if (json.meta.filters) {
                    state.availableFilters = json.meta.filters
                }
            })
            return json;
        })
    },

    getOptStatusOptions() {
        this.cancelQRequest("OptStatusOptions");
        return this.getRequest('/api/private/opt_leads/statuses', null, "OptStatusOptions").then(function(json) {
            return json.data;
        })
    },

    getOptLeadCounts(agent_code) {
        this.cancelQRequest("OptLeadCountQ");
        return this.getRequest('/api/private/opt_leads/agent/' + agent_code + "/counts", null, "OptLeadCountQ").then(function(json) {
            return json.data;
        })
    },

    getOptLead(lead_code, attributes) {
        this.cancelQRequest("getOptLeadQ");
        OptLead.commit((state) => {
            state.currentLead = null
        })
        var params = {};
        if (attributes) {
            var params = {
                attr: attributes.join(",")
            }
        }
        return this.getRequest('/api/private/opt_leads/' + lead_code, params, "getOptLeadQ").then(function(json) {
            //OptLead.insert({ data: json.data })
            return json.data;
        })
    },

    updateOptLead(lead) {
        var params = {};

        return this.putRequest('/api/private/opt_leads/' + lead.LeadCode, { lead: lead }).then(function(json) {
            //OptLead.insert({ data: json.data })
            return json
        })
    }
}
