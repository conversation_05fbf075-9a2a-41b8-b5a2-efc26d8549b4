import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {},
  getters: {},
  mutations: {},
  actions: {
    getFields(dispatchable, page) {
      return new Promise((resolve, reject) => {
        if (!page) {
          page = 1
        }
        http
          .get(`/api/home-office/field-names?page=${page}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    saveFields(dispatchable, page) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/home-office/field-name`, {
            page: page,
          })
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
  },
}
