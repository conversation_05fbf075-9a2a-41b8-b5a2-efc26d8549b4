import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    pages: {},
    tick: 0,
    loading: false,
  },
  getters: {
    loading: state => state.loading,
    activeReviewer(state) {
      return pageId => {
        if (!pageId) return null
        return state.pages[pageId] && state.pages[pageId].name
      }
    },
    tick(state) {
      return state.tick
    },
  },
  mutations: {
    SET_LOADING(state, data) {
      state.loading = data
    },
    SET_PAGELIST(state, data) {
      state.pages = data
      state.tick = state.tick + 1
    },
    LOCK_PAGE(state, { pageId, data }) {
      state.pages = {
        ...state.pages,
        [pageId]: data,
      }
      state.tick = state.tick + 1
    },
    UNLOCK_PAGE(state, { pageId }) {
      delete state.pages[pageId]
      state.tick = state.tick + 1
    },
  },
  actions: {
    setPageList({ commit }, data) {
      commit('SET_PAGELIST', data)
    },
    lockPage({ commit }, payload) {
      commit('LOCK_PAGE', payload)
    },
    unlockPage({ commit }, payload) {
      commit('UNLOCK_PAGE', payload)
    },

    getNotes({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/application-review/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    saveNote({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/application-review/save`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    resolveNote({ commit }, { appId, fixed }) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .patch(`/api/application-review/resolve/${appId}`, {
            fixed: fixed ? 1 : 0,
          })
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
  },
}
