import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    loading: false,
    information: {},
  },
  getters: {
    loading: state => state.loading,
    states(state) {
      return state.states
    },
  },
  mutations: {
    SET_LOADING(state, data) {
      state.loading = data
    },
    SET_INFO(state, data) {
      state.information = data
    },
  },
  actions: {
    getContent({ commit }, user_id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/company-information/content/${user_id}`)
          .then(response => {
            resolve(response);
          })
          .catch(err => reject(err))
      })
    },
    saveContent({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/company-information/content/save`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
  },
}
