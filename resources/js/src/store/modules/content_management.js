import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    loading: false,
    states: [],
  },
  getters: {
    loading: state => state.loading,
    states(state) {
      return state.states
    },
  },
  mutations: {
    SET_LOADING(state, data) {
      state.loading = data
    },
    SET_STATES(state, data) {
      state.states = data
    },
  },
  actions: {
    getStateList({ commit }) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/content-management/states`)
          .then(response => {
            commit('SET_STATES', response.data)
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getContentList({ commit }, queryString) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/content-management/content-list?${queryString}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    getContent(dispatchable, content_id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/content-management/content/${content_id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    saveContent(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        if (formData.content_id) {
          http
            .put(`/api/content-management/content/${formData.content_id}`, formData)
            .then(response => {
              resolve(response)
            })
            .catch(err => reject(err))
        } else {
          http
            .post(`/api/content-management/content`, formData)
            .then(response => {
              resolve(response)
            })
            .catch(err => reject(err))
        }
      })
    },
    getGuideContent() {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/guide/content/`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },

    getContactContent(dispatchable, content_id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/guide/state/${content_id}/contact`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    pushContactContent(dispatchable, data) {
      return new Promise((resolve, reject) => {
        http
          .post(
            `/api/content-management/content/${data.content_id}/contact/${
              data.formData.id == undefined ? 0 : data.formData.id
            }`,
            data.formData,
          )
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },

    getFaqStateLicensing(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/license/faq/${id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    pushFaqStateLicensing(dispatchable, data) {
      console.info(data)
      return new Promise((resolve, reject) => {
        http
          .post(`/api/license/faq/${data.id == undefined || data.id == null ? 0 : data.id}`, data)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },

    removeFaqStateLicensing(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .delete(`/api/license/faq/${id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
  },
}
