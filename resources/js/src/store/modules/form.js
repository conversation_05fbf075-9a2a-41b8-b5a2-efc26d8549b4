import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    loading: false,
    saving: false,
    loadingOptions: false,
  },
  getters: {
    loading: state => state.loading,
    saving: state => state.saving,
    loadingOptions: state => state.loadingOptions,
  },
  mutations: {
    SET_LOADING(state, value) {
      state.loading = value
    },
    SET_SAVING(state, value) {
      state.saving = value
    },
    SET_LOADING_OPTIONS(state, value) {
      state.loadingOptions = value
    },
  },
  actions: {
    getOptions({ commit }, postData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING_OPTIONS', true)
        http
          .post('/api/field/options', postData)
          .then(({ data }) => {
            resolve(data)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => commit('SET_LOADING_OPTIONS', false))
      })
    },
    saveField({ commit }, postData) {
      return new Promise((resolve, reject) => {
        commit('SET_SAVING', true)
        http
          .post('/api/field/save', postData)
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            commit('SET_SAVING', false)
          })
      })
    },
    saveFieldBulk({ commit }, postData) {
      return new Promise((resolve, reject) => {
        commit('SET_SAVING', true)
        http
          .post('/api/field/save_bulk', postData)
          .then(res => {
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
          .finally(() => {
            commit('SET_SAVING', false)
          })
      })
    },
  },
}
