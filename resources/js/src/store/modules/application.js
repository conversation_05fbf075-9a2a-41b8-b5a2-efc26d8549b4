import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    loading: false,
  },
  getters: {
    loading: state => state.loading,
  },
  mutations: {
    SET_LOADING(state, data) {
      state.loading = data
    },
  },
  actions: {
    getApplications({ commit }, queryString) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/application/list?${queryString}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    getApplicationDetailForReview({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/application/view/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    getApplicationDetailForTrack({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/application/track/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    getVectorResults({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/application/vector/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    updateApplication({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/application/update`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    approveApplicationNpn({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/application/clear_npn_flag/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    rejectApplicationNpn({ commit }, appId) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/application/reject_application_npn/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    verifyNpn({ commit }, formData) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/application/verify_npn`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    toggleBlacklist({ commit }, appId) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/application/toggle_blacklist/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getInternalNotes({ commit }, appId) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/application/internal_notes/${appId}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    saveInternalNote({ commit }, formData) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/application/internal_notes`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getCurrentStep() {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/application-status/current-step`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getViewForm(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/viewform`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getStatusOptions() {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/application/status-options`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getOnboardSteps({ commit }) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/onboard-steps`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    getImage({ commit }, file_id) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/file/upload/${file_id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    deleteImage({ commit }, file_id) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .delete(`/api/file/upload/${file_id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    adminResetPassword({ commit }, payload) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/admin/reset-password/${payload.id}`, { password: payload.password })
          .then(res => {
            resolve(res)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    adminUpdateEmail({ commit }, payload) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/admin/update-email/${payload.id}`, { 
            email: payload.email,
            name: payload.name 
          })
          .then(res => {
            resolve(res)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    updateUser(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/api/user/'+formData.user_id, formData)
          .then(res => {
            resolve(res)
          })
          .catch(err => reject(err))
      })
    },
    sendReminder(dispatchable, user_id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/application/reminder/${user_id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    }
  },
}
