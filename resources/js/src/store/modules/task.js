import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    loading: false,
  },
  getters: {
    loading: state => state.loading,
  },
  mutations: {
    SET_LOADING(state, data) {
      state.loading = data
    },
  },
  actions: {
    getTasks({ commit }, queryString) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .get(`/api/tasks/get?${queryString}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    createTask({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/tasks/create`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
    completeTask({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOADING', true)
        http
          .post(`/api/tasks/complete`, formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOADING', false))
      })
    },
  },
}
