import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {},
  getters: {},
  mutations: {},
  actions: {
    getCorporateDivision(){
      return new Promise((resolve, reject) => {
        http
          .get('/api/user/corporate-division')
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getContractLevels() {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user/contract-levels')
          .then(response => {
            response.data.push("0")
            response.data = response.data.map(level => {
              return {
                label: level == "0" ? "0% - LOA" : level, 
                value: level
              }
            })
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getStates() {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user/states')
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getAssignableUplines(dispatchable, agent_code) {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user/assignable-uplines?agent_code='+agent_code)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    sendInvitePreflight(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/api/user-invites-preflight', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    sendInvite(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/api/user-invites', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    searchCandidateExists(dispatchable, formData) {
      console.log("doing search", formData)
      return new Promise((resolve, reject) => {
        http
          .post('/api/search-candidate-exists', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    sendDemoInvite(dispatchable, params){
      return new Promise((resolve, reject) => {
        http
          .post('/api/request-demo-invite', params)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getMyInvitations(dispatchable, params) {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user-invites', { params })
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getInvitation(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .get(`/api/user-invites/${id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },

    updateInvitation(dispatchable, formData) {
      formData.append('_method', 'PUT');
      return new Promise((resolve, reject) => {
        http
          .post(`/api/user-invites/${formData.get('id')}`, formData, {
            headers: {
              'Accept': 'application/json'
            }
          })
          .then(response => {
            resolve(response);
          })
          .catch(err => reject(err))
      })
      // return new Promise((resolve, reject) => {
      //   http
      //     .put(`/api/user-invites/${formData.get('id')}`, formData)
      //     .then(response => {
      //       resolve(response)
      //     })
      //     .catch(err => reject(err))
      // })
    },

    deleteInvitation(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .delete(`/api/user-invites/${id}`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    resendInvitation(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/user-invites/${id}/resend`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    getUserInvitesGroups(dispatchable) {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user-invites-groups')
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    approveUserInvitesGroups(dispatchable, id) {
      return new Promise((resolve, reject) => {
        http
          .post(`/api/user-invites-groups/${id}/approve`)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    saveNewGroup(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/api/user-invites-groups', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    updateGroupStatus(dispatchable, item) {
      http
        .post(`/api/user-invites-groups/${item.id}/active/${item.active}`)
        .then(response => {
          resolve(response)
        })
        .catch(err => reject(err))
    }
  },
}
