import http from '@/plugins/http'

export default {
  namespaced: true,
  state: {
    authenticated: false,
    user: {},
    loggingIn: false,
  },
  getters: {
    authenticated(state) {
      return state.authenticated
    },
    user(state) {
      return state.user
    },
    hasUserRole(state) {
      return role => {
        if (!role) return true

        let qRoles

        if (typeof role == 'string') qRoles = [role]
        else qRoles = role

        return state.user.roles.filter(r => qRoles.includes(r.name)).length > 0
      }
    },
    hasUserPermission(state) {
      return permission => {
        if (!permission) return true

        let ret = false
        state.user.roles.every(role => {
          ret ||= role.permissions.findIndex(p => p.name === permission) !== -1
          return !ret
        })

        return ret
      }
    },
    isSignatureSigned(state) {
      return state.user.is_signature_signed;
    }
  },
  mutations: {
    SET_AUTHENTICATED(state, value) {
      state.authenticated = value
    },
    SET_LOGGING_IN(state, value) {
      state.loggingIn = value
    },
    SET_USER(state, value) {
      state.user = value
    },
    SET_SIGNED(state, value) {
      state.user.is_signature_signed = true;
    },
  },
  actions: {
    async getCookie() {
      await http.get('/sanctum/csrf-cookie')
    },
    login({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOGGING_IN', true)
        http
          .post('/auth/login', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOGGING_IN', false))
      })
    },
    register({ commit }, formData) {
      return new Promise((resolve, reject) => {
        commit('SET_LOGGING_IN', true)
        http
          .post('/auth/register', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => commit('SET_LOGGING_IN', false))
      })
    },
    getUser({ commit }) {
      return new Promise((resolve, reject) => {
        http
          .get('/api/user')
          .then(({ data }) => {
            commit('SET_USER', data)
            commit('SET_AUTHENTICATED', true)
            resolve()
          })
          .catch(() => {
            commit('SET_USER', {})
            commit('SET_AUTHENTICATED', false)
            reject()
          })
      })
    },
    setUser({ commit }) {
      commit('SET_SIGNED', true)
    },
    getInvitation({ commit }, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/auth/user-invitation', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    logout({ commit }) {
      return new Promise((resolve, reject) => {
        http
          .post('/auth/logout')
          .then(response => {
            if(response.data && response.data.auth0LogoutUrl) {
              window.location.href = response.data.auth0LogoutUrl;
              return;
            }

            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => {
            commit('SET_USER', {})
            commit('SET_AUTHENTICATED', false)
          })
      })
    },
    forgotPassword(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/auth/forgot-password', formData)
          .then(response => {
            resolve(response)
          })
          .catch(err => reject(err))
      })
    },
    resetPassword(dispatchable, formData) {
      return new Promise((resolve, reject) => {
        http
          .post('/auth/reset-password', formData)
          .then(res => {
            resolve(res)
          })
          .catch(err => reject(err))
      })
    },
  },
}
