import Vue from 'vue'

import VueMask from 'v-mask'
import { VueMaskDirective } from 'v-mask'
import VueSignaturePad from 'vue-signature-pad'
import VueCoreVideoPlayer from 'vue-core-video-player'
import LazyTube from 'vue-lazytube'

import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './i18n'
import vuetify from './plugins/vuetify'
import http from './plugins/http'

import './plugins/base'
import './plugins/chartist'
import './plugins/i18n'

Vue.use(VueSignaturePad)
Vue.use(VueCoreVideoPlayer)
Vue.use(LazyTube)

Vue.directive('mask', VueMaskDirective)
Vue.use(VueMask, {
  placeholders: {
    S: /[*0-9]/, // define new placeholder for SSN character
  },
})

Vue.config.errorHandler = function (err, vm, info) {
  console.log('Custom vue error handler: ', err, vm.name, info)
}
Vue.config.warnHandler = function (err, vm, info) {
  console.log('Custom vue warn handler: ', err, vm.name, info)
}
Vue.config.productionTip = false

Vue.prototype.$http = http

new Vue({
  router,
  store,
  i18n,
  vuetify,
  render: h => h(App),
}).$mount('#app')
