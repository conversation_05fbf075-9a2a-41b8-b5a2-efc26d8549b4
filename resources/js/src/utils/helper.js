import { trim } from 'lodash'
import moment from 'moment'
import { FORM_FIELD_CONDITION_DATES, FORM_FIELD_CONDITION_OPERATORS } from './const'

export const compare = (value, opr, operand) => {
  if (!value || !operand) return false

  const isDateCompare = moment(value, 'YYYY-MM-DD', true).isValid()

  if (!isDateCompare) {
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_EQUALS) return value == operand
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_MORE_THAN) return value > operand
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_LESS_THAN) return value < operand
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_LIKE) return operand.split(',').includes(value.toLowerCase())

    throw 'Invalid operand!'
  } else {
    let dateOperand = ''

    if (operand === FORM_FIELD_CONDITION_DATES.DATE_FIELD_7_YEARS_PRIOR) dateOperand = moment().add(-7, 'years')
    else if (operand === FORM_FIELD_CONDITION_DATES.DATE_FIELD_1_YEAR_PRIOR) dateOperand = moment().add(-1, 'year')
    else if (moment(operand, 'YYYY-MM-DD', true).isValid()) dateOperand = moment(operand)
    else dateOperand = moment()

    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_EQUALS) return moment(value) == dateOperand
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_MORE_THAN) return moment(value) > dateOperand
    if (opr === FORM_FIELD_CONDITION_OPERATORS.TYPE_LESS_THAN) return moment(value) < dateOperand

    throw 'Invalid operand!'
  }
}

export const isValueIdentical = (val1, val2) => {
  if (typeof val1 !== typeof val2) return false
  if (typeof val1 === 'string')
    return trim(val1) == trim(val2);
  return JSON.stringify(val1) === JSON.stringify(val2)
}

export const extractObjectToArray = obj => {
  if (!obj) return []

  if (typeof obj !== 'object') return obj

  let ret = []
  Object.keys(obj).map(k => (ret[k] = obj[k]))

  return ret
}

export const mergeDeep = (target, ...sources) => {
  if (!sources.length) return target
  const source = sources.shift()

  if (typeof target === 'object' && typeof source === 'object') {
    Object.keys(source).map(key => {
      if (typeof source[key] === 'object') {
        if (!target[key]) Object.assign(target, { [key]: {} })
        mergeDeep(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    })
  }

  return mergeDeep(target, ...sources)
}

export const getXSRFToken = () => {
  let cookieDict = document.cookie
    .split(';')
    .map(a => a.split('=').map(a => a.trim()))
    .map(a => {
      let k = a[0],
        v = a[1]
      return { [k]: v }
    })
    .reduce((combinedObj, o) => {
      let key = Object.keys(o)[0]
      combinedObj[key] = o[key]

      return combinedObj
    }, {})

  return decodeURIComponent(cookieDict['XSRF-TOKEN'])
}

export const validateEmail = em => {
  // simplifying the regex to check if the email is valid,
  // this is to avoid the old regex from throwing errors
  return /^\S+@\S+\.\S+$/.test(em)

  // const re =
  //    /^(?=[^@]*[A-Za-z])([a-zA-Z0-9])(([a-zA-Z0-9])*([\.])?([a-zA-Z0-9]))*@(([a-zA-Z0-9\-])+(\.))+([a-zA-Z]{2,4})+$/i
  //   // Old regex: // /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/

  let re = /^(([a-zA-Z0-9])+[^\!\#\$\%\&\~\+\@])*([a-zA-Z0-9])+@([a-zA-Z0-9])+([^\!\#\$\%\&\~\+\@])*([a-zA-Z0-9])+$/i
  if(!re.test(String(em).toLowerCase())) // overall format
    return false;

  re = /^(((?!\-))(xn\-\-)?[a-z0-9\-_]{0,61}[a-z0-9]{1,1}\.)*(xn\-\-)?([a-z0-9\-]{1,61}|[a-z0-9\-]{1,30})\.[a-z]{2,}$/i
  if(!re.test(String(em.split("@")[1]).toLowerCase())) // domain format
    return false;

  return true;
}

export const validateSSN = () => {
  return true
}

export const validatePhone = () => {
  return true
}

export const abbrString = (str, maxLength = 50) => {
  return str.length > maxLength ? str.substring(0, maxLength) + '...' : str
}

export const narrateDate = (dateUTC, bOffset = true) => {
  if (!dateUTC) return ''

  const now = moment()
  const input = moment(dateUTC)
  const diff = now.diff(input, 'hours')

  let offset_minutes = 0
  if(bOffset)
    offset_minutes = moment().utcOffset()

  if(diff < 24)
    return moment(input).add(offset_minutes, 'minutes').fromNow()

  return moment(input).add(offset_minutes, 'minutes').format('MMMM D, YYYY [at] h:mm a')
}
