export const PAGE_TITLES = {
  register: {
    title: 'New Agent Registeration',
    subtitle: "Please Confirm The Information Below And Click 'Register' To Activate Your Account",
  },
  portal: {
    title: 'Onboarding Portal',
    subtitle: '',
  },
  onboard: {
    title: 'Onboarding Application',
    subtitle: 'Please complete the application form below',
  },
  my_task: {
    title: 'My Tasks',
    subtitle: 'View & Manage Your Assigned Tasks',
  },
  help_center: {
    title: 'Help Center',
    subtitle: 'Guide For Using The Onboarding Portal',
  },
  privacy_policy: {
    title: 'Privacy Policy',
    subtitle: '',
  },
  send_invitation: {
    title: 'Send Invitation',
    subtitle: 'Send Application Invites To New Candidates',
  },
  edit_invitation: {
    title: 'Edit Invitation',
    subtitle: 'Edit Application Invites To New Candidates',
  },
  view_my_invites: {
    title: 'View My Invitations',
    subtitle: 'View All Invitations Sent Out',
  },
  applications: {
    title: 'Applications',
    subtitle: 'Find And View Applications',
  },
  all_applications: {
    title: 'All Applications',
    subtitle: 'Find And View All Applications',
  },
  pending_applications: {
    title: 'Pending Applications',
    subtitle: 'Find And View Currently Pending Applications',
  },
  completed_applications: {
    title: 'Completed Applications',
    subtitle: 'Find And View Previously Completed Applications',
  },
  track_applications: {
    title: 'Track Applications',
    subtitle: 'Real-Time Application Status Tracker',
  },
  group_invitations: {
    title: 'Group Invitations',
    subtitle: 'Find and Approve Group Invitations',
  },
  track_application_entry: {
    title: 'Track Applications',
    subtitle: 'Real-Time Application Status Tracker',
  },
  application_review: {
    title: 'Review',
    subtitle: 'Review a submitted application',
  },
  content_management: {
    title: 'Content Management',
    subtitle: 'Edit Content',
  },
  content_management_all: {
    title: 'Content Management',
    subtitle: 'Edit Content',
  },
  content_list: {
    title: 'Content Management',
    subtitle: 'Content List',
  },
  form_fields: {
    title: 'Form Fields Editor',
    subtitle: 'Update Application Question Text',
  },
  guide: {
    title: 'License Guide',
    subtitle: 'Guide',
  },
  preview_application_form: {
    title: 'Onboarding Application',
    subtitle: 'Preview Application Form',
  },
  company_information: {
    title: 'Company Information',
    subtitle: 'Information Page',
  },
  emails: {
    title: 'Test Emails',
    subtitle: 'View Test Emails',
  },
}

export const FORM_SECTION_FILL_MODES = {
  FILLMODE_MANUAL: 'manual',
  FILLMODE_GOOGLE_AUTOCOMPLETE: 'google_autocomplete',
}

export const FORM_FIELD_TYPES = {
  TYPE_TEXT: 'text',
  TYPE_EMAIL: 'email',
  TYPE_PASSWORD: 'password',
  TYPE_PHONE: 'phone',
  TYPE_SSN: 'ssn',
  TYPE_EIN: 'ein',
  TYPE_ROUTING_NUMBER: 'routing_number',

  TYPE_SELECT: 'select',

  TYPE_DATE: 'date',
  TYPE_DOB: 'dob',

  TYPE_HTML: 'html',
  TYPE_TEXTAREA: 'textarea',

  TYPE_UPLOAD: 'upload',
  TYPE_CHECKBOX: 'checkbox',

  TYPE_SIGNATURE: 'signature',

  TYPE_CONFIRM: 'confirm',
  TYPE_EMAIL_CONFIRMATION: 'email_confirmation',
}

export const FORM_FIELD_SIZES = {
  WIDTH_FULL: 'full-width',
  WIDTH_HALF: 'half-width',
  WIDTH_THIRD: 'third-width',
  WIDTH_FOURTH: 'third-fourth',
}

export const FORM_FIELD_CONDITION_ACTION_TYPES = {
  ACTION_SHOW: 'show',
  ACTION_HIDE: 'hide',
  ACTION_REPEAT_DATE: 'repeat',
}

export const FORM_FIELD_CONDITION_OPERATORS = {
  TYPE_EQUALS: '=',
  TYPE_MORE_THAN: '>',
  TYPE_LESS_THAN: '<',
  TYPE_LIKE: 'LIKE',
}

export const FORM_FIELD_CONDITION_DATES = {
  DATE_FIELD_7_YEARS_PRIOR: '-7 years',
  DATE_FIELD_1_YEAR_PRIOR: '-1 year',
}

export const FORM_FIELD_AUTOSAVE_DURATION = 5000

export const FORM_OPTION_TTL = 60 * 60 // 60 mins

export const FILE_UPLOAD_FILEBIN = 'https://httpbin.org/post'
export const FILE_UPLOAD_CHUNKSIZE = 1024 * 1024 * 1

export const SFG_GLOSSARY_CONTENT = [
  {
    title: 'Onboarding Status Terminology',
    content: `
      <b>Pending Contract Signature - </b>The application has been filled out in entirety, but the New Agent has yet to sign the Independent Contractor Agreement.<br/><br/>

      <b>Pending Licensing Completion - </b>The New Agent has filled out the application and signed the Independent Contractor Agreement, but they made note that they do not have their license yet, so the application is currently waiting in their My Tasks inbox for when they are able to update and complete the licensing section.<br/><br/>

      <b>Pending Approval by Direct Upline - </b>The application has been signed by the New Agent, and it is under review by the Direct Upline's office.<br/><br/>

      <b>Pending Approval by Agency Owner - </b>The application has been signed by the New Agent, and it is under review by the Agency Owner's office.<br/><br/>

      <b>Submitted to Onboarding Dept. - </b>The Direct Upline and/or Agency Owner have reviewed the application and submitted it to the Corporate Office's Onboarding Department for final approval.<br/><br/>

      <b>Pending Approval by Onboarding Dept. - </b>The application has been assigned to an Onboarding Department employee and is currently under review for final approval.<br/><br/>

      <b>Pending Revision by New Agent - </b>An error or omission in the application has been found, so it has been returned to the New Agent for revision and resubmission.<br/><br/>

      <b>Onboarding Complete - </b>The application has been approved, and the Contracting team is now working diligently to register the New Agent in our systems and complete their carrier contracts.<br/><br/>`,
  },
  {
    title: 'Invitations Terminology',
    content: `
      <b>Sent - </b>An invitation has been sent to a New Agent, but they haven't clicked the link to complete registration within the Onboarding Portal.<br/><br/>

      <b>Registered - </b>New Agent has followed the link within their invitation and has registered for the Onboarding Portal, but they haven't completed the application.<br/><br/>

      <b>Applied - </b>New Agent has completed the application<br/><br/>`,
  },
  {
    title: 'Onboarding Portal Roles',
    content: `
      <b>Agency Director</b> - Added/managed by the Home Office; Anyone with an Agency Owner under them. While they may hold a different Level of Leadership within Symmetry's overall hierarchy, for the Onboarding Portal's purposes, they have this role. Within the Portal, this user is essentially the same as an Agency Owner because they have the ability to review and approve applications, track their Team’s Invitations, add Recruiting Agents to the Portal, and send Invitations to New Agents.<br/><br/>

      <b>Agency Owner</b> - Added/managed by the Home Office; Anyone that is classified as an Agency Owner by Symmetry's Level of Leadership. This user has the ability to review and approve applications, track their Team's Invitations, add Recruiting Agents to the Portal, and send Invitations to New Agents.<br/><br/>

      <b>Recruiting Agent</b> - Sales Representative/Key Leader; Anyone that is building their business. This user can send invitations to New Agents, track applications, and view their own invitations. They have the ability to review and approve applications, but only if their upline AO selected this option when they added them to the Portal. By default, all applications pass the Recruiting Agent and flow directly to the AO. <br/><br/>`,
  },
]

export const RECRUIT_CONTRACT_LEVELS = [
  { label: '0', value: 0 },
  // { label: '70', value: 70 },
  // { label: '75', value: 75 },
  { label: '80', value: 80 },
  { label: '85', value: 85 },
  { label: '90', value: 90 },
  { label: '95', value: 95 },
  { label: '100', value: 100 },
  { label: '105', value: 105 },
  { label: '110', value: 110 },
  { label: '115', value: 115 },
  { label: '120', value: 120 },
  { label: '125', value: 125 },
  { label: '130', value: 130 },
]

export const RECRUIT_COMMISSIONS = [
  {
    label: 'Advancing',
    value: 'in_advance',
  },
  {
    label: 'As Earned',
    value: 'as_earned',
  },
]

export const RECRUIT_SOURCE = [
  {
    label: 'Zip Recruiter – recruiting funnel',
    value: 'Zip Recruiter'
  },
  {
    label: 'Opt Recruiting Lead',
    value: 'Opt Recruiting Lead'
  },
  {
    label: 'Warm Market',
    value: 'Warm Market'
  },
  {
    label: 'Cold Market - job board',
    value: 'Cold Market - job board'
  },
  {
    label: 'Cold Market - social media',
    value: 'Cold Market - social media'
  },
  {
    label: 'Other',
    value: 'Other'
  },
]

export const LICENSE_STATUS = [
  {
    label: 'Unlicensed',
    value: 'unlicensed'
  },
  {
    label: 'Licensed',
    value: 'licensed'
  }
]

let message = `We're excited that you're interested in joining us at Symmetry Financial Group! Use the link below to activate your account and complete your application.`
if(process.env.MIX_APP_TENANT == 'Q2B') {
  message = `We're excited that you're interested in working with Quility® B2B! Use the link below to activate your account and complete your contracting application.`
}
export const RECRUIT_INVITE_PERSONAL_MESSAGE_DEFAULT = message

export const USER_ROLES_TYPES = {
  RECRUIT: 'Recruit',
  UNLICENSED_AGENT: 'UnlicensedAgent',
  SALES_REP: 'SalesRep',
  AGENCY_OWNER: 'AgencyOwner',
  STAFF: 'Staff',
  SUPER_ADMIN: 'SuperAdmin',
}

export const APPLICATION_TYPES = {
  PRINCIPAL: 'b2b-principal',
  B2B_AGENT: 'b2b-agent'
}

export const ONBOARD_STATUS_TYPES = {
  EDITING: 'editing',
  UNENROLLED: 'unenrolled',
  ENROLLED: 'enrolled',
  SUBMITTED_AO: 'submitted-ao',
  SUBMITTED_HO: 'submitted-ho',
  ERROR: 'approval-error',
  AO_APPROVED: 'ao-approved',
  HO_APPROVED: 'ho-approved',
  REJECTED_AO: 'rejected-ao',
  REJECTED_HO: 'rejected-ho',
  UNLICENSED: 'created-unlicensed-account',
  ASSIGNED: 'assigned-to-onboarding-dept',
  REVISION_AO: 'revision-ao',
  REVISION_HO: 'revision-ho',
  FORCE_HQ: 'force-hq',
}

export const ONBOARD_PREVENT_NEXT = [{ label: 'Are you enrolled in a pre-licensing course?', value: 'NO' }]

export const TASK_TYPES = {
  FILL_APPLICATION: 'Fill Application',
  UPDATE_APPLICATION: 'Update Application',
  APPROVE_APPLICATION: 'Approve Application',
  CREATE_UNLICENSED_ACCOUNT: 'Create Unlicensed Account',
  ASSIGN_TO_ONBOARDING: 'Assign to Onboarding Dept',
  UNENROLLED: 'Contact AO for Pre-Licensing Course Enrollment',
  ENROLLED: 'Complete course, then register for and complete your state exam'
}

export const INTERVAL_LAZY_LOADING = 1500

export const CONTENT_TYPES = [
  {
    label: 'Company',
    value: 'company',
  },
  {
    label: 'State',
    value: 'state',
  },
]

export const HELLOSIGN_SIGNATURE_PROCESS = '__hellosign_agreement_process__'
