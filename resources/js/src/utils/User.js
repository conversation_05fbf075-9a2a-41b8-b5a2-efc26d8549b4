/**
 * User utility class with constants matching the backend
 */
class User {
  // Auth0 keys matching the backend constants
  static AUTH0_KEY_METADATA = 'http://quility.com/meta_data';
  static AUTH0_KEY_ROLES = 'http://quility.com/roles';
  static AUTH0_KEY_USERDATA = 'http://quility.com/user_data';

  // User types matching backend constants
  static ROLE_TYPE_RECRUIT = 'Recruit';
  static ROLE_TYPE_UNLICENSED_AGENT = 'UnlicensedAgent';
  static ROLE_TYPE_SALES_REP = 'SalesRep';
  static ROLE_TYPE_AGENCY_OWNER = 'AgencyOwner';
  static ROLE_TYPE_STAFF = 'Staff';
  static ROLE_TYPE_SUPER_ADMIN = 'SuperAdmin';
  static ROLE_TYPE_B2B_STAFF = 'B2BOnboardingStaff';
}

export default User;
