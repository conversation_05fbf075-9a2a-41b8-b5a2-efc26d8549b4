var websocket
var heartbeat

export function startSocket(url, { cbOpen, cbMessage }) {
  websocket = new WebSocket(url)
  websocket.onopen = function (evt) {
    onOpen(evt, cbOpen)
  }
  websocket.onclose = function (evt) {
    onClose(evt)
  }
  websocket.onmessage = function (evt) {
    onMessage(evt, cbMessage)
  }
  websocket.onerror = function (evt) {
    onError(evt)
  }
}

export function closeSocket() {
  if (websocket) {
    websocket.close()
  }
}

export function doSend(message) {
  if (typeof websocket != 'undefined') {
    if (websocket.readyState == 1) {
      websocket.send(message)
    }
  }
}
function startKeepAlive() {
  heartbeat = setInterval(function () {
    doSend(
      JSON.stringify({
        event: 'heartbeat',
        data: '00000000-0000-0000-0000-000000000000',
      }),
    )
  }, 10000)
}
function onOpen(evt, callback) {
  startKeepAlive()
  if (callback) callback(evt)
}
function onClose(evt) {
  console.log('socket disconnected.', evt)
  clearInterval(heartbeat)
}
function onMessage(evt, callback) {
  const obj = JSON.parse(evt.data)
  // console.log(obj);
  // if (obj.action == "loggedin") {
  //     // successful login event
  // }
  // if (obj.action == "pagelist") {
  //     // list of all locked pages
  // }
  // if (obj.action == "pagelock") {
  //     // new single page locked, update row
  // }
  // if (obj.action == "pageunlock") {
  //     // single page has been unlocked
  // }

  if (callback) callback(obj)
}
function onError(evt) {
  console.log('socket errored.', evt)
  clearInterval(heartbeat)
}
