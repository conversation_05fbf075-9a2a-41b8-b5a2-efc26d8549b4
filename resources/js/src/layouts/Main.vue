<template>
  <v-app>
    <v-alert dense type="warning" class="text-center mb-0" v-if="user.placeholder">
      This is a <strong>placeholder</strong> account. Please make sure you login via <strong>auth0</strong> to setup
      your account fully.
    </v-alert>
    <v-row class="width-100 ma-0">
      <v-col class="flex-grow-0 pa-0" v-if="showMode !== 'stack'">
        <symmetry-menu-side :items="sidebarMenu" :mode="showMode"></symmetry-menu-side>
      </v-col>
      <v-col class="pa-0 overflow-auto">
        <symmetry-header :username="authenticated && user.name" :mode="showMode" :items="sidebarMenu" :user="user">
          <h1 class="text-h1 white--text text-center">
            {{ title }}
          </h1>
          <hr class="ma-4" />
          <h3 class="text-h3 white--text darken-2 text-center">
            {{ subtitle }}
          </h3>
        </symmetry-header>
        <transition name="fade" mode="out-in">
          <router-view></router-view>
        </transition>
      </v-col>
    </v-row>
    <v-btn v-scroll="onScroll" v-show="fab" fab dark fixed bottom right color="primary" @click="toTop" class="mb-6">
      <v-icon large>mdi-chevron-up</v-icon>
    </v-btn>
    <symmetry-footer></symmetry-footer>
    <v-snackbar v-model="snackbar.status" :color="snackbar.color" :timeout="timeout" :top="false" :bottom="true">
      {{ snackbar.text }}
    </v-snackbar>
  </v-app>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import SymmetryHeader from './symmetry/Header'
import SymmetryMenuSide from './symmetry/MenuSide'
import SymmetryFooter from './symmetry/Footer'
import { navSymmetry as nav } from '../_nav'
import { PAGE_TITLES } from '@/utils/const'

export default {
  name: 'SymmetryMain',
  components: {
    SymmetryHeader,
    SymmetryMenuSide,
    SymmetryFooter,
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
    }),
    user() {
      return this.$store.state.auth.user
    },
    authenticated() {
      return this.$store.state.auth.authenticated
    },
    getCurrentRoute() {
      return this.$route.name
    },
    toShowMenu() {
      return !(this.getCurrentRoute === 'portal' || this.getCurrentRoute === 'privacy_policy')
    },
    sidebarMenu() {
      if (!this.authenticated) return []

      /* Filter Main Links */
      let menu_sidebar = nav(this.$i18n).filter(
        menu => {
          if(menu.show != null){
            return this.hasUserPermission(menu.permission) && this.hasUserRole(menu.role) && menu.show == 'true';
          }else{
            return this.hasUserPermission(menu.permission) && this.hasUserRole(menu.role);
          }
        }
      )

      /* Filter SubLinks */
      menu_sidebar.forEach(option => {
        if (option.subLinks) {
          let links = option.subLinks.filter(
            link => {
              if(link.show != null){
                return this.hasUserPermission(link.permission) && this.hasUserRole(link.role) && link.show == 'true';
              }else{
                return this.hasUserPermission(link.permission) && this.hasUserRole(link.role)
              }
            },
          )
          option.subLinks = links
        }
      })

      return menu_sidebar
    },
    title() {
      const route = this.getCurrentRoute

      if (PAGE_TITLES[route]) return PAGE_TITLES[route].title

      return 'Default Title'
    },
    subtitle() {
      const route = this.getCurrentRoute

      if (PAGE_TITLES[route]) return PAGE_TITLES[route].subtitle

      return 'Default SubTitle'
    },
    showMode() {
      switch (this.$vuetify.breakpoint.name) {
        case 'xs':
        case 'sm':
          return 'stack'
        case 'md':
          return 'temporary'
      }
      return 'permanent'
    },
  },
  data() {
    return {
      fab: false,
      timeout: 3000,
      snackbar: {
        text: this.$store.state.snackbar.text,
        status: this.$store.state.snackbar.status,
        color: this.$store.state.snackbar.color,
      },
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
    }),
    onScroll(e) {
      if (typeof window === 'undefined') return
      const top = window.pageYOffset || e.target.scrollTop || 0
      this.fab = top > 20
    },
    toTop() {
      this.$vuetify.goTo(0)
    },
  },
  watch: {
    '$store.state.snackbar.text'(val) {
      this.snackbar.text = val
    },
    '$store.state.snackbar.status'(val) {
      if (val) {
        this.snackbar.status = true
      }
    },
    '$store.state.snackbar.color'(val) {
      this.snackbar.color = val
    },
    'snackbar.status'(val) {
      if (!val) {
        this.setSnackbar({ status: false })
      }
    },
  },
}
</script>

<style lang="sass" scoped></style>
