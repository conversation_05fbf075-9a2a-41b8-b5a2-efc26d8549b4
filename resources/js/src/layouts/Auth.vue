<template>
  <v-app>
    <v-row class="auth-container ma-0 pa-0" justify="center">
      <v-col cols="12" sm="12" md="6" lg="5" xl="4" class="seg h-100 py-4" style="max-height:100vh; overflow:auto;">
        <v-layout align-center justify-center flex-column>
          <div class="text-center mb-5">
            <router-link :to="{ name: 'portal' }">
              <img :src="images.hq_logo" height="175" class="pa-4" style="max-width: 300px;" />
            </router-link>
          </div>

          <transition name="fade" mode="out-in">
            <router-view @changeLoginTab="updateSlider"></router-view>
          </transition>

          <router-link class="link mt-2" :to="{ name: 'privacy_policy' }"> Privacy Policy </router-link>
        </v-layout>
      </v-col>
      <v-col xs="0" sm="0" md="6" lg="7" xl="8" class="seg h-100 pa-0 d-none d-md-flex" :style="`height:100vh;background-image:url(${images.bg_auth});background-size:cover;background-position: center center;`">
        <v-row class="align-content-center">
          <v-col cols="12" lg="8" class="d-flex align-content-center flex-wrap px-5 pt-8" style="position: relative; margin: auto;">
            <q-page-slider v-if="showSlider" :login_tab="current_login_tab" slug="homepage-slider"></q-page-slider>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-app>
</template>

<script>
import QPageSlider from "@/components/utils/QPageSlider.vue";

export default {
  props: {
    value: Number,
    total: Number,
    description: String,
  },
  components: {
    QPageSlider
  },
  data() {
    return {
      images: {
        hq_logo: process.env.MIX_APP_TENANT_LOGO,
        bg_auth: process.env.MIX_APP_AUTH_BACKGROUND_IMAGE ?? 'https://hq-image-cdn.azureedge.net/servers/production/cms_images/Quility-Onboarding-Portal-Background_JvY0s6.png',
      },  
      current_login_tab: localStorage.login_tab ? parseInt(localStorage.login_tab) : 0
    }
  },
  mounted() {
    console.log("bg img",process.env.MIX_APP_AUTH_BACKGROUND_IMAGE, this.images.bg_auth)
  },
  computed: {
    showSlider: function() {
      return process.env.MIX_SHOW_LOGIN_SLIDER == "true"
    }
  },
  methods: {
    updateSlider: function(tab) {
      this.current_login_tab = tab
    }
  }
}
</script>

<style scoped lang="scss">
.auth-container {
  background-color: #f0f3f2;
}
</style>
