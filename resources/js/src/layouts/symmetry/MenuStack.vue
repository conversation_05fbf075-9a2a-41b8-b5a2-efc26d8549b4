<template>
  <v-container class="menu-stack">
    <v-list-item class="pa-0 ma-0">
      <v-container class="ma-0 d-flex flex-row justify-space-between">
        <v-img
          class="sidebar-logo"
          :src="images.logo"
          @click="goTo('/')"
          :style="{
            maxWidth: !$vuetify.breakpoint.xsOnly ? '150px' : '100px',
          }"
        ></v-img>
        <v-icon large @click="toggleMenu()">{{ showMenu ? 'mdi-close' : 'mdi-menu' }}</v-icon>
      </v-container>
    </v-list-item>

    <v-list dense nav class="items" v-if="showMenu">
      <v-list-item-group v-model="menuIndex" mandatory color="primary darken-3">
        <v-list-item
          v-for="item in menuItems"
          :key="item.text"
          link
          @click="goTo(item.link)"
          aria-selected="true"
          :class="`menu-content pt-2 pb-2 pl-8 mb-0 ${item.hide && 'd-none'}`"
        >
          <v-list-item-icon>
            <v-icon>{{ item.icon }}</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title>{{ item.text }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item key="download_ica" v-if="authenticated && isSignatureSigned" class="menu-content pt-2 pb-2 pl-8 mb-0" href="/api/user/download_ica">
          <v-list-item-icon>
            <v-icon class="mr-2">mdi-download</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title>Download Agreement</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item key="logout" v-if="authenticated" class="menu-content pt-2 pb-2 pl-8 mb-0" @click="logout">
          <v-list-item-icon>
            <v-icon class="mr-2">mdi-logout</v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title>{{ $i18n.t('menu.log_out') }}</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list-item-group>
    </v-list>
  </v-container>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'SymmetryMenuStack',
  props: {
    items: Array,
    mode: String,
  },
  components: {},
  data() {
    return {
      showMenu: false,
      menuIndex: null,
      images: {
        logo: process.env.MIX_APP_TENANT_LOGO ?? require('@resources/images/logo-stacked.png').default,

      },
    }
  },
  computed: {
    ...mapGetters('auth', ['authenticated']),
    ...mapGetters({
      isSignatureSigned: 'auth/isSignatureSigned'
    }),
    menuItems() {
      return this.items.filter(item => {
        if (item.tenants) {
          return item.tenants.includes(this.tenant);
        }
        return true;
      });
    },
    tenant() {
      return process.env.MIX_APP_TENANT
    },
  },
  created() {
    this.updateMenuIndex()
  },
  watch: {
    '$route.name'() {
      this.updateMenuIndex()
    },
  },
  methods: {
    ...mapActions({
      signOut: 'auth/logout',
    }),
    updateMenuIndex() {
      this.menuIndex = this.items.findIndex(item => item.link === this.$route.name)
    },
    goTo(link) {
      this.showMenu = false
      this.$router.push(link)
    },
    toggleMenu() {
      this.showMenu = !this.showMenu
    },
    async logout() {
      await this.signOut().finally(() => {
        this.$router.push({ name: 'login' })
      })
    },
  },
}
</script>

<style lang="sass" scoped>
.menu-stack
  position: relative
  overflow: visible
  box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%)

  .sidebar-close
    position: absolute
    top: 50%
    right: 10px
    z-index: 20

  .sidebar-logo
    cursor: pointer

    &:hover
      opacity: 0.7

    &.v-image__image
      background-size: contain


  .menu-content
    border-bottom: 1px solid #E9EEE3

    .v-list-item__title
      font-size: 15px !important
      line-height: 1

    .v-list-item__icon
      margin-right: 15px !important

  .items
    position: absolute
    left: 0
    width: 100%
    top: 100%
    z-index: 100
</style>
