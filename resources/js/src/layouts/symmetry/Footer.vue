<template>
  <v-footer class="my-footer">
    <v-container fluid class="bar primary darken-4 ma-0 d-flex flex-row align-center">
      <v-icon
        v-if="!$vuetify.breakpoint.xsOnly"
        large
        class="white--text darken-2 mr-3 pa-2"
        @click="toggleFooterMenu()"
      >
        {{ !showMenu ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
      </v-icon>
      <span class="flex-grow-1 text-h5 text-uppercase white--text"
        >{{ copyrightText }}. ALL RIGHTS RESERVED. <span class="mx-2">|</span> v{{version}} <span class="mx-2">|</span> 
          <a v-if="tenant == 'Q2B'" text dark href="https://quility.jotform.com/243383789517067" target="_blank" style="text-decoration: none;">Report a Problem</a> 
          <!-- <span class="mx-2" v-if="showChangeLogButton()">|
            <a text dark @click="show_change_log = true" class="ml-2">Release Notes</a>
          </span> -->
      </span>
      <v-icon v-if="$vuetify.breakpoint.xsOnly" large color="white darken-2" @click="scrollTop()">
        mdi-chevron-up
      </v-icon>
    </v-container>
    <v-container
      v-if="tenant != 'Q2B' && (showMenu || $vuetify.breakpoint.xsOnly)"
      fluid
      class="inner primary darken-3 white--text ma-0"
      :style="{
        padding: !$vuetify.breakpoint.xsOnly ? '30px 100px' : '20px 30px',
      }"
    >
      <v-row>
        <v-col cols="12" md="4" key="important-links">
          <h4 class="section">Important Links</h4>
          <a v-bind:key="index" :href="link.url" class="link" v-for="(link, index) in links">{{ link.title }}</a>
          <a class="link" v-if="authenticated" @click="logout">Log out</a>
        </v-col>
        <v-col cols="12" md="4" key="follow-us">
          <h4 class="section">Follow Us</h4>
          <a class="footer-icon-link" href="https://twitter.com/sfglife" rel="noopener" target="_blank">
            <v-icon key="icon-twitter" class="icon-social" color="white darken-2"> mdi-twitter </v-icon>
          </a>
          <a
            class="footer-icon-link"
            href="https://www.facebook.com/SymmetryFinancialGroup"
            rel="noopener"
            target="_blank"
          >
            <v-icon key="icon-facebook" class="icon-social" color="white darken-2"> mdi-facebook </v-icon>
          </a>
          <a class="footer-icon-link" href="https://www.instagram.com/sfglife/" rel="noopener" target="_blank">
            <v-icon key="icon-instagram" class="icon-social" color="white darken-2"> mdi-instagram </v-icon>
          </a>
          <a
            class="footer-icon-link"
            href="https://www.linkedin.com/company/symmetryfinancialgroup"
            rel="noopener"
            target="_blank"
          >
            <v-icon key="icon-linkedin" class="icon-social" color="white darken-2"> mdi-linkedin </v-icon>
          </a>
        </v-col>
        <v-col cols="12" md="4" key="better-business-bureau">
          <h4 class="section">Better Business Bureau</h4>
          <a
            target="_blank"
            rel="noopener"
            href="https://www.bbb.org/us/nc/swannanoa/profile/insurance-companies/symmetry-financial-group-0473-92005516"
          >
            <v-img :src="images.logo" class="bbb-logo"> </v-img>
          </a>
        </v-col>
      </v-row>
    </v-container>
    <v-dialog v-model="show_change_log" max-width="800" scrollable>
      <v-card>
        <v-card-title>
          Release Notes
        </v-card-title>
        <v-card-text>
          <h3 class="mt-4">May 8, 2024</h3>
          <ul>
            <li>Fixed NPN Approval update process.</li>
            <li>Added ability to deny NPN flagged app.</li>
            <li>Added questions about existing carrier contracts.</li>
            <li>Improved error handling when pushing to HQ during approval process.</li>
            <li>Added switch to show/hide stale application on the pending applications page.</li>
            <li>Fixed applications list to include Lapsed and Stalled agents so that upline AOs can see all applications. </li>
          </ul>
          <h3 class="mt-4">Dec 4, 2023</h3>
          <ul>
            <li>Revisions UI updated to better highlight needed changes.</li>
          </ul>
          <h3 class="mt-4">Nov 8, 2023</h3>
          <ul>
            <li>Added ability to manually mark tasks as complete in My Tasks.</li>
            <li>Added ability to mark an invitation as part of an onboarding group.</li>
            <li>Updates AML training content to link to WebCE.</li>
            <li>Fixed a bug that caused the Sign Agreement button to fail.</li>
          </ul>
          <h3 class="mt-4">Oct 11, 2023</h3>
          <ul>
            <li>Staff can manually update an app's status.</li>
            <li>Fixes issues with onboarding higher contract levels.</li>
            <li>Adds ability for Agents to see if a candidate is already in the system before sending an invitation.</li>
          </ul>
          <h3 class="mt-4">Jul 25, 2023</h3>
          <ul>
            <li>Added date filter options to Track Applications page.</li>
            <li>Added E&O insurance purchase info.</li>
            <li>Added US Citizen checkbox.</li>
            <li>App Review button opens in new tab for staff.</li>
          </ul>
          <h3 class="mt-4">Jun 21, 2023</h3>
          <ul>
            <li>Updates and fixes to carrier selections.</li>
            <li>Updates and fixes to SSN and banking info masking.</li>
            <li>Added Advanced Markets (AM) and Transferring (T) agents indicators.</li>
            <li>Fixes to track applications specifically for large organizations.</li>
          </ul>
          <h3 class="mt-4">Jun 7, 2023</h3>
          <ul>
            <li>Improvements to saving of application data.</li>
            <li>Email content updated.</li> <!-- replace AgentSync -->
            <li>Allow staff to update an application that's in review.</li>
          </ul>
          <h3 class="mt-4">May 11, 2023</h3>
          <ul>
            <li>Implemented NIPR for NPN verification.</li>
            <li>Updated agent experience selections on invitation.</li>
            <li>Updated carrier selection logic on AO review.</li>
          </ul>
          <h3 class="mt-4">May 1, 2023</h3>
          <ul>
            <li>Added completed apps export.</li>
          </ul>
          <h3 class="mt-4">Apr 26, 2023</h3>
          <ul>
            <li>Allow staff to undo a rejection and send back to the AO.</li>
            <li>Fixed tasks not showing up for AOs in some instances.</li>
            <li>Daily approvals report email</li>
            <li>Returning agent indicator sent to HQ for sticky note creation.</li>
            <li>Advanced Markets checkbox shows on invitation for both licensed and unlicensed.</li>
            <li>Fixed duplicate comments issue on applications.</li>
            <li>Fixed timezone issue on comments</li>
            <li>Stale app login emails are modified to allow for new apps for same email address.</li>
            <li>Send additional data to HQ regarding advanced markets, agent experience, and carrier selections.</li>
          </ul>
          <h3 class="mt-4">Apr 25, 2023</h3>
          <ul>
            <li>PHP and Laravel Updates</li>
          </ul>
          <h3 class="mt-4">Mar 15, 2023</h3>
          <ul>
            <li>Added date range filter to Track Applications page.</li>
            <li>Notifications formatting changes.</li>
            <li>Auto refresh of notifications.</li>
            <li>Changes to legal questions section.</li>
            <li>Added documents for transferring agents.</li>
            <li>Fixes missing agent code for some agents when logging in.</li>
            <li>Added caching of downline queries for speed.</li>
            <li>Fixed link in rejected email template.</li>
            <li>Adjusted saving of app data to avoid database deadlocking.</li>
            <li>Added ability for Agency Owners to adjust upline and commission level of an app before approval.</li>
            <li>Added searching capability when changing upline on app.</li>
          </ul>
          <h3 class="mt-4">Feb 27, 2023</h3>
          <ul>
            <li>Added notifications in header</li>
          </ul>
          <h3 class="mt-4">Feb 17, 2023</h3>
          <ul>
            <li>Fixes issue on application review page.</li>
            <li>Makes it mandatory to click the Privacy Notice link for California residents.</li>
            <li>Adds phone number to CSV download data.</li>
          </ul>

          <h3 class="mt-4">Feb 15, 2023</h3>
          <ul>
            <li>Adds additional fields to invitation form for license status, experience, and Advanced Markets agents.</li>
            <li>Adds carrier selections to AO review form. AOs will need to select initial carriers for contracting when approving an application.</li>
          </ul>

          <h3 class="mt-4">Feb 2, 2023</h3>
          <ul>
            <li>Quick update to add Privacy Notice for California Contractors.</li>
          </ul>

          <h3 class="mt-4">Feb 1, 2023</h3>
          <ul>
            <li>Login: Added image sliders to the login page. One for candidates and another for agents/staff, depending on which login tab is active.</li>
            <li>Track Applications: Added "Stale Applications" filter for apps that have been inactive for more than 30 days.</li>
            <li>Track Applications: Apps with no activity for more than 60 days are filtered out to reduce clutter.</li>
            <li>Track Applications: Stale applications will have a new "Send Reminder" button to send an email to the candidate, inviting them to come back to update the application.</li>
          </ul>

          <h3 class="mt-4">Jan 18, 2023</h3>
          <ul>
            <li>New version notification and release notes.</li>
            <li>Fixes timezone differences in date/time display.</li>
            <li>Agency Owners now receive emails when a new app is ready to review.</li>
            <li>Improvements to Track Applications page/table (filter, search, sort, remembers settings).</li>
          </ul>

        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="show_change_log = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-snackbar
      v-model="show_new_version_alert"
      :multi-line="true"
      timeout="-1"
      bottom left
    >
      A new version of the website is available.

      <template v-slot:action="{ attrs }">
        <v-btn
          color="green"
          text
          v-bind="attrs"
          @click="reloadPage()"
        >
          Reload
        </v-btn>
        <v-btn
          v-bind="attrs"
          @click="show_new_version_alert = false"
          icon
        >
          <v-icon>mdi-close-circle</v-icon>
        </v-btn>
      </template>
    </v-snackbar>
  </v-footer>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import http from '@/plugins/http'
import { USER_ROLES_TYPES } from '@/utils/const'

export default {
  name: 'SymmetryFooter',
  data() {
    return {
      showMenu: false,
      links: [
        { title: 'Who We Are', url: 'https://www.sfglife.com/about-us/' },
        { title: 'Our Privacy Policy', url: '/extra/privacy-policy' },
      ],
      images: {
        logo: require('@resources/images/bbb-logo.png').default,
      },
      version: '',
      show_change_log: false,
      show_new_version_alert: false
    }
  },
  computed: {
    ...mapGetters('auth', ['authenticated']),
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
    }),
    copyrightText() {
      return process.env.MIX_APP_COPYRIGHT_TEXT ?? `COPYRIGHT @ ${new Date().getFullYear()} SYMMETRY FINANCIAL GROUP.`
    },
    tenant() {
      return process.env.MIX_APP_TENANT
    },
  },
  mounted: function() {
    this.loadVersion()

    if(this.hasUserRole([USER_ROLES_TYPES.AGENCY_OWNER, USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.SUPER_ADMIN])){
      let g = this
      this.checkVersionTimer = setInterval(function(){
        g.loadVersion()
      }, 15*60*1000)  //every 15 min
    }
  },
  watch: {
    showMenu(val) {
      if (val) {
        const timerId = setTimeout(() => {
          window.scrollTo({
            left: 0,
            top: document.body.scrollHeight,
            behavior: 'smooth',
          })
          if (timerId) clearTimeout(timerId)
        }, 200)
      }
    },
  },
  methods: {
    ...mapActions({
      signOut: 'auth/logout',
    }),
    showChangeLogButton() {
      return this.hasUserRole([USER_ROLES_TYPES.SALES_REP, USER_ROLES_TYPES.AGENCY_OWNER, USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.SUPER_ADMIN])
    }, 
    reloadPage(){
      window.location.reload()
    },
    toggleFooterMenu(force) {
      if (typeof force !== 'undefined') this.showMenu = force
      else this.showMenu = !this.showMenu
    },
    scrollTop() {
      window.scrollTo({
        left: 0,
        top: 0,
        behavior: 'smooth',
      })
    },
    async logout() {
      await this.signOut().finally(() => {
        this.$router.push({ name: 'login' })
      })
    },
    loadVersion() {
      let currentVersion = window.localStorage.getItem('version');
      let g = this
      http
          .get('/api/version')
          .then(response => {
            if(g.version && g.version != response.data)
              g.show_new_version_alert = true
            else
              g.version = response.data
            window.localStorage.setItem('version', response.data)
          })
    }
  },
}
</script>

<style lang="sass" scoped>
.my-footer
  position: absolute
  top: 100%
  left: 0
  padding: 0
  width: 100%

  .bar

    .v-icon
      cursor: pointer

      &:hover
        background: grey
        opacity: 0.8

  .inner

    .section
      font-size: 13px
      font-weight: 400
      line-height: 1
      letter-spacing: .5px
      text-transform: capitalize
      border-color: #46C3B2
      border-width: 0 0 0 3px
      border-style: solid
      padding-left: 15px
      margin: 0 0 20px

    .link
      font-size: 15px
      text-decoration: none
      color: white
      line-height: 30px
      display: block
      text-transform: capitalize

      &:hover
        color: #46C3B2

    .icon-social
      padding: 15px
      margin-right: 1rem
      border: 1px solid #ddd
      border-radius: 2px

      &:hover
        background: transparent
        color: #46C3B2 !important
        border: 1px solid #46C3B2 !important

    .bbb-logo
      width: 158px
      height: 62px
      cursor: pointer

  .footer-icon-link
    text-decoration: none
</style>
