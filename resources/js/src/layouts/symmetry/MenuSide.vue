<template>
  <v-navigation-drawer
    v-model="$store.state.layout.drawer"
    :permanent="mode !== 'temporary'"
    :absolute="mode === 'temporary'"
    :temporary="mode === 'temporary'"
    width="300"
    class="menu-side"
  >
    <v-icon v-if="mode === 'temporary'" large @click="toggleSidebar()" class="sidebar-close">mdi-close</v-icon>

    <v-flex class="sidebar-logo pa-4 mt-8 mb-8">
      <v-img :src="images.logo" @click="goTo('portal')"></v-img>
    </v-flex>

    <v-flex class="d-flex flex-column">
      <div v-for="item in menuItems" :key="item.link" :class="`pa-0 ma-0 ${item.hide && 'd-none'}`">
        <menu-link v-if="!item.subLinks" :text="item.text" :icon="item.icon" :link="item.link"> </menu-link>
        <menu-folder v-else :subLinks="item.subLinks" :text="item.text" :icon="item.icon"> </menu-folder>
      </div>
      <div key="download_ica" v-if="authenticated && isSignatureSigned">
        <v-flex
          :class="`menu-entry d-flex flex-row align-center justify-space-between pr-2 pt-2 pb-2 pl-6`"
        >
          <v-icon left large color="primary">
            mdi-download
          </v-icon>
          <a :class="`flex-grow-1 text-h5`" style="text-decoration:none;color:inherit;" href="/api/user/download_ica">
            Download Agreement
          </a>
        </v-flex>
      </div>
      <div key="logout" v-if="authenticated" @click="logout">
        <menu-link :text="$i18n.t('menu.log_out')" icon="mdi-logout"> </menu-link>
      </div>
    </v-flex>
  </v-navigation-drawer>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import MenuLink from '@/components/menu/MenuLink'
import MenuFolder from '@/components/menu/MenuFolder'

export default {
  name: 'SymmetryMenuSide',
  props: {
    items: Array,
    mode: String,
  },
  components: {
    MenuLink,
    MenuFolder,
  },
  computed: {
    ...mapGetters('auth', ['authenticated']),
    ...mapGetters({
      isSignatureSigned: 'auth/isSignatureSigned'
    }),
    menuItems() {
      return this.items.filter(item => {
        if (item.tenants) {
          return item.tenants.includes(this.tenant);
        }
        return true;
      });
    },
    tenant() {
      return process.env.MIX_APP_TENANT
    },
  },
  data() {
    return {
      drawer: true,
      menuIndex: null,
      images: {
        logo: process.env.MIX_APP_TENANT_LOGO ?? require('@resources/images/logo-stacked.png').default,
      },
    }
  },
  mounted() {
    console.log("logo",process.env.MIX_APP_TENANT_LOGO)
  },
  methods: {
    ...mapActions({
      signOut: 'auth/logout',
      setDrawer: 'layout/setDrawer',
    }),
    goTo(route) {
      this.$router.push({ name: route })
    },
    toggleSidebar() {
      this.setDrawer(!this.$store.state.layout.drawer)
    },
    async logout() {
      await this.signOut().finally(() => {
        this.$router.push({ name: 'login' })
      })
    },
  },
}
</script>

<style lang="sass" scoped>

.menu-side
  .sidebar-logo
    cursor: pointer

  .sidebar-close
    position: absolute
    top: 10px
    right: 10px
    z-index: 20

  .menu-content
    border-bottom: 1px solid #E9EEE3

    .v-list-item__title
      font-size: 16px !important
      font-weight: 400
      line-height: 1

    .v-list-item__icon
      margin-right: 10px !important
</style>
