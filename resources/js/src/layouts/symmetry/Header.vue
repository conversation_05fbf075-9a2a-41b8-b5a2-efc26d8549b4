<template>
  <div>
    <div
      class="header pa-5"
      v-if="!!username"
      :style="{ textAlign: $vuetify.breakpoint.smAndDown ? 'center' : 'right' }"
    >
      <v-btn color="warning" v-if="user.impersonated_by" class="mr-4" href="/impersonate/leave">
        <v-icon left>mdi-account-switch-outline</v-icon>
        {{$i18n.t('menu.switch_back')}}
      </v-btn>
      <v-icon v-if="mode === 'temporary'" @click="toggleSidebar()" class="nav-icon pl-3 pt-3">mdi-menu</v-icon>
      
      <!-- notification bell -->
      <v-menu v-if="showNotifications"  class="mr-4">
        <template v-slot:activator="{ on, attrs }">
          <v-btn
            text
            v-bind="attrs"
            v-on="on"
            @click="updateLastReadNotification()"
          >
            <v-badge v-if="notification_count > 0" v-bind="attrs" v-on="on" :content="notification_count" color="error">
              <v-icon>mdi-bell-outline</v-icon>
            </v-badge>
            <v-icon v-else>mdi-bell-outline</v-icon>
          </v-btn>
        </template>
        <v-list two-line style="max-height:80vh;overflow:auto;">
            <v-list-item v-for="notification in notifications" :key="notification.id">
              <v-list-item-content>
                <v-list-item-title class="text--primary" v-text="notification.notification"></v-list-item-title>
                <v-list-item-subtitle v-text="narrateDate(notification.created_at, false)" class="disabled--text" style="font-size:.75rem;"></v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
        </v-list>
      </v-menu>
      

      Hello, &nbsp; {{ username }}!
    </div>
    <symmetry-menu-stack v-if="mode === 'stack'" :items="items"></symmetry-menu-stack>
    <div class="title pa-8 primary d-flex flex-column align-center justify-center">
      <slot />
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import SymmetryMenuStack from './MenuStack'
import http from '@/plugins/http'
import { narrateDate } from '@/utils/helper'

export default {
  name: 'SymmetryHeader',
  props: {
    username: [String, Boolean],
    mode: String,
    items: Array,
    user: Object,
  },
  components: {
    SymmetryMenuStack,
  },
  data() {
    return {
      interval: null,
      notifications: []
    }
  },
  mounted: function() {
    this.notifications = this.user.notifications
    let g = this
    this.interval = setInterval(() => {
      http.get('/api/user/notifications')
        .then(response => {
          g.notifications = response.data
        })
    }, 1000*60*5)
  },
  computed: {
    showNotifications() {
      return this.notifications.length > 0
    },
    notification_count() {
      let new_notifications = this.notifications.filter(notification => {
        return notification.created_at > this.user.last_read_notification
      })
      return new_notifications.length
    }
  },
  methods: {
    ...mapActions({
      setDrawer: 'layout/setDrawer',
    }),
    narrateDate,
    toggleSidebar() {
      this.setDrawer(!this.$store.state.layout.drawer)
    },
    updateLastReadNotification() {
      http.get(`/api/user/last_read_notification`)
        .then(response => {
          this.user.last_read_notification = response.data
        })
    },
    
  },
}
</script>

<style lang="sass" scoped>
.header
  position: relative

  .nav-icon
    position: absolute
    top: 0
    left: 0
    width: 50px
    height: 50px

.title

  & hr
    min-width: 48px
    height: 1px
    border: 2px solid rgba(255,255,255,0.2)
</style>
