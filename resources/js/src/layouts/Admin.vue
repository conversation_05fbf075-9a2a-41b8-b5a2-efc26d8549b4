<template>
  <v-app>
    <v-row class="width-100 ma-0">
      <v-col class="flex-grow-0 pa-0" v-if="showMode !== 'stack'">
        <symmetry-menu-side
          :items="sidebarMenu.filter(menu => hasUserPermission(menu.permission))"
          :mode="showMode"
        ></symmetry-menu-side>
      </v-col>
      <v-col class="pa-0 overflow-auto">
        <symmetry-header
          :username="authenticated && user.name"
          :mode="showMode"
          :items="sidebarMenu.filter(menu => hasUserPermission(menu.permission))"
        >
          <h1 class="text-h1 white--text text-center">
            {{ title }}
          </h1>
          <hr class="ma-4" />
          <h3 class="text-h3 white--text darken-2 text-center">
            {{ subtitle }}
          </h3>
        </symmetry-header>
        <transition name="fade" mode="out-in">
          <router-view></router-view>
        </transition>
      </v-col>
    </v-row>
    <symmetry-footer></symmetry-footer>
    <v-snackbar v-model="snackbar.status" :color="snackbar.color" :timeout="timeout" :top="false" :bottom="true">
      {{ snackbar.text }}
    </v-snackbar>
  </v-app>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import SymmetryHeader from './symmetry/Header'
import SymmetryMenuSide from './symmetry/MenuSide'
import SymmetryFooter from './symmetry/Footer'
import { navSymmetry as nav } from '../_nav'
import { PAGE_TITLES } from '@/utils/const'

export default {
  name: 'SymmetryMain',
  components: {
    SymmetryHeader,
    SymmetryMenuSide,
    SymmetryFooter,
  },
  computed: {
    ...mapGetters({
      hasUserPermission: 'auth/hasUserPermission',
    }),
    user() {
      return this.$store.state.auth.user
    },
    authenticated() {
      return this.$store.state.auth.authenticated
    },
    getCurrentRoute() {
      return this.$route.name
    },
    toShowMenu() {
      return !(this.getCurrentRoute === 'portal' || this.getCurrentRoute === 'privacy_policy')
    },
    sidebarMenu() {
      return !this.authenticated ? [] : nav(this.$i18n)
    },
    title() {
      const route = this.getCurrentRoute

      if (PAGE_TITLES[route]) return PAGE_TITLES[route].title

      return 'Default Title'
    },
    subtitle() {
      const route = this.getCurrentRoute

      if (PAGE_TITLES[route]) return PAGE_TITLES[route].subtitle

      return 'Default SubTitle'
    },
    showMode() {
      switch (this.$vuetify.breakpoint.name) {
        case 'xs':
        case 'sm':
          return 'stack'
        case 'md':
          return 'temporary'
      }
      return 'permanent'
    },
  },
  data() {
    return {
      timeout: 3000,
      snackbar: {
        text: this.$store.state.snackbar.text,
        status: this.$store.state.snackbar.status,
        color: this.$store.state.snackbar.color,
      },
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
    }),
  },
  watch: {
    '$store.state.snackbar.text'(val) {
      this.snackbar.text = val
    },
    '$store.state.snackbar.status'(val) {
      if (val) {
        this.snackbar.status = true
      }
    },
    '$store.state.snackbar.color'(val) {
      this.snackbar.color = val
    },
    'snackbar.status'(val) {
      if (!val) {
        this.setSnackbar({ status: false })
      }
    },
  },
}
</script>

<style lang="sass" scoped></style>
