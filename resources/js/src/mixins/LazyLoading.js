import * as moment from 'moment'
import { INTERVAL_LAZY_LOADING } from '@/utils/const'

export default {
  data: () => ({
    _timerRequested: null,
    _timerLastCreatedAt: null,
  }),
  methods: {
    newLazyLoadingRequest(callback) {
      const now = moment()

      if (this._timerRequested && moment().diff(this._timerLastCreatedAt) < INTERVAL_LAZY_LOADING) {
        if (this._timerRequested) {
          clearTimeout(this._timerRequested)
        }
      }

      this._timerRequested = setTimeout(() => {
        callback()
        this._timerRequested = null
      }, INTERVAL_LAZY_LOADING)
      this._timerLastCreatedAt = now
    },
  },
  computed: {},
}
