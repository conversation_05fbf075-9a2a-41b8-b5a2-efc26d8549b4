import moment from 'moment'
import {
  mergeDeep,
  compare,
  extractObjectToArray,
  isValueIdentical,
  narrateDate,
  getXSRFToken,
  validateEmail,
  validateSSN,
  validatePhone,
  abbrString
} from '../../utils/helper'

test('test helper method - compare', () => {
  expect(compare('', '', '-1 year')).toBe(false)

  expect(compare('2021-07-07', '>', '-1 year')).toBe(true)
  expect(compare('2021-07-07', '>', '2021-01-07')).toBe(true)
  expect(compare('2021-07-07', '>', 'blabla')).toBe(false)
  expect(() => compare('2021-07-07', '<>', '-1 year')).toThrow('Invalid operand!')

  expect(compare('abcde', '<', 'abcdef')).toBe(true)
  expect(() => compare('abcde', '<>', '-1 year')).toThrow('Invalid operand!')
})

test('test helper method - isValueIdentical', () => {
  expect(isValueIdentical('2021-07-07', '-1 year')).toBe(false)
  expect(isValueIdentical('2021-07-07', '2021-07-07')).toBe(true)
  expect(isValueIdentical('2021-07-07', 10)).toBe(false)
  expect(isValueIdentical('10', 10)).toBe(false)
  expect(isValueIdentical(10, 10)).toBe(true)
  expect(isValueIdentical([10], 10)).toBe(false)
  expect(isValueIdentical([10], [10])).toBe(true)
  expect(isValueIdentical([10], ['10'])).toBe(false)
})

test('test helper method - extractObjectToArray', () => {
  expect(extractObjectToArray(null)).toStrictEqual([]);
  expect(extractObjectToArray([1,2])).toStrictEqual([1,2]);
  expect(extractObjectToArray({0: 1, 1: 2})).toStrictEqual([1,2]);
})

test('test helper method - mergeDeep', () => {
  expect(mergeDeep({ a: 1 })).toStrictEqual({ a: 1 });
  expect(mergeDeep({ a: 1 }, { b: 1 })).toStrictEqual({ a: 1, b: 1 });
  expect(mergeDeep(
    { a: 1 },
    { b: 1, c: { d: 1 } }
  )).toStrictEqual({ a: 1, b: 1, c: { d: 1 } });
})

test('test helper method - getXSRFToken', () => {
  expect(getXSRFToken()).toBe('aaa') // initiated in dom.js
})

test('test helper method - validateEmail', () => {
  expect(validateEmail('aaa')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(true)

  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(true)
  expect(validateEmail('<EMAIL>')).toBe(true)

  expect(validateEmail('test')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(false)
  expect(validateEmail('test123@.com')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(false)
  expect(validateEmail('.<EMAIL>')).toBe(false)
  expect(validateEmail('test()*@gmail.com')).toBe(false)
  expect(validateEmail('test@<EMAIL>')).toBe(false)
  expect(validateEmail('<EMAIL>')).toBe(false)
  expect(validateEmail('<EMAIL>.1a')).toBe(false)
  expect(validateEmail('test123@gmail.a')).toBe(false)
})

// should recap
test('test helper method - validateSSN', () => {
  expect(validateSSN('aaa')).toBe(true)
})

// should recap
test('test helper method - validatePhone', () => {
  expect(validatePhone('aaa')).toBe(true)
})

test('test helper method - abbrString', () => {
  expect(abbrString('aaa')).toBe('aaa')
  expect(abbrString('1234567890', 5)).toBe('12345...')
})

test('test helper method - narrateDate', () => {
  expect(narrateDate('2021-10-27T10:21:00.000000Z')).toBe('October 27, 2021 at 5:21 am')
})
