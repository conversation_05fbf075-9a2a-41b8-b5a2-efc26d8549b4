import '@testing-library/jest-dom'
import <PERSON><PERSON>ield from '@/components/form/FormField'
import renderWithVuetify from './_setup-vuetify'

test('test FormField - show textinput with errors', async () => {
  const { getByText, getByPlaceholderText, findAllByRole } = renderWithVuetify(FormField, {
    props: {
      data: {
        alias_autofill: "",
        form_section_id: "XX-XXX-XXXX",
        id: "XX-XXX-XXXX",
        is_required: 1,
        is_secure: 0,
        label: "First Name",
        max_length: 255,
        pii: 1,
        placeholder: "First Name",
        sort: 0,
        type: "text",
        value: "",
        width: "third-width"
      },
      index: 0,
      showValidationErrors: true
    }
  })

  getByText('First Name')
  getByPlaceholderText('First Name')

  const alerts = await findAllByRole('alert')
  expect(alerts).toHaveLength(1)
})

test('test FormField - show optional email with errors', async () => {
  const { getByText, getByPlaceholderText, findAllByRole } = renderWithVuetify(FormField, {
    props: {
      data: {
        alias_autofill: "",
        form_section_id: "XX-XXX-XXXX",
        id: "XX-XXX-XXXX",
        is_required: 0,
        is_secure: 0,
        label: "Email Address",
        max_length: 255,
        pii: 1,
        placeholder: "Email Address",
        sort: 0,
        type: "email",
        value: "aaa",
        width: "third-width"
      },
      index: 0,
      showValidationErrors: true
    }
  })

  getByText('Email Address')
  getByText('(optional)')
  getByPlaceholderText('Email Address')

  const alerts = await findAllByRole('alert')
  expect(alerts).toHaveLength(1)

  getByText('The email address is not valid.')
})

test('test FormField - show datepicker with errors', async () => {
  const { getByText, findAllByRole } = renderWithVuetify(FormField, {
    props: {
      data: {
        alias_autofill: "",
        form_section_id: "XX-XXX-XXXX",
        id: "XX-XXX-XXXX",
        is_required: 1,
        is_secure: 0,
        label: "Enter Date",
        max_length: 255,
        pii: 1,
        sort: 0,
        type: "date",
        value: "",
        width: "third-width"
      },
      index: 0,
      showValidationErrors: true
    }
  })

  const alerts = await findAllByRole('alert')
  expect(alerts).toHaveLength(2)

  getByText('The date is not valid.')
})
