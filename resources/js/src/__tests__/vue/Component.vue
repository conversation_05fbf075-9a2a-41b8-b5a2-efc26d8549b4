<template>
  <div>
    <p>Times clicked: {{ count }}</p>
    <button @click="increment">increment</button>
  </div>
</template>

<script>
import { validateEmail } from "@/utils/helper";

  export default {
    data: () => ({
      count: 0,
    }),

    created() {
      console.log("test util function: ", validateEmail('<EMAIL>'))
    },

    methods: {
      increment() {
        this.count++
      },
    },
  }
</script>
