import '@testing-library/jest-dom'
import { fireEvent } from '@testing-library/vue'
import TextInput from '@/components/base/TextInput'
import renderWithVuetify from './_setup-vuetify'
import { FORM_FIELD_TYPES } from '@/utils/const'

test('test TextInput - simple', async () => {
  const { container, getByText, getByPlaceholderText, findAllByRole } = renderWithVuetify(TextInput, {
    props: {
      label: 'TextInput Test',
      name: 'textinput-test',
      placeholder: 'Placeholder for test',
      errors: 'The field is required',
    }
  })

  getByText('TextInput Test')
  getByPlaceholderText('Placeholder for test')

  container.querySelector('input[name=textinput-test]')

  const alerts = await findAllByRole('alert')
  expect(alerts).toHaveLength(1)
})

test('test TextInput - password', async () => {
  const { container, getByText } = renderWithVuetify(TextInput, {
    props: {
      label: 'TextInput Password',
      name: 'textinput-password',
      password: true,
      default: 'password1'
    }
  })

  getByText('TextInput Password')

  expect(container.querySelector("input[name='textinput-password']")).not.toBe(null)

  const input = container.querySelector("input[type='password']")
  expect(input).toHaveValue('password1')
})

test('test TextInput - set model', async () => {
  const { container, queryAllByRole } = renderWithVuetify(TextInput, {
    props: {
      noLabel: true,
      name: 'textinput',
    }
  })

  const labels = await queryAllByRole('input-label')
  expect(labels).toHaveLength(0)

  const input = container.querySelector("input[name='textinput']")
  await fireEvent.update(input, 'Mingyi')

  expect(input).toHaveValue('Mingyi')

})

test('test TextInput - phone mask', async () => {
  const { container } = renderWithVuetify(TextInput, {
    props: {
      noLabel: true,
      name: 'textinput',
      extra: FORM_FIELD_TYPES.TYPE_PHONE
    }
  })

  const input = container.querySelector("input[name='textinput']")
  await fireEvent.update(input, '123c4567890a')

  expect(input).toHaveValue('(*************')

})

test('test TextInput - clear input on secure field', async () => {
  const { container } = renderWithVuetify(TextInput, {
    props: {
      noLabel: true,
      name: 'textinput',
      default: '***-**-***',
      extra: FORM_FIELD_TYPES.TYPE_SSN
    }
  })

  const input = container.querySelector("input[name='textinput']")
  await fireEvent.keyUp(input, {key: 'A', code: 'KeyA'})

  expect(input).toHaveValue('')

})
