import '@testing-library/jest-dom'
import SelectBox from '@/components/base/SelectBox'
import renderWithVuetify from './_setup-vuetify'

test('test SelectBox - simple', async () => {
  const { getByText, findAllByRole } = renderWithVuetify(SelectBox, {
    props: {
      label: 'SelectBox Test',
      name: 'selectbox-test',
      errors: 'The field is required',
      items: [
        { label: 'A is selected', value: 1 },
        { label: 'B is selected', value: 2 },
      ],
      default: 2
    }
  })

  getByText('SelectBox Test')

  const alerts = await findAllByRole('alert')
  expect(alerts).toHaveLength(1)

  getByText('B is selected')
})
