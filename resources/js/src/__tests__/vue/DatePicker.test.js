import '@testing-library/jest-dom'
import DatePicker from '@/components/base/DatePicker'
import renderWithVuetify from './_setup-vuetify'

test('test DatePicker - simple', async () => {
  const { container, getByText } = renderWithVuetify(DatePicker, {
    props: {
      label: 'DatePicker Test',
      name: 'datepicker-test',
      errors: 'The field is required',
      default: '01/01/2022'
    }
  })

  getByText('DatePicker Test')

  const input = container.querySelector("input[type='text']")
  expect(input).toHaveValue('01/01/2022')
})
