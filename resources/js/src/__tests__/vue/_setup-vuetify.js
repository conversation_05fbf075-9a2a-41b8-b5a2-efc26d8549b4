import '@testing-library/jest-dom'
import {render} from '@testing-library/vue'
import Vue from 'vue'
import Vuetify from 'vuetify'
import VueMask from 'v-mask'
import { VueMaskDirective } from 'v-mask'

Vue.use(Vuetify)
Vue.directive('mask', VueMaskDirective)
Vue.use(VueMask, {
  placeholders: {
    S: /[*0-9]/, // define new placeholder for SSN character
  },
})

const renderWithVuetify = (component, options, callback) => {
  const root = document.createElement('div')
  root.setAttribute('data-app', 'true')

  return render(
    component,
    {
      container: document.body.appendChild(root),
      // for Vuetify components that use the $vuetify instance property
      vuetify: new Vuetify(),
      ...options,
    },
    callback,
  )
}

export default renderWithVuetify;
