import Vue from 'vue'
import SymmetryLayoutBlank from '@/layouts/Auth'
import i18n from '@/i18n'
import Error from '@/views/errors/Error404'
import Login from '@/views/auth/Login'
import Register from '@/views/auth/Register'
import ForgotPassword from '@/views/auth/ForgotPassword'
import ResetPassword from '@/views/auth/ResetPassword'

/**
 * Error component
 */
Vue.component('Error', Error)

export default {
  path: '/auth',
  component: SymmetryLayoutBlank,
  children: [
    {
      path: 'login',
      name: 'login',
      component: Login,
      meta: {
        title: i18n.t('routes.login'),
        middleware: 'guest',
      },
    },
    {
      path: 'register',
      name: 'register',
      component: Register,
      meta: {
        title: i18n.t('routes.register'),
        middleware: 'guest',
      },
    },
    {
      path: 'forgot_password',
      name: 'forgot_password',
      component: ForgotPassword,
      meta: {
        title: i18n.t('routes.forgot_password'),
        middleware: 'guest',
      },
    },
    {
      path: 'password/reset',
      name: 'reset_password',
      component: ResetPassword,
      meta: {
        title: i18n.t('routes.reset_password'),
        middleware: 'guest',
      },
    },
    {
      path: '*',
      component: Error,
      meta: {
        title: i18n.t('routes.not_found'),
        middleware: 'guest',
      },
    },
  ],
}
