import SymmetryLayout from '@/layouts/Main'
import i18n from '@/i18n'
import PrivacyPolicy from '@/views/extra/PrivacyPolicy'

export default {
  path: '/extra',
  component: SymmetryLayout,
  children: [
    {
      path: 'privacy-policy',
      name: 'privacy_policy',
      component: PrivacyPolicy,
      meta: {
        title: i18n.t('routes.privacy_policy'),
        middleware: 'guest',
      },
    },
  ],
}
