import Vue from 'vue'
import AdminLayout from '@/layouts/Admin'
import Dashboard from '@/views/admin/Dashboard'
import FormFields from '@/views/admin/FormFields'
import Error from '@/views/errors/Error404'
import i18n from '@/i18n'

/**
 * Error component
 */
Vue.component('Error', Error)

export default {
  path: '/dashboard',
  component: AdminLayout,
  meta: {
    title: i18n.t('routes.home'),
  },
  children: [
    {
      path: '',
      name: 'dashboard',
      component: Dashboard,
      meta: {
        title: i18n.t('routes.dashboard'),
      },
    },
    {
      path: 'form-fields',
      name: 'form_fields',
      component: FormFields,
      meta: {
        title: i18n.t('routes.form_fields'),
      },
    },
    {
      path: '*',
      component: Error,
      meta: {
        title: i18n.t('routes.not_found'),
      },
    },
  ],
}
