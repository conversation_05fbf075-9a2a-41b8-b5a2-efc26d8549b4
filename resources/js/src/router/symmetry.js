import Vue from 'vue'
import SymmetryLayout from '@/layouts/Main'
import i18n from '@/i18n'
import Error from '@/views/errors/Error404'
import Portal from '@/views/symmetry/Portal'
import OnBoard from '@/views/symmetry/OnBoard'
import OnBoardPreview from '@/views/symmetry/OnBoardPreview'
import MyTask from '@/views/symmetry/MyTask'
import HelpCenter from '@/views/symmetry/HelpCenter'
import InviteUser from '@/views/symmetry/InviteUser'
import ViewMyInvitations from '@/views/symmetry/ViewMyInvitations'
import ContentList from '@/views/symmetry/ContentList'
import ContentManagement from '@/views/symmetry/ContentManagement'
import Guide from '@/views/symmetry/Guide'
import CompanyInformation from '@/views/symmetry/CompanyInformation.vue'

import AllApplications from '@/views/symmetry/AllApplications'
import PendingApplications from '@/views/symmetry/PendingApplications'
import CompletedApplications from '@/views/symmetry/CompletedApplications'
import GroupInvitations from '@/views/symmetry/GroupInvitations'
import ApplicationReview from '@/views/symmetry/Review'
import TrackApplications from '@/views/symmetry/TrackApplications'
import TrackApplicationEntry from '@/views/symmetry/TrackApplicationEntry'

import EmailViewer from '@/views/symmetry/EmailViewer'


Vue.component('Error', Error)

export default {
  path: '/app',
  component: SymmetryLayout,
  children: [
    {
      path: '',
      name: 'index',
    },
    {
      path: 'portal',
      name: 'portal',
      component: Portal,
      meta: {
        title: i18n.t('routes.onboarding_application'),
        middleware: 'guest',
      },
    },
    {
      path: 'onboard',
      name: 'onboard',
      component: OnBoard,
      meta: {
        title: i18n.t('routes.onboarding_application'),
        permissions: ['fill applications']
      },
    },
    {
      path: 'application/all_application',
      name: 'all_applications',
      component: AllApplications,
      meta: {
        title: i18n.t('routes.all_applications'),
        permissions: ['review applications']
      },
    },
    {
      path: 'application/pending',
      name: 'pending_applications',
      component: PendingApplications,
      meta: {
        title: i18n.t('routes.pending_applications'),
        permissions: ['review applications']
      },
    },
    {
      path: 'application/completed',
      name: 'completed_applications',
      component: CompletedApplications,
      meta: {
        title: i18n.t('routes.completed_applications'),
        permissions: ['review applications']
      },
    },
    {
      path: 'group_invitations',
      name: 'group_invitations',
      component: GroupInvitations,
      meta: {
        title: i18n.t('routes.group_invitations'),
        permissions: ['review applications']
      },
    },
    {
      path: 'application/track',
      name: 'track_applications',
      component: TrackApplications,
      meta: {
        title: i18n.t('routes.track_applications'),
        permissions: ['track applications']
      },
    },
    {
      path: 'application/track/:user_id',
      name: 'track_application_entry',
      component: TrackApplicationEntry,
      meta: {
        title: i18n.t('routes.track_applications'),
        permissions: ['track applications']
      },
    },
    {
      path: 'application/review/:user_id',
      name: 'application_review',
      component: ApplicationReview,
      meta: {
        title: i18n.t('routes.application_review'),
        permissions: ['review applications']
      },
    },
    {
      path: 'send_invitation',
      name: 'send_invitation',
      component: InviteUser,
      meta: {
        title: i18n.t('routes.send_invite'),
        permissions: ['create invites']
      },
    },
    {
      path: 'edit_invitation/:invitation_id',
      name: 'edit_invitation',
      component: InviteUser,
      meta: {
        title: i18n.t('routes.edit_invite'),
        permissions: ['create invites']
      },
    },
    {
      path: 'view_my_invites',
      name: 'view_my_invites',
      component: ViewMyInvitations,
      meta: {
        title: i18n.t('routes.view_my_invites'),
        permissions: ['create invites']
      },
    },
    {
      path: 'my_task',
      name: 'my_task',
      component: MyTask,
      meta: {
        title: i18n.t('routes.my_task'),
      },
    },
    {
      path: 'content_management/content',
      name: 'content_management',
      component: ContentManagement,
      meta: {
        title: i18n.t('routes.content_management'),
        permissions: ['agency content']
      },
    },
    {
      path: 'content_management/content/:content_id',
      name: 'content_management_all',
      component: ContentManagement,
      meta: {
        title: i18n.t('routes.content_management'),
        permissions: ['agency content']
      },
    },
    {
      path: 'content_management/list',
      name: 'content_list',
      component: ContentList,
      meta: {
        title: i18n.t('routes.content_list'),
        permissions: ['agency content']
      },
    },
    {
      path: 'help_center',
      name: 'help_center',
      component: HelpCenter,
      meta: {
        title: i18n.t('routes.help_center'),
      },
    },
    {
      path: 'guide',
      name: 'guide',
      component: Guide,
      meta: {
        title: i18n.t('routes.guide'),
      },
    },
    {
      path: 'preview_application_form',
      name: 'preview_application_form',
      component: OnBoardPreview,
      meta: {
        title: i18n.t('routes.preview_application_form'),
      },
    },
    {
      path: 'company_information',
      name: 'company_information',
      component: CompanyInformation,
      meta: {
        title: i18n.t('routes.company_information'),
        permissions: ['company information']
      },
    },
    {
      path: 'emails',
      name: 'emails',
      component: EmailViewer,
    },
  ],
}
