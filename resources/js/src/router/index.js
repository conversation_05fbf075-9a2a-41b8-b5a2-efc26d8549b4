import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '../store'
import auth from './auth'
import admin from './admin'
import symmetry from './symmetry'
import extra from './extra'
import { USER_ROLES_TYPES } from '@/utils/const'

const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(VueRouter)

const routes = [
  auth,
  symmetry,
  extra,
  admin,
  {
    path: '*',
    redirect: '/app',
  },
]

var router = new VueRouter({
  mode: 'history',
  base: '',
  routes,
})

router.beforeEach((to, from, next) => {
  if (to.meta && to.meta.title) document.title = `${to.meta.title} - ${process.env.MIX_APP_NAME}`
  if (to.meta.middleware == 'guest') {
    if (store.state.auth.authenticated && to.name !== 'portal') {
      next({ name: 'portal' })
    }
    next()
  } else {
    if (store.state.auth.authenticated) {
      const hasUserRole = store.getters['auth/hasUserRole']
      const hasUserPermission = store.getters['auth/hasUserPermission']
      if (to.name === 'index' && hasUserRole) {
        if (hasUserRole(USER_ROLES_TYPES.SALES_REP)) next({ name: 'send_invitation' })
        else if (hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER)) next({ name: 'my_task' })
        else if (hasUserRole(USER_ROLES_TYPES.STAFF) || hasUserRole(USER_ROLES_TYPES.SUPER_ADMIN)) next({ name: 'pending_applications' })
        else next({ name: 'portal' })
      } else {
        const requiredPermissions = (to.meta && to.meta.permissions) || []
        const hasAllPermissions = requiredPermissions.every(p => hasUserPermission(p))
        if(!hasAllPermissions) {
          console.log("User doesn't have permission to view this page.")
          next({ name: 'index' })
        } else {
          next()
        }
      }
    } else {
      next({ name: 'login' })
    }
  }
})

export default router
