<template>
  <v-container class="d-flex flex-column pa-8">
    <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" />
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Onboarding Status" :items="itemsApplicationStatus" v-model="search.status" :default="search.default" />
      </v-col>
    </v-row>
    <v-row>
      <v-spacer></v-spacer>
      <v-col class="text-right">
        <v-btn text small class="mb-6" @click="downloadSureLcExport" :loading="downloadingSureLc">SureLC Export Report</v-btn>
      </v-col>
    </v-row>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      sort-by="name"
      :options.sync="options"
      :server-items-length="total"
    >
      <template v-slot:[`item.license_status`]="{ item }">
        <license-status-indicator :item="item" />
      </template>
      <template v-slot:[`item.agent`]="{ item }">
        <a class="pr-2 text--darken-3 text--secondary" @click="goToAction(item, true)">
          {{ item.agent }}
        </a>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ item.status }}
        <h5 class="text-h5 info--text" v-if="activeReviewer(item.id)" v-bind:key="tickReviewStatus">
          {{ activeReviewer(item.id) }} is reviewing...
        </h5>
      </template>
      <template v-slot:[`item.applied_on`]="{ item }">
        {{ narrateDate(item.applied_on) }}
      </template>
      <template v-slot:[`item.completed_on`]="{ item }">
        {{ narrateDate(item.completed_on) }}
      </template>
      <template v-slot:[`item.actions`]="{ item }">
          <v-btn
            v-show="hasUserRole('SuperAdmin') || (hasUserRole('AgencyOwner') && item.status_slug == onboard_status_types.SUBMITTED) || (hasUserRole('Staff') && item.approved_by_ao == true)"
            @click="goToAction(item)"
            :color="item.is_new_submit ? 'primary' : 'error'"
            x-small
            class="my-1"
          >
            Review
          </v-btn>
          <v-btn
            @click="goToAction(item, true)"
            color="secondary"
            x-small
            class="my-1"
          >
            View Only
          </v-btn>
      </template>
      <template v-slot:no-data>
        <h4>No applications</h4>
      </template>
      <template v-slot:[`item.homeoffice_submission_on`]="{ item }">
        {{ narrateDate(item.homeoffice_submission_on) }}
      </template>
    </v-data-table>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'
import LicenseStatusIndicator from '@/components/base/LicenseStatusIndicator'

export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
    LicenseStatusIndicator
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: '', value:'license_status' },
        { text: 'FULL LEGAL NAME:', value: 'agent', class: 'app-table-col' },
        { text: 'APPLIED ON', value: 'applied_on', class: 'app-table-col' },
        { text: 'DATE SUBMITTED TO HOME OFFICE', value: 'homeoffice_submission_on', class: 'app-table-col' },
        { text: 'COMPLETED ON', value: 'completed_on', class: 'app-table-col' },
        { text: "ONBOARDING STATUS", value: "status", class: "app-table-col" },
        {
          text: 'DIRECT UPLINE NAME',
          value: 'direct_upline',
          class: 'app-table-col',
        },
        {
          text: 'AGENCY OWNER NAME',
          value: 'agency_owner',
          class: 'app-table-col',
        },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
        },
      ],
      items: [],
      total: 0,
      loading: false,

      itemsApplicationStatus: [],

      options: {}, // option for pagination
      search: {
        // option for search
        text: '',
        status: '',
        default: 'approved'
      },

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,
      downloadingSureLc: false,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
    }),
  },
  watch: {
    options: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
    search: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  mounted() {
    this.getApplicationStatusOptions()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplications: 'application/getApplications',
      getStatusOptions: 'application/getStatusOptions',
    }),
    narrateDate,
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    goToAction(item, view = false) {
      if (view) this.goToPath('/app/application/track/' + item.id)
      else if (this.canReview(item)) this.goToPath('/app/application/review/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        ((this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED) ||
         (this.hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && item.approved_by_ao)
        ) &&
        !this.activeReviewer(item.id)
      )
    },
    getDataFromApi() {
      this.loading = true
      this.getApplications(
        new URLSearchParams({
          ...this.options,
          ...this.search,
          completed: 'completed',
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getApplicationStatusOptions() {
      this.getStatusOptions().then(response => {
        this.itemsApplicationStatus = response.data
        if(response.data){
          const items = response.data.filter(status => status.value.indexOf("approved") > 0);
          // console.log("getApplicationStatusOptions:", items);
          this.itemsApplicationStatus = items;
        }
      })
    },
    downloadSureLcExport() {
      this.downloadingSureLc = true
      let options = {...this.options}
      options.itemsPerPage = -1

      this.getApplications(
        new URLSearchParams({
          ...options,
          ...this.search,
          completed: 'completed',
          include_ssn: true,
          include_dob: true,
          include_last_name: true,
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            // this.items = res.data.items
            // this.total = res.data.total
            this.downloadCsv(res.data.items)
          }
        })
        .finally(() => {
          this.downloadingSureLc = false
        })
    },
    downloadCsv: function(data) {
        const lines = []
        
        //array of data table fields for csv header row
        const fields = []
        const headers = []
        this.headers.forEach(header => {
            if(header.text != 'ACTIONS' && header.text !== '') {
              headers.push(header)
              fields.push(header.text)
            }
        })

        let phone_header = { text: 'PHONE', value: 'phone' }
        let last_name_header = { text: 'LAST NAME', value: 'last_name' }
        let ssn_header = { text: 'SSN', value: 'ssn' }
        let dob_header = { text: 'DOB', value: 'dob' }
        headers.splice(2, 0, phone_header)
        headers.splice(2, 0, dob_header)
        headers.splice(2, 0, ssn_header)
        headers.splice(2, 0, last_name_header)
        fields.splice(2, 0, 'PHONE')
        fields.splice(2, 0, 'DOB')
        fields.splice(2, 0, 'SSN')
        fields.splice(2, 0, 'LAST NAME')

        //build the string and add to lines array
        lines.push(`"`+fields.join(`","`)+`"`)
        //loop through carrier records and build the csv text line
        data.map(application => {
            //array of carrier field values based on fields defined by data table
            let values = headers.map(header => {
                switch (header.value) {
                  // case 'updated_at':
                  //   return narrateDate(invitation[header.value])
                  // case 'is_fully_licensed':
                  // case 'has_passed_exam':
                  //   return invitation[header.value] == true ? 'Yes' : 'No'
                  // case 'recruiter':
                  //   return invitation.user.name
                  // case 'direct_upline':
                  //   return invitation?.upline_agent?.AgentName
                  // case 'status':
                  //   return this.getInvitationStatus(invitation)
                  default:
                    return application[header.value]
                }
            })
            //build the string and add to lines array
            lines.push(`"`+values.join(`","`)+`"`)
        })

        //build all rows of csv by joining lines array
        let txt = lines.join("\n")

        //generate the download
        var element = document.createElement('a')
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(txt))
        element.setAttribute('download', "CompletedApplications.csv")
        element.style.display = 'none'
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)

    },
  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
