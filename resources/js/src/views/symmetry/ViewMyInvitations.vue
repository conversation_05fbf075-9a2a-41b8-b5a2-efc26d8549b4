<template>
  <v-container class="d-flex flex-column text-center pa-8" fluid>
    <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" />
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Status" :items="itemsInvitationStatus" v-model="search.status" />
      </v-col>
    </v-row>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      sort-by="name"
      :options.sync="options"
      :server-items-length="total"
    >
      <!-- <template v-slot:[`item.status`]="{ item }">
        {{ item.status }}
      </template> -->
        <template v-slot:top>
          <v-row>
            <v-col>
              <v-switch :disabled="loading" color="primary" background-color="white" v-model="all_invites" :label="show_all_invites"></v-switch>
            </v-col>
            <v-spacer></v-spacer>
            <v-col class="text-right">
              <v-btn color="grey" text @click="downloadInvitations" :loading="downloading">Download</v-btn>
            </v-col>
          </v-row>
        </template>
      <template v-slot:[`item.created_at`]="{ item }">
        {{ narrateDate(item.created_at, false) }}
      </template>
      <template v-slot:[`item.updated_at`]="{ item }">
        {{ narrateDate(item.updated_at) }}
      </template>
      <template v-slot:[`item.direct_upline`]="{ item }">
        {{ item.upline_agent && item.upline_agent.AgentName }}
      </template>
      <template v-slot:[`item.baseshop_owner`]="{ item }">
        {{ item.baseshop_owner && item.baseshop_owner.AgentName }}
      </template>
      <template v-slot:[`item.recruiter`]="{ item }">
        {{ item.user && item.user.name }}
      </template>
      <template v-slot:[`item.status`]="{ item }">
        {{ getInvitationStatus(item) }}
      </template>
      <template v-slot:[`item.actions`]="{ item }">
        <div style="white-space:nowrap;">
          <v-btn
            @click="copyInviteLink(item.code)"
            small
            class="mr-2"
            icon
            text
            title="Copy Invite Link"
          >
            <v-icon>mdi-content-copy</v-icon>
          </v-btn>
          <v-btn
            v-if="!item.invite_accepted || (hasUserRole('Staff'))"
            @click="edit(item)"
            color="primary"
            small
            icon
            text
            title="Edit Invitation"
          >
            <v-icon>mdi-pen</v-icon>
          </v-btn>
          <v-btn
            v-if="item.invite_sent && !item.invite_accepted"
            @click="resend(item)"
            color="info"
            :disabled="item.id == resendingID"
            small
            icon
            text
            title="Resend Invitation"
          >
            <v-icon v-if="item.id != resendingID">mdi-refresh</v-icon>
            <v-icon v-else>fas fa-circle-notch fa-spin</v-icon>
          </v-btn>
          <v-btn
            v-if="!item.invite_accepted"
            @click="del(item.id)"
            :disabled="item.id == deletingID"
            small
            icon
            text
            title="Delete Invitation"
          >
            <v-icon v-if="item.id != deletingID" color="red">mdi-delete</v-icon>
            <v-icon v-else color="red">fas fa-circle-notch fa-spin</v-icon>
          </v-btn>
        </div>
      </template>
      <template v-slot:no-data>
        <h4>No Invites</h4>
      </template>
    </v-data-table>
    <v-row justify="center">
      <v-dialog v-model="showConfirmDeleteModal" persistent max-width="500">
        <v-card style="background-color: #f5f5f5 !important;">
          <v-card-text
            >Are you sure you want to delete this invitation?</v-card-text
          >
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="primary darken-1" @click=";(showConfirmDeleteModal = false), (deletingID = null)">
              No
            </v-btn>
            <v-btn color="error darken-1" @click=";(showConfirmDeleteModal = false), del(deletingID, false)"> Yes </v-btn>
            <v-spacer></v-spacer>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-row>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'
import moment from 'moment'

export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: 'NEW AGENT', value: 'name', class: 'app-table-col' },
        { text: 'EMAIL', value: 'email', class: 'app-table-col' },
        { text: '%', value: 'contract_level', class: 'app-table-col' },
        { text: 'RECRUITER', value: 'recruiter', class: 'app-table-col', sortable: false },
        {
          text: 'DIRECT UPLINE',
          value: 'direct_upline',
          class: 'app-table-col',
          sortable: false
        },
        {
          text: 'BASESHOP OWNER',
          value: 'baseshop_owner',
          class: 'app-table-col',
          sortable: false
        },
        { text: 'CREATED', value: 'created_at', class: 'app-table-col' },
        // { text: 'LAST UPDATED', value: 'updated_at', class: 'app-table-col' },
        { text: 'STATUS', value: 'status', class: 'app-table-col', sortable: false },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
          sortable: false
        },
      ],
      items: [],
      total: 0,
      loading: false,
      resendingID: null,
      deletingID: null,

      showConfirmDeleteModal: false,

      itemsInvitationStatus: [
        { label: '-', value: '' },
        { label: 'Accepted', value: 'accepted' },
        { label: 'Expired', value: 'expired' },
        { label: 'Stalled', value: 'stalled' },
        { label: 'Lapsed', value: 'lapsed' },
      ],

      options: {
        sortBy:['created_at'],
        sortDesc: ['true']
      }, // option for pagination
      search: {
        // option for search
        text: '',
        status: '',
      },
      all_invites: false,
      downloading: false,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
    }),
    show_all_invites() {
        return this.all_invites ? 'Show all' : 'Show mine'
    },
  },
  mounted() {
    this.getDataFromApi()
  },
  watch: {
    options: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
    search: {
      handler() {
        this.options.page = 1
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
    all_invites: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getMyInvitations: 'user_invite/getMyInvitations',
      deleteInvitation: 'user_invite/deleteInvitation',
      resendInvitation: 'user_invite/resendInvitation',
    }),
    narrateDate,
    copyInviteLink(code) {
      const url = `${window.location.origin}/auth/register?code=${code}`;
      navigator.clipboard.writeText(url)
      this.setSnackbar({
        status: true,
        text: 'Invite link copied to clipboard',
        color: 'success',
      })
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    getDataFromApi() {
      this.loading = true
      this.getMyInvitations(
        new URLSearchParams({
          ...this.options,
          ...this.search,
          'all_invites': this.all_invites
        })
      )
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    edit(item) {
      this.goToPath('/app/edit_invitation/' + item.id)
    },
    resend(item) {
      this.resendingID = item.id;
      this.resendInvitation(this.resendingID)
        .then(() => {
          this.setSnackbar({
            status: true,
            text: 'Your invitation is successfully scheduled to resend!',
            color: 'success',
          })
        })
        .catch(() => {
          this.setSnackbar({
            status: true,
            text: 'Sorry, we failed to resend your invitation.',
            color: 'warning',
          })
        })
        .finally(() => {
          this.resendingID = null
          this.getDataFromApi()
        })
    },
    del(itemID, needConfirm = true) {
      this.deletingID = itemID;
      if(needConfirm) {
        this.showConfirmDeleteModal = true
        return;
      }

      this.deleteInvitation(this.deletingID)
        .then(() => {
          this.setSnackbar({
            status: true,
            text: 'Invitation deleted successfully!',
            color: 'success',
          })
        })
        .catch(() => {
          this.setSnackbar({
            status: true,
            text: 'Failed to delete your invitation.',
            color: 'warning',
          })
        })
        .finally(() => {
          this.deletingID = null
          this.getDataFromApi()
        })
    },
    downloadInvitations() {
      this.downloading = true
      let options = {...this.options}
      options.itemsPerPage = -1

      this.getMyInvitations(
        new URLSearchParams({
          ...options,
          ...this.search,
          'use_baseshop': this.use_baseshop,
          'all_invites': this.all_invites
        })
      )
        .then(res => {
          if (res && res.data) {
            // this.items = res.data.items
            // this.total = res.data.total
            this.downloadCsv(res.data.items)
          }
        })
        .finally(() => {
          this.downloading = false
        })
    },
    downloadCsv: function(data) {
        const lines = []

        //array of data table fields for csv header row
        const fields = []
        const headers = []
        this.headers.forEach(header => {
            if(header.text != 'ACTIONS') {
              headers.push(header)
              fields.push(header.text)
            }
        })

        let phone_header = { text: 'PHONE', value: 'phone' }
        headers.splice(2, 0, phone_header)
        fields.splice(2, 0, 'PHONE')

        // Insert baseshop owner header after direct upline
        let baseshop_header = { text: 'BASESHOP OWNER', value: 'baseshop_owner' }
        // Find index of direct_upline
        let duIndex = headers.findIndex(h => h.value === 'direct_upline')
        if (duIndex !== -1) {
          headers.splice(duIndex + 1, 0, baseshop_header)
          fields.splice(duIndex + 1, 0, 'BASESHOP OWNER')
        }

        //build the string and add to lines array
        lines.push(`"`+fields.join(`","`)+`"`)
        //loop through carrier records and build the csv text line
        data.map(invitation => {
            //array of carrier field values based on fields defined by data table
            let values = headers.map(header => {
                switch (header.value) {
                  case 'updated_at':
                    return narrateDate(invitation[header.value])
                  case 'is_fully_licensed':
                  case 'has_passed_exam':
                    return invitation[header.value] == true ? 'Yes' : 'No'
                  case 'recruiter':
                    return invitation.user && invitation.user.name
                  case 'direct_upline':
                    return invitation?.upline_agent?.AgentName
                  case 'baseshop_owner':
                    return invitation?.baseshop_owner?.AgentName
                  case 'status':
                    return this.getInvitationStatus(invitation)
                  default:
                    return invitation[header.value]
                }
            })
            //build the string and add to lines array
            lines.push(`"`+values.join(`","`)+`"`)
        })

        //build all rows of csv by joining lines array
        let txt = lines.join("\n")

        //generate the download
        var element = document.createElement('a')
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(txt))
        element.setAttribute('download', "Invitations.csv")
        element.style.display = 'none'
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)

    },
    getInvitationStatus: function(invitation) {
      if(!invitation.invite_sent) {
        return 'Saved for Later'
      }

      let status = invitation.invite_accepted
            ? 'Accepted'
            : (invitation.invite_expired
                ? 'Expired'
                : (invitation.invite_sent
                    ? 'Waiting for Acceptance...'
                    : 'Sending...'
                  )
              )
      if(status == 'Accepted') {
        let app_status = invitation?.invited_user?.user_status?.name ?? null
        if(app_status) {
          var givenDate = moment(invitation.invited_user.updated_at); // Replace with your date
          var currentDate = moment();
          var daysSince = currentDate.diff(givenDate, 'days');
          status = `${app_status} (${daysSince} days)`
        }
      }
      return status
    }


  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
