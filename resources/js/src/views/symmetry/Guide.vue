<template>
  <v-container class="guide-container d-flex flex-column align-center">
    <div v-if="guide" v-html="guide"></div>
    <div v-if="company" v-html="company"></div>

    <div v-if="contentAskQuestion != []" class="mt-2">
      <h1>Frequently Asked Questions ( FAQ )</h1>
      <br />
      <div class="mt-3">
        <div v-html="faqs" />
      </div>
      <div class="mt-2">
        <h1>Frequently Asked Questions 2 ( FAQ )</h1>

        <plain-accordion :content="JSON.stringify(contentAskQuestion)" class="accordion-glossary" />
      </div>
    </div>

    <div class="wrap mt-10">
      <strong> For more information you can contact us</strong>
      <p class="mb-0">
        {{ contactInfo.name == null ? '' : contactInfo.name }} {{ contactInfo.email == null ? '' : contactInfo.email }}
      </p>
      <span class="mt-0">
        {{ contactInfo.address == null ? '' : contactInfo.address }}
      </span>
      <span>
        {{ contactInfo.phone == null ? '' : contactInfo.phone }} {{ contactInfo.city == null ? '' : contactInfo.city }}
        {{ contactInfo.zip == null ? '' : contactInfo.zip }}
      </span>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
import PlainAccordion from '../../components/compound/PlainAccordion.vue'

export default {
  components: { PlainAccordion },
  name: 'SymmetryGuide',
  data() {
    return {
      loading: false,
      guide: '',
      company: '',
      faqs: '',
      faqs2: '',
      contentAskQuestion: [],
      contactInfo: {
        id: null,
        name: null,
        email: null,
        phone: null,
        address: null,
        zip: null,
        city: null,
      },
    }
  },
  mounted() {
    this.getGuide()
  },
  methods: {
    ...mapActions({
      getGuideContent: 'content_management/getGuideContent',
      getContactContent: 'content_management/getContactContent',
      getFaqStateLicensing: 'content_management/getFaqStateLicensing',
    }),
    async getGuide() {
      this.loading = true
      let id = null
      const response = await this.getGuideContent()
      const {
        data: { content, company },
      } = response

      if (!content && !company) {
        console.error('Guide: Null content')
        return
      }

      this.guide = content ? content.content : ''
      this.company = company ? company.content : ''

      id = (this.guide ? content.id : '') || (this.company ? company.id : '')

      //get faqs
      const resp = await this.getFaqStateLicensing(id)
      if (resp.data.length > 0) {
        this.faqs2 = resp.data.filter(fq => fq.answer || fq.question)

        const topFaqIndex = this.faqs2.findIndex(fq => !fq.question)
        if (topFaqIndex == -1) topFaqIndex = 0

        this.faqs = this.faqs2[topFaqIndex].answer
        this.faqs2.splice(topFaqIndex, 1)
      } else {
        this.faqs = null
        this.faqs2 = null
      }

      //get contact information
      await this.getContactContent(id)
        .then(resp => {
          this.contactInfo = resp.data
        })
        .finally(() => {
          this.loading = false
        })

      this.faqs2 &&
        this.faqs2.forEach(element => {
          this.contentAskQuestion.push({
            content: element.answer == null ? 'answer' : element.answer,
            title: element.question == null ? 'Question' : element.question,
          })
        })
      console.log(this.contentAskQuestion)
    },
  },
}
</script>

<style lang="css" scope>
.wrap {
  margin-bottom: auto;
}
.accordion-glossary {
  width: 100%;
  max-width: 1000px;
}
</style>
