<template>
  <v-container class="invite-container d-flex flex-column pa-10">
    <div class="text-center" v-if="isEdit && !loadedInvitationID">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <v-tabs v-model="tab" grow>
      <v-tab v-if="!isEdit || isAgent">Agent Invitation</v-tab>
      <v-tab v-if="!isEdit">Group Invitation</v-tab>
      <v-tab v-if="hasUserRole(['Staff', 'SuperAdmin']) && (!isEdit || isPrincipal)">Principal Invitation</v-tab>

      <!-- AGENT INVITATION -->
      <v-tab-item v-if="tenant === 'Q2A' && (!isEdit || isAgent)" class="pt-8">
        <v-form ref="form" @submit.prevent="save" :style="{ visibility: (isEdit && !loadedInvitationID) ? 'hidden' : 'visible' }">
          <v-row>
            <v-col cols="6">
              <ul>
                <li class="text-h4">Invitations expire after 7 days.</li>
                <li class="text-h4">To resend an existing invitation, you'll need to delete the original one first.</li>
              </ul>
            </v-col>
            <v-col cols="6" class="text-right">
              <v-dialog v-if="tenant === 'Q2A'" width="500" v-model="show_demo_invitation">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    v-on="on"
                    @click="demo_request_response = false"
                  >
                    Demo Invitation
                  </v-btn>
                </template>
                <v-card>
                  <v-card-title>
                    Create Demo Invitation
                  </v-card-title>
                  <v-card-text v-if="!demo_request_response">
                    <p>You can create an invitation and onboarding account for demonstration purposes.
                    The email will be delivered to your email address.
                    The application form will work like a real application but the data and app will not be submitted for processing.
                    </p>
                    <p>
                      <span class="font-weight-bold">Note:</span> The email will direct you to log in at <span class="text-decoration-underline">demo.quilityonboarding.com</span>. Please pay attention to what domain you are using when logging in.
                    </p>
                  </v-card-text>
                  <v-card-text v-else>
                    <p>Check your email for your demo login registration.</p>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn v-if="!demo_request_response" color="primary" @click="processDemoInvite" :loading="requesting_demo_invite">Send Invitation</v-btn>
                    <v-btn v-else color="primary" @click="show_demo_invitation = false">Close</v-btn>
                  </v-card-actions>
                </v-card>
              </v-dialog>

              <v-dialog width="700" v-model="show_candidate_search">
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    v-on="on"
                    @click="show_candidate_search = false"
                    title="Search Existing Candidates"
                    icon
                    style="background-color:transparent;"
                    color="grey"
                  >
                    <v-icon>mdi-magnify</v-icon>
                  </v-btn>
                </template>
                <v-card>
                  <v-card-title>
                    Search for Existing Candidates
                  </v-card-title>
                  <v-card-text>
                    <p>Here you can see if a candidate is already active in onboarding or HQ. Enter as many fields as you can for the most accurate results.
                    </p>
                    <v-row>
                      <v-col cols="12">
                        <text-input label="Name" v-model="search.first_name" :errors="formErrors.search_first_name" />
                      </v-col>
                      <!-- <v-col cols="6">
                        <text-input label="Last Name" v-model="search.last_name" :errors="formErrors.search_last_name" />
                      </v-col> -->
                      <v-col cols="12">
                        <text-input label="Email" em v-model="search.email" :errors="formErrors.search_email" />
                      </v-col>
                      <v-col cols="12">
                        <text-input label="Phone" em v-model="search.phone" :errors="formErrors.search_phone" />
                      </v-col>
                      <!-- <v-col cols="6">
                        <text-input label="State" em v-model="search.state" :errors="formErrors.search_state" />
                      </v-col> -->
                      <!-- <v-col cols="6">
                        <date-picker v-model="search.dob" label="Date of Birth"></date-picker>
                      </v-col> -->
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <!-- <v-alert type="error" v-if="candidate_search_response">{{ candidate_search_response }}</v-alert> -->
                    <v-spacer></v-spacer>
                    <v-btn color="primary" @click="search_existing_candidate" :loading="searching_candidates" class="ml-2">Search</v-btn>
                    <!-- <v-btn v-else color="primary" @click="show_candidate_search = false">Close</v-btn> -->
                  </v-card-actions>
                </v-card>
              </v-dialog>
            </v-col>


            <v-col cols="6">
              <text-input ref="first_name" label="First Name" maxlength="50" v-model="first_name" :errors="formErrors.first_name" :disabled="invite_accepted == 1" />
            </v-col>
            <v-col cols="6">
              <text-input ref="last_name" label="Last Name" maxlength="50" v-model="last_name" :errors="formErrors.last_name" :disabled="invite_accepted == 1" />
            </v-col>
            <v-col cols="6">
              <text-input ref="email" label="Email" em v-model="email" :errors="formErrors.email"  :disabled="invite_accepted == 1" />
            </v-col>
            <v-col cols="6">
              <text-input label="Confirm Email" v-model="email_confirm" :errors="formErrors.email" :disabled="invite_accepted == 1" />
            </v-col>
            <v-col cols="6">
              <text-input
                ref="phone_number"
                label="Phone Number"
                v-model="phone_number"
                :extra="formFieldTypes.TYPE_PHONE"
                :errors="formErrors.phone_number"
                :disabled="invite_accepted == 1"
              />
            </v-col>
            <v-col cols="6">
              <select-box
                ref="resident_state"
                label="Resident State"
                :items="states"
                v-model="state"
                :errors="formErrors.state"
                itemText="text"
                itemValue="value"
              />
            </v-col>
            <v-col cols="12">
              <select-box
                ref="contract_level"
                label="Contract Level"
                :items="contractLevels"
                v-model="contract_level"
                :errors="formErrors.contract_level"
              />
            </v-col>
            <v-col cols="12">
              <select-box
                ref="upline"
                label="Upline"
                :items="uplineAgents"
                v-model="upline"
                item-text="agent_select_text"
                item-value="agent_id"
                :errors="formErrors.upline"
              />
            </v-col>
            <v-col v-show="false" cols="12">
              <select-box
                label="Commissions"
                :items="optionsCommissions"
                v-model="commissions"
                :errors="formErrors.commissions"
                item-text="label"
                item-value="value"
                ref="commissions"
              />
            </v-col>
            <v-col cols="12">
              <select-box
                ref="source"
                label="Source"
                :items="optionsSource"
                v-model="source"
                :errors="formErrors.source"
              />
            </v-col>
            <v-col cols="12">
              <select-box
                ref="license_status"
                label="License Status"
                :items="optionsLicenseStatus"
                v-model="license_status"
                :errors="formErrors.license_status"
              />
            </v-col>
            <v-col cols="12" id="experience">

              <v-radio-group v-model="experience" outlined :error-messages="formErrors.experience">
                <v-radio dense hideDetails label="New" value="new" class="mb-0"></v-radio>
                <!-- <div class="pl-8">(unlicensed or licensed agent who is not currently contracted with any carrier)</div> -->
                <div class="pl-8 caption">
                  Has just received license or unlicensed and has no prior experience in the industry. Or an agent that has experience in the industry but is not tied to another IMO and is considered a free agent.
                </div>

                <v-radio dense hideDetails label="Returning" value="returning" class="mt-4 mb-0"></v-radio>
                <div class="pl-8 caption">
                  Agents previously contracted with SFG.
                </div>

                <v-radio dense hideDetails label="Transfer" value="transfer" class="mt-4 mb-0"></v-radio>
                <div class="pl-8 caption">
                  An agent that is contracted under another IMO and is not considered a Free Agent, locking them up with certain carriers.
                </div>

                <v-radio dense hideDetails label="Advanced Market" value="experienced" class="mt-4 mb-0"></v-radio>
                <div class="pl-8 caption">
                  Agents that are focused on ONLY Advanced Market solutions (IUL, IBC, DFL, & Annuities).
                </div>

                <!-- <v-alert v-if="experience == 'returning' || experience == 'transfer'" type="info" class="mt-4">
                  <p>In order to expedite contracting with carriers, please complete appropriate documentation found below and <NAME_EMAIL>.</p>
                  <ul>
                    <li><a href="https://hq.quility.com/api/public/document/188169/view/1678826266/9bb81b20b291b51d3f85ba2ef1000726" class="white--text" target="_blank">Carrier Release Forms</a></li>
                    <li><a href="https://hq.quility.com/api/public/document/188170/view/1678826393/350f2bfef2a66b9604cc2718ea43ecf1" class="white--text" target="_blank">Group Transfer from Another IMO</a></li>
                  </ul>
                </v-alert> -->
              </v-radio-group>
            </v-col>
            <!-- <v-col cols="12">
              <v-checkbox dense hideDetails label="Advanced Market Agent" v-model="advanced_markets"></v-checkbox>
              <div class="pl-8">(Agent is DFL, IUL, and Annuity focused)</div>
            </v-col> -->
            <v-col cols="12" v-if="corporateDivision === true && user.roles[0].name ==='AgencyOwner' ">
              <v-checkbox
                ref="corporate"
                v-model="corporate"
                :label="`Corporate Candidate`"
                class="black-label"
                :disabled="invite_accepted == 1"
              ></v-checkbox>
            </v-col>
            <v-col cols="12" >
              <text-area
                ref="person_message"
                label="Personalized Message"
                subline="Use the standard messaging, or send a personalized message with your invitation."
                v-model="person_message"
                :default="personalizedMessageDefault"
                :disabled="invite_accepted == 1"
              />
            </v-col>
            <!-- <v-col cols="12">
              <v-flex>
                <div class="text-h5" role="input-label">Invitation Group Name</div>
                <div class="caption" role="input-label">Group Onboarding: When 5 or more agents are transferring from the same IMO at one time. Less than 5 can utilize the Transfer path individually.</div>
                <v-combobox
                  v-model="selectedGroup"
                  ref="invite_group"
                  outlined
                  :items="groups"
                  item-text="group_name"
                  item-value="id"
                  :errors="formErrors.invite_group"
                ></v-combobox>
              </v-flex>
            </v-col> -->
            <v-col cols="12" class="text-center">
              <v-btn color="primary" large :loading="saving" type="submit">
                {{ isEdit ? 'UPDATE INVITATION' : 'SEND INVITATION' }}
              </v-btn>
            </v-col>
          </v-row>
        </v-form>
      </v-tab-item>

      <!-- B2B AGENT INVITATION -->
      <v-tab-item v-else-if="!isPrincipal" class="pt-8">
        <v-form ref="form_b2b_agent" @submit.prevent="save_b2b_agent" :style="{ visibility: (isEdit && !loadedInvitationID) ? 'hidden' : 'visible' }">
          <v-row>
            <v-col cols="12">
              <ul>
                <li class="text-h4">Invitations expire after 7 days.</li>
                <li class="text-h4">To resend an existing invitation, you'll need to delete the original one first.</li>
              </ul>
            </v-col>
            <v-col cols="6">
              <text-input ref="first_name" label="First Name" v-model="first_name" :errors="formErrors.first_name" :disabled="invite_accepted == 1" maxlength="50" />
            </v-col>
            <v-col cols="6">
              <text-input ref="last_name" label="Last Name" v-model="last_name" :errors="formErrors.last_name" :disabled="invite_accepted == 1" maxlength="50" />
            </v-col>
            <v-col cols="6">
              <text-input ref="email" label="Email" em v-model="email" :errors="formErrors.email"  :disabled="invite_accepted == 1" :extra="formFieldTypes.TYPE_EMAIL" />
            </v-col>
            <v-col cols="6">
              <text-input ref="alt_email" label="Alternate Email" v-model="alt_email" :errors="formErrors.alt_email" :disabled="invite_accepted == 1"  :extra="formFieldTypes.TYPE_EMAIL" />
            </v-col>
            <v-col cols="6">
              <text-input
                ref="phone_number"
                label="Phone Number"
                v-model="phone_number"
                :extra="formFieldTypes.TYPE_PHONE"
                :errors="formErrors.phone_number"
                :disabled="invite_accepted == 1"
              />
            </v-col>
            <v-col cols="6">
              <select-box
                ref="carrier"
                label="Carriers"
                v-model="carrier"
                :items="getCarriers"
                multiple
                :errors="formErrors.carrier"
                :hint="'Please only select carriers with which you are already contracted as an upline.'"
                persistent-hint
              ></select-box>
            </v-col>
            <v-col cols="12">
              <select-box
                ref="upline"
                label="Upline"
                :items="uplineAgents"
                v-model="upline"
                item-text="agent_select_text"
                item-value="agent_id"
                :errors="formErrors.upline"
              />
            </v-col>
            <v-col cols="6">
              <select-box
                label="Commissions"
                :items="optionsCommissions"
                v-model="commissions"
                :errors="formErrors.commissions"
                item-text="label"
                item-value="value"
                ref="commissions"
              />
            </v-col>

            <v-col cols="6">
              <select-box
                ref="contract_level"
                label="Contract Level"
                :items="contractLevels"
                v-model="contract_level"
                :errors="formErrors.contract_level"
              />
            </v-col>

            <v-col cols="12" >
              <text-area
                ref="person_message"
                label="Personalized Message"
                subline="Use the standard messaging, or send a personalized message with your invitation."
                v-model="person_message"
                :default="personalizedMessageDefault"
                :disabled="invite_accepted == 1"
              />
            </v-col>

            <v-col cols="12">
              <v-row justify="space-between">
                <v-col>
                  <v-btn large :loading="saving && save_for_later" type="submit" @click="save_for_later = true">SAVE FOR LATER</v-btn>
                </v-col>
                <v-col class="text-right">
                  <v-btn color="primary" large :loading="saving && !save_for_later" type="submit">
                    {{ isEdit ? 'UPDATE INVITATION' : 'SEND INVITATION' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-form>
      </v-tab-item>

      <!-- GROUP INVITATION -->
      <v-tab-item v-if="!isEdit" class="pt-8">
        <v-row>
          <v-col>
            <v-alert
              prominent
              color="primary"
              outlined
            >
              <v-row align="center">
                <v-col class="grow">
                  <!-- If you need to onboard a group of more than 5 agents that are transferring
                  from another IMO, you can use a single group invitation. After creating the
                  group invitation, you can share the unique URL with that group so they can
                  register in the Onboarding Portal. -->
                  Group Onboarding is for when 5 or more agents are transferring from the same IMO at one time. Less than 5 can utilize the Transfer path individually.
                  Once your group invitation is approved, you will have an exclusive URL to share with the respective group members, enabling them to enroll through the Onboarding Portal.
                </v-col>
                <v-col class="shrink">
                  <v-btn color="primary" @click="show_new_group_dialog=true">Create Group Invitation Request</v-btn>
                </v-col>
              </v-row>
            </v-alert>
          </v-col>
        </v-row>
        <v-data-table
            v-bind="$attrs"
            :headers="group_headers"
            :items="groups"
            :mobile-breakpoint="0"
            :loading="loading_groups"
            class="q-carrier-list"
        >
          <template #item.group_name="{ item }">
            {{ item.group_name }}
          </template>

          <template v-slot:[`item.created_at`]="{ item }">
            {{ narrateDate(item.created_at, false) }}
          </template>

          <template #item.invitation_link="{ item }">
            <a v-if="showGroupLink(item)" :href="getGroupInviteUrl(item)">{{ getGroupInviteUrl(item) }}</a>
            <div v-else>Pending Approval</div>
          </template>

          <template v-slot:[`item.copy_link`]="{ item }">
            <v-btn v-if="showGroupLink(item)" icon text x-small @click="copyURL(getGroupInviteUrl(item))">
                <v-icon>mdi-content-copy</v-icon>
            </v-btn>
          </template>

          <template #item.active="{ item }">
            <v-switch
              v-model="item.active"
              label=""
              class="status_switch"
              @change="updateStatus(item)"
            ></v-switch>
          </template>
        </v-data-table>
        <v-dialog max-width="80%" v-model="show_new_group_dialog">
          <v-card style="background-color: #FFFFFF !important;">
            <v-card-title>Create Group Invitation</v-card-title>
            <v-card-text>
              <v-row>
                <v-col cols="6">
                  <text-input label="Group Name" em v-model="new_group_name" />
                </v-col>
                <v-col cols="6">
                  <select-box
                    ref="group_size"
                    label="Group Size"
                    :items="group_sizes"
                    v-model="new_group_size"
                  />
                </v-col>
                <!-- <v-col cols="6">
                  <text-input label="Preferred Contact Info" em v-model="new_group_contact_info" />
                </v-col>
                <v-col cols="6">
                  <text-input label="Preferred Contact Times" em v-model="new_group_contact_times" />
                </v-col> -->
                <v-col cols="12">
                  <text-area
                    label="Notes/details"
                    subline="We will be setting up an onboarding call to discuss details. Please provide any information up front so we can come prepared to walk through the process with your group."
                    v-model="new_group_notes"
                  />
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn @click="show_new_group_dialog=false">Cancel</v-btn>
              <v-btn color="primary" @click="createGroup()" :loading="loading_groups">Save</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-tab-item>

      <!-- PRINCIPAL INVITATION -->
      <v-tab-item v-if="hasUserRole(['Staff', 'SuperAdmin']) && (!isEdit || invite.type == 'b2b-principal')" class="pt-8">
        <v-form ref="form_principal" @submit.prevent="save_principal" :style="{ visibility: (isEdit && !loadedInvitationID) ? 'hidden' : 'visible' }">
          <v-row>
            <v-col cols="6">
              <ul>
                <li class="text-h4">Invitations expire after 7 days.</li>
                <li class="text-h4">To resend an existing invitation, you'll need to delete the original one first.</li>
              </ul>
            </v-col>
            <v-col cols="6" class="text-right">

            </v-col>
            <v-col cols="6">
              <text-input ref="first_name" label="First Name" v-model="first_name" :errors="formErrors.first_name" :disabled="invite_accepted == 1"  maxlength="50" />
            </v-col>
            <v-col cols="6">
              <text-input ref="last_name" label="Last Name" v-model="last_name" :errors="formErrors.last_name" :disabled="invite_accepted == 1"  maxlength="50" />
            </v-col>
            <v-col cols="6">
              <text-input ref="email" label="Email" em v-model="email" :errors="formErrors.email"  :disabled="invite_accepted == 1" :extra="formFieldTypes.TYPE_EMAIL" />
            </v-col>
            <v-col cols="6">
              <text-input ref="alt_email" label="Alternate Email" v-model="alt_email" :errors="formErrors.alt_email" :disabled="invite_accepted == 1" :extra="formFieldTypes.TYPE_EMAIL" />
            </v-col>
            <v-col cols="6">
              <text-input
                ref="phone_number"
                label="Phone Number"
                v-model="phone_number"
                :extra="formFieldTypes.TYPE_PHONE"
                :errors="formErrors.phone_number"
                :disabled="invite_accepted == 1"
              />
            </v-col>
            <!-- <v-col cols="6">
              <text-input ref="source" label="Referral Source" v-model="source" :errors="formErrors.first_name" :disabled="invite_accepted == 1" />
            </v-col> -->
            <v-col cols="6">
              <select-box
                ref="carrier"
                label="Carriers"
                v-model="carrier"
                :items="getCarriers"
                multiple
                :errors="formErrors.carrier"
              ></select-box>
            </v-col>
            <v-col cols="6">
              <select-box
                ref="contract_level"
                label="Contract Level"
                :items="contractLevels"
                v-model="contract_level"
                :errors="formErrors.contract_level"
              />
            </v-col>

            <v-col cols="6">
              <select-box
                ref="service_level"
                label="Service Level"
                :items="['Driven', 'Advisory']"
                v-model="service_level"
                :errors="formErrors.service_level"
              />
            </v-col>
            <v-col cols="6">
              <select-box
                ref="agreement_type"
                label="Agreement Type"
                :items="['Standard', 'Custom']"
                v-model="agreement_type"
                :errors="formErrors.agreement_type"
              />
            </v-col>
            <v-col cols="6">
              <select-box
                label="Commissions"
                :items="optionsCommissions"
                v-model="commissions"
                :errors="formErrors.commissions"
                item-text="label"
                item-value="value"
                ref="commissions"
              />
            </v-col>
            <v-col cols="6">
              <v-switch
                ref="bonus_addendum"
                v-model="bonus_addendum"
                label="Will there be a bonus addendum?"
                class="pl-4"
              ></v-switch>
              <v-switch
                ref="multiple_owners"
                v-model="multiple_owners"
                label="Are there multiple owners?"
                class="pl-4"
              ></v-switch>
            </v-col>
            <v-col cols="6" v-if="agreement_type == 'Custom' || multiple_owners">
              <v-flex>
                <span class="text-h5" role="input-label">Custom Contract</span>
                <v-file-input
                  v-if="!custom_contract_upload_url"
                  ref="custom_contract_upload"
                  outlined
                  prepend-inner-icon="mdi-paperclip"
                  prepend-icon=""
                  v-model="custom_contract_upload"
                  :error-messages="formErrors.custom_contract_upload"
                ></v-file-input>
                <v-btn
                  v-if="custom_contract_upload_url"
                  text
                  small
                  color="primary"
                  :href="`/storage/passthrough?url=${custom_contract_upload_url}`"
                  target="_blank"
                  class="ml-2"
                >
                  View
                </v-btn>
                <v-btn
                  v-if="custom_contract_upload_url"
                  text
                  small
                  color="error"
                  @click="custom_contract_upload_url = null"
                >
                  Replace
                </v-btn>
              </v-flex>
            </v-col>
            <v-col cols="12">
              <v-row justify="space-between">
                <v-col>
                  <v-btn large :loading="saving && save_for_later" type="submit" @click="save_for_later = true">SAVE FOR LATER</v-btn>
                </v-col>
                <v-col class="text-right">
                  <v-btn color="primary" large :loading="saving && !save_for_later" type="submit">
                    {{ isEdit && invite.sent ? 'UPDATE INVITATION' : 'SEND INVITATION' }}
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-form>
      </v-tab-item>
    </v-tabs>
    <v-dialog v-model="potential_match_found" width="400">
      <v-card>
        <v-card-title>Potential Match Found</v-card-title>
        <v-card-text>Do you wish to proceed?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="potential_match_found = false">No</v-btn>
          <v-btn @click="potentialMatchOverride()" class="ml-2">Yes</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import TextInput from '@/components/base/TextInput'
import TextArea from '@/components/base/TextArea'
import SelectBox from '@/components/base/SelectBox'
import DatePicker from '@/components/base/DatePicker'
import {
  FORM_FIELD_TYPES,
  RECRUIT_CONTRACT_LEVELS,
  RECRUIT_COMMISSIONS,
  RECRUIT_INVITE_PERSONAL_MESSAGE_DEFAULT,
  RECRUIT_SOURCE,
  LICENSE_STATUS,
} from '@/utils/const'
import { validateEmail } from '@/utils/helper'
import { narrateDate } from '@/utils/helper'

export default {
  components: {
    TextInput,
    TextArea,
    SelectBox,
    DatePicker,
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
    }),
    group_headers() {
      return [
        { text: 'Group Name', value: 'group_name' },
        { text: 'Created', value: 'created_at' },
        { text: 'Invitation Link', value: 'invitation_link' },
        { text: '', value: 'copy_link' },
        { text: 'Active', value: 'active' },
      ]
    },
    formFieldTypes() {
      return FORM_FIELD_TYPES
    },
    optionsContractLevels() {
      return RECRUIT_CONTRACT_LEVELS
    },
    optionsCommissions() {
      return RECRUIT_COMMISSIONS
    },
    optionsSource() {
      return RECRUIT_SOURCE
    },
    optionsLicenseStatus() {
      return LICENSE_STATUS
    },
    personalizedMessageDefault() {
      return RECRUIT_INVITE_PERSONAL_MESSAGE_DEFAULT
    },
    isEdit() {
      return this.$route.name == 'edit_invitation';
    },
    isAgent() {
      return this.tenant === 'Q2A' || this.invite.type == 'b2b-agent'
    },
    isPrincipal() {
      return this.invite.type == 'b2b-principal'
    },
    basicDataLoaded() {
      return this.contractLevels.length && this.possibleUplines.length && this.corporateDivision !== null
    },
    uplineAgents() {
      let upline_agents =  this.possibleUplines.map(item => {
        item.agent_select_text = `${item.name} - ${item.agent_code}`
        return item
      })
      return upline_agents
    },
    tenant()  {
      return process.env.MIX_APP_TENANT
    },
    getCarriers() {
      return process.env.MIX_AVAILABLE_CARRIERS.split(',')
    }
  },
  data() {
    return {
      first_name: '',
      last_name: '',
      email: '',
      email_confirm: '',
      phone_number: '',
      state: '',
      contract_level: '',
      commissions: '',
      source: '',
      upline: null,
      person_message: RECRUIT_INVITE_PERSONAL_MESSAGE_DEFAULT,
      corporate: false,
      invite_accepted: false,

      // saving status
      user: this.$store.state.auth.user,
      saving: false,
      formErrors: {},

      contractLevels: [],
      possibleUplines: [],
      corporateDivision: null,

      loadedInvitationID: null,
      show_demo_invitation: false,
      requesting_demo_invite: false,
      demo_request_response: null,

      license_status: "",
      experience: "",
      advanced_markets: false,

      show_candidate_search: false,
      searching_candidates: false,
      candidate_search_response: null,
      search: {
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        state: '',
        dob: '',
      },
      groups: [],
      search_groups: null,
      invite_group: null,
      selectedGroup: null,
      tab: 0,
      loading: false,
      show_new_group_dialog: false,
      new_group_name: '',
      // new_group_contact_info: '',
      // new_group_contact_times: '',
      loading_groups: false,
      group_sizes: [
        '5 - 10',
        '10 - 25',
        '25 - 50',
        '50 - 75',
        '75 - 100',
        '100+'
      ],
      new_group_notes: '',
      new_group_size: null,
      potential_match_found: false,
      potential_match_override: false,
      states: [],

      // b2b principal fields
      alt_email: '',
      carrier: [],
      agreement_type: '',
      service_level: '',
      bonus_addendum: false,
      multiple_owners: false,
      custom_contract_upload: null,
      custom_contract_upload_url: null,
      save_for_later: false,
      invite: {},
      serviceLevelOptions: [
        { text: 'Advisory', value: 'Advisory' },
        { text: 'Driven', value: 'Driven' }
      ],
    }
  },
  mounted() {
    this.getUserContractLevels();
    this.getUserStates();
    this.getUserPossibleUplines();
    this.getUserCorporateDivision();
    this.getInvitesGroups()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      sendInvite: 'user_invite/sendInvite',
      sendInvitePreflight: 'user_invite/sendInvitePreflight',
      searchCandidateExists: 'user_invite/searchCandidateExists',
      getContractLevels: 'user_invite/getContractLevels',
      getStates: 'user_invite/getStates',
      getAssignableUplines: 'user_invite/getAssignableUplines',
      getCorporateDivision: 'user_invite/getCorporateDivision',
      getInvitation: 'user_invite/getInvitation',
      updateInvitation: 'user_invite/updateInvitation',
      sendDemoInvite: 'user_invite/sendDemoInvite',
      getUserInvitesGroups: 'user_invite/getUserInvitesGroups',
      saveNewGroup: 'user_invite/saveNewGroup',
      updateGroupStatus: 'user_invite/updateGroupStatus',
    }),
    narrateDate,
    onFileChange(file) {
      this.custom_contract_upload = file;
    },
    getGroupInviteUrl(group_invite) {
      return `${window.location.origin}/auth/register?group_code=${group_invite.group_code}`
    },
    showGroupLink(group_invite) {
      return group_invite.group_invite_status == 'approved'
    },
    async copyURL(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.setSnackbar({
              status: true,
              text: 'Copied to clipboard',
              color: 'success',
            })
        } catch($e) {
            console.error($e)
        }
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    async save_principal() {
      await this.save('b2b-principal')
    },
    async save_b2b_agent() {
      console.log('save_b2b_agent', this.first_name, this.last_name, this.email, this.phone_number)
      await this.save('b2b-agent')
    },
    async save(type = null) {
      this.setSnackbar({ status: false })
      this.formErrors = {}
      let isPrincipal = type == 'b2b-principal'
      let isB2bAgent = type == 'b2b-agent'
      let isB2b = isPrincipal || isB2bAgent


      if (!this.first_name) {
        this.formErrors.first_name = 'First name required'
      }

      if (!this.last_name) {
        this.formErrors.last_name = 'Last name required'
      }

      if (!this.email) {
        this.formErrors.email = 'Email required'
      } else if (!this.isEmail(this.email)) {
        this.formErrors.email = 'Email is not valid'
      } else if (!isB2b && this.email !== this.email_confirm && !this.invite_accepted) {
        this.formErrors.email = "Email confirmation doesn't match"
      }

      if (this.alt_email != '' && !this.isEmail(this.alt_email)) {
        this.formErrors.alt_email = 'Alternate Email is not valid'
      }

      if (!isB2b && !this.phone_number) {
        this.formErrors.phone_number = 'Phone number required'
      }

      //optional fields if saving for later
      if(!this.save_for_later) {
        if (!isB2bAgent && !this.contract_level) {
          this.formErrors.contract_level = 'Contract Level required'
        }

        if (!isPrincipal && !this.upline) {
          this.formErrors.upline = 'Upline required'
        }

        if (isB2b && !this.commissions) {
          this.formErrors.commissions = 'Commissions field is required'
        }

        if (!isB2b && !this.source) {
          this.formErrors.source = 'Source required'
        }

        if (!isB2b && !this.license_status) {
          this.formErrors.license_status = 'License status required'
        }

        if (!isB2b && !this.experience) {
          this.formErrors.experience = 'Agent experience required'
        }

        if(isPrincipal && !this.carrier.length > 0) {
          this.formErrors.carrier = 'Carrier selection is required'
        }

        if(isPrincipal && !this.agreement_type) {
          this.formErrors.agreement_type = 'Agreement Type is required'
        }

        if(isPrincipal && this.multiple_owners && this.agreement_type != 'Custom') {
          this.formErrors.agreement_type = 'A Custom agreement is required if multiple owners are selected.'
        }

        if(isPrincipal && !this.service_level) {
          this.formErrors.service_level = 'Service Level is required'
        }

        if(isPrincipal && this.multiple_owners && !this.custom_contract_upload) {
          this.formErrors.custom_contract_upload = 'Custom Contract Upload is required'
        }
      }


      if (!this.isEdit && Object.keys(this.formErrors).length > 0) {
        this.setSnackbar({
          status: true,
          text: 'You have some errors to fix before saving.',
          color: 'warning',
        })
      } else {
        this.saving = true

        if(this.isEdit) {
          const formData = new FormData();
          formData.append('id', this.loadedInvitationID);
          formData.append('name', `${this.first_name} ${this.last_name}`);
          formData.append('email', this.email);
          formData.append('user_id', this.$store.state.auth.user.id);
          formData.append('contract_level', this.contract_level == '' ? '80' : this.contract_level);
          formData.append('upline', this.upline);
          formData.append('phone', this.phone_number.replace(/[^0-9]/g, ''));
          formData.append('corporate', this.corporate);
          formData.append('person_message', this.person_message);
          formData.append('source', this.source);
          formData.append('commissions', this.commissions);
          formData.append('license_status', this.license_status);
          formData.append('experience', this.experience);
          formData.append('advanced_markets', this.advanced_markets);
          formData.append('group_name', this.selectedGroup);
          formData.append('state', this.state);
          formData.append('alt_email', this.alt_email);
          formData.append('carrier', this.carrier.join(', '));
          formData.append('agreement_type', this.agreement_type);
          formData.append('service_level', this.service_level);
          formData.append('bonus_addendum', this.bonus_addendum);
          formData.append('multiple_owners', this.multiple_owners);
          formData.append('custom_contract_upload', this.custom_contract_upload); // Assuming this is a File object
          formData.append('type', type);
          formData.append('save_for_later', this.save_for_later);
          await this.updateInvitation(formData)
            .then(() => {
              this.clearForm();
              this.setSnackbar({
                status: true,
                text: 'Invitation updated successfully.',
                color: 'success',
              });
              this.$router.push({
                name: 'view_my_invites',
              })
            })
            .catch(
              ({
                response: {
                  data: { errors, message },
                },
                message: messageHttp,
              }) => {
                if (errors) this.formErrors = errors
                this.setSnackbar({
                  status: true,
                  text: message || messageHttp || '',
                  color: 'rgba(255, 0, 0, 0.8)',
                })
              },
            )
            .finally(() => {
              this.saving = false
            })
        } else {

          //preflight to look for potential matches in b2b
          if(!this.potential_match_override) {
            await this.sendInvitePreflight({
              name: `${this.first_name} ${this.last_name}`,
              first_name: this.first_name,
              last_name: this.last_name,
              email: this.email,
              phone: this.phone_number.replace(/[^0-9]/g, ''),
              state: this.state,
            })
            .then((response) => {
              if(response.data.found)
                this.potential_match_found = true
              else
                this.potential_match_found = false
            })
          }

          if(this.potential_match_found) {
            this.saving = false
            return
          }

          const formData = new FormData();
          formData.append('name', `${this.first_name} ${this.last_name}`);
          formData.append('email', this.email);
          formData.append('user_id', this.$store.state.auth.user.id);
          formData.append('contract_level', this.contract_level == '' ? '80' : this.contract_level);
          formData.append('upline', this.upline);
          formData.append('phone', this.phone_number.replace(/[^0-9]/g, ''));
          formData.append('corporate', this.corporate);
          formData.append('person_message', this.person_message);
          formData.append('source', this.source);
          formData.append('commissions', this.commissions);
          formData.append('license_status', this.license_status);
          formData.append('experience', this.experience);
          formData.append('advanced_markets', this.advanced_markets);
          formData.append('group_name', this.selectedGroup);
          formData.append('state', this.state);
          formData.append('alt_email', this.alt_email);
          formData.append('carrier', this.carrier.join(', '));
          formData.append('agreement_type', this.agreement_type);
          formData.append('service_level', this.service_level);
          formData.append('bonus_addendum', this.bonus_addendum);
          formData.append('multiple_owners', this.multiple_owners);
          formData.append('custom_contract_upload', this.custom_contract_upload); // Assuming this is a File object
          formData.append('type', type);
          formData.append('save_for_later', this.save_for_later);

          await this.sendInvite(formData)
            .then((response) => {
              this.clearForm();
              this.setSnackbar({
                status: true,
                text: 'Invitation created successfully.',
                color: 'success',
              });
              this.$router.push({
                name: 'view_my_invites',
              })
            })
            .catch(
              (response) => {
              //  {
              //   response: {
              //     data: { errors, message },
              //   },
              //   message: messageHttp,
              // }
                if(response?.data?.id) {
                  this.clearForm();
                  this.setSnackbar({
                    status: true,
                    text: 'Invitation created successfully.',
                    color: 'success',
                  });
                  this.$router.push({
                    name: 'view_my_invites',
                  })

                } else if (response?.message) {
                  this.formErrors = response?.data?.errors
                  this.setSnackbar({
                    status: true,
                    text: response?.message || response?.data?.messageHttp || '',
                    color: 'rgba(255, 0, 0, 0.8)',
                  })
                }
              },
            )
            .finally(() => {
              this.saving = false
            })
        }
      }
    },
    potentialMatchOverride() {
      this.potential_match_found = false
      this.potential_match_override = true
      this.save()
    },
    async createGroup() {
      this.loading_groups = true
      await this.saveNewGroup({
            group_name: this.new_group_name,
            group_size: this.new_group_size,
            // contact_info: this.new_group_contact_info,
            // preferred_times: this.new_group_contact_times,
            notes: this.new_group_notes
          })
            .then(() => {
              this.new_group_name = ''
              this.setSnackbar({
                status: true,
                text: 'Group invitation created successfully.',
                color: 'success',
              });
              this.show_new_group_dialog = false
              this.getInvitesGroups()
            })
            .catch(
              ({
                response: {
                  data: { errors, message },
                },
                message: messageHttp,
              }) => {
                if (errors) this.formErrors = errors
                this.setSnackbar({
                  status: true,
                  text: message || messageHttp || '',
                  color: 'rgba(255, 0, 0, 0.8)',
                })
              },
            )
            .finally(() => {
              this.loading_groups = false
            })
    },
    updateStatus(item) {
      this.updateGroupStatus(item)
      .then(response => {
        this.setSnackbar({
                status: true,
                text: 'Group status updated.',
                color: 'success',
              });
      }).catch(error => {
        this.setSnackbar({
                status: true,
                text: 'Error updating group status.',
                color: 'rgba(255, 0, 0, 0.8)',
              });
      })
    },
    getUserContractLevels() {
      this.getContractLevels().then(response => {
        this.contractLevels = response.data
        if(this.basicDataLoaded)
          this.loadInvitation();
      })
    },
    getUserStates() {
      this.getStates().then(response => {
        this.states = response.data
        // if(this.basicDataLoaded)
        //   this.loadInvitation();
      })
    },
    getUserPossibleUplines() {
      this.getAssignableUplines().then(response => {
        this.possibleUplines = response.data
        if(this.basicDataLoaded)
          this.loadInvitation();
      })
    },
    getUserCorporateDivision(){
      this.getCorporateDivision().then(response => {
        this.corporateDivision = response.data
        if(this.basicDataLoaded)
          this.loadInvitation();
      })
    },
    loadInvitation() {
      if(this.isEdit) {
        this.getInvitation(this.$route.params.invitation_id)
          .then(res => {
            if(!res.data || !res.data['user-invite'])
              throw 'invalid invitation data'

            const item = res.data['user-invite']
            console.log('Loaded invitation data:', item); // Debug log
            console.log('Service level from API:', item.service_level); // Debug log

            this.invite = res.data['user-invite']
            this.loadedInvitationID = item.id

            // Move service_level assignment before $nextTick
            this.service_level = item.service_level
            console.log('Set service_level to:', this.service_level); // Debug log
            this.commissions = item.commissions

            let g = this
            this.$nextTick(() => {
              console.log('Setting service_level:', item.service_level)
              console.log('service_level ref:', g.$refs.service_level)

              g.$refs.first_name?.setValue?.(item.name.split(" ")[0] || '')
              g.$refs.last_name?.setValue?.(item.name.split(" ")[1] || '')
              g.$refs.email?.setValue?.(item.email)
              g.$refs.phone_number?.setValue?.(item.phone)
              g.$refs.source?.setValue?.(item.source)
              g.$refs.person_message?.setValue?.(item.person_message)
              g.$refs.alt_email?.setValue?.(item.alt_email)
              g.$refs.contract_level?.setValue?.(item.contract_level)
              g.$refs.upline?.setValue?.(item.upline_agent_id)
              g.$refs.license_status?.setValue?.(item?.license_status)
              g.$refs.carrier?.setValue?.(item.carrier ? item.carrier.split(',').map(c => c.trim()) : [])
              g.$refs.agreement_type?.setValue?.(item.agreement_type)
              g.$refs.service_level?.setValue?.(item.service_level)
              g.$refs.commissions?.setValue?.(item.commissions || 'in_advance')
            });

            this.upline = item.upline_agent_id
            this.corporate = item.corporate
            this.invite_accepted = item.invite_accepted
            this.experience = item.experience
            this.advanced_markets = item.advanced_markets
            this.commissions = item.commissions || 'in_advance'
            this.selectedGroup = item.group
            this.agreement_type = item.agreement_type
            this.bonus_addendum = item.bonus_addendum
            this.multiple_owners = item.multiple_owners
            this.custom_contract_upload_url = item.custom_contract_upload
            this.service_level = item.service_level
            this.commissions = item.commissions || 'in_advance'

          })
          .catch(err => {
            console.log("error: ", err)
            this.goTo('view_my_invites')
          })
      }
    },
    // https://stackoverflow.com/questions/46155/whats-the-best-way-to-validate-an-email-address-in-javascript
    isEmail(email) {
      return validateEmail(email);
    },
    clearForm(){
      if(this.$refs.form)
      this.$refs.form.reset();
    },
    async processDemoInvite() {
      this.requesting_demo_invite = true
      let response = await this.sendDemoInvite({name: this.user.name, email: this.user.email})
      this.requesting_demo_invite = false
      this.demo_request_response = true
    },
    async search_existing_candidate() {
      this.searching_candidates = true
      this.candidate_search_response = null

      this.formErrors.search_first_name = null
      this.formErrors.search_email = null
      this.formErrors.search_phone = null

      let response = await this.searchCandidateExists(this.search)
      this.candidate_search_response = response.data != '0' ? response.data : null
      if(response.data.user_name || response.data.agent_name)
        this.formErrors.search_first_name = "Match found"
      if(response.data.user_email || response.data.agent_email)
        this.formErrors.search_email = "Match found"
        if(response.data.phone)
        this.formErrors.search_phone = "Match found"

      this.searching_candidates = false
    },
    getInvitesGroups() {
      this.loading_groups = true
      this.getUserInvitesGroups()
        .then(res => {
          if(!res.data || !res.data['groups'])
            throw 'invalid invitation data'
          this.groups = res.data.groups
        })
        .catch(err => {
          console.log("error:", err)
        })
        .finally(() => {
          this.loading_groups = false
        })
    },
  },
  watch: {
    'license_status': function(newV) {
      if(newV == 'licensed')
        return

      this.advanced_markets = false
      this.experience = ""
    },
    $route(to, from) {
      // Logic to handle route change
      console.log('Route changed:', to.path);
      this.clearForm()
      // make sure to reset the form
    },
    service_level(newVal, oldVal) {
      console.log('service_level changed:', oldVal, '->', newVal);
    }
  }
}
</script>

<style scope>
  .black-label label {
    color: rgba(0, 0, 0, 0.87) !important;
  }
  #experience .v-input__slot {
    background-color: transparent !important;
  }
  .v-tab:not(.v-tab--active) {
    border-bottom: 2px solid #efefef; /* Adjust color and opacity as needed */
  }
  .v-input.status_switch.theme--light.v-input--selection-controls.v-input--switch .v-input__slot {
    background-color:transparent !important;
  }
</style>
