<template>
  <v-container class="pa-5 d-flex flex-column align-center justify-center" >
    <div class="text-center" v-if="loading">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <v-container v-else class="ma-0 pa-0">
      <v-container v-if="countTotalNotes()">
        <v-card class="pa-2 d-flex flex-row justify-space-between">
          <v-card-title>
            <v-icon color="warning" v-if="countTotalNotes() != countTotalNotesFixed()">
              mdi-information-outline
            </v-icon>
            <h3 class="warning--text ml-1" v-if="countTotalNotes() != countTotalNotesFixed()">
              Attention! There are {{ countTotalNotes() }} notes for your application. ({{ countTotalNotesFixed() }}
              fixed)
            </h3>
            <h3 class="success--text ml-1" v-else>
              Great! You have fixed all {{ countTotalNotes() }} notes from reviewer.
            </h3>
          </v-card-title>
          <v-card-actions>
            <v-btn text @click="showNotesModal = true">
              <v-icon left> mdi-format-list-bulleted </v-icon>
              Show notes
            </v-btn>
            <!-- <v-btn text>
              <v-icon left> mdi-arrow-left </v-icon>
              Previous
            </v-btn>
            <v-btn text>
              Next
              <v-icon right> mdi-arrow-right </v-icon>
            </v-btn> -->
          </v-card-actions>
        </v-card>
      </v-container>
      <v-container>
        <progress-bar
          v-if="currentStepIndex !== null"
          :value="currentStepIndex + 1"
          :total="totalSteps"
          :description="currentStepTitle"
        ></progress-bar>
      </v-container>
      <form-page ref="page" v-bind:key="currentStepKey" v-if="data" :data="data" :showValidationErrors="showErrors" />
      <div class="text-center" v-else>
        <h3>No content</h3>
      </div>
      <v-container>
        <v-row>
          <v-col>
            <hr class="mt-1" />
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-btn
              v-if="currentStepIndex > 0"
              depressed
              class="mr-2"
              color="primary"
              @click="previous()"
              :disabled="loading"
            >
              PREVIOUS
            </v-btn>
            <v-btn
              depressed
              class="mr-2"
              :color="currentStepIndex == totalSteps - 1 ? 'success' : 'primary'"
              @click="askForNext()"
              :disabled="loading"
            >
              {{ currentStepIndex == totalSteps - 1 ? 'Submit for Review' : 'NEXT' }}
            </v-btn>
            <v-btn
              depressed
              color="success"
              v-if="currentStepIndex == totalSteps - 1"
              @click="helloSign()"
              :disabled="bGettingSignRequest"
              :loading="bGettingSignRequest"
            >
              Sign Agreement
            </v-btn>
          </v-col>
        </v-row>
        <v-row justify="center">
          <v-dialog v-model="showConfirmProceedModal" persistent max-width="590">
            <v-card style="background-color: #fff !important;">
              <v-card-title class="text-h2"> Force proceed? </v-card-title>
              <v-card-text
                >You have some errors with the fields in this page. Do you want to continue anyway?</v-card-text
              >
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="secondary darken-1" @click=";(showConfirmProceedModal = false), (showErrors = true)">
                  No, show me the errors
                </v-btn>
                <v-btn color="primary darken-1" @click="next()"> Yes, I will look back later </v-btn>
                <v-spacer></v-spacer>
              </v-card-actions>
            </v-card>
          </v-dialog>
          <v-dialog v-model="showNotesModal" width="1000">
            <v-card class="pa-5">
              <data-table :headers="noteTableHeaders" :items="Object.values(notes)">
                <template v-slot:[`item.fixed`]="{ item }">
                  <v-icon v-if="item.fixed" color="success"> mdi-check-outline </v-icon>
                </template>
                <template v-slot:[`item.actions`]="{ item }">
                  <v-container class="d-flex flex-row">
                    <v-btn color="primary darken-1" @click="goToSection(item)" text>
                      <v-icon color="primary darken-2"> mdi-arrow-right </v-icon>
                      Go
                    </v-btn>
                    <v-btn v-if="!item.fixed" color="success" text @click="resolveNote(item, 1)">
                      <v-icon color="green darken-1"> mdi-check </v-icon>
                      Mark as Fixed
                    </v-btn>
                    <v-btn v-else color="warning" text @click="resolveNote(item, 0)">
                      <v-icon color="green darken-1"> mdi-close </v-icon>
                      Rollback
                    </v-btn>
                  </v-container>
                </template>
              </data-table>
            </v-card>
          </v-dialog>
        </v-row>
      </v-container>
    </v-container>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'

import HelloSign from 'hellosign-embedded'

import DataTable from '@/components/base/DataTable'
import ProgressBar from '@/components/compound/ProgressBar'
import FormPage from '@/components/form/FormPage'
import { ONBOARD_STATUS_TYPES } from '@/utils/const'

export default {
  name: 'SymmetryOnboard',
  components: {
    ProgressBar,
    FormPage,
    DataTable,
  },
  data() {
    return {
      steps: [], // flow step array, retrieved from api
      currentStepIndex: null, // current step index
      data: {}, // step data retrieved from api
      loading: false,
      showErrors: false,

      bGettingSignRequest: false,

      showConfirmProceedModal: false,

      notes: {}, // review notes for current application
      showNotesModal: false,
      noteTableHeaders: [
        { text: 'Page', value: 'page' },
        { text: 'Section', value: 'section' },
        { text: 'Order (section)', value: 'sort' },
        { text: 'Content', value: 'content' },
        { text: 'Fixed?', value: 'fixed' },
        { text: '', value: 'actions', sortable: false },
      ],

      goToSectionRefKey: null,
    }
  },
  computed: {
    onboardStatus() {
      return this.$store.state.auth.user.status.slug
    },
    submitted() {
      return this.onboardStatus === ONBOARD_STATUS_TYPES.SUBMITTED
    },
    approved() {
      return this.onboardStatus === ONBOARD_STATUS_TYPES.AO_APPROVED || this.onboardStatus === ONBOARD_STATUS_TYPES.HO_APPROVED
    },
    rejected() {
      return this.onboardStatus === ONBOARD_STATUS_TYPES.REJECTED
    },
    unlicensed() {
      return this.onboardStatus === ONBOARD_STATUS_TYPES.UNLICENSED
    },
    completed() {
      return this.onboardStatus === ONBOARD_STATUS_TYPES.ASSIGNED
    },
    totalSteps() {
      return this.steps.length
    },
    currentStepTitle() {
      return this.steps[this.currentStepIndex] && this.steps[this.currentStepIndex].label
    },
    currentStepKey() {
      return this.steps[this.currentStepIndex] && this.steps[this.currentStepIndex].step_ident
    },
  },
  created() {
    this.loading = true
    this.getOnboardSteps()
      .then(response => {
        this.steps = response.data
        this.getCurrentStep()
          .then(res => {
            if (res && res.data && res.data.current_page && res.data.current_page.sort)
              this.currentStepIndex = res.data.current_page.sort - 1;
          })
          .finally(() => {
            if (!this.currentStepIndex) this.currentStepIndex = 0
            this.loading = false
          })
      })
      .catch(err => {
        console.error(err)
        this.setSnackbar({
          status: true,
          text: 'Failed to get flow steps!',
          color: 'warning',
        })
      })

    this.getApplicationReviewNotes(this.$store.state.auth.user.id)
      .then(res => {
        if (res && res.data && typeof res.data === 'object')
          res.data.map(
            note =>
              (this.notes[`${note.form_section_id}-${note.form_section_sort}`] = {
                id: note.id,
                page: note.form_section.form_page.label,
                section: note.form_section.label,
                sectionId: note.form_section_id,
                sort: note.form_section_sort,
                fixed: note.fixed,
                content: note.content,
              }),
          )
      })
      .catch(() => (this.notes = {}))
  },
  watch: {
    currentStepIndex(val) {
      if (this.steps[val]) {
        this.loading = true
        this.showConfirmProceedModal = false
        this.showErrors = false

        this.getViewForm({
          step: this.steps[val].step_ident,
        })
          .then(res => {
            if (res && res.data) {
              this.data = res.data
              setTimeout(() => {
                if (this.$refs.page) this.$refs.page.goToSection(this.goToSectionRefKey)
                this.goToSectionRefKey = null
              }, 100)
            }
          })
          .finally(() => (this.loading = false))
      } else {
        this.data = null
      }
    },
  },
  methods: {
    ...mapActions({
      getUser: 'auth/getUser',
      setSnackbar: 'snackbar/set',
      getOnboardSteps: 'application/getOnboardSteps',
      getCurrentStep: 'application/getCurrentStep',
      updateApplication: 'application/updateApplication',
      getViewForm: 'application/getViewForm',
      getHelloSignView: 'hellosign/view',
      getApplicationReviewNotes: 'review/getNotes',
      resolveApplicationReviewNote: 'review/resolveNote',
    }),
    askForNext() {
      const errorExist = this.$refs.page.hasErrors()
      if (errorExist) {
        // [STEP-VALIDATION]

        // Option1: deactivated
        //  If there is validation errors, ask to continue with errors
        this.showConfirmProceedModal = true

        // Option2: activated
        //  If there is validation errors, you can't move on
        // this.showErrors = true
        // this.$root.$emit('check_validation');
        // this.setSnackbar({
        //   status: true,
        //   text: 'Sorry, you have to fix above errors to move on.',
        //   color: 'warning',
        // })
      } else {
        this.next()
      }
    },
    next() {
      if (this.currentStepIndex < this.steps.length - 1) this.currentStepIndex = this.currentStepIndex + 1
      else {
        // Check review notes are all fixed before re-submittion
        if (this.countTotalNotes() != this.countTotalNotesFixed()) {
          this.setSnackbar({
            status: true,
            text: 'Sorry, you still have some notes left to fix!',
            color: 'warning',
          })
          this.showConfirmProceedModal = false
        } else {
          // this.submit()
        }
      }
    },
    previous() {
      this.currentStepIndex = this.currentStepIndex > 0 ? this.currentStepIndex - 1 : this.currentStepIndex
    },

    countTotalNotes() {
      return this.notes ? Object.keys(this.notes).length : 0
    },
    countTotalNotesFixed() {
      return this.notes ? Object.keys(this.notes).filter(n => this.notes[n].fixed).length : 0
    },

    helloSign() {
      this.bGettingSignRequest = true
      this.getHelloSignView()
        .then(res => {
          if (res && res.data) {
            const { signUrl, clientId } = res.data

            if (signUrl && clientId) {
              const client = new HelloSign()

              client.open(signUrl, {
                clientId,
                skipDomainVerification: (process.env.MIX_HELLOSIGN_SKIP_DOMAIN_VERIFICATION === 'true') ? true : false,
                locale: HelloSign.locales.EN_US,
                redirectTo: '/portal',
                // container: document.getElementById('hellosign-area')
              })

              client.on('finish', () => {
                console.log('Signing process is finished!')
              })

              client.on('message', ({ type, payload }) => {
                console.log('Message recevied: ', type, payload)
              })
            }
          }
        })
        .finally(() => {
          this.bGettingSignRequest = false
        })
    },
    submit() {
      this.setSnackbar({
        status: true,
        text: 'Sorry, you cannot submit application in preview mode.',
        color: 'warning',
      });
      return;

      this.loading = true
      this.showConfirmProceedModal = false

      this.updateApplication({
        id: this.$store.state.auth.user.id,
        status: ONBOARD_STATUS_TYPES.SUBMITTED,
      })
        .then(() => {
          this.getUser()
        })
        .finally(() => (this.loading = false))
    },
    goToSection(item) {
      this.showNotesModal = false

      const newStep = this.steps.findIndex(s => s.label === item.page)
      if (newStep === this.currentStepIndex) {
        this.$refs.page.goToSection(`section_${item.sectionId}_${item.sort}`)
      } else if (newStep !== -1) {
        this.showNotesModal = false
        this.currentStepIndex = newStep

        this.goToSectionRefKey = `section_${item.sectionId}_${item.sort}`
      }
    },
    resolveNote(item, fixed) {
      this.resolveApplicationReviewNote({ appId: item.id, fixed })
        .then(() => {
          item.fixed = fixed
          this.setSnackbar({
            status: true,
            text: 'Resolving note success!',
            color: 'success',
          })
        })
        .catch(() =>
          this.setSnackbar({
            status: true,
            text: 'Resolving note failed!',
            color: 'warning',
          }),
        )
    },
  },
}
</script>

<style lang="sass" scope></style>
