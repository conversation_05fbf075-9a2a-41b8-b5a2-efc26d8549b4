 <template>
  <v-container class="invite-container d-flex flex-column justify-content-center pa-10" style="gap: 20px">
    <v-row>

      <v-col id="content-main" cols="12">
        <div v-html="content"></div>
      </v-col>

      <v-col id="content-1" v-show="false" v-if="hasUserRole('Recruit') || hasUserRole('UnlicensedAgent') || hasUserRole('Staff')" cols="12">
        <div v-html="content"></div>
      </v-col>

      <v-col id="content-2" v-show="false" v-if="hasUserRole('Recruit') || hasUserRole('UnlicensedAgent')" cols="12">
        <div v-html="admin_content"></div>
      </v-col>

      <v-col id="content-3" v-show="false" v-if="hasUserRole('AgencyOwner') || hasUserRole('SuperAdmin')" cols="12">
        <div v-html="content"></div>
        <!-- <editor :api-key="api_key" v-model="content" :init="initEditor" /> -->
      </v-col>

      <v-col id="content-4" v-show="false" v-if="hasUserRole('Staff') || hasUserRole('SuperAdmin')" cols="12">
        <!-- <editor :api-key="api_key" v-model="admin_content" :init="initEditor" /> -->
      </v-col>

      <v-col id="content-5" v-show="false" v-if="hasUserRole('AgencyOwner') || hasUserRole('Staff') || hasUserRole('SuperAdmin')"
       cols="12"
       class="text-center justify-content-center align-items-center">
        <!-- <v-btn color="primary" @click="save"> Save Content </v-btn> -->
      </v-col>

      <v-col id="content-6" v-show="false" v-if="1 == 2 && hasUserRole('AgencyOwner')" cols="12">
        <div v-html="admin_content"></div>
      </v-col>

    </v-row>
  </v-container>
</template>

 <script>
import { mapActions, mapGetters } from 'vuex'
import Editor from '@tinymce/tinymce-vue'

export default {
  name: 'company-editor',
  data() {
    return {
      api_key: process.env.MIX_TINYMCE_APIKEY,
      content: '',
      admin_content: '',
      role: '',
      initEditor: {
        height: 900,
        plugins: [
          'advlist autolink lists link image charmap print preview anchor',
          'searchreplace visualblocks code fullscreen',
          'insertdatetime media table paste code help wordcount',
          'image code media mediaembed pageembed',
        ],
        menubar: 'file edit view insert format tools table tc help',
        toolbar:
          'undo redo | bold italic underline strikethrough | fontselect fontsizeselect formatselect | \
           alignleft aligncenter alignright alignjustify | \
           bullist numlist outdent indent | removeformat | help | \
           link image media pageembed | \
           table tabledelete | tableprops tablerowprops tablecellprops | \
           tableinsertrowbefore tableinsertrowafter tabledeleterow | \
           tableinsertcolbefore tableinsertcolafter tabledeletecol | \
           code',
        image_title: true,
        automatic_uploads: true,
        file_picker_types: 'image',
        file_picker_callback: function (cb, value, meta) {
          var input = document.createElement('input')
          input.setAttribute('type', 'file')
          input.setAttribute('accept', 'image/*')

          input.onchange = function () {
            var file = this.files[0]

            var reader = new FileReader()
            reader.onload = function () {
              /*
                Note: Now we need to register the blob in TinyMCEs image blob
                registry. In the next release this part hopefully won't be
                necessary, as we are looking to handle it internally.
              */
              var id = 'blobid' + new Date().getTime()
              var blobCache = tinymce.activeEditor.editorUpload.blobCache
              var base64 = reader.result.split(',')[1]
              var blobInfo = blobCache.create(id, file, base64)
              blobCache.add(blobInfo)

              /* call the callback and populate the Title field with the file name */
              cb(blobInfo.blobUri(), { title: file.name })
            }
            reader.readAsDataURL(file)
          }

          input.click()
        },
      },
    }
  },
  components: {
    editor: Editor,
  },
  mounted() {
    this.getInformation()
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
    }),
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getContent: 'company_information/getContent',
      saveContent: 'company_information/saveContent',
    }),
    async getInformation() {
      await this.getContent(this.$store.state.auth.user.id).then(res => {
        if (res && res.data) {
          this.content = res.data.content;
          this.admin_content = res.data.admin_content;
        }
      })
    },
    async save() {

      let is_agent_owner = false
      if (this.hasUserRole('AgencyOwner')) {
        is_agent_owner = true
      }

      await this.saveContent({
        user_id: this.$store.state.auth.user.id,
        content: this.content,
        admin_content: this.admin_content,
      }).then(() => {
        this.setSnackbar({
          status: true,
          text: 'Content saved successfully.',
          color: 'success',
        })
      })
    },
  },
}
</script>
