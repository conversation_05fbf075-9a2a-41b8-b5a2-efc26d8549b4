<template>
  <v-container class="review-container pa-5 d-flex flex-column">
    <!-- <div v-if="loading || (this.activeReviewer(this.$route.params.user_id) == undefined && isNotLocalEnv())" class="text-center"> -->
    <div v-if="loading" class="text-center">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <div class="text-center" v-else-if="!application">
      <h3 class="text-h3">Invalid application data</h3>
    </div>
    <div class="text-center" v-else-if="hasUserRole('AgencyOwner') && application.status != onboard_status_types.SUBMITTED_AO">
      <h3 class="text-h3">Application has not been submitted yet.</h3>
    </div>
    <div class="text-center" v-else-if="hasUserRole(['SuperAdmin', 'Staff']) && application.approved_by_ao != true">
      <h3 class="text-h3">Pending Approval by Agency Owner.</h3>
    </div>
    <div class="text-center" v-else-if="isBeingReviewed()">
      <h3 class="text-h3">{{ activeReviewer($route.params.user_id) }} is reviewing this application.</h3>
    </div>
    <v-container v-else>
      <v-card elevation="0" outlined rounded="0" class="pa-5">
        <v-btn class="float-right mb-4" :href="`/impersonate/`+this.$route.params.user_id" target="_blank">Login</v-btn>
        <ul class="primary--text text--darken-4" style="clear:both;">
          <li>
            Please review the following submitted application thoroughly. When finished, you will need to click the
            appropriate status to continue the application.
          </li>
          <li class="mt-1">
            If everything looks good, click <b>'Approve'</b> to submit the application to your
            <b v-if="hasUserRole('AgencyOwner')">Home Office</b>
            <b v-else-if="hasUserRole(['SuperAdmin', 'Staff'])">Contracting Dept</b>
            .
          </li>
          <li class="mt-1">
            If there is an issue with the application that needs to be resolved, click <b>'Needs Revision'</b> to
            require additional input from the candidate/new agent.
          </li>
          <li class="mt-1">
            If you are unable to approve or resolve the application, click
            <b>'Reject'</b> to end the workflow and send a notification to the candidate/new agent. All rejected
            submissions will <b>require</b> a reason in the notes section.
          </li>
          <li class="mt-1 success--text">
            <b>Double-Click</b> on any section you want to leave message with. The ones with <b>information icon</b> already
            has a message which you can update too. <br />* Checked notes mean they are <b>fixed</b> by candidates. If you
            update the content, it turns un-fixed again.
          </li>
        </ul>
        <br />
        Comment From Last Review: <br />
        <p class="pa-2" v-if="!application.comments.length">No comment from reviewer</p>
        <div class="pa-2" v-else>
          <div v-for="comment in application.comments.filter(c => c.trigger)" :key="comment.id" class="pa-0">
            <b>{{ narrateDate(comment.created_at) }}</b>
              <span v-if="comment.user_status.slug.indexOf('Error') == -1">
                by <b>{{ comment.trigger.name }}</b> ({{ comment.trigger.roles[0].name }}) :
              </span>
              <br/>
              <span v-if="comment.user_status.slug.indexOf('Error') > -1" class="error--text">
                <b class="text-h3">|</b> &nbsp; <strong>{{comment.user_status.name}}:</strong> {{ comment.note }}
              </span>
              <span v-else>
                <b class="text--secondary text-lighten-3 text-h3">|</b> &nbsp; {{ comment.note }}
              </span>
          </div>
        </div>
      </v-card>
      <!-- <v-container class="pa-0 mt-5">
        <router-link :to="{ name: 'my_task' }">
          <v-btn depressed color="primary">
            <v-icon left dark> mdi-arrow-left </v-icon>
            Back to Task List
          </v-btn>
        </router-link>
      </v-container> -->

      <internal-notes v-if="hasUserRole('SuperAdmin') || hasUserRole('Staff')" :internal_notes="application.internal_notes" :app_id="$route.params.user_id"></internal-notes>

      <check-box v-if="false" class="d-block float-right mt-5 mb-4" label="Show Empty Fields"></check-box>
      <span class="d-block mt-5 mb-2 text-h4"
        >ONBOARDING APPLICATION : ENTRY <b>#{{ application.id }}</b> <span class="d-inline-flex align-center"><pathway-chips :pathway="application.pathway"></pathway-chips></span></span
      >

      <v-alert v-if="applicationGroupName()" type="info">Group Invitation: {{ applicationGroupName() }}</v-alert>

      <v-alert v-if="showVectorAlert" type="error">
        <div class="d-flex justify-space-between align-center w-100">
          <span>Vector exists for this application.</span>
          <v-btn small outlined color="white" @click="showVectorDialog = true">View Details</v-btn>
        </div>
      </v-alert>

      <v-container v-for="page in application.pages" :key="page.id" class="pa-0">
        <h2 class="text-h3 primary">{{ page.label }}</h2>
        <p v-if="!!page.subline">{{ page.subline }}</p>
        <v-container>
          <v-row
            v-for="section in page.sections"
            :key="section.id"
            :style="{ display: `${canShowSection(section) ? 'flex' : 'none'}` }"
          >
            <v-col
              cols="12"
              lg="7"
              md="8"
              v-for="(v, sort) in (new Array(section.repeats))"
              v-bind:key="sort"
            >
              <v-tooltip top>
                <template v-slot:activator="{ on, attrs }">
                  <div
                    class="element-section"
                    v-on:dblclick="noteHere(section, sort)"
                    v-bind="attrs"
                    v-on="isNoteExist(section.id, sort) && on"
                  >
                    <br v-if="sort>0"/>
                    <v-flex>
                      <h3 class="text-subtitle-2">
                        <b v-html="section.label"></b>
                        <v-icon v-if="isNoteExist(section.id, sort)" color="primary">mdi-information-outline </v-icon>
                        <v-icon v-if="isNoteFixed(section.id, sort)" color="success">mdi-check-outline </v-icon>
                      </h3>
                      <p v-if="!!section.subline">{{ section.subline }}</p>
                    </v-flex>
                    <v-flex v-for="(field, field_id) in section.fields" v-bind:key="field_id">
                      <v-container class="overflow-auto ma-0 pa-0" v-if="field.value[sort]">
                        <template v-if="isImageField(field) && isUpload(field)">
                           <span v-for="(f, i) in field.value[sort]" v-bind:key="i">
                            <template v-if="isPDF(f) || isWord(f)">
                              <a :href="f" target="_blank" download>File</a>
                            </template>
                            <template v-else>
                              <img :src="f" style="width: 100%" />
                            </template>
                            <br>
                          </span>
                        </template>
                        <template v-else-if="isImageField(field) && isSignature(field)">
                          <img :src="field.value[sort]" style="width: 100%" />
                        </template>
                        <template v-else-if="(field.value[sort] == 'YES' && page.label.indexOf('Legal Questions') > -1) || (section.label.indexOf('returning to Symmetry') > -1 && field.value[sort] == 'YES')">
                          <span class="error white--text font-weight-black pa-2">{{ `${field.label && field.label + ':'} ${field.value[sort]}` }}</span>
                        </template>
                        <template v-else>
                          <span v-if="field.is_secure && hasUserRole('AgencyOwner')">{{ `${field.label && field.label + ':'} *********` }}</span>
                          <span v-else v-html="renderValue(field, sort)"></span>
                          <span v-if="section.label.indexOf('I was recruited into') > -1 && application.source">(Recruiter: {{application.source}})</span>
                        </template>
                      </v-container>
                    </v-flex>
                  </div>
                </template>
                <span>{{ getNoteTooltip(section.id, sort) }}</span>
              </v-tooltip>
            </v-col>
          </v-row>
        </v-container>
      </v-container>
      <v-card elevation="0" outlined rounded="0" class="mt-3 pa-5">
        <h4 class="text-h4 mb-5">Workflow</h4>
        <span class="mb-2 d-block">Entry ID: {{ application.id }}</span>
        <span class="mb-2 d-block">Pathway: <span class="d-inline-flex align-center"><pathway-chips :pathway="application.pathway"></pathway-chips></span></span>
        <span class="mb-2 d-block">Upline: {{ application.direct_upline }} <v-btn text @click="showUplineEditModal" class="ml-4" :loading="updating_user_upline">Change</v-btn></span>
        <span class="mb-2 d-block">Agency Owner: {{ application.agency_owner }}</span>
        <span class="mb-2 d-block">Contract Level: {{ application.contract_level }} <v-btn text @click="show_contract_level_edit_modal = true" class="ml-4" :loading="updating_user_contract_level">Change</v-btn></span>
        <span class="mb-2 d-block">Submitted: {{ narrateDate(application.submitted_at) }}</span>
        <span class="mb-2 d-block">Last updated: {{ narrateDate(application.updated_at) }}</span>
        <span class="mb-2 d-block">Submitted by: {{ application.name }}</span>
        <span class="mb-2 d-block">Status: {{ application.status }}</span>
        <v-divider></v-divider>

        <!-- INVITATION INFO -->
        <template v-if="application.invitation || tenant == 'Q2B'">
          <span class="my-2 d-block font-weight-bold">Invitation Settings</span>
          <span class="mb-2 d-block" v-if="application.invitation && application.invitation.type">Type: {{ application.invitation.type.replace(/-/g, ' ').split(' ').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ') }}</span>
          <span class="mb-2 d-block" v-if="application.invitation && application.invitation.license_status">Licensed: {{ application.invitation.license_status }}</span>
          <span class="mb-2 d-block" v-if="application.invitation && (application.invitation.experience || isLicensed())">Experience: {{ application.invitation.experience }}</span>
          <span class="mb-2 d-block" v-if="isLicensed() && isExperienced()">Advanced Markets: {{ application.invitation.advanced_markets == 1 ? 'Yes' : 'No' }}</span>
          <span class="mb-2 d-block" v-if="application.invitation && application.invitation.commissions" :class="{'error white--text font-weight-black pa-2': application.invitation.commissions === 'as_earned'}">Commissions: {{ formatCommissions(application.invitation.commissions) }}</span>
          <div v-if="tenant == 'Q2B'">
            <span class="mb-2 d-block">Carriers: {{ application.invitation.carrier }}</span>
            <span class="mb-2 d-block">Alternative Email: {{ application.invitation.alt_email }}</span>
            <span class="mb-2 d-block">Agreement: {{ application.invitation.agreement_type }}</span>
            <span class="mb-2 d-block">Service Level: {{ application.invitation.service_level }}</span>
            <span class="mb-2 d-block">Bonus Addendum: {{ application.invitation.bonus_addendum ? 'Yes' : 'No' }}</span>
            <span class="mb-2 d-block">Multiple Owners: {{ application.invitation.multiple_owners ? 'Yes' : 'No' }}</span>
          </div>

          <v-divider></v-divider>
        </template>

        <!-- DISPLAY SELECTED CARRIERS IF STAFF REVIEWING -->
        <template v-if="tenant != 'Q2B' && hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF])">
          <strong>Carriers:</strong>
          <span v-if="isContracted()"> {{application.carriers}} (SBLI, LGA, and UHL included by default)</span>
          <span v-else>Recommended carriers for agent based on selected pathway:
            (Mutual of Omaha, Foresters, Americo, SBLI, LGA, AIG, F&G, UHL)</span>
        </template>

        <template v-if="tenant == 'Q2B' && hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && application.ao_carriers">
          <v-divider class="my-4"></v-divider>
          <div>
            <strong>Agency Owner's Selected Carriers:</strong>
            <span class="d-block mt-2">{{ application.ao_carriers }}</span>
          </div>
        </template>

        <!-- CARRIER SELECTION BY AO-->
        <template v-else-if="hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER)">
          <div v-if="isContracted()">
            <!-- <span v-if="isAdvancedMarkets()" class="mt-4 d-block font-weight-bold">Choose 3 carriers. SBLI will be included by default.</span> -->
            <span class="mt-4 d-block font-weight-bold">SBLI, LGA, and UHL will be assigned by default. Choose up to 6 additional carriers.</span>
            <v-select
              label="Select Carriers"
              v-model="carriers"
              :items="availableCarriers()"
              :menu-props="{ maxHeight: '400' }"
              multiple
              :hint="carrierSelectionHint()"
              persistent-hint
              class="mb-8"
              :rules="limitSelections()"
            ></v-select>
          </div>
          <div v-else>
            <strong>Carriers:</strong>
            Recommended carriers for agent based on selected pathway:
              (Mutual of Omaha, Foresters, Americo, SBLI, LGA, AIG, F&G, UHL)
          </div>
          <span class="mt-4 d-block font-weight-bold">Previous Experience: {{ application.previous_experience }}</span>
        </template>

        <!-- Add new section for Advanced Market enrollment -->
        <template v-if="application.previous_experience === 'YES'">
          <v-checkbox
            v-model="advanced_market_enrollment"
            label="Is this agent ready to enroll in Advanced Market training and certification?"
            class="mt-4"
            :disabled="!hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER)"
          ></v-checkbox>

          <v-card
            v-if="advanced_market_enrollment"
            outlined
            class="mt-4 pa-4"
          >
            <p>This agent will be automatically enrolled in Advanced Market training. Once complete, the following carrier contract links will be automatically sent for the agent to get contracted with Advisors Excel.</p>
            <ul class="mt-2">
              <li>North American</li>
              <li>Security Benefit</li>
              <li>Investors Heritage</li>
              <li>Augustar</li>
            </ul>
          </v-card>
        </template>


        <!-- <template v-else-if="!isContracted()">
          <strong>Carriers:</strong>
            Recommended carriers for agent based on selected pathway:
            (Mutual of Omaha, Foresters, Americo, SBLI, LGA, AIG, F&G, UHL)
        </template> -->


        <!-- <template v-else-if="!isLicensed()">
          <strong>Carriers:</strong> Default carriers will be added by contracting.
        </template> -->



        <hr class="my-4" />
        <text-area class="mt-2" ref="finalNote" label="Comment" v-model="finalNote" />
        <v-flex class="mt-4">

          <!-- AO Buttons -->
          <div v-if="hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER)">
            <v-btn
              small
              class="mr-1"
              color="primary"
              @click="completeReview(onboard_status_types.AO_APPROVED)"
              :disabled="loading || carriers.length >= carrierLimit()"
              :loading="loading"
            >
              <v-icon left color="success">mdi-check</v-icon>
              AO APPROVE
            </v-btn>

            <v-btn
              small
              depressed
              class="mr-1"
              color="primary"
              @click="completeReview(onboard_status_types.REJECTED_AO)"
              :disabled="loading"
              :loading="loading"
            >
              <v-icon left color="red">mdi-close</v-icon>
              REJECT
            </v-btn>

            <v-btn
              small
              depressed
              color="primary"
              @click="completeReview(onboard_status_types.REVISION_AO)"
              :disabled="loading"
              :loading="loading"
            >
              <v-icon left color="blue">mdi-refresh</v-icon>
              Need Revision
            </v-btn>
          </div>


          <!-- STAFF Buttons -->
          <div v-if="hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF])">
            <v-row>
              <v-col>
                <v-checkbox v-model="felony_knockout" label="Application rejected for felony knock out?"></v-checkbox>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="6">
                <v-btn
                  v-if="hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF])"
                  small
                  class="mr-1"
                  color="primary"
                  @click="completeReview(onboard_status_types.HO_APPROVED)"
                  :disabled="loading || felony_knockout"
                  :loading="loading"
                >
                  <v-icon left color="success">mdi-check</v-icon>
                  APPROVE
                </v-btn>

                <v-btn
                  small
                  depressed
                  class="mr-1"
                  color="primary"
                  @click="completeReview(onboard_status_types.REJECTED_HO)"
                  :disabled="loading"
                  :loading="loading"
                >
                  <v-icon left color="red">mdi-close</v-icon>
                  REJECT
                </v-btn>

                <v-btn
                  small
                  depressed
                  color="primary"
                  @click="completeReview(onboard_status_types.REVISION_HO)"
                  :disabled="loading || felony_knockout"
                  :loading="loading"
                >
                  <v-icon left color="blue">mdi-refresh</v-icon>
                  Need Revision
                </v-btn>
              </v-col>

              <v-col cols="6" class="text-right">
                <v-btn
                  v-if="hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && application.status == onboard_status_types.HO_APPROVED"
                  small
                  class="mr-1"
                  color="error"
                  @click="completeReview(onboard_status_types.FORCE_HQ)"
                  :disabled="loading || felony_knockout"
                  :loading="loading"
                >
                  <v-icon left color="white">mdi-refresh</v-icon>
                  FORCE RE-SUBMIT TO HQ
                </v-btn>
              </v-col>
            </v-row>
          </div>


        </v-flex>
      </v-card>
    </v-container>

    <v-dialog v-model="showNoteModal" width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-textarea
          outlined
          placeholder="Input your note here"
          hide-details="auto"
          auto-grow
          v-model="editingNote"
        ></v-textarea>

        <v-divider></v-divider>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="saveNote()"> Save </v-btn>
          <v-btn color="primary" text @click="showNoteModal = false"> Close </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_upline_edit_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          <select-box
            :autocomplete="true"
            :loading="uplineAgents.length == 0"
            ref="upline"
            label="Upline"
            :items="uplineAgents"
            v-model="upline"
            item-text="agent_select_text"
            item-value="agent_id"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="updateUserUpline" color="primary" :loading="updating_user_upline">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_contract_level_edit_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          <select-box
            ref="contract_level"
            label="Contract Level"
            :items="contractLevels"
            v-model="contract_level"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="updateUserContractLevel" color="primary" :loading="updating_user_contract_level">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="showVectorDialog" max-width="700">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-title>Vector Entity Details</v-card-title>
        <v-divider></v-divider>
        <v-card-text style="max-height: 60vh; overflow-y: auto;">
          <div v-if="application && application.vector && application.vector.EntitySearchResult">
            <div>
              <vector-details :data="application.vector.EntitySearchResult" />
            </div>
          </div>
          <div v-else>
            No vector details found.
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="showVectorDialog = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
// TODO: repeat section
import TextArea from '@/components/base/TextArea'
import CheckBox from '@/components/base/CheckBox'
import SelectBox from '@/components/base/SelectBox'
import InternalNotes from '../../components/InternalNotes.vue'

import { mapActions, mapGetters } from 'vuex'
import { doSend } from '@/utils/websocket'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import { FORM_FIELD_TYPES, FORM_FIELD_AUTOSAVE_DURATION, FORM_FIELD_CONDITION_ACTION_TYPES } from '@/utils/const'
import { compare, abbrString, narrateDate } from '@/utils/helper'
import moment from 'moment'
import PathwayChips from '@/components/PathwayChips'

// Recursive component for displaying JSON fields and values
const VectorDetails = {
  name: 'VectorDetails',
  props: ['data'],
  methods: {
    isObject(val) {
      return val && typeof val === 'object' && !Array.isArray(val)
    },
    isArray(val) {
      return Array.isArray(val)
    }
  },
  render(h) {
    if (this.isArray(this.data)) {
      return h('div', this.data.map((item, idx) =>
        h('div', { style: { marginLeft: '1em' }, key: idx }, [
          h('vector-details', { props: { data: item } })
        ])
      ))
    } else if (this.isObject(this.data)) {
      return h('div', Object.keys(this.data).map(key =>
        h('div', { style: { marginBottom: '0.5em', marginLeft: '1em' }, key }, [
          h('strong', key + ':'),
          this.isObject(this.data[key]) || this.isArray(this.data[key])
            ? h('vector-details', { props: { data: this.data[key] } })
            : ' ' + String(this.data[key])
        ])
      ))
    } else {
      return h('span', String(this.data))
    }
  }
}

export default {
  components: {
    TextArea,
    CheckBox,
    SelectBox,
    InternalNotes,
    PathwayChips,
    VectorDetails,
  },
  data() {
    return {
      loading: false,

      // application data
      application: null,
      hashNotes: {}, // hash object for section notes
      allFieldValues: {},

      // final note of task completion: mandatory for REJE
      finalNote: '',

      // fields for section notes
      showNoteModal: false,
      editingNote: '',
      selectedSectionId: null,
      selectedSectionSort: null,

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,
      USER_ROLES_TYPES: USER_ROLES_TYPES,

      show_upline_edit_modal: false,
      // possibleUplines: [],
      upline: null,
      uplineAgents: [],
      contract_level: null,
      show_contract_level_edit_modal: false,
      contractLevels: [],
      updating_user_upline: false,
      updating_user_contract_level: false,
      user_id: null,
      carriers: [],
      felony_knockout: false,
      advanced_market_enrollment: false,
      showVectorDialog: false,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
    }),
    tenant() {
      return process.env.MIX_APP_TENANT
    },
    showVectorAlert() {
      let showVector = this.application?.vector?.EntitySearchResult?.Entities?.["EntityReports.EntitySearchResult"]?.Matches > 0
      console.log('showVector', showVector)
      return showVector
    }
  },
  mounted() {
    this.getUserContractLevels();
    this.user_id = this.$route.params.user_id
  },
  created() {
    this.loading = true
    this.getApplication()
  },
  beforeDestroy() {
    if (this.application && !this.isBeingReviewed()) {
      doSend(JSON.stringify({ event: 'endEdit', data: this.application.id }))
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplicationDetailForReview: 'application/getApplicationDetailForReview',
      updateApplication: 'application/updateApplication',
      saveApplicationReviewNote: 'review/saveNote',
      getContractLevels: 'user_invite/getContractLevels',
      getAssignableUplines: 'user_invite/getAssignableUplines',
      updateUser: 'application/updateUser'
    }),
    narrateDate,
    renderValue(field, sort) {
      // return field.label + "!"
      if([
            'American Amicable/Occidental',
            'AIG',
            'Americo',
            'Banner/LGA',
            'F&G',
            'Foresters',
            'Mutual of Omaha',
            'SBLI',
            'UHL'
        ].includes(field.label) && field.value[sort] == "0")
        return ''
      if(
        field.label == 'When was your signed contract date?'
        || field.label == 'When was the last piece of business written?'
      ) {
        const date = moment(field.value[sort]);
        const currentDate = moment();
        const sixMonthsAgo = currentDate.clone().subtract(6, 'months');
        // Check if the given date is between six months ago and the current date
        if (date.isBetween(sixMonthsAgo, currentDate, null, '[]')) {
          let html = `${field.label && field.label + ':'} ${field.value[sort]}`
          if(field.label.indexOf('the last') > 0)
            html += '<div class="bold-red-text mb-4">This candidate is not a free agent with this carrier</div>'
          return html
        }
      }
      return `${field.label && field.label + ':'} ${field.value[sort]}`
    },
    applicationGroupName() {
      return this.application?.invitation?.group?.group_name ?? false
    },
    showCarrierSelections() {
      return this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER)
        // && this.isContracted()
        // && this.isLicensed()
        // && this.isExperienced()
    },
    showCoreCarriersMessage() {
      return this.isLicensed() && !this.isExperienced()
    },
    isLicensed() {
      return this.application?.invitation?.license_status == 'licensed'
    },
    isExperienced() {
      return this.application?.invitation?.experience == 'experienced'
    },
    isAdvancedMarkets(){
      return this.application?.invitation?.advanced_markets == 1
    },
    isContracted() {
      return this.application?.invitation?.experience == 'experienced'
    },
    carrierLimit: function() {
      return 7
      // if(!this.isLicensed() || !this.isExperienced())
      //   return 0
      // return 3
      // if(this.application?.invitation?.advanced_markets)
      //   return 2
      // return 3
    },
    carrierSelectionHint() {
      return 'Pick up to SIX carriers (SBLI and LGA will be included automatically)'
      // return this.application?.invitation?.advanced_markets
      //   ? 'Pick TWO carriers. SBLI will be included automatically.'
      //   : 'Pick THREE initial carriers for this agent'


    },
    limitSelections () {
      let num = this.carrierLimit()
      if(this.carriers.length <= num)
        return []
      if(this.carriers.length > num)
        return [`Select a maximum of ${num} carriers.`]
      return [`Select up to ${num} carriers.`]
    },
    isNotLocalEnv()  {
      return process.env.MIX_APP_ENV !== 'local'
    },
    availableCarriers() {
      //Foresters, F&G, Columbus, LaFayette Life, Mutual Trust, American Equity, Athene
      // if(this.application?.invitation?.advanced_markets) {
      //   return [
      //     'Foresters',
      //     'F&G',
      //     'Columbus',
      //     'LaFayette Life',
      //     'Mutual Trust',
      //     'American Equity',
      //     'Athene'
      //   ]
      // }

      return [
        'American Amicable & Occidental',
        'American General (AIG)',
        // 'American Equity',
        'Americo',
        'Assurity',
        // 'Athene',
        // 'Columbus Life',
        'F&G',
        'Foresters',
        'Gerber',
        'Global Atlantic',
        'Great Western',
        'John Hancock',
        'Lafayette Life',
        // 'Legal & General (LGA)',
        'Liberty Bankers',
        'Lincoln Financial',
        'Manhattan Life',
        'Mutual of Omaha (MOO)',
        'Mutual Trust',
        'National Life Group (NLG & LSW)',
        // 'North American (NACOLAH)',
        'Prosperity Life',
        'Royal Neighbors',
        // 'SBLI',
        'Transamerica',
        // 'UHL'
      ]
    },
    getApplication() {
      this.getApplicationDetailForReview(this.$route.params.user_id)
      .then(async res => {
        if (res && res.data) {
          this.application = res.data
          // Initialize advanced_market_enrollment from application data
          this.advanced_market_enrollment = this.application.advanced_market_enrollment || false
        }
        else throw 'Cannot get application detail!'

        // if (this.application.status != ONBOARD_STATUS_TYPES.SUBMITTED)
        // Review session can be initialized when
        //  - reviewer is super admin OR
        //  - agency owner wants to see a Submitted application
        //  - staff wants to see a ao-approved application
        if (!(this.hasUserRole(USER_ROLES_TYPES.SUPER_ADMIN)
            || (this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && this.application.status == ONBOARD_STATUS_TYPES.SUBMITTED_AO)
            || (this.hasUserRole(USER_ROLES_TYPES.STAFF) && this.application.approved_by_ao)))
          throw `Application status is ${this.application.status}`

        const { notes } = this.application
        if (notes) {
          notes.map(
            note =>
              (this.hashNotes[`${note.form_section_id}-${note.form_section_sort}`] = {
                fixed: note.fixed,
                content: note.content,
              }),
          )

          delete this.application.notes

          this.application.pages.map(page => {
            page.sections.map(section => {
              Object.keys(section.fields).map(fieldId => this.allFieldValues[fieldId] = section.fields[fieldId].value)
            })
          })

          if (this.application) doSend(JSON.stringify({ event: 'edit', data: this.application.id }))
        }

        this.updating_user_contract_level = false
        this.updating_user_upline = false
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to initialize review session.',
          color: 'warning',
        })
        console.log('Error from review page: ', err)
        this.goTo('portal')
        this.updating_user_contract_level = false
        this.updating_user_upline = false
      })
      .finally(() => (this.loading = false))
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    getNote(sectionId, sort) {
      const key = `${sectionId}-${sort}`
      return (this.hashNotes[key] && this.hashNotes[key].content) || ''
    },
    isNoteExist(sectionId, sort) {
      return !!this.getNote(sectionId, sort)
    },
    isNoteFixed(sectionId, sort) {
      const key = `${sectionId}-${sort}`
      return !!(this.hashNotes[key] && this.hashNotes[key].fixed)
    },
    getNoteTooltip(sectionId, sort) {
      return abbrString(this.getNote(sectionId, sort))
    },
    noteHere(section, sort) {
      this.selectedSectionId = section.id
      this.selectedSectionSort = sort
      this.editingNote = this.getNote(section.id, sort)
      this.showNoteModal = true
    },
    saveNote() {
      if (this.getNote(this.selectedSectionId, this.selectedSectionSort) === this.editingNote) {
        this.setSnackbar({
          status: true,
          text: 'No change on this note.',
          color: 'gray',
        })
        return
      }

      this.saveApplicationReviewNote({
        sectionId: this.selectedSectionId,
        sectionSort: this.selectedSectionSort,
        userId: this.$route.params.user_id,
        content: this.editingNote,
      })
        .then(() => {
          this.hashNotes[`${this.selectedSectionId}-${this.selectedSectionSort}`] = {
            fixed: 0,
            content: this.editingNote,
          }

          this.setSnackbar({
            status: true,
            text: 'Your note saved successfully!',
            color: 'success',
          })
        })
        .catch(() => {
          this.setSnackbar({
            status: true,
            text: 'An error occured while saving your note!',
            color: 'warning',
          })
        })
        .finally(() => {
          this.selectedSectionId = null
          this.selectedSectionSort = null
          this.editingNote = ''
          this.showNoteModal = false
        })
    },
    completeReview(type) {
      if(this.felony_knockout && type != ONBOARD_STATUS_TYPES.REJECTED_HO)
        return
      if(type == ONBOARD_STATUS_TYPES.AO_APPROVED) {
        if(this.showCarrierSelections() && this.carriers.length >= this.carrierLimit()) {
          let num = this.carrierLimit()
          this.setSnackbar({
            status: true,
            text: `Select ${num} carriers.`,
            color: 'warning',
          })

          return
        }
      }

      if (type == ONBOARD_STATUS_TYPES.REJECTED && !this.finalNote) {
        this.setSnackbar({
          status: true,
          text: 'You have to leave a note for rejection!',
          color: 'warning',
        })

        return
      }

      if (type == ONBOARD_STATUS_TYPES.EDITING && !this.finalNote) {
        this.setSnackbar({
          status: true,
          text: 'You have to leave a note for revision!',
          color: 'warning',
        })

        return
      }

      this.loading = true

      if(type == ONBOARD_STATUS_TYPES.FORCE_HQ) {
        //quick hack to allow re-submit to hq. The note isn't saved on approval anyway.
        type = ONBOARD_STATUS_TYPES.HO_APPROVED
        this.finalNote = 'override'
      }


      this.updateApplication({
        id: this.application.id,
        status: type,
        note: this.finalNote,
        carriers: this.carriers,
        felony_knockout: this.felony_knockout,
        advanced_market_enrollment: this.advanced_market_enrollment,
      })
        .then(() => {
          this.setSnackbar({
            status: true,
            text: 'Review successfully completed!',
            color: 'success',
          })
          if(this.hasUserRole(['SuperAdmin', 'Staff']))
            this.goTo('pending_applications')
          else
            this.goTo('my_task')
        })
        .catch((err) => {
          this.setSnackbar({
            status: true,
            text: err.response?.data?.message || 'Failed to complete revision. Please contact support.',
            color: 'warning',
          })
        })
        .finally(() => (this.loading = false))
    },
    canShowSection(section, deepStack = []) {
      if (!section) return false
      if(deepStack.includes(section.id)) {
        return true;
      }

      if (section.conditions) {
        const indexConditionRefused = section.conditions.findIndex(condition => {
          const { action, field, type, value } = condition
          const res = compare(
            Array.isArray(this.allFieldValues[field]) ? this.allFieldValues[field][0] : this.allFieldValues[field],
            type,
            value,
          )

          const canShowParentSection = this.canShowSection(
            this.findSectionContainingField(field),
            [
              ...deepStack,
              section.id
            ]
          );

          return (
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !res) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !canShowParentSection) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_HIDE && res)
          )
        })

        return indexConditionRefused === -1
      }

      return true
    },
    findSectionContainingField(fieldId) {
      let sectionFound = null;

      this.application.pages.some(page => {
        page.sections.some(section => {
          Object.keys(section.fields).some(fId => {
            if(fId == fieldId)
              sectionFound = section
            return sectionFound
          })
          return sectionFound
        })
        return sectionFound
      })

      return sectionFound;
    },
    isImageField(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_UPLOAD || field.type === FORM_FIELD_TYPES.TYPE_SIGNATURE;
    },
    isUpload(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_UPLOAD;
    },
    isSignature(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_SIGNATURE;
    },
    isPDF(field) {
      var re = /(?:\.([^.]+))?$/;
      return re.exec(field)[1] === 'pdf';
    },
    isWord(field) {
      var re = /(?:\.([^.]+))?$/;
      return re.exec(field)[1] === 'doc' || re.exec(field)[1] === 'docx';
    },
    showUplineEditModal() {
      this.getUserPossibleUplines();
      this.show_upline_edit_modal = true;
    },
    updateUserUpline() {
      this.updating_user_upline = true
      this.updateUser({ user_id: this.$route.params.user_id, upline_agent_id: this.upline }).then(response => {
        this.show_upline_edit_modal = false
        this.setSnackbar({
          status: true,
          text: 'Upline updated',
          color: 'success',
        })
        this.getApplication()
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to update upline.',
          color: 'warning',
        })
        this.updating_user_upline = false
      })
    },
    updateUserContractLevel() {
      this.updating_user_contract_level = true
      this.updateUser({ user_id: this.$route.params.user_id, contract_level: this.contract_level }).then(response => {
        this.show_contract_level_edit_modal = false
        this.setSnackbar({
          status: true,
          text: 'Contract Level updated',
          color: 'success',
        })

        this.getApplication()
      })
      .catch(err => {
        this.updating_user_contract_level = false
        this.setSnackbar({
          status: true,
          text: 'Failed to update contract level.',
          color: 'warning',
        })
      })
    },
    getUserContractLevels() {
      this.getContractLevels().then(response => {
        this.contractLevels = response.data
      })
    },
    getUserPossibleUplines() {
      this.getAssignableUplines(this.application.agency_owner_code).then(response => {
        this.uplineAgents =  response.data.map(item => {
          item.agent_select_text = `${item.name} - ${item.agent_code}`
          return item
        })
      })
    },
    isBeingReviewed() {
      let reviewer_name = this.activeReviewer(this.user_id)
      //being reviewed by another user?
      if(reviewer_name != undefined && reviewer_name != this.$store.state.auth.user.name)
        return true
      return false
    },
    formatCommissions(commissions) {
      if (!commissions) return ''
      return commissions.split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    isObject(val) {
      return val && typeof val === 'object' && !Array.isArray(val)
    },
  },
}
</script>

<style lang="sass" scope>
.element-section
  cursor: pointer
  width: 100%
  text-align: left
.element-section:hover
  background-color: var(--v-primary-base)
  opacity: 0.8
</style>
