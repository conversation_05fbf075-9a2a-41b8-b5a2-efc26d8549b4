<template>
  <v-flex fluid v-if="!authenticated" class="d-flex flex-column align-center pt-12" justify="center">
    <router-link :to="{ name: 'login' }">
      <v-btn depressed color="primary"> Log In </v-btn>
    </router-link>
    <router-link class="text-h4 mt-5 text-decoration-none" :to="{ name: 'forgot_password' }">
      Forgot your password
    </router-link>
    <router-link class="text-h4 mt-2 text-decoration-none" :to="{ name: 'privacy_policy' }">
      Privacy Policy
    </router-link>
  </v-flex>
  <v-flex fluid v-else class="portal-logged-container d-flex flex-column w-100 pa-5" justify="center">
    <div
      v-if="hasUserRole('Recruit') || hasUserRole('UnlicensedAgent')"
      class="grey lighten-2 pa-4 d-flex flex-column align-center justify-center"
    >
      <h2 class="text-h2 text-center mb-2">Welcome, {{ user.name }}!</h2>
      <!-- <h4 class="text-h4 text-center mb-4">Direct Upline: {{ user.upline_agent_name }}, Agency Owner: {{ user.agency_owner_name }}</h4> -->
      <v-alert type="info" style="max-width: 800px;">
        ATTENTION >> Is this your Direct Upline: <strong>{{ user.upline_agent_name }}</strong> and Agency Owner: <strong>{{ user.agency_owner_name }}</strong>?
        If this information is incorrect you have multiple applications.
        Each application invite is unique.  If you cannot locate the other
        invite, please reach out to your chosen upline to resolve before moving forward.
      </v-alert>
      <template v-if="pendingSignature">
        <p class="text-h4 text-center mt-4">You just signed agreement. It will take a few minutes to process your submission.</p>
      </template>
      <template v-else-if="(user.status && user.status.form_page_id)">
        <router-link :to="{ name: 'onboard' }">
          <v-btn depressed color="primary"> BEGIN MY APPLICATION! </v-btn>
        </router-link>
        <p class="text-h4 text-center mt-4">To Begin Onboarding, Make Sure You Have The Following Items:</p>
      </template>
      <template v-else-if="approved">
        <p class="text-h4 text-center mt-4">Your application has been approved.</p>
      </template>
      <template v-else>
        <p class="text-h4 text-center mt-4">Your application is under review now.</p>
      </template>
    </div>
    <v-row>
      <v-col v-if="tenant=='Q2A'" class="d-flex flex-row align-start mt-10">
        <v-icon large class="text-h1 primary--text lighten-2 mt-3 mr-1"> mdi-check-circle </v-icon>
        <v-container>
          <h1 class="text-h2">State Insurance License Number <br />(If Licensed)</h1>
          <hr class="mt-1 mb-2" />
          <p>
            To complete your insurance license info, you'll need your Resident State License Number. If you're
            contracting as a
            <b>Business</b>, you'll need the following:
          </p>
          <ul>
            <li key="1">Business Name</li>
            <li key="2">Employer Identification Number (EIN)</li>
            <li key="3">Business Insurance License#</li>
            <li key="4">A copy of the Articles of Incorporation to upload</li>
          </ul>
        </v-container>
      </v-col>
      <v-col class="d-flex flex-row align-start mt-10">
        <v-icon large class="text-h1 primary--text lighten-2 mt-3 mr-1"> mdi-check-circle </v-icon>
        <v-container>
          <h1 class="text-h2">Want To Get Paid? We'll Need Your Preferred Banking Info</h1>
          <hr class="mt-1 mb-2" />
          <p>
            You'll need your banking account information to complete the Electronic Funds Transfer (EFT) section of this
            application.<br />
            <b
              >Insurance companies pay you via direct deposit to your account, so we need this information to ensure
              you're set up to receive your commission payments.</b
            >
          </p>
          <ul>
            <li key="1">Bank Account Number</li>
            <li key="2">Bank Routing Number</li>
            <li key="3">A voided check OR an EFT authorization letter</li>
          </ul>
        </v-container>
      </v-col>
    </v-row>
    <div
      v-if="tenant=='Q2A' && (hasUserRole('Recruit') || hasUserRole('UnlicensedAgent'))"
      class="grey lighten-2 pa-4 d-flex flex-column align-center justify-center mt-8"
    >
      <h2 class="text-h2 text-center mb-4">Onboarding Portal Resource Guide</h2>
      <p class="text-center">Please refer to our <a href="https://hq.quility.com/api/public/document/168297/view/onboarding-agent-help-sheet" target="_blank">Onboarding Portal Resource Guide</a> for additional help and information regarding the Onboarding portal and the application process!</p>
      <v-btn depressed color="primary" href="https://hq.quility.com/api/public/document/168297/view/onboarding-agent-help-sheet" target="_blank"> Resource Guide </v-btn>
    </div>
  </v-flex>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getWithExpiry } from '@/utils/storage'
import { ONBOARD_STATUS_TYPES, HELLOSIGN_SIGNATURE_PROCESS, USER_ROLES_TYPES } from '@/utils/const'

export default {
  computed: {
    ...mapGetters('auth', ['authenticated', 'user', 'hasUserRole']),
    tenant() {
      return process.env.MIX_APP_TENANT
    },
    onboardStatus() {
      return this.$store.state.auth.user.status.slug
    },
    approved() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.AO_APPROVED||this.onboardStatus===ONBOARD_STATUS_TYPES.HO_APPROVED
    }
  },
  data() {
    return {
      pendingSignature: false
    }
  },
  watch: {},
  mounted() {
    if(this.hasUserRole(USER_ROLES_TYPES.RECRUIT) || this.hasUserRole(USER_ROLES_TYPES.UNLICENSED_AGENT))
      this.checkPendingSignatureStatus()
    else if(this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER))
      this.goTo('my_task');
    else if(this.hasUserRole(USER_ROLES_TYPES.SALES_REP))
      this.goTo('send_invitation');
    else
      this.goTo('pending_applications');
  },
  methods: {
    ...mapActions('auth', ['getUser']),
    // check signature completion status every 2 seconds
    checkPendingSignatureStatus() {
      if(getWithExpiry(HELLOSIGN_SIGNATURE_PROCESS)) {
        this.pendingSignature = true

        this.getUser().finally(() => {
          if(this.user.is_signature_signed) {
            this.pendingSignature = false
          } else {
            setTimeout(() => {
              this.checkPendingSignatureStatus()
            }, 2000);
          }
        })
      } else {
        this.pendingSignature = false
      }
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
  }
}
</script>

<style lang="sass" scope></style>
