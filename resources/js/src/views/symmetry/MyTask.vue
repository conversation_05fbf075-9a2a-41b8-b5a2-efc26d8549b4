<template>
  <v-container class="d-flex flex-column pa-8" v-if="total">
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      sort-by="name"
      :options.sync="options"
      :server-items-length="total"
    >
      <template v-slot:[`item.actions`]="{ item }">
        <h5 class="text-h5 info--text" v-if="activeReviewer(item.target_id)" v-bind:key="tickReviewStatus">
          {{ activeReviewer(item.target_id) }} is reviewing...
        </h5>
        <v-btn x-small color="grey" outlined :loading="item.deleting" @click="deleteTask(item)" icon>
          <v-icon>fas fa-times-circle</v-icon>
        </v-btn>
      </template>
      <template v-slot:[`item.agent`]="{ item }">
        <a color="primary" class="pr-2" @click="goToAction(item)">
          {{ item.agent }}
        </a>
      </template>
      <template v-slot:[`item.submitted_at`]="{ item }">
        {{ narrateDate(item.submitted_at, false) }}
      </template>
      <template v-slot:[`item.assigned_at`]="{ item }">
        {{ narrateDate(item.assigned_at) }}
      </template>
      <template v-slot:[`item.type`]="{ item }">
        {{item.description || item.type}}
      </template>
    </v-data-table>
  </v-container>
  <v-container v-else fluid class="d-flex flex-column align-center justify-center" justify="center">
    <v-icon class="mt-10 mb-2 text-h1 primary--text text--lighten-1"> mdi-check-circle-outline </v-icon>
    <h1 class="secondary--text text--lighten-2">No pending tasks</h1>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, TASK_TYPES } from '@/utils/const'

export default {
  name: 'SymmetryMyTask',
  components: {},
  data() {
    return {
      headers: [
        {
          text: 'Form',
          align: 'start',
          value: 'form',
          class: 'app-table-col',
        },
        {
          text: 'Agent Name',
          align: 'start',
          sortable: false,
          value: 'agent',
          class: 'app-table-col',
        },
        {
          text: 'Workflow Task',
          sortable: false,
          value: 'type',
          class: 'app-table-col',
        },
        {
          text: 'Application Submitted',
          value: 'submitted_at',
          class: 'app-table-col',
        },
        {
          text: 'Assigned To Onboarding',
          value: 'assigned_at',
          class: 'app-table-col',
        },
        { text: '', value: 'actions' },
      ],
      items: [],
      total: 0,
      options: {},
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
      loading: 'task/loading',
    }),
  },
  created() {
    this.getDataFromApi()
  },
  // watch: {
  //   options: {
  //     handler() {
  //       this.getDataFromApi();
  //     },
  //     deep: true,
  //   },
  // },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getTasks: 'task/getTasks',
      completeTask: 'task/completeTask',
    }),
    narrateDate,
    deleteTask(item) {
      item.deleting = true
      this.completeTask({...item}).then(res => {
        if (res && res.data) {
          this.items = res.data.items
          this.total = res.data.total
        }
      }).finally(() => item.deleting = false)
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED_AO &&
        !this.activeReviewer(item.target_id)
      )
    },
    goToAction(item) {
      if (item.type == TASK_TYPES.FILL_APPLICATION || item.type == TASK_TYPES.UPDATE_APPLICATION) {
        this.goTo('onboard')
      } else if (item.type == TASK_TYPES.APPROVE_APPLICATION) {
        if (this.canReview(item)) this.goToPath('/app/application/review/' + item.target_id)
        else {
          this.setSnackbar({
            status: true,
            text: 'You cannot review this application now.',
            color: 'warning',
          })
        }
      } else {
        console.log('Cannot handle this task: ', item)
      }
    },
    getDataFromApi() {
      this.getTasks(new URLSearchParams(this.options).toString()).then(res => {
        if (res && res.data) {
          this.items = res.data.items
          this.total = res.data.total
        }
      })
    },
  },
}
</script>

<style lang="sass" scope>
.task-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
