<template>
  <v-container class="d-flex flex-column pa-8">
    <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" />
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Onboarding Status" :items="itemsApplicationStatus" v-model="search.status" />
      </v-col>
      <v-col cols="2">
        <v-switch
          label="Show Stale"
          v-model="search.show_stale"
          class="mt-10"
        ></v-switch>
      </v-col>
    </v-row>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      sort-by=""
      :options.sync="options"
      :server-items-length="total"
    >
    <template v-slot:top>
        <v-row class="mb-6">
          <v-col cols="3">
            <v-menu
              ref="start_date_menu"
              v-model="start_date_menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="search.start_date"
                  outlined
                  label="Start Date"
                  append-icon="mdi-calendar"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="search.start_date"
                no-title
                scrollable
                @input="start_date_menu = false"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="3">
            <v-menu
              ref="end_date_menu"
              v-model="end_date_menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="search.end_date"
                  outlined
                  label="End Date"
                  append-icon="mdi-calendar"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="search.end_date"
                no-title
                scrollable
                @input="end_date_menu = false"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="4">
            <v-select
              v-model="search.pathway_types"
              :items="pathwayOptions"
              label="Pathway Types"
              multiple
              chips
              outlined
              dense
              id="pathway-types-select"
            >
              <template v-slot:selection="{ item, index }">
                <v-chip
                  v-if="index < 3"
                  small
                  :color="getPathwayColor(item)"
                  text-color="white"
                  class="mr-1"
                >
                  {{ item }}
                </v-chip>
                <span v-if="index === 3" class="grey--text text-caption pl-2">
                  (+{{ search.pathway_types.length - 3 }} more)
                </span>
              </template>
              <template v-slot:item="{ item }">
                <div class="d-flex align-center">
                  <v-chip
                    small
                    :color="getPathwayColor(item)"
                    text-color="white"
                    class="mr-2"
                  >
                    {{ item }}
                  </v-chip>
                  <span class="text-body-2">{{ getPathwayDescription(item) }}</span>
                </div>
              </template>
            </v-select>
          </v-col>
          <v-col cols="2">
            <v-btn class="mt-2" @click="resetSearch()">Reset Search</v-btn>
          </v-col>
        </v-row>
      </template>
      <template v-slot:[`item.pathway`]="{ item }">
        <v-icon v-if="item.flagged" color="red" :title="item.flagged">mdi-flag</v-icon>
        <pathway-chips v-else :pathway="item.pathway"></pathway-chips>
      </template>
      <template v-slot:[`item.agent`]="{ item }">
        <a class="pr-2 text--darken-3 text--secondary" @click="goToAction(item, true)">
          <span :class="item.blacklist ? 'black white--text pa-1' : ''">{{ item.agent }}</span>
        </a>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <span v-if="item.status.indexOf('Error') > -1" class="error--text font-weight-black">
          <v-icon color="error" class="">mdi-alert</v-icon>
          {{ item.status }}
        </span>
        <span v-else>{{ item.status }}</span>
        <h5 class="text-h5 info--text" v-if="activeReviewer(item.id)" v-bind:key="tickReviewStatus">
          {{ activeReviewer(item.id) }} is reviewing...
        </h5>
      </template>
      <template v-slot:[`item.applied_on`]="{ item }">
        {{ narrateDate(item.applied_on) }}
      </template>
      <template v-slot:[`item.completed_on`]="{ item }">
        {{ narrateDate(item.completed_on) }}
      </template>
      <template v-slot:[`item.actions`]="{ item }">
        <div style="white-space:nowrap;">
          <v-btn
            v-show="hasUserRole('SuperAdmin') || (hasUserRole('AgencyOwner') && item.status_slug == onboard_status_types.SUBMITTED) || (hasUserRole('Staff') && item.approved_by_ao == true)"
            :href="`/app/application/review/${item.id}`"
            target="_blank"
            :color="(item.previously_submitted_ho) ? 'error' : 'primary'"
            x-small
          >
            Review
          </v-btn>
          <v-btn
            @click="goToAction(item, true)"
            color="secondary"
            x-small
          >
            View
          </v-btn>
        </div>
      </template>
      <template v-slot:no-data>
        <h4>No applications</h4>
      </template>
      <template v-slot:[`item.homeoffice_submission_on`]="{ item }">
        {{ narrateDate(item.homeoffice_submission_on) }}
      </template>
    </v-data-table>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'
import PathwayChips from '@/components/PathwayChips'

export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
    PathwayChips,
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: '', value:'pathway' },
        { text: 'FULL LEGAL NAME:', value: 'agent', class: 'app-table-col' },
        { text: 'APPLIED ON', value: 'applied_on', class: 'app-table-col' },
        { text: 'DATE SUBMITTED TO HOME OFFICE', value: 'homeoffice_submission_on', class: 'app-table-col' },
        // { text: "COMPLETED ON", value: "completed_on", class: "app-table-col" },
        { text: 'ONBOARDING STATUS', value: 'status', class: 'app-table-col' },
        {
          text: 'DIRECT UPLINE NAME',
          value: 'direct_upline',
          class: 'app-table-col',
        },
        {
          text: 'AGENCY OWNER NAME',
          value: 'agency_owner',
          class: 'app-table-col',
        },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
        },
      ],
      items: [],
      total: 0,
      loading: false,

      itemsApplicationStatus: [],

      options: {
        itemsPerPage: -1
      }, // option for pagination
      search: {
        // option for search
        text: '',
        status: 'submitted-ho',
        show_stale: false,
        start_date: '',
        end_date: '',
        date_field: 'homeoffice_submission_on',
        pathway_types: []
      },

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,

      pathwayOptions: ['U', 'L', 'T', 'R', 'AM', 'AM?'],
      pathwayDescriptions: {
        'U': 'Unlicensed',
        'L': 'Licensed',
        'T': 'Transferring',
        'R': 'Returning',
        'AM': 'Advanced Markets - Yes',
        'AM?': 'Advanced Markets - Maybe'
      },

      start_date_menu: false,
      start_date: "",
      end_date_menu: false,
      end_date: "",
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
    }),
  },
  updated() {

  },
  watch: {
    options: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
        window.localStorage.setItem("pending_applications_options", JSON.stringify(this.options))
      },
      deep: true,
    },
    search: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  mounted() {
    this.getApplicationStatusOptions()
    this.options = JSON.parse(window.localStorage.getItem('pending_applications_options')) ?? {itemsPerPage: -1}
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplications: 'application/getApplications',
      getStatusOptions: 'application/getStatusOptions',
    }),
    narrateDate,
    resetSearch() {
      const initialSearch = {
        text: '',
        status: 'submitted-ho',
        show_stale: false,
        start_date: '',
        end_date: '',
        date_field: 'homeoffice_submission_on',
        pathway_types: []
      };

      window.localStorage.setItem("search_all", JSON.stringify(initialSearch));
      this.search = { ...initialSearch };
      this.options = { itemsPerPage: -1 };
      this.search_text_key = Math.random();
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    goToAction(item, view = false) {
      if (view) this.goToPath('/app/application/track/' + item.id)
      else if (this.canReview(item)) this.goToPath('/app/application/review/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        ((this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED) ||
         (this.hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && item.approved_by_ao)
        ) &&
        !this.activeReviewer(item.id)
      )
    },
    getDataFromApi() {
      // Check if any date filter field is set
      const hasStartDate = !!this.search.start_date;
      const hasEndDate = !!this.search.end_date;

      // If any date field is set but not all are set, return without action
      if ((hasStartDate || hasEndDate) &&
          !(hasStartDate && hasEndDate)) {
        return;
      }

      this.loading = true;
      this.getApplications(
        new URLSearchParams({
          ...this.options,
          ...this.search,
          completed: 'pending',
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            let filteredItems = res.data.items;

            // Apply pathway filtering on the client side if pathway types are selected
            if (this.search.pathway_types && this.search.pathway_types.length > 0) {
              filteredItems = filteredItems.filter(item => {
                if (!item.pathway) return false;
                const pathwayPrefix = item.pathway.split('/')[0];
                return this.search.pathway_types.includes(pathwayPrefix);
              });
            }

            this.items = filteredItems;
            this.total = this.search.pathway_types.length > 0 ? filteredItems.length : res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getApplicationStatusOptions() {
      this.getStatusOptions().then(response => {
        this.itemsApplicationStatus = response.data
      })
    },
    getPathwayColor(type) {
      switch(type) {
        case 'U':
          return 'grey';
        case 'L':
          return 'success';
        case 'R':
          return 'warning';
        case 'T':
          return 'error';
        case 'AM':
          return 'primary';
        case 'AM?':
          return 'primary';
        default:
          return 'grey';
      }
    },
    getPathwayDescription(type) {
      return this.pathwayDescriptions[type] || '';
    }
  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important

#app > div > div.row.width-100.ma-0 > div.pa-0.overflow-auto.col > div.container.d-flex.flex-column.pa-8 > div.v-data-table.v-data-table--has-top.v-data-table--has-bottom.theme--light > div.row.mb-6 > div.col.col-4 > div > div > div.v-input__slot
  padding-bottom: 9px !important
</style>
