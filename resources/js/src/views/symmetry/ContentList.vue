<template>
  <v-container class="d-flex flex-column pa-8">
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      :options.sync="options"
      :server-items-length="total"
    >
      <template v-slot:[`item.state_name`]="{ item }">
        {{ item.state_name ? item.state_name : 'COMPANY' }}
      </template>
      <template v-slot:[`item.content`]="{ item }">
        <h5 class="text-h5 info--text">
          {{ item.content ? 'Edited' : 'None' }}
        </h5>
      </template>
      <template v-slot:[`item.actions`]="{ item }">
        <v-icon small class="mr-2" @click="goToContentManage(item)"> mdi-pencil </v-icon>
      </template>
      <template v-slot:no-data>
        <h4>No applications</h4>
      </template>
    </v-data-table>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { USER_ROLES_TYPES } from '@/utils/const'

export default {
  data() {
    return {
      headers: [
        { text: 'STATE', value: 'state_name', class: 'app-table-col', sortable: false },
        { text: 'CONTENT STATUS', value: 'content', class: 'app-table-col', sortable: false, align: 'center' },
        { text: 'OPERATION', value: 'actions', class: 'app-table-col', sortable: false, align: 'center' },
      ],
      items: [],
      total: 0,
      loading: false,
      options: {},
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
    }),
  },
  watch: {
    options: {
      handler() {
        this.getDataFromApi()
      },
      deep: true,
    },
  },
  mounted() {
    this.getStateList()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getContentList: 'content_management/getContentList',
      getStateList: 'content_management/getStateList',
    }),
    goToPath(path) {
      this.$router.push({ path })
    },
    canEditContent() {
      return this.hasUserPermission('admin content') && this.hasUserRole(USER_ROLES_TYPES.SUPER_ADMIN)
    },
    getDataFromApi() {
      this.loading = true
      this.getContentList(new URLSearchParams(this.options).toString())
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    goToContentManage(item) {
      if (this.canEditContent()) this.goToPath('/app/content_management/content/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
