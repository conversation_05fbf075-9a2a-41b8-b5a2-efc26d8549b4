<template>
  <v-container class="d-flex flex-column pa-8">
    <!-- <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" />
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Onboarding Status" :items="itemsApplicationStatus" v-model="search.status" :default="search.default" />
      </v-col>
    </v-row> -->
    <!-- <v-row>
      <v-spacer></v-spacer>
      <v-col class="text-right">
        <v-btn text small class="mb-6" @click="downloadSureLcExport" :loading="downloadingSureLc">SureLC Export Report</v-btn>
      </v-col>
    </v-row> -->
    <v-data-table
        v-bind="$attrs"
        :headers="group_headers"
        :items="groups"
        :mobile-breakpoint="0"
        :loading="loading_groups"
        class="q-carrier-list"
        :single-expand="singleExpand"
        :expanded.sync="expanded"
        item-key="id"
        show-expand
    >
      <template #item.group_name="{ item }">
        {{ item.group_name }}
      </template>
      
      <template v-slot:[`item.created_at`]="{ item }">
        {{ narrateDate(item.created_at, false) }}
      </template>
      
      <template #item.invitation_link="{ item }">
        <a v-if="showGroupLink(item)" :href="getGroupInviteUrl(item)">{{ getGroupInviteUrl(item) }}</a>
        <div v-else>Pending Approval</div>
      </template>

      <template v-slot:[`item.copy_link`]="{ item }">
        <div style="white-space: nowrap;text-align:right;">
          <v-btn v-if="showGroupLink(item)" icon text x-small @click="copyURL(getGroupInviteUrl(item))">
              <v-icon>mdi-content-copy</v-icon>
          </v-btn>
          <!-- <v-btn icon text x-small @click="viewGroupInvitationRequest(item)">
              <v-icon>mdi-pencil-outline</v-icon>
          </v-btn> -->
        </div>
      </template>

      <template v-slot:[`item.group_invite_status`]="{ item }">
        <v-btn v-if="item.group_invite_status  == 'submitted'" x-small @click="approveInvitation(item)" :loading="approving == item.id">
          Approve
        </v-btn>
        <span v-else>Approved</span>
      </template>

      <template #item.active="{ item }">
        <v-switch
          v-model="item.active"
          label=""
          class="status_switch"
          @change="updateStatus(item)"
        ></v-switch>
      </template>

      <template v-slot:expanded-item="{ headers, item }">
        <td :colspan="headers.length" class="pa-4">
          <!-- <div><span class="font-weight-bold">Agent:</span> {{ item.user.name }}</div> -->
          <!-- <div><span class="font-weight-bold">Contact Info:</span> {{ item.contact_info }}</div>
          <div><span class="font-weight-bold">Preferred Times:</span> {{ item.preferred_times }}</div> -->
          <div><span class="font-weight-bold">Notes:</span> {{ item.notes }}</div>
        </td>
      </template>
    </v-data-table>

    <v-dialog max-width="80%" v-model="show_group_invitation_dialog">
      <v-card>
        <v-card-title>View Group Invitation</v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="6">
              <strong>Group Name</strong>
              <div>{{ current_invitation.group_name }}</div>
            </v-col>
            <v-cols cols="6">
              <strong>Group Size</strong>
              <div>{{ current_invitation.group_size }}</div>
            </v-cols>
            <!-- <v-col cols="6">
              <strong>Preferred Contact Info</strong>
              <div>{{ current_invitation.contact_info }}</div>
            </v-col>
            <v-col cols="6">
              <strong>Preferred Contact Times</strong>
              <div>{{ current_invitation.preferred_times }}</div>
            </v-col> -->
            <v-col cols="12">
              <strong>Notes</strong>
              <div>{{ current_invitation.notes }}</div>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="show_group_invitation_dialog=false">Cancel</v-btn>
          <v-btn color="primary" @click="approveInvitation()" :loading="approving">Approve</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'
import LicenseStatusIndicator from '@/components/base/LicenseStatusIndicator'
import upperFirst from 'lodash/upperFirst'


export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
    LicenseStatusIndicator
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: '', value:'license_status' },
        { text: 'FULL LEGAL NAME:', value: 'agent', class: 'app-table-col' },
        { text: 'APPLIED ON', value: 'applied_on', class: 'app-table-col' },
        { text: 'DATE SUBMITTED TO HOME OFFICE', value: 'homeoffice_submission_on', class: 'app-table-col' },
        { text: 'COMPLETED ON', value: 'completed_on', class: 'app-table-col' },
        { text: "ONBOARDING STATUS", value: "status", class: "app-table-col" },
        {
          text: 'DIRECT UPLINE NAME',
          value: 'direct_upline',
          class: 'app-table-col',
        },
        {
          text: 'AGENCY OWNER NAME',
          value: 'agency_owner',
          class: 'app-table-col',
        },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
        },
      ],
      items: [],
      total: 0,
      loading: false,

      itemsApplicationStatus: [],

      options: {}, // option for pagination
      search: {
        // option for search
        text: '',
        status: '',
        default: 'approved'
      },

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,
      downloadingSureLc: false,
      groups: [],
      loading_groups: false,
      show_group_invitation_dialog: false,
      current_invitation: {},
      approving: false,
      expanded: [],
      singleExpand: false,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
    }),
    group_headers() {
      return [
        { text: 'Group Name', value: 'group_name' },
        { text: 'Created', value: 'created_at' },
        { text: 'Agent', value: 'user.name' },
        { text: 'Invitation Link', value: 'invitation_link' },
        { text: '', value: 'copy_link' },
        { text: 'Status', value: 'group_invite_status'},
        { text: 'Active', value: 'active' },
      ]
    },
  },
  watch: {
    options: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
    search: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  mounted() {
    // this.getApplicationStatusOptions()
    this.getInvitesGroups()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplications: 'application/getApplications',
      getStatusOptions: 'application/getStatusOptions',
      getUserInvitesGroups: 'user_invite/getUserInvitesGroups',
      approveUserInvitesGroups: 'user_invite/approveUserInvitesGroups',
      updateGroupStatus: 'user_invite/updateGroupStatus',
    }),
    narrateDate,
    viewGroupInvitationRequest(invitation) {
      console.log(invitation)
      this.current_invitation = invitation
      this.show_group_invitation_dialog = true
    },
    // statusButtonText(item) {
    //   if(item.group_invite_status == 'submitted')
    //     return 'Approve'
    //   if(item.group_invite_status == 'approved')
    //     return 'Revo'
    // },
    getInvitesGroups() {
      this.loading_groups = true
      this.getUserInvitesGroups()
        .then(res => {
          console.log("got the groups?")
          if(!res.data || !res.data['groups'])
            throw 'invalid invitation data'
          this.groups = res.data.groups
        })
        .catch(err => {
          console.log("error:", err)
        })
        .finally(() => {
          this.loading_groups = false
        })
    },
    updateStatus(item) {
      this.updateGroupStatus(item)
      .then(response => {
        this.setSnackbar({
                status: true,
                text: 'Group status updated.',
                color: 'success',
              });
      }).catch(error => {
        this.setSnackbar({
                status: true,
                text: 'Error updating group status.',
                color: 'rgba(255, 0, 0, 0.8)',
              });
      })
    },
    getGroupInviteUrl(group_invite) {
      return `https://${window.location.host}/auth/register?group_code=${group_invite.group_code}`
    },
    showGroupLink(group_invite) {
      return group_invite.group_invite_status == 'approved'
    },
    async copyURL(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.setSnackbar({
              status: true,
              text: 'Copied to clipboard',
              color: 'success',
            })
        } catch($e) {
            console.error($e)
        }
    },
    approveInvitation(item) {
      this.approving = item.id
      // this.show_group_invitation_dialog = false
      this.approveUserInvitesGroups(item.id)
        .then(res => {
          console.log("approved")
          if(!res.data || !res.data['groups'])
            throw 'invalid invitation data'
          this.groups = res.data.groups
        })
        .catch(err => {
          console.log("error:", err)
        })
        .finally(() => {
          this.loading_groups = false
          this.approving = false
        })
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    goToAction(item, view = false) {
      if (view) this.goToPath('/app/application/track/' + item.id)
      else if (this.canReview(item)) this.goToPath('/app/application/review/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        ((this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED) ||
         (this.hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && item.approved_by_ao)
        ) &&
        !this.activeReviewer(item.id)
      )
    },
    getDataFromApi() {
      this.loading = true
      this.getApplications(
        new URLSearchParams({
          ...this.options,
          ...this.search,
          completed: 'completed',
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getApplicationStatusOptions() {
      this.getStatusOptions().then(response => {
        this.itemsApplicationStatus = response.data
        if(response.data){
          const items = response.data.filter(status => status.value.indexOf("approved") > 0);
          // console.log("getApplicationStatusOptions:", items);
          this.itemsApplicationStatus = items;
        }
      })
    },
    downloadSureLcExport() {
      this.downloadingSureLc = true
      let options = {...this.options}
      options.itemsPerPage = -1

      this.getApplications(
        new URLSearchParams({
          ...options,
          ...this.search,
          completed: 'completed',
          include_ssn: true,
          include_dob: true,
          include_last_name: true,
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            // this.items = res.data.items
            // this.total = res.data.total
            this.downloadCsv(res.data.items)
          }
        })
        .finally(() => {
          this.downloadingSureLc = false
        })
    },
    downloadCsv: function(data) {
        const lines = []
        
        //array of data table fields for csv header row
        const fields = []
        const headers = []
        this.headers.forEach(header => {
            if(header.text != 'ACTIONS' && header.text !== '') {
              headers.push(header)
              fields.push(header.text)
            }
        })

        let phone_header = { text: 'PHONE', value: 'phone' }
        let last_name_header = { text: 'LAST NAME', value: 'last_name' }
        let ssn_header = { text: 'SSN', value: 'ssn' }
        let dob_header = { text: 'DOB', value: 'dob' }
        headers.splice(2, 0, phone_header)
        headers.splice(2, 0, dob_header)
        headers.splice(2, 0, ssn_header)
        headers.splice(2, 0, last_name_header)
        fields.splice(2, 0, 'PHONE')
        fields.splice(2, 0, 'DOB')
        fields.splice(2, 0, 'SSN')
        fields.splice(2, 0, 'LAST NAME')

        //build the string and add to lines array
        lines.push(`"`+fields.join(`","`)+`"`)
        //loop through carrier records and build the csv text line
        data.map(application => {
            //array of carrier field values based on fields defined by data table
            let values = headers.map(header => {
                switch (header.value) {
                  // case 'updated_at':
                  //   return narrateDate(invitation[header.value])
                  // case 'is_fully_licensed':
                  // case 'has_passed_exam':
                  //   return invitation[header.value] == true ? 'Yes' : 'No'
                  // case 'recruiter':
                  //   return invitation.user.name
                  // case 'direct_upline':
                  //   return invitation?.upline_agent?.AgentName
                  // case 'status':
                  //   return this.getInvitationStatus(invitation)
                  default:
                    return application[header.value]
                }
            })
            //build the string and add to lines array
            lines.push(`"`+values.join(`","`)+`"`)
        })

        //build all rows of csv by joining lines array
        let txt = lines.join("\n")

        //generate the download
        var element = document.createElement('a')
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(txt))
        element.setAttribute('download', "CompletedApplications.csv")
        element.style.display = 'none'
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)

    },
  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
