<template>
  <v-container class="invite-container d-flex flex-column pa-10">
    <div v-if="loading" class="text-center">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <v-form ref="form" @submit.prevent="save" v-else>
      <v-row>
        <v-col cols="12">
          <div v-for="item in optionsStates" :key="item.value">
            <h1 v-if="state == item.value">{{ state }} - {{ item.label }}</h1>
          </div>
          <ul>
            <li class="text-h4">You can edit the information for state licensing page.</li>
            <!-- <li class="text-h4">
              If you want to edit the company information, you have to select the company in the content type.
            </li> -->
          </ul>
        </v-col>
        <v-col cols="12">
          <v-row>
            <span class="ml-4 mt-3">Contact Information</span> &nbsp; &nbsp;
            <v-switch class="mr-2 mb-2" v-model="contact"></v-switch>
          </v-row>
        </v-col>
        <div v-if="contact">
          <v-row>
            <v-col cols="6">
              <v-text-field v-model="contactInfo.name" :value="contactInfo.name" label="Name" outlined></v-text-field>
            </v-col>
            <v-col cols="6">
              <v-text-field
                v-model="contactInfo.email"
                :value="contactInfo.email"
                label="email contact"
                outlined
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="contactInfo.address"
                :value="contactInfo.address"
                label="address"
                outlined
              ></v-text-field>
            </v-col>
            <v-col cols="6">
              <v-text-field v-model="contactInfo.city" :value="contactInfo.city" label="city" outlined></v-text-field>
            </v-col>
            <v-col cols="6">
              <v-text-field v-model="contactInfo.zip" :value="contactInfo.zip" label="zip" outlined></v-text-field>
            </v-col>
          </v-row>
        </div>
      </v-row>
      <v-row>
        <v-col cols="12">
          <tinymce-editor
            idtiny="tiynymce"
            :toolbar="toolbarTinyOptions"
            :initVal="initContent"
            v-model="content"
          ></tinymce-editor>
        </v-col>
        <v-col cols="12">
          <h1>Frequently Asked Questions ( FAQ )</h1>
          <tinymce-editor
            idtiny="tiny2"
            toolbar="bold italic underline"
            :initVal="faq.answer"
            v-model="faq.answer"
          ></tinymce-editor>
        </v-col>
        <v-col cols="12">
          <h1>Frequently Asked Questions 2( FAQ )</h1>
          <plain-accordion :content="JSON.stringify(contentAskQuestion)" />

          <dynamic-input @getData="getData"></dynamic-input>
        </v-col>
        <v-col cols="12" class="text-center">
          <v-btn color="primary" large :loading="saving" type="submit"> Save </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import TinymceEditor from '@/components/base/TinymceEditor'
import { USER_ROLES_TYPES, CONTENT_TYPES } from '@/utils/const'
import DynamicInput from '@/components/DynamicInput.vue'
import PlainAccordion from '../../components/compound/PlainAccordion.vue'

export default {
  components: {
    TinymceEditor,
    DynamicInput,
    PlainAccordion,
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      optionsStates: 'content_management/states',
      getFaq: 'faq/getFaqStateLicensing',
    }),

    optionsContentTypes() {
      return CONTENT_TYPES
    },
    disabledContentType() {
      return this.$store.state.auth.user.roles[0].name === USER_ROLES_TYPES.AGENCY_OWNER ? true : false
    },
  },
  data() {
    return {
      // saving status
      saving: false,
      loading: false,
      formErrors: {},
      contactInfo: {
        id: null,
        name: '',
        email: '',
        phone: '',
        address: '',
        zip: '',
        city: '',
      },
      contact: false,

      contentType: this.$store.state.auth.user.roles[0].name === USER_ROLES_TYPES.AGENCY_OWNER ? 'company' : null,
      initContentType: this.$store.state.auth.user.roles[0].name === USER_ROLES_TYPES.AGENCY_OWNER ? 'company' : null,
      state: '',
      initState: '',
      content: '',
      initContent: '',
      faq: {
        id: null,
        answer: '',
        question: '',
      },
      toolbarTinyOptions:
        'undo redo | formatselect | bold italic backcolor | \
           alignleft aligncenter alignright alignjustify | \
           bullist numlist outdent indent | removeformat | help',
      faqs: [],
      contentAskQuestion: [],
    }
  },
  watch: {
    contentType(nVal) {
      this.state = nVal === 'state' ? this.state : ''
      this.formErrors.state = ''
      return nVal
    },
  },
  mounted() {
    this.getContactInfo()
    this.getUserContent()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      saveContent: 'content_management/saveContent',
      getContent: 'content_management/getContent',
      getContentList: 'content_management/getContentList',
      getContactContent: 'content_management/getContactContent',
      pushContactContent: 'content_management/pushContactContent',
      getFaqStateLicensing: 'content_management/getFaqStateLicensing',
      pushFaqStateLicensing: 'content_management/pushFaqStateLicensing',
    }),
    async save() {
      this.setSnackbar({ status: false })
      this.formErrors = {}

      if (!this.contentType) {
        this.formErrors.contentType = 'Select the content type'
      }
      if (this.contentType === 'state' && !this.state) {
        this.formErrors.state = 'Please select the state'
      }

      if (Object.keys(this.formErrors).length > 0) {
        this.setSnackbar({
          status: true,
          text: 'You have some errors to fix before saving.',
          color: 'warning',
        })
      } else {
        this.saving = true
        let content_id = ''
        if (this.hasUserRole(USER_ROLES_TYPES.SUPER_ADMIN)) {
          content_id = this.$route.params.content_id
        }

        await this.saveContent({
          content_type: this.contentType,
          state_name: this.state,
          content: this.content,
          content_id: content_id,
        })
          .then(() => {
            //update contact information abd faq
            this.pushContactInfo()
            this.pushFaq(content_id)
            this.pushFaq2(content_id)

            this.setSnackbar({
              status: true,
              text: 'Content created successfully.',
              color: 'success',
            })
          })
          .catch(
            ({
              response: {
                data: { errors, message },
              },
              message: messageHttp,
            }) => {
              if (errors) this.formErrors = errors
              this.setSnackbar({
                status: true,
                text: message || messageHttp || '',
                color: 'rgba(255, 0, 0, 0.8)',
              })
            },
          )
          .finally(() => {
            this.saving = false
          })
      }
    },
    getUserContent() {
      let content_id = ''
      if (this.hasUserRole(USER_ROLES_TYPES.SUPER_ADMIN)) {
        content_id = this.$route.params.content_id
      }
      this.loading = true
      this.getContent(content_id)
        .then(response => {
          let content = response.data
          this.contentType = parseInt(content.content_type) === 1 ? 'state' : 'company'
          this.initContentType = content.content_type === 1 ? 'state' : 'company'
          let state = content.state_name ? content.state_name : ''
          let state_arr = this.optionsStates.filter(item => item.label === state)
          this.state = state_arr.length > 0 ? state_arr[0].value : ''
          this.initState = this.state
          this.content = content.content || ''
          this.initContent = content.content || ''
        })
        .finally(() => {
          this.loading = false
        })
    },

    /**
     * get contact information
     */
    getContactInfo() {
      const content_id = this.$route.params.content_id
      this.getContactContent(content_id)
        .then(resp => {
          this.contactInfo = resp.data
        })
        .catch(error => {
          console.error(error)
        })

      this.getFaqStateLicensing(content_id)
        .then(resp => {
          if (resp.data.length > 0) {
            this.faq = resp.data[0]
            resp.data.forEach(element => {
              this.contentAskQuestion.push({
                content: element.answer == null ? 'answer' : element.answer,
                title: element.question == null ? 'Question' : element.question,
              })
            })
          }
        })
        .catch(error => {
          console.error(error)
        })
    },

    /**
     * create or update contact Information
     */
    pushContactInfo() {
      const content_id = this.$route.params.content_id
      const payload = {
        content_id: content_id,
        formData: this.contactInfo,
      }
      this.pushContactContent(payload)
    },

    /**
     * create or update FAQ Information
     */
    async pushFaq(content_id) {
      const payload = {
        id: this.faq.id,
        question: this.faq.question == null ? '' : this.faq.question,
        answer: this.faq.answer == null ? '' : this.faq.answer,
        content_id: content_id,
      }

      await this.pushFaqStateLicensing(payload)
    },

    // FAQ option 2
    getData(data) {
      this.faqs = data
    },

    /**
     * this method can be better (send all elements in the same request)
     ***/
    pushFaq2(content_id) {
      this.faqs.forEach(element => {
        const payload = {
          id: element.id,
          question: element.question,
          answer: element.answer,
          content_id: content_id,
        }
        this.pushFaqStateLicensing(payload)
      })
    },
  },
}
</script>

<style lang="sass" scope></style>
