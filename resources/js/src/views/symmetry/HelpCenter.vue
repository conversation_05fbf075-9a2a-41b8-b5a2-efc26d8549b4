<template>
  <v-container fluid class="help-center-container d-flex flex-column align-center">
    <heading
      key="heading-faq"
      title="Frequently Asked Questions"
      subline="List Of FAQs and Tips for Using This Onboarding Portal"
    />
    <v-row class="container-flip-cards">
      <v-col cols="12" sm="12" md="4" key="1">
        <flip-card
          title="How Do I Approve Applications?"
          text="Select My Tasks from the menu, and click on the link to review the application. When finished, select Approve, Reject, or Needs Revision to complete your workflow task."
        />
      </v-col>
      <v-col cols="12" sm="12" md="4" key="2">
        <flip-card
          title="How do I send invitations?"
          text="In the menu on the left, select 'Send New Invitation' and complete the form. Candidates will receive a custom link and invitation code needed to register for the portal."
        />
      </v-col>
      <v-col cols="12" sm="12" md="4" key="3">
        <flip-card
          title="How Do I Track Applications?"
          text="In the menu, under Applications select Track Applications There you'll find applications submitted by your and your downline's candidates. Current Status shows the step the application is currently at."
        />
      </v-col>
      <v-col v-if="appTenant != 'Q2B'" cols="12" sm="12" md="12" key="4">
        <flip-card
          title="I'm an unlicensed candidate, can I complete my application?"
          text="You're more than welcome to get started in our system. You will be able to create an account in the portal and begin your application, but you will only be able to get 30% through the system. After that, the next step will be to talk to your AO, get a course discount code from your AO, and get enrolled in a pre-licensing course. You must provide proof of enrollment in a pre-licensing course to complete your application."
        />
      </v-col>
    </v-row>
    <v-row style="width:90%" v-if="appTenant != 'Q2B'">
      <v-col cols="12">
        <heading
          key="heading-training-videos"
          title="Onboarding Portal Training Videos"
        />
        <v-responsive :aspect-ratio="16/9" width="100%">
          <iframe src='https://vimeo.com/showcase/9936036/embed' allowfullscreen frameborder='0' style='width:100%;height:100%;'></iframe>
        </v-responsive>
      </v-col>
    </v-row>

    <heading
      key="heading-need-help"
      title="Still Need Help?"
      :subline="`Refer to our <a href='${helpSheetUrl}' target='_blank'>Help Sheet</a> or for questions regarding how to use the portal, please contact your direct upline or Agency Owner. <br/><br/>For technical issues with the portal, please submit a ticket. Thank you!`"
      class="mt-16"
    />
    <v-dialog v-model="dialog4" width="500">
      <template v-slot:activator="{ on, attrs }">
        <v-btn v-bind="attrs" v-on="on" color="primary" class="ma-1 mb-16">Submit a Ticket </v-btn>
      </template>
      <v-card>
        <v-card-title class="text-h5 lighten-2"> Submit a Ticket </v-card-title>

        <v-card-text>
          <v-text-field label="Subject" v-model="form.subject"></v-text-field>
          <v-textarea label="Message" v-model="form.message"></v-textarea>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="submitForm()" :disabled="form.message == '' || form.subject == ''">
            Submit
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import Heading from '@/components/base/Heading'
import VideoPlayer from '@/components/base/VideoPlayer'
import FlipCard from '@/components/compound/FlipCard'
import PlainAccordion from '@/components/compound/PlainAccordion'
import http from '@/plugins/http'
import { SFG_GLOSSARY_CONTENT } from '@/utils/const'

export default {
  name: 'SymmetryHelpCenter',
  components: {
    Heading,
    PlainAccordion,
    FlipCard,
    VideoPlayer,
  },
  data() {
    return {
      dialog1: false,
      dialog2: false,
      dialog3: false,
      dialog4: false,
      form: {
        subject: '',
        message: '',
      },
    }
  },
  computed: {
    contentGlossary() {
      return JSON.stringify(SFG_GLOSSARY_CONTENT)
    },
    baChangeFormUrl() {
      if(this.appTenant == 'Q2B')
        return 'https://quility.jotform.com/242333119469963'
      return 'https://form.jotform.com/Quility/change-to-business-entity-form'
    },
    appTenant() {
      console.log("appTenant", process.env.MIX_APP_TENANT)
      return process.env.MIX_APP_TENANT
    },
    helpSheetUrl() {
      if(this.appTenant == 'Q2B')
        return 'https://b2b-hq.quility.com/api/public/document/8175/view/1737496263/cdfe87fd621b8a2f005b11944b7f4c8c'
      return 'https://hq.quility.com/api/public/document/168297/view/onboarding-principal-help-sheet'
    }
  },
  methods: {
    submitForm() {
      let formData = new FormData()
      formData.append('message', this.form.message)
      formData.append('subject', this.form.subject)

      http
        .post('/api/support-ticket', formData)
        .then(response => {
          this.dialog4 = false
          this.form.message = ''
          this.form.subject = ''
        })
        .catch(error => {
          console.log(error)
        })
    },
  },
}
</script>

<style lang="sass" scope>
.help-center-container

  .container-flip-cards
    width: 90%

  .accordion-glossary
    width: 100%
    max-width: 1000px

  .video-player
    max-width: 600px
    width: 100%
</style>
