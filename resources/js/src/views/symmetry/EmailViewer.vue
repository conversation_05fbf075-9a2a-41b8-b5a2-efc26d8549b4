<template>
  <v-container fluid>
    <v-row>
      <!-- Email List Column -->
      <v-col cols="4">
        <div class="d-flex align-center mb-4 ml-4">
          <h1 class="flex-grow-1">Stored Emails</h1>
          <v-btn icon @click="refreshEmails" :loading="isLoading" text>
            <v-icon>mdi-refresh</v-icon>
          </v-btn>
        </div>
        
        <v-list dense>
          <v-list-item
            v-for="email in emails"
            :key="email.id"
            @click="viewEmail(email)"
            class="email-list-item"
          >
            <v-list-item-content>
              <v-list-item-title class="font-weight-bold">{{ email.subject }}</v-list-item-title>
              <v-list-item-subtitle class="d-flex justify-space-between">
                <span>To: {{ email.to }}</span>
                <span>{{ formatDate(email.sent_at) }}</span>
              </v-list-item-subtitle>
            </v-list-item-content>
            <v-list-item-action>
              <v-icon small @click.stop="deleteEmail(email)" class="delete-icon">mdi-close</v-icon>
            </v-list-item-action>
          </v-list-item>
        </v-list>
      </v-col>

      <!-- Email Viewer Column -->
      <v-col cols="8">
        <div v-if="selectedEmail" class="email-viewer">
          <h2>{{ selectedEmail.subject }}</h2>
          <v-simple-table dense class="mb-4">
            <tbody>
              <tr>
                <td class="font-weight-bold" width="100">From:</td>
                <td>{{ selectedEmail.from }}</td>
              </tr>
              <tr>
                <td class="font-weight-bold">To:</td>
                <td>{{ selectedEmail.to }}</td>
              </tr>
            </tbody>
          </v-simple-table>
          <div v-html="selectedEmail.body"></div>
        </div>
        <div v-else class="email-viewer-placeholder">
          <p class="text-center grey--text">Select an email to view its contents</p>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import moment from 'moment';

export default {
  data() {
    return {
      emails: [],
      selectedEmail: null,
      isLoading: false,
    };
  },
  methods: {
    async fetchEmails() {
      this.isLoading = true;
      try {
        const response = await fetch('/api/test/emails');
        const data = await response.json();
        this.emails = data.data;
      } finally {
        this.isLoading = false;
      }
    },
    refreshEmails() {
      this.fetchEmails();
    },
    viewEmail(email) {
      this.selectedEmail = null
      fetch(`/api/test/emails/${email.id}`)
        .then(response => response.json())
        .then(data => {
          this.selectedEmail = data;
        });
    },
    formatDate(date) {
      return moment(date).fromNow();
    },
    deleteEmail(email) {
      fetch(`/api/test/emails/${email.id}/delete`, {
        method: 'GET',
      }).then(() => {
        this.fetchEmails();
      });
    }
  },
  mounted() {
    this.fetchEmails();
  },
};
</script>

<style>
.email-viewer {
  height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 20px;
  border-left: 1px solid #ccc;
}

.email-viewer-placeholder {
  height: calc(100vh - 120px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #ccc;
}

.email-list-item {
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.email-list-item:hover {
  background-color: #f5f5f5;
}

.v-list-item__subtitle {
  margin-top: 4px !important;
}

.delete-icon {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.email-list-item:hover .delete-icon {
  opacity: 1;
}
</style>