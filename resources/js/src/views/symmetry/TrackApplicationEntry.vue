<template>
  <v-container class="review-container pa-5 d-flex flex-column">
    <div v-if="loading" class="text-center">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <div class="text-center" v-else-if="!application">
      <h3 class="text-h3">Invalid application data</h3>
    </div>
    <v-container v-else>
      <v-container>
        <v-btn depressed color="primary" @click="$router.go(-1)">
          <v-icon left dark> mdi-arrow-left </v-icon>
          Go Back
        </v-btn>
        <v-btn v-if="hasUserRole('SuperAdmin') || hasUserRole('Staff')" class="float-right" @click="uploadFiles" :loading="uploadingFiles" :disabled="uploadingFiles">{{uploadFilesText}}</v-btn>
        <v-btn 
          v-if="hasUserRole('SuperAdmin') || hasUserRole('Staff')" 
          @click="toggleAppBlacklist(application.id)" 
          :loading="updating_blacklist" 
          class="float-right mr-2" 
          :outlined="application.blacklist" 
          color="black"
        >{{application.blacklist ? 'Unblock' : 'Block'}}</v-btn>
        <v-btn v-if="application.status == onboard_status_types.REJECTED_AO && (hasUserRole('SuperAdmin') || hasUserRole('Staff'))"
          class="float-right mr-2" 
          :loading="unrejecting" 
          @click="unrejectApplication()"
        >Unreject</v-btn>
        <v-btn v-if="hasUserRole('SuperAdmin') || hasUserRole('Staff')" class="float-right mb-4 mr-2" :href="`/impersonate/`+this.$route.params.user_id" target="_blank">Login</v-btn>
      </v-container>
      <v-container class="mt-5">
        <v-row>
          <v-col cols="12">
            <internal-notes v-if="hasUserRole('SuperAdmin') || hasUserRole('Staff')" :internal_notes="application.internal_notes" :app_id="$route.params.user_id"></internal-notes>
          </v-col>
        </v-row>
        <v-row v-if="applicationGroupName() || application.flagged">
          <v-col cols="12">
            <v-alert v-if="applicationGroupName()" type="info">Group Invitation: {{ applicationGroupName() }}</v-alert>
            <v-alert v-if="application.status == onboard_status_types.REJECTED_HO" type="error">Application Rejected</v-alert>
            <v-alert v-else-if="!hasUserRole(['SalesRep', 'AgencyOwner']) && application.flagged" type="warning">Flagged: {{ application.flagged }}
              <v-btn class="float-right" outlined color="white" @click="approveNpn()" :loading="updating_approve_npn">Approve NPN</v-btn>
              <v-btn class="float-right mr-2" outlined color="white" @click="show_confirm_reject_npn_modal = true">Reject</v-btn>
            </v-alert>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">NEW AGENT:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ application.name }}
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">DIRECT UPLINE:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ application.direct_upline }} <v-btn text @click="showUplineEditModal" class="ml-4" :loading="updating_user_upline">Change</v-btn>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">AGENCY OWNER:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ application.agency_owner }}
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">CONTRACT LEVEL:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ application.contract_level }} <v-btn text @click="show_contract_level_edit_modal = true" class="ml-4" :loading="updating_user_contract_level">Change</v-btn>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">CARRIERS:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{application.carriers}}
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">Submitted:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ narrateDate(application.submitted_at) }}
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">Last updated:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            {{ narrateDate(application.updated_at) }}
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">Status:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            <span v-if="application.status_label.indexOf('Error') > -1 || application.status.indexOf('Error') > -1">
              <span v-if="!hasUserRole(['SalesRep', 'AgencyOwner'])" class="error--text font-weight-black">
                {{ application.status_label || application.status }}
              </span>
              <span v-else>
                Approved
              </span>
            </span>
            <span v-else>
              {{ application.status_label || application.status }}
            </span>
            <span v-if="!hasUserRole(['SalesRep', 'AgencyOwner']) && application.approved_by.length > 0">
              <div v-for="(approval, index) in application.approved_by" :key="index">
                {{approval.name}} - {{approval.email}}
              </div>
            </span>
            <span v-if="hasUserRole([USER_ROLES_TYPES.STAFF, USER_ROLES_TYPES.SUPER_ADMIN])">
              <v-btn text @click="show_status_edit_modal = true" class="ml-4" :loading="updating_user_status">Change</v-btn>
            </span>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="4">
            <v-flex>
              <h3 class="text-subtitle-2">Comment From Last Review:</h3>
            </v-flex>
          </v-col>
          <v-col cols="8">
            <p class="pa-0" v-if="!application.comments.length">No comment from reviewer</p>
            <div class="pa-0" v-else>
              <div v-for="comment in application.comments.filter(c => c.trigger)" :key="comment.id" class="pa-0">
                <b>{{ narrateDate(comment.created_at) }}</b> 
                <span v-if="comment.user_status.slug.indexOf('error') == -1">
                  by <b>{{ comment.trigger.name }}</b> ({{ comment.trigger.roles[0].name }}) : 
                </span>
                <br/>
                <span v-if="comment.user_status.slug.indexOf('error') > -1" class="error--text">
                  <span v-if="!hasUserRole(['SalesRep', 'AgencyOwner'])">
                    <b class="text-h3">|</b> &nbsp; <strong>{{comment.user_status.name}}:</strong> {{ comment.note }}
                  </span>
                </span>
                <span v-else>
                  <b class="text--secondary text-lighten-3 text-h3">|</b> &nbsp; {{ comment.note }}
                </span>
              </div>
            </div>
          </v-col>
        </v-row>
        <div v-if="tenant == 'Q2B'">
          <v-row>
            <v-col cols="4">Type:</v-col>
            <v-col cols="8">{{ application.invitation.type.replace(/-/g, ' ').split(' ').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ') }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="4">Carriers:</v-col>
            <v-col cols="8">{{ application.invitation.carrier }}</v-col>
          </v-row>
          <v-row>
            <v-col cols="4">Alternative Email:</v-col>
            <v-col cols="8">{{ application.invitation.alt_email }}</v-col>
          </v-row>
          <v-row>
              <v-col cols="4">Agreement:</v-col>
              <v-col cols="8">{{ application.invitation.agreement_type }}</v-col>
          </v-row>
          <v-row>
              <v-col cols="4">Service Level:</v-col>
              <v-col cols="8">{{ application.invitation.service_level }}</v-col>
          </v-row>
          <v-row>
              <v-col cols="4">Bonus Addendum:</v-col>
              <v-col cols="8">{{ application.invitation.bonus_addendum ? 'Yes' : 'No' }}</v-col>
          </v-row>
          <v-row>
              <v-col cols="4">Multiple Owners:</v-col>
              <v-col cols="8">{{ application.invitation.multiple_owners ? 'Yes' : 'No' }}</v-col>
          </v-row>
        </div>
      </v-container>
      <v-divider></v-divider>
      <v-container v-for="page in application.pages" :key="page.id" class="mt-2">
        <p v-if="!!page.subline">{{ page.subline }}</p>
        <v-container
          v-for="section in page.sections"
          class="pa-0 ma-0"
          :key="section.id"
          :style="{ display: `${canShowSection(section) ? 'flex' : 'none'}` }"
        >
          <v-row>
            <v-col cols="4" class="mt-4">
              <v-flex>
                <h3 class="text-subtitle-2" v-html="section.label">
                </h3>
                <p v-if="!!section.subline">{{ section.subline }}</p>
              </v-flex>
            </v-col>
            <v-col cols="8" class="mt-4">
              <v-flex v-for="(v, sort) in (new Array(section.repeats))" :key="`sort-${sort}`">
                  <br v-if="sort>0"/>
                  <v-flex v-for="(field, field_id) in section.fields" v-bind:key="field_id">
                    <v-container class="overflow-auto ma-0 pa-0" v-if="field.value[sort]">
                        <template v-if="isImageField(field) && isUpload(field)">
                          <span v-for="(f, i) in field.value[sort]" v-bind:key="i">
                            <template v-if="isPDF(f) || isWord(f)">
                              <a :href="f" target="_blank" download>File</a>
                            </template>
                            <template v-else>
                              <img :src="f" style="width: 100%" />
                            </template>
                            <br>
                          </span>
                        </template>
                        <template v-else-if="isImageField(field) && isSignature(field)">
                          <img :src="field.value[sort]" style="width: 100%" />
                        </template>
                        <template v-else-if="(field.value[sort] == 'YES' && page.label.indexOf('Legal Questions') > -1) || (section.label.indexOf('returning to Symmetry') > -1 && field.value[sort] == 'YES')">
                          <span class="error white--text font-weight-black pa-2">{{ `${field.label && field.label + ':'} ${field.value[sort]}` }}</span>
                        </template>
                        <template v-else>
                          <span v-if="section.label.indexOf('I was recruited into') > -1 && application.source">(Recruiter: {{application.source}})</span>
                          <span v-if="field.is_secure && hasUserRole('AgencyOwner')">{{ `${field.label && field.label + ':'} *********` }}</span>
                          <span v-else v-html="renderValue(field, sort)"></span>
                        </template>
                    </v-container>
                  </v-flex>
              </v-flex>
            </v-col>
          </v-row>
        </v-container>
      </v-container>
    </v-container>
    <p class="text-h5 mt-10 pt-10 text-center">
      Please note that applications are saved online for 60 days. Pending applications older than 60 days will need to
      be resubmitted.
    </p>

    <v-dialog v-model="show_upline_edit_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          <select-box
            :autocomplete="true"
            :loading="uplineAgents.length == 0"
            ref="upline"
            label="Upline"
            :items="uplineAgents"
            v-model="upline"
            item-text="agent_select_text"
            item-value="agent_id"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="updateUserUpline" color="primary" :loading="updating_user_upline">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_contract_level_edit_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          <select-box
            ref="contract_level"
            label="Contract Level"
            :items="contractLevels"
            v-model="contract_level"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="updateUserContractLevel" color="primary" :loading="updating_user_contract_level">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_status_edit_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          <select-box
            ref="status"
            label="Status"
            :items="user_statuses"
            v-model="edit_status"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="updateUserStatus" color="primary" :loading="updating_user_status">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_confirm_reject_npn_modal" max-width="500">
      <v-card style="background-color: #f5f5f5 !important;">
        <v-card-text>
          Are you sure you want to reject this application?
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="show_confirm_reject_npn_modal = false">Cancel</v-btn>
          <v-btn @click="rejectNpn" color="primary" :loading="udpating_reject_npn">Reject</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES, FORM_FIELD_TYPES, FORM_FIELD_AUTOSAVE_DURATION, FORM_FIELD_CONDITION_ACTION_TYPES } from '@/utils/const'
import { compare, abbrString, narrateDate } from '@/utils/helper'
import http from '@/plugins/http'
import SelectBox from '@/components/base/SelectBox'
import InternalNotes from '../../components/InternalNotes.vue'
import moment from 'moment'

export default {
  components: { SelectBox, InternalNotes },
  data() {
    return {
      loading: false,
      onboard_status_types: ONBOARD_STATUS_TYPES,
      // application data
      application: null,
      allFieldValues: {},
      uploadingFiles: false,
      uploadFilesText: 'Upload Files to HQ',

      show_upline_edit_modal: false,
      upline: null,
      uplineAgents: [],
      contract_level: null,
      show_contract_level_edit_modal: false,
      show_status_edit_modal: false,
      edit_status: null,
      contractLevels: [],
      updating_user_upline: false,
      updating_user_contract_level: false,
      updating_user_status: false,
      updating_approve_npn: false,
      udpating_reject_npn: false,
      show_confirm_reject_npn_modal: false,
      user_id: null,
      updating_blacklist: false,
      unrejecting: false,
      user_statuses: [
        {
          label: 'Submitted to AO',
          value: 'submitted-ao'
        },
        {
          label: 'Submitted to HO',
          value: 'submitted-ho',
        },
        {
          label: 'User Editing',
          value: 'eft-information'
        }
      ],
      USER_ROLES_TYPES: null,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole'
    }),
    tenant() {
      return process.env.MIX_APP_TENANT
    }
  },
  created() {
    this.getApplication()
  },
  mounted: function() {
    this.getUserContractLevels();
    this.USER_ROLES_TYPES = USER_ROLES_TYPES
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplicationDetailForTrack: 'application/getApplicationDetailForTrack',
      getImage: 'application/getImage',
      getContractLevels: 'user_invite/getContractLevels',
      getAssignableUplines: 'user_invite/getAssignableUplines',
      updateUser: 'application/updateUser',
      toggleBlacklist: 'application/toggleBlacklist',
      updateApplication: 'application/updateApplication',
      approveApplicationNpn: 'application/approveApplicationNpn',
      rejectApplicationNpn: 'application/rejectApplicationNpn',
    }),
    narrateDate,
    renderValue(field, sort) {
      // return field.label + "!"
      if([
            'American Amicable/Occidental',
            'AIG',
            'Americo',
            'Banner/LGA',
            'F&G',
            'Foresters',
            'Mutual of Omaha',
            'SBLI',
            'UHL'
        ].includes(field.label) && field.value[sort] == "0")
        return ''
      if(
        field.label == 'When was your signed contract date?' 
        || field.label == 'When was the last piece of business written?'
      ) {
        const date = moment(field.value[sort]);
        const currentDate = moment();
        const sixMonthsAgo = currentDate.clone().subtract(6, 'months');
        // Check if the given date is between six months ago and the current date
        if (date.isBetween(sixMonthsAgo, currentDate, null, '[]')) {
          let html = `${field.label && field.label + ':'} ${field.value[sort]}`
          if(field.label.indexOf('the last') > 0)
            html += '<div class="bold-red-text mb-4">This candidate is not a free agent with this carrier</div>'
          return html
        }
      }
      return `${field.label && field.label + ':'} ${field.value[sort]}`
    },
    applicationGroupName() {
      return this.application?.invitation?.group?.group_name ?? false
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    isImageField(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_UPLOAD || field.type === FORM_FIELD_TYPES.TYPE_SIGNATURE;
    },
    isUpload(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_UPLOAD;
    },
    isSignature(field) {
      return field.type === FORM_FIELD_TYPES.TYPE_SIGNATURE;
    },
    isPDF(field) {
      var re = /(?:\.([^.]+))?$/;
      return re.exec(field)[1] === 'pdf';
    },
    isWord(field) {
      var re = /(?:\.([^.]+))?$/;
      return re.exec(field)[1] === 'doc' || re.exec(field)[1] === 'docx';
    },
    canShowSection(section, deepStack = []) {
      if (!section) return false
      if(deepStack.includes(section.id)) {
        return true;
      }

      if (section.conditions) {
        const indexConditionRefused = section.conditions.findIndex(condition => {
          const { action, field, type, value } = condition
          const res = compare(
            Array.isArray(this.allFieldValues[field]) ? this.allFieldValues[field][0] : this.allFieldValues[field],
            type,
            value,
          )

          const canShowParentSection = this.canShowSection(
            this.findSectionContainingField(field),
            [
              ...deepStack,
              section.id
            ]
          );

          return (
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !res) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_SHOW && !canShowParentSection) ||
            (action === FORM_FIELD_CONDITION_ACTION_TYPES.ACTION_HIDE && res)
          )
        })

        return indexConditionRefused === -1
      }

      return true
    },
    findSectionContainingField(fieldId) {
      let sectionFound = null;

      this.application.pages.some(page => {
        page.sections.some(section => {
          Object.keys(section.fields).some(fId => {
            if(fId == fieldId)
              sectionFound = section
            return sectionFound
          })
          return sectionFound
        })
        return sectionFound
      })

      return sectionFound;
    },
    uploadFiles() {
      var appId = this.$route.params.user_id
      var g = this
      return new Promise((resolve, reject) => {
        this.uploadingFiles = true
        http
          .get(`/api/application/upload_files/${appId}`)
          .then(response => {
            g.uploadFilesText = response.data
            resolve(response)
          })
          .catch(err => reject(err))
          .finally(() => this.uploadingFiles = false)
      })
    },
    showUplineEditModal() {
      this.getUserPossibleUplines();
      this.show_upline_edit_modal = true;
    },
    getApplication() {
      this.loading = true

      this.getApplicationDetailForTrack(this.$route.params.user_id)
        .then(async res => {
          if (res && res.data) this.application = res.data
          else throw 'Cannot get application detail for track!'

          this.application.pages.map(page => {
            page.sections.map(section => {
              Object.keys(section.fields).map(fieldId => this.allFieldValues[fieldId] = section.fields[fieldId].value)
            })
          })
        })
        .finally(() => (this.loading = false))
    },
    updateUserUpline() {
      this.updating_user_upline = true
      this.updateUser({ user_id: this.$route.params.user_id, upline_agent_id: this.upline }).then(response => {
        this.show_upline_edit_modal = false
        this.setSnackbar({
          status: true,
          text: 'Upline updated',
          color: 'success',
        })
        this.getApplication()
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to update upline.',
          color: 'warning',
        })
      })
      .finally(() => this.updating_user_upline = false)
    },
    getUserContractLevels() {
      this.getContractLevels().then(response => {
        this.contractLevels = response.data
      })
    },
    getUserPossibleUplines() {
      this.getAssignableUplines(this.application.agency_owner_code).then(response => {
        this.uplineAgents =  response.data.map(item => {
          item.agent_select_text = `${item.name} - ${item.agent_code}`
          return item
        })
      })
    },
    updateUserContractLevel() {
      this.updating_user_contract_level = true
      this.updateUser({ user_id: this.$route.params.user_id, contract_level: this.contract_level }).then(response => {
        this.show_contract_level_edit_modal = false
        this.setSnackbar({
          status: true,
          text: 'Contract Level updated',
          color: 'success',
        })
        this.getApplication()
      })
      .catch(err => {
        console.error(err)
        
        this.setSnackbar({
          status: true,
          text: 'Failed to update contract level.',
          color: 'error',
        })
      })
      .finally(() => this.updating_user_contract_level = false)
    },
    updateUserStatus() {
      this.updating_user_status = true
      this.updateUser({ user_id: this.$route.params.user_id, user_status_slug: this.edit_status }).then(response => {
        this.show_status_edit_modal = false
        this.setSnackbar({
          status: true,
          text: 'Status updated',
          color: 'success',
        })
        this.getApplication()
      })
      .catch(err => {
        this.setSnackbar({
          status: true,
          text: 'Failed to update status.',
          color: 'warning',
        })
      })
      .finally(() => this.updating_user_status = false)
    },
    toggleAppBlacklist() {
      this.updating_blacklist = true
      this.toggleBlacklist(this.application.id).then(response => {
        this.$set(this.application, "blacklist", response.data.blacklist)
      })
      .catch(err => {
        console.error(err)
        this.setSnackbar({
          status: true,
          text: 'Failed to update blocked status.',
          color: 'error',
        })
      })
      .finally(() => this.updating_blacklist = false)
    },
    approveNpn() {
      this.updating_approve_npn = true
      this.approveApplicationNpn(this.application.id)
      .then(() => {
        this.application.flagged = null
        window.location.reload()
      })
      .finally(() => this.updating_approve_npn = false)
    },
    rejectNpn() {
      this.udpating_reject_npn = true
      this.rejectApplicationNpn(this.application.id)
      .then(() => {
        // this.application.flagged = null
        this.show_confirm_reject_npn_modal = false
        window.location.reload()
      })
      .finally(() => this.udpating_reject_npn = false)
    },
    unrejectApplication() {
      this.updateApplication({
        id: this.application.id,
        status: this.onboard_status_types.SUBMITTED_AO,
        note: this.finalNote,
      })
        .then(() => {
          this.setSnackbar({
            status: true,
            text: 'Review successfully sent back to AO!',
            color: 'success',
          })
          if(this.hasUserRole(['SuperAdmin', 'Staff']))
            this.goTo('pending_applications')
          else
            this.goTo('my_task')
        })
        .catch((err) => {
          this.setSnackbar({
            status: true,
            text: err.response?.data?.message || 'Failed to send to AO. Please contact support.',
            color: 'warning',
          })
        })
        .finally(() => (this.loading = false))
    }
  },
}
</script>

<style>
.bold-red-text {
  font-weight: bold;
  color: red;
}
</style>
