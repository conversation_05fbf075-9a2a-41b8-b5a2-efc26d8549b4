<template>
  <v-container class="d-flex flex-column pa-8">
    <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" :default="search.text" :key="search_text_key" />
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Onboarding Status" :items="itemsApplicationStatus" v-model="search.status" :default="search.status" />
      </v-col>
    </v-row>
    <v-data-table
      :headers="headers"
      :items="items"
      :loading="loading"
      sort-by="registered"
      :options.sync="options"
      :server-items-length="total"
    >
      <template v-slot:top>
        <v-row class="mb-6">
          <v-col class="" cols="3">
            <v-select
              label="Date Field"
              v-model="search.date_field"
              outlined
              :items="filter_date_options">
            </v-select>
          </v-col>
          <v-col cols="3">
            <v-menu
              ref="start_date_menu"
              v-model="start_date_menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="search.start_date"
                  outlined
                  label="Start Date"
                  append-icon="mdi-calendar"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="search.start_date"
                no-title
                scrollable
                @input="start_date_menu = false"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="3">
            <v-menu
              ref="end_date_menu"
              v-model="end_date_menu"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="search.end_date"
                  outlined
                  label="End Date"
                  append-icon="mdi-calendar"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="search.end_date"
                no-title
                scrollable
                @input="end_date_menu = false"
              >
              </v-date-picker>
            </v-menu>
          </v-col>
          <v-col cols="3">
            <v-btn class="mt-2" @click="resetSearch()" block>Reset Search</v-btn>
          </v-col>
        </v-row>
      </template>
      <template v-slot:[`item.agent`]="{ item }">
        <a class="pr-2 text--darken-3 text--secondary" @click="goToAction(item, true)" style="white-space:nowrap;">
          <span :class="item.blacklist ? 'black white--text pa-1' : ''">{{ item.agent }}</span>
        </a>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <span v-if="item.status && item.status.indexOf('Error') > -1" class="error--text font-weight-black">
          <v-icon color="error" class="">mdi-alert</v-icon>
          {{ item.status }}
        </span>
        <span v-else>{{ item.status }}</span>
        <h5 class="text-h5 info--text" v-if="activeReviewer(item.id)" v-bind:key="tickReviewStatus">
          {{ activeReviewer(item.id) }} is reviewing...
        </h5>
      </template>
      <template v-slot:[`item.applied_on`]="{ item }">
        {{ narrateDate(item.applied_on) }}
      </template>
      <template v-slot:[`item.completed_on`]="{ item }">
        {{ narrateDate(item.completed_on) }}
      </template>
      <template v-slot:[`item.actions`]="{ item }">
          <v-menu
            bottom
            left
            open-on-hover
            :offset-x="true"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                v-on="on"
                fab
                elevation="0"
                :color="item.status_slug == onboard_status_types.SUBMITTED ? item.is_new_submit ? 'primary' : 'error' : 'secondary'"
                x-small
              >
                <v-icon>mdi-menu-down</v-icon>
              </v-btn>
            </template>

            <v-list dense>
              <v-list-item
                v-if="hasUserRole('SuperAdmin') || (hasUserRole('AgencyOwner') && item.status_slug == onboard_status_types.SUBMITTED) || (hasUserRole('Staff') && item.approved_by_ao == true)"
                @click="goToAction(item)"
                class="pr-8 text-center font-weight-bold"
                :class="item.is_new_submit ? 'primary--text' : 'error--text'"
              >Review</v-list-item>
              <v-list-item
                @click="goToAction(item, true)"
                color="secondary"
                class="pr-8 text-center"
              >View</v-list-item>
              <v-list-item
                v-show="hasUserRole('SuperAdmin') || hasUserRole('Staff')"
                :href="`/impersonate/${item.id}`"
                class="pr-8 text-center"
              >Login</v-list-item>
              <v-list-item
                v-show="hasUserRole('SuperAdmin') || hasUserRole('Staff')"
                @click="showResetPasswordModal(item)"
                class="pr-8 text-center"
              >Reset Password</v-list-item>
              <v-list-item
                v-show="hasUserRole('SuperAdmin') || hasUserRole('Staff')"
                @click="showUpdateEmailModal(item)"
                class="pr-8 text-center"
              >Update Name and Email</v-list-item>
            </v-list>
          </v-menu>
      </template>
      <template v-slot:no-data>
        <h4>No applications</h4>
      </template>
      <template v-slot:[`item.homeoffice_submission_on`]="{ item }">
        {{ narrateDate(item.homeoffice_submission_on) }}
      </template>
      <template v-slot:[`item.pathway`]="{ item }">
        <pathway-chips :pathway="item.pathway"></pathway-chips>
      </template>
    </v-data-table>

    <v-dialog v-model="show_reset_password_modal" persistent max-width="500">
      <v-card style="background-color: white !important;">
        <v-card-text>
          Reset password for <span class="font-weight-bold">{{ password_reset_user.agent }}</span>.
          <v-text-field
            v-model="new_password"
            label="New Password"
            :error="reset_password_error != ''"
            :messages="reset_password_error"
          ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="show_reset_password_modal = false">Cancel</v-btn>
          <v-btn
            color="primary darken-1"
            @click="resetPassword"
            :loading="resetting_password"
            :disabled="resetting_password"
          >Reset</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="show_update_email_modal" persistent max-width="500">
      <v-card style="background-color: white !important;">
        <v-card-text>
          Update email and name for <span class="font-weight-bold">{{ email_update_user.agent }}</span>.
          <div class="text-caption mb-4" style="color: #666;">
            This will update the login/display information only. It does not change anything on their application.
          </div>
          <v-text-field
            v-model="new_email"
            label="New Login Email"
            type="email"
            :error="update_email_error != ''"
            :messages="update_email_error"
          ></v-text-field>
          <v-text-field
            v-model="new_name"
            label="New Display Name"
            :error="update_name_error != ''"
            :messages="update_name_error"
          ></v-text-field>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="show_update_email_modal = false">Cancel</v-btn>
          <v-btn
            color="primary darken-1"
            @click="updateEmail"
            :loading="updating_email"
            :disabled="updating_email"
          >Update</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'
import PathwayChips from '@/components/PathwayChips'

export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
    PathwayChips,
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: '', value:'pathway' },
        { text: 'FULL LEGAL NAME:', value: 'agent', class: 'app-table-col' },
        { text: 'APPLIED TO AO ON', value: 'applied_on', class: 'app-table-col' },
        { text: 'DATE SUBMITTED TO HOME OFFICE', value: 'homeoffice_submission_on', class: 'app-table-col' },
        // { text: "COMPLETED ON", value: "completed_on", class: "app-table-col" },
        { text: 'ONBOARDING STATUS', value: 'status', class: 'app-table-col' },
        {
          text: 'DIRECT UPLINE NAME',
          value: 'direct_upline',
          class: 'app-table-col',
          sortable: false
        },
        {
          text: 'AGENCY OWNER NAME',
          value: 'agency_owner',
          class: 'app-table-col',
          sortable: false
        },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
          sortable: false
        },
      ],
      items: [],
      total: 0,
      loading: false,

      itemsApplicationStatus: [],

      options: {}, // option for pagination
      search: {
        // option for search
        text: '',
        status: '',
        start_date: '',
        end_date: '',
        date_field: 'updated_at'
      },

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,

      show_reset_password_modal: false,
      password_reset_user: {agent: ''},
      new_password: "",
      reset_password_error: "",
      resetting_password: false,
      search_text_key: 'search_text_key',

      start_date_menu: false,
      start_date: "",
      end_date_menu: false,
      end_date: "",

      filter_date_options: [
        { text: "Date Updated", value: "updated_at" },
        { text: "Date Created", value: "created_at" },
        { text: "Date Submitted to AO", value: "applied_on" },
        { text: "Date Submitted to HO", value: "homeoffice_submission_on"}
      ],

      show_update_email_modal: false,
      email_update_user: { agent: '' },
      new_email: "",
      update_email_error: "",
      updating_email: false,
      new_name: "",
      update_name_error: "",
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
    }),
  },
  watch: {
    options: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
        window.localStorage.setItem("all_applications_options", JSON.stringify(this.options))
      },
      deep: true,
    },
    search: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  mounted() {
    this.getApplicationStatusOptions()
    let search = window.localStorage.getItem("search_all")
    this.options = JSON.parse(window.localStorage.getItem('all_applications_options'))
    if(search) {
      let { text, status, start_date, end_date, date_field } = JSON.parse(search)
      this.search = { text, status, start_date, end_date, date_field }
      this.search_text_key = Math.random()
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplications: 'application/getApplications',
      getStatusOptions: 'application/getStatusOptions',
      adminResetPassword: 'application/adminResetPassword',
      adminUpdateEmail: 'application/adminUpdateEmail',
    }),
    narrateDate,
    resetSearch() {
      window.localStorage.setItem("search_all", JSON.stringify({ text: '', status: '', start_date: '', end_date: '', date_field: 'updated_at' }))
      this.search = { text: '', status: '' }
      this.options = {}
      this.search_text_key = Math.random()
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    goToAction(item, view = false) {
      if (view) this.goToPath('/app/application/track/' + item.id)
      else if (this.canReview(item)) this.goToPath('/app/application/review/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
    showResetPasswordModal(user) {
      this.reset_password_error = ""
      this.show_reset_password_modal = true
      this.password_reset_user = user
    },
    showUpdateEmailModal(user) {
      this.update_email_error = ""
      this.update_name_error = ""
      this.show_update_email_modal = true
      this.email_update_user = user
      this.new_email = user.email || ""
      this.new_name = user.agent || ""
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        ((this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED) ||
         (this.hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && item.approved_by_ao)
        ) &&
        !this.activeReviewer(item.id)
      )
    },
    getDataFromApi() {
      this.loading = true

      //saving search settings
      window.localStorage.setItem("search_all", JSON.stringify(this.search))

      this.getApplications(
        new URLSearchParams({
          ...this.options,
          ...this.search,
          completed: 'pending',
          show_all: 'true',
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
          }
          if(res.data.total < this.options.page * this.options.itemsPerPage && this.options.page > 1)
            this.options.page = 1
        })
        .finally(() => {
          this.loading = false
        })
    },
    getApplicationStatusOptions() {
      this.getStatusOptions().then(response => {
        this.itemsApplicationStatus = response.data
      })
    },
    resetPassword() {
      this.reset_password_error = ""
      if(this.new_password.length < 8) {
        this.reset_password_error = 'Password must be at least 8 characters long.'
        return
      }
      this.resetting_password = true
      this.adminResetPassword({id: this.password_reset_user.id, password: this.new_password})
      .then(res => {
        this.resetting_password = false
        this.show_reset_password_modal = false
        this.setSnackbar({
          status: true,
          text: 'Password reset successful',
          color: 'success',
        })
      })
      .catch((err) => {
        console.error(err)
        this.resetting_password = false
        this.setSnackbar({
          status: true,
          text: 'Password reset failed.',
          color: 'warning',
        })
      })
    },
    updateEmail() {
      // Clear errors
      this.update_email_error = ""
      this.update_name_error = ""

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(this.new_email)) {
        this.update_email_error = 'Please enter a valid email address.'
        return
      }

      // Check that the new name is not empty
      if (!this.new_name.trim()) {
        this.update_name_error = 'Please enter a valid name.'
        return
      }

      this.updating_email = true
      this.adminUpdateEmail({ id: this.email_update_user.id, email: this.new_email, name: this.new_name })
        .then(res => {
          this.updating_email = false
          this.show_update_email_modal = false
          this.setSnackbar({
            status: true,
            text: 'Email and name updated successfully',
            color: 'success',
          })
          // Refresh the data to show updated info
          this.getDataFromApi()
        })
        .catch((err) => {
          console.error(err)
          this.updating_email = false
          this.update_email_error = err.response?.data?.message || 'Email update failed.'
          this.setSnackbar({
            status: true,
            text: 'Email update failed.',
            color: 'error',
          })
        })
    },
  },
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
