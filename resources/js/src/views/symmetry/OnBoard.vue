<template>
  <v-container class="pa-5 d-flex flex-column align-center justify-center" v-if="canEdit">
    <div class="text-center" v-if="loading">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <v-container v-else class="ma-0 pa-0">
      <v-container v-if="countTotalNotes() || comments.length">
        <v-card class="pa-2 d-flex flex-row justify-space-between">
          <v-card-title>
            <v-icon color="warning" v-if="countTotalNotes() != countTotalNotesFixed()">
              mdi-information-outline
            </v-icon>
            <h4 class="warning--text ml-1" v-if="countTotalNotes() != countTotalNotesFixed()">
              Attention! There are {{ countTotalNotes() }} notes for your application. ({{ countTotalNotesFixed() }}
              fixed)
            </h4>
            <h3 class="success--text ml-1" v-else>
              Great! You have fixed all {{ countTotalNotes() }} notes from reviewer.
            </h3>
          </v-card-title>
          <v-card-actions>
            <v-btn
              depressed
              class="mr-2"
              color="success"
              v-if="isSignatureSigned && countTotalNotes() == countTotalNotesFixed()"
              @click="askForNext(true)"
              :disabled="loading"
              :loading="loading"
            >
              <v-icon left> mdi-check </v-icon>
              Submit for Review
            </v-btn>
            <!-- <v-btn text @click="showNotesModal = true">
              <v-icon left> mdi-format-list-bulleted </v-icon>
              Show notes
            </v-btn> -->
            <!-- <v-btn text>
              <v-icon left> mdi-arrow-left </v-icon>
              Previous
            </v-btn>
            <v-btn text>
              Next
              <v-icon right> mdi-arrow-right </v-icon>
            </v-btn> -->
          </v-card-actions>
        </v-card>
      </v-container>
      <v-container v-if="countTotalNotes() || comments.length" width="1000">
        <v-card class="pa-5">
          <b>Comment From Last Review: </b><br />
          <p class="pa-2" v-if="!comments.length">No comment from reviewer</p>
          <div class="pa-2" v-else>
            <div v-for="comment in comments.filter(c => c.trigger && c.note.indexOf('Client error') == -1)" :key="comment.id" class="pa-0">
              <b>{{ narrateDate(comment.created_at, false) }}</b> by <b>{{ comment.trigger.name }}</b> ({{ comment.trigger.roles[0].name }}) : <br/>
              <b class="text--secondary text-lighten-3 text-h3">|</b> &nbsp; {{ comment.note }}
            </div>
          </div>
          <br/>
          <data-table :headers="noteTableHeaders" :items="Object.values(notes)">
            <template v-slot:[`item.fixed`]="{ item }">
              <v-icon v-if="item.fixed" color="success"> mdi-check-outline </v-icon>
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-container class="d-flex flex-row">
                <v-btn color="primary darken-1" @click="goToSection(item)" text :disabled="inCurrentStep(item)">
                  <v-icon color="primary darken-2"> mdi-arrow-right </v-icon>
                  Go
                </v-btn>
                <v-tooltip top v-if="item.fixed == '1' || !inCurrentStep(item)">
                  <template v-slot:activator="{ on, attrs }">
                    <div
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-btn :disabled="item.fixed == '1' || !inCurrentStep(item)" color="success" text @click="resolveNote(item, 1)">
                        <v-icon color="green darken-1"> mdi-check </v-icon>
                        Mark as Fixed
                      </v-btn>
                    </div>
                  </template>
                  <div style="max-width:200px" v-if="item.fixed">Already fixed. Click undo if you still need to make corrections.</div>
                  <div style="max-width:200px" v-else>You need to click the "Go" button to visit the required
                    page for corrections before marking as fixed.</div>
                </v-tooltip>
                <v-btn v-else color="success" text @click="resolveNote(item, 1)">
                  <v-icon color="green darken-1"> mdi-check </v-icon>
                  Mark as Fixed
                </v-btn>
                <v-btn :disabled="!item.fixed" color="warning" text @click="resolveNote(item, 0)">
                  <v-icon color="green darken-1"> mdi-close </v-icon>
                  Undo
                </v-btn>
              </v-container>
            </template>
          </data-table>
        </v-card>
      </v-container>
      <v-container>
        <progress-bar
          v-if="currentStepIndex !== null && Object.keys(notes).length == 0"
          :value="currentStepIndex + 1"
          :total="totalSteps"
          :description="currentStepTitle"
          :steps= "steps"
          :user_history_slugs="userHistorySlugs"
          @goToStep="progressBarGoToStep"
          :type="$store.state.auth.user.type"
        ></progress-bar>
      </v-container>
      <form-page
        @setPreventNextEvent="setPreventNext"
        ref="page"
        v-bind:key="currentStepKey"
        :notes="notes"
        v-if="data"
        :data="data"
        :showValidationErrors="showErrors"
        @reloadFormPage="reloadFormPage()"
      />
      <div class="text-center" v-else>
        <h3>No content</h3>
      </div>
      <v-container>
        <v-row>
          <v-col>
            <hr class="mt-1" />
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-btn
              v-if="currentStepIndex > 0"
              depressed
              class="mr-2"
              color="primary"
              @click="previous()"
              :disabled="loading"
            >
              PREVIOUS
            </v-btn>

            <v-alert v-if="show_ca_privacy_notice_error" class="my-4" type="warning">Please click the link to review the Privacy Notice for California Contractors in order to continue.</v-alert>

            <v-btn
              depressed
              class="mr-2"
              color="primary"
              v-if="showNextButton"
              @click="askForNext(false)"
              :disabled="loading"
            >
              NEXT
            </v-btn>
            <v-btn
              depressed
              class="mr-2"
              color="success"
              v-if="showSubmitForReviewButton"
              @click="askForNext(true)"
              :disabled="loading"
              :loading="loading"
            >
              <v-icon left> mdi-check </v-icon>
              Submit for Review
            </v-btn>
            <v-btn
              depressed
              color="success"
              v-if="showSignAgreement"
              @click="helloSign()"
              :disabled="bGettingSignRequest"
              :loading="bGettingSignRequest"
            >
              Sign Agreement
            </v-btn>
            <v-btn
              depressed
              class="mr-2"
              color="primary"
              v-if="showVerifyNpnButton"
              @click="setVerifyNpnContinueEditing(false);verify_npn = true;showConfirmLicensingStepModal = true;preventNextButton = true;"
              :disabled="loading"
            >
              Verify NPN
            </v-btn>
          </v-col>
        </v-row>
        <v-row justify="center">
          <v-dialog v-model="showConfirmProceedModal" persistent max-width="590">
            <v-card style="background-color: #fff !important;">
              <v-card-title class="text-h2"> Force proceed? </v-card-title>
              <v-card-text
                >You have some errors with the fields in this page. Do you want to continue anyway?</v-card-text
              >
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="secondary darken-1" @click=";(showConfirmProceedModal = false), (showErrors = true)">
                  No, show me the errors
                </v-btn>
                <v-btn color="primary darken-1" @click="next()"> Yes, I will look back later </v-btn>
                <v-spacer></v-spacer>
              </v-card-actions>
            </v-card>
          </v-dialog>

          <v-dialog v-model="showSubmitForReviewDialog" max-width="590">
            <v-card>
              <v-card-title class="text-h3 success--text" style="text-align: center;">Great! You have fixed all {{ countTotalNotes() }} notes from reviewer.</v-card-title>
              <!-- <v-card-text
                >You have some errors with the fields in this page. Do you want to continue anyway?</v-card-text
              > -->
              <v-card-actions class="pb-4">
                <v-spacer></v-spacer>
                <v-btn
                  depressed
                  class="mr-2"
                  color="success"
                  v-if="isSignatureSigned && countTotalNotes() == countTotalNotesFixed()"
                  @click="askForNext(true)"
                  :disabled="loading"
                  :loading="loading"
                >
                  <v-icon left> mdi-check </v-icon>
                  Submit for Review
                </v-btn>
                <v-spacer></v-spacer>
              </v-card-actions>
            </v-card>
          </v-dialog>

          <v-dialog v-model="showAcknowledgeVectorDialog" persistent max-width="590">
            <v-card>
              <v-card-title class="text-h2"> Issues Found </v-card-title>
              <v-card-text>
                <v-alert
                  v-if="vector_matches > 0"
                  border="left"
                  type="warning"
                >
                  You have {{ vector_matches }} {{ vector_matches == 1 ? 'vector' : 'vectors' }} with {{ vector_company }}.
                  You may experience a delay in contracting.
                </v-alert>
                <v-alert
                  v-if="npn_results && npn_results.message"
                  border="left"
                  :type="npn_results.message == 'No entity found' ? 'error' : 'warning'"
                >
                  NPN Error: {{ npn_results.message }}
                  <div v-if="npn_results.message == 'No entity found'">
                    Double-check your name and SSN in your application and try again.
                  </div>
                </v-alert>
                <v-row v-if="npn_results && npn_results.message != 'No entity found'">
                  <v-col>
                    <v-checkbox v-model="vector_acknowledged" label="Acknowledge" class="ml-4"></v-checkbox>
                  </v-col>
                </v-row>

              </v-card-text>
              <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="secondary darken-1" @click="showAcknowledgeVectorDialog = false">
                  Cancel
                </v-btn>
                <v-btn  v-if="npn_results && npn_results.message != 'No entity found'" color="primary darken-1" :disabled="!vector_acknowledged"  @click="next()">Acknowledge and Submit </v-btn>
                <v-spacer></v-spacer>
              </v-card-actions>
            </v-card>
          </v-dialog>

          <v-overlay
            :value="showConfirmLicensingStepModal"
            opacity="1"
            class="text-center pa-4"
            z-index="100"
          >
            <v-sheet maxWidth="765" color="transparent">
              <div class="text-h2">{{confirmationTitle}}</div>
              <div v-if="!getUserMeta('npn-exists') && !show_npn_exists_alert" v-html="confirmationText" class="mt-4 mb-6"></div>
              <div v-if="verify_npn">
                <v-row v-if="use_license_number" class="mb-8">
                  <v-col>
                    <span>State License Number</span>
                    <input type="text" v-model="licenseId" style="width:100%;margin:auto;border:1px solid #fff;" class="pa-4 mt-4 rounded-lg text-center font-weight-bold white--text" />
                    <span v-if="license_id_error != ''" class="text-error">{{license_id_error}}</span>
                  </v-col>
                  <v-col>
                    <span>State</span>
                    <select v-model="state" class="mt-4 pa-4 rounded-lg text-center white--text" style="width:100%;border:1px solid #fff;">
                      <option style="color:#fff;background-color:#212121;" v-for="state in states" :key="state.value" :value="state.value">{{state.label}}</option>
                    </select>
                    <span v-if="state_error != ''" class="text-error">{{state_error}}</span>
                  </v-col>
                </v-row>
                <div v-else-if="!getUserMeta('npn-exists') && !show_npn_exists_alert">
                  <div class="mb-8" v-if="show_license_option_button"><v-btn @click="useLicenseNumber()" color="primary darken-1" x-large>Try State License Number Verification</v-btn></div>
                  <span>Last 4 Digits of SSN</span>
                  <div class="ma-auto" style="max-width: 220px">
                    <!-- <input type="password" maxlength="4" v-model="ssn_last_4" style="max-width:155px; margin:auto;border:1px solid #fff;letter-spacing:1em;" class="pa-4 my-4 rounded-lg text-center font-weight-bold white--text" /> -->
                    <v-otp-input
                        v-model="ssn_last_4"
                        length="4"
                        dark
                        type="password"
                        id="last4_input"
                    ></v-otp-input>
                  </div>
                </div>
                <div v-if="!getUserMeta('npn-exists') && !show_npn_exists_alert">
                  <v-btn color="secondary darken-1" @click="showConfirmLicensingStepModal = false;setVerifyNpnContinueEditing(true);refreshData(2);" class="ma-2" style="min-width:155px;">
                    Continue Editing
                  </v-btn>
                  <v-btn color="primary darken-1" @click="verifyRecruitNpn()" class="ma-2" :loading="verifying" v-if="!progress_saved" style="min-width:155px;">
                    Verify
                  </v-btn>
                  <div v-if="use_license_number" class="mt-4">
                    <v-btn text @click="use_license_number = false">
                      Back to Verification by SSN
                    </v-btn>
                  </div>
                </div>
                <v-alert
                  v-if="show_npn_exists_alert || getUserMeta('npn-exists')"
                  outlined
                  type="warning"
                  color="primary"
                  prominent
                  border="left"
                  class="mt-8 text-left"
                >
                  <div>Your NPN was identified with an affiliated partner. In order to move forward with your application, an approved release request is required. Please click the link below to fill out your Release Form. Once you've completed this step, please upload your receipt of form submission.</div>
                  <v-btn class="mt-4" color="primary" link href="https://form.jotform.com/241305600335140" target="_blank">
                    Go to Release Form
                  </v-btn>
                  <v-btn color="secondary darken-1" @click="showConfirmLicensingStepModal = false;setVerifyNpnContinueEditing(true);refreshData(2);" class="mt-4 ml-2" style="min-width:155px;">
                    Continue Editing
                  </v-btn>
                  <!-- <v-btn class="mt-4 ml-2" color="primary" outlined @click="triggerFileInput">Upload Receipt</v-btn>
                  <input type="file" ref="fileInput" @change="handleFileUpload" style="display: none;"> -->
                  <!-- <div v-if="isImpersonating">
                    <v-btn outlined color="warning" x-large class="my-4" @click="verifyRecruitNpn(true)">Staff Override</v-btn>
                  </div> -->
                </v-alert>
              </div>
              <div v-else>
                <v-btn color="secondary darken-1" @click="showConfirmLicensingStepModal = false" class="ma-2" style="min-width:155px;">
                  Continue Editing
                </v-btn>
                <v-btn color="primary darken-1" @click="saveProgress()" class="ma-2" :loading="saving_progress" v-if="!progress_saved" style="min-width:155px;">
                  Save Progress
                </v-btn>
                <v-btn v-else color="success" style="min-width:155px;" class="ma-2"><v-icon class="mr-2">mdi-check</v-icon> Saved</v-btn>
              </div>
            </v-sheet>
          </v-overlay>

        </v-row>
      </v-container>
    </v-container>
  </v-container>
  <v-container class="text-center pt-10" v-else-if="submitted">
    <h1 class="primary--text text--lighten-1">Your application is under review now.</h1>
  </v-container>
  <v-container class="text-center pt-10" v-else-if="approved">
    <h1 class="primary--text text--lighten-1">Congratulations! Your application is approved.</h1>
  </v-container>
  <v-container class="text-center pt-10" v-else-if="rejected">
    <h1 class="primary--text text--lighten-1">Sorry, your application is rejected.</h1>
  </v-container>
  <v-container class="text-center pt-10" v-else-if="unlicensed">
    <h1 class="primary--text text--lighten-1">Unlicensed account is created for you.</h1>
  </v-container>
  <v-container class="text-center pt-10" v-else-if="completed">
    <h1 class="primary--text text--lighten-1">Congratulations! Your application is completed.</h1>
  </v-container>
  <v-container class="text-center pt-10" v-else>
    <h1 class="warning--text text--lighten-1">Sorry, please contact admin.</h1>
  </v-container>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

import HelloSign from 'hellosign-embedded'

import DataTable from '@/components/base/DataTable'
import ProgressBar from '@/components/compound/ProgressBar'
import FormPage from '@/components/form/FormPage'

import { setWithExpiry } from '@/utils/storage'
import { extractObjectToArray } from '@/utils/helper'
import { narrateDate } from '@/utils/helper'
import http from '@/plugins/http'
import { ONBOARD_STATUS_TYPES, HELLOSIGN_SIGNATURE_PROCESS, APPLICATION_TYPES } from '@/utils/const'

export default {
  name: 'SymmetryOnboard',
  components: {
    ProgressBar,
    FormPage,
    DataTable,
  },
  data() {
    return {
      passedExam: null,
      preventNextButton:false,
      steps: [], // flow step array, retrieved from api
      currentStepIndex: null, // current step index
      data: {}, // step data retrieved from api
      loading: false,
      showErrors: false,

      bGettingSignRequest: false,

      showConfirmProceedModal: false,
      nextToSubmit: false,

      notes: {}, // review notes for current application
      comments: [],
      showNotesModal: false,
      noteTableHeaders: [
        { text: 'Page', value: 'page' },
        { text: 'Section', value: 'section' },
        // { text: 'Order (section)', value: 'sort' },
        { text: 'Content', value: 'content' },
        { text: 'Fixed?', value: 'fixed' },
        { text: '', value: 'actions', sortable: false },
      ],

      goToSectionRefKey: null,
      lifeInsuranceLicense: null,
      enrolled: null,
      enrolled_upload: null,
      exam_upload: null,
      hasProofOfEnrolled: null,
      hasProofOfPassingExam: null,
      showConfirmLicensingStepModal: false,
      confirmationTitle: '',
      confirmationText: '',
      passed_exam: null,
      passedExam: null,
      isEnrolled: null,
      isLicensed: null,
      saving_progress: false,
      progress_saved: false,
      status: null,
      new_history_slugs: [],
      verify_npn: false,
      ssn_last_4: '',
      verifying: false,
      npn_is_verified: false,
      verify_npn_continue_editing: false,
      licenseId: '',
      state: '',
      use_license_number: '',
      show_license_option_button: false,
      state_error: '',
      license_id_error: '',
      states: [],
      ca_privacy_notice_clicked: false,
      show_ca_privacy_notice_error: false,
      showSubmitForReviewDialog: false,
      show_npn_exists_alert: false,
      values: {},
      showAcknowledgeVectorDialog: false,
      vector_company: '',
      vector_matches: null,
      vector_acknowledged: false,
      npn_results: null,
    }
  },
  mounted() {
    http.get('/api/content_management/states')
      .then(response => {
        this.states = response.data
      })
    document.addEventListener('click', this.handleDocumentClick)
  },
  _computed: {
    ...mapGetters({
      isSignatureSigned: 'auth/isSignatureSigned',
      optionsStates: 'content_management/states',
      // getStateList: 'content_management/getStateList',
    }),
    isImpersonating() {
      return this.$store.state.auth.user.impersonated_by
    },
    onboardStatus() {
      return this.$store.state.auth.user.status.slug
    },
    canEdit() {
      return this.$store.state.auth.user.status.form_page_id||this.$store.state.auth.user.status.slug=='revision-ho'||this.$store.state.auth.user.impersonated_by
    },
    submitted() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.SUBMITTED_AO||this.onboardStatus===ONBOARD_STATUS_TYPES.SUBMITTED_HO
    },
    approved() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.AO_APPROVED||this.onboardStatus===ONBOARD_STATUS_TYPES.HO_APPROVED
    },
    rejected() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.REJECTED_AO||this.onboardStatus===ONBOARD_STATUS_TYPES.REJECTED_HO
    },
    unlicensed() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.UNLICENSED
    },
    completed() {
      return this.onboardStatus===ONBOARD_STATUS_TYPES.ASSIGNED
    },
    preventNext() {
      return this.preventNext===ONBOARD_PREVENT_NEXT
    },
    totalSteps() {
      return this.steps.length
    },
    currentStepTitle() {
      return this.steps[this.currentStepIndex]&&this.steps[this.currentStepIndex].label
    },
    currentStepKey() {
      return this.steps[this.currentStepIndex]&&this.steps[this.currentStepIndex].step_ident
    },
    userHistorySlugs() {
      let history_slugs = [];
      if (this.$store.state.auth.user && this.$store.state.auth.user.status_history) {
        this.$store.state.auth.user.status_history.forEach(item => {
          if (item.user_status && item.user_status.slug) {
            history_slugs.push(item.user_status.slug);
          }
        });
      }
      return [...history_slugs, ...this.new_history_slugs];
    },
    showSignAgreement() {
      if([APPLICATION_TYPES.PRINCIPAL, APPLICATION_TYPES.B2B_AGENT].includes(this.$store.state.auth.user.type))
        return this.currentStepIndex==0 &&!this.isSignatureSigned
      return this.currentStepIndex==1 && !this.isSignatureSigned
    },
    showVerifyNpnButton() {
      return this.currentStepIndex == 2
        && this.$store.state.auth.user.type != APPLICATION_TYPES.PRINCIPAL
        && this.isLicensed == 'YES'
        && !this.$store.state.auth.user.npn
        && !this.npn_is_verified
    },
    showNextButton() {
      //!showSignAgreement && !preventNextButton && ((currentStepIndex < totalSteps - 1 && currentStepIndex != 1) || (isSignatureSigned && currentStepIndex == 1))
      if(this.preventNextButton) return false
      // if(this.currentStepIndex < this.totalSteps - 1 && !this.showSignAgreement && !this.showSubmitForReviewButton) return true
      return this.currentStepIndex < this.totalSteps - 1 &&!this.showSignAgreement && !this.showSubmitForReviewButton
    },
    showSubmitForReviewButton() {
      return this.isSignatureSigned && this.currentStepIndex == this.totalSteps - 1
    }
  },
  get computed() {
    return this._computed
  },
  set computed(value) {
    this._computed=value
  },
  created() {
    this.loading = true
    this.getOnboardSteps()
      .then(response => {
        this.steps = response.data
        this.getCurrentStep()
          .then(res => {
            if (res && res.data && res.data.current_page && res.data.current_page.sort)
              this.currentStepIndex = res.data.current_page.sort - 1
          })
          .finally(() => {
            if (!this.currentStepIndex) this.currentStepIndex = 0
            this.loading = false
          })
      })
      .catch(err => {
        console.error(err)
        this.setSnackbar({
          status: true,
          text: 'Failed to get flow steps!',
          color: 'warning',
        })
      })

    this.getApplicationReviewNotes(this.$store.state.auth.user.id)
      .then(res => {
        if (res && res.data && typeof res.data === 'object') {
          const { comments, notes } = res.data;
          this.comments = comments || [];
          notes.map(
            note =>
              (this.notes[`${note.form_section_id}-${note.form_section_sort}`] = {
                id: note.id,
                page: note.form_section.form_page.label,
                section: note.form_section.label,
                sectionId: note.form_section_id,
                sort: note.form_section_sort,
                fixed: note.fixed,
                content: note.content,
              }),
          )
        }
      })
      .catch(() => (this.notes = {}))
  },

  watch: {
    currentStepIndex(val) {
      this.loadStep(val)
      return
    },
  },
  methods: {
    ...mapActions({
      getUser: 'auth/getUser',
      setUser: 'auth/setUser',
      setSnackbar: 'snackbar/set',
      getOnboardSteps: 'application/getOnboardSteps',
      getCurrentStep: 'application/getCurrentStep',
      updateApplication: 'application/updateApplication',
      getVectorResults: 'application/getVectorResults',
      getViewForm: 'application/getViewForm',
      getHelloSignView: 'hellosign/view',
      getApplicationReviewNotes: 'review/getNotes',
      resolveApplicationReviewNote: 'review/resolveNote',
      verifyNpn: 'application/verifyNpn',
    }),
    narrateDate,
    useLicenseNumber() {
      this.confirmationText = "<span style=\"font-size:1.5em\">Please verify your NPN by entering your license number and state.</span>"
      this.use_license_number = true
    },
    setVerifyNpnContinueEditing(val) {
      this.verify_npn_continue_editing = val
    },
    saveProgress() {
      var g = this
      g.saving_progress = true
      this.updateApplication({
        id: this.$store.state.auth.user.id,
        status: this.status,
      }).then(() => {
        g.saving_progress = false
        g.setSnackbar({
          status: true,
          text: 'Progress saved!',
          color: 'success',
        })
        this.goTo('my_task')
      }).catch(err => {
        g.setSnackbar({
          status: true,
          text: err.response?.data?.message || 'Failed to save. Please contact support.',
          color: 'warning',
        })
        g.saving_progress = false
      })
    },
    reloadFormPage() {
      this.loadStep(this.currentStepIndex)
    },
    loadStep(val) {
      if (this.canEdit && this.steps[val]) {
        this.loading = true
        this.showConfirmProceedModal = false
        this.showErrors = false

        this.getViewForm({
          step: this.steps[val].step_ident,
        })
          .then(res => {
            if (res && res.data) {
              this.data = res.data
              this.passed_exam = this.data.form_sections.find(o => o.label === 'Have you passed your state exam?');
              this.lifeInsuranceLicense = this.data.form_sections.find(o => o.label === 'Do you have a resident life insurance license?');
              this.enrolled = this.data.form_sections.find(o => o.label === 'Are you enrolled in a pre-licensing course?');
              this.enrolled_upload = this.data.form_sections.find(o => o.label === 'Please upload proof of having enrolled in a pre-licensing course')
              this.exam_upload = this.data.form_sections.find(o => o.label === 'Please upload proof of having successfully passed your state exam')

              this.antiMoneyLaundering = this.data.form_sections.find(o => o.label === 'Have you previously taken an AML training course?');

              setTimeout(() => {
                if (this.$refs.page) this.$refs.page.goToSection(this.goToSectionRefKey)
                this.goToSectionRefKey = null
              }, 100)
            }
          })
          .catch(err => {
            if(err.response && err.response.status == 403) {
              this.getUser()
            }
          })
          .finally(() => {
            this.loading = false
            window.scrollTo({
              top: 0,
              behavior: 'smooth',
            })
            //adding slug so the interactive progress bar will work for steps hit during this session, without reloading the page
            this.new_history_slugs.push(this.steps[val].step_ident)
          })
      } else {
        this.data = null
      }
    },
    refreshData(val) {
      this.getViewForm({
        step: this.steps[val].step_ident,
      })
      .then(res => {
        if (res && res.data) {
          this.data = res.data

          // const arraySelectFieldIds = []
          this.data.sections.map(section => {
            if (!section.fields || !Array.isArray(section.fields)) return
            this.values = section.fields.reduce((obj, field) => {
              if (field.value)
                return {
                  ...obj,
                  [field.id]: extractObjectToArray(field.value),
                }
              return obj
            }, this.values)
          })
          this.setPreventNext(this.values)
        }
      })
    },
    setPreventNext(pageValues){
      console.log("setPreventNext", this.currentStepIndex, this.$store.state.auth.user.type)
      //pageValues is an object of key:value pairs of the selected answers to questions ( field_id: answer value )
      //i.e. [236f47ca-72a5-41dd-839b-a22a441031c1:"NO"]

      this.verify_npn = false

      //are we on the licensing step? (step index 2) and not the PRINCIPAL app
      if(this.currentStepIndex == 2 && this.$store.state.auth.user.type != APPLICATION_TYPES.PRINCIPAL){
        console.log("0. this.currentStepIndex == 2 && this.$store.state.auth.user.type != APPLICATION_TYPES.PRINCIPAL")
        this.isLicensed = this.lifeInsuranceLicense && this.lifeInsuranceLicense.fields ? pageValues[this.lifeInsuranceLicense.fields[0].id] : null
        this.passedExam = this.passed_exam && this.passed_exam.fields ? pageValues[this.passed_exam.fields[0].id] : null
        this.isEnrolled = this.enrolled && this.enrolled.fields ? pageValues[this.enrolled.fields[0].id] : null
        this.hasProofOfEnrolled = this.enrolled_upload && this.enrolled_upload.fields ? pageValues[this.enrolled_upload.fields[0].id] : null
        this.hasProofOfPassingExam = this.exam_upload && this.exam_upload.fields ? pageValues[this.exam_upload.fields[0].id] : null

        console.log("this.isLicensed", this.isLicensed, this.$store.state.auth.user.npn, !this.$store.state.auth.user.npn)
        //is licensed - show next button
        if(this.isLicensed == 'YES' && (this.$store.state.auth.user.npn != '' || this.npn_is_verified)) {
          console.log("1. isLicensed == 'YES' && (this.$store.state.auth.user.npn != '' || this.npn_is_verified)")
          return this.preventNextButton = false
        }

        //licensed but no npn - need to verify NPN
        if(this.isLicensed == 'YES' && !this.$store.state.auth.user.npn && !this.npn_is_verified && !this.verify_npn_continue_editing) {
          console.log("2. this.isLicensed == 'YES' && this.$store.state.auth.user.npn == '' && !this.npn_is_verified && !this.verify_npn_continue_editing")
          if(!this.data.is_citizen) {
            this.useLicenseNumber()
          } else {
            this.confirmationTitle = "Verify License Status"
            this.confirmationText = "<span style=\"font-size:1.5em\">To help speed up your application, please verify your NPN by entering the last four of your SSN.</span>"
            this.confirmationText += "<br/>We will use your legal first and last name and last four of your SSN to lookup your National Produder Number (NPN) in the National Producer Insurance Registry (NIPR). If found, you'll be able to continue as a fully licensed agent."
          }
          this.verify_npn = true
          this.showConfirmLicensingStepModal = true
          return this.preventNextButton = true
        }

        //not licensed but passed exam - show next button
        if(this.isLicensed == 'NO' && this.passedExam == 'YES' && this.hasProofOfPassingExam) {
          return this.preventNextButton = false
        }

        //not licensed and not enrolled in a course - show dialog - hide next button
        if(this.isLicensed == 'NO' && this.passedExam == 'NO' && this.isEnrolled == 'NO') {
          console.log("3. this.isLicensed == 'NO' && this.passedExam == 'NO' && this.isEnrolled == 'NO'")
          this.confirmationTitle = "Enroll in a Course"
          this.confirmationText = "<span style=\"font-size:1.5em\">It's time to enroll in a pre-licensing course! Please reach out to your Agency Owner.</span><br />Click the <strong>Save Progress</strong> button below to acknowledge and save your application progress up to this point."
          this.confirmationText += "<br/>Once you're enrolled in a course, come back and click the <strong>Continue Editing</strong> button to upload a copy of your enrollement information."
          this.status = ONBOARD_STATUS_TYPES.UNENROLLED
          this.showConfirmLicensingStepModal = true
          return this.preventNextButton = true
        }

        //unlicensed and enrolled in a course - show dialog - hide next button
        if(this.isLicensed == 'NO' && this.passedExam == 'NO' && this.isEnrolled == 'YES' && this.hasProofOfEnrolled) {
          console.log("4. this.isLicensed == 'NO' && this.passedExam == 'NO' && this.isEnrolled == 'YES' && this.hasProofOfEnrolled")
          this.confirmationTitle = "Pass Your Exam"
          this.confirmationText = "<p style=\"font-size:1.5em\">Once you have completed your pre-licensing course, your next step is to register and complete your state exam.</p>"
          this.confirmationText += "<p><a href=\"https://www.examfx.com/insurance-prelicensing-training/view-insurance-state-requirements\" target=\"_blank\">Click on the link for requirements in your state</a></p>"
          this.confirmationText += "<p>Once you've passed your state exam, please come back to this page and complete your application."
          this.confirmationText += "<br />Click the <strong>Save Progress</strong> button below to acknowledge and save your application progress up to this point.</p>"
          this.status = ONBOARD_STATUS_TYPES.ENROLLED
          this.showConfirmLicensingStepModal = true
          return this.preventNextButton = true
        }

        //not licensed and hasn't passed exam, disable the 'next' button by default
        return this.preventNextButton = true

      } else if(
        this.antiMoneyLaundering &&
        typeof this.antiMoneyLaundering.fields[0].id !== 'undefined' &&
        typeof pageValues[this.antiMoneyLaundering.fields[0].id] !== 'undefined' &&
        pageValues[this.antiMoneyLaundering.fields[0].id] == 'NO' &&
        this.currentStepIndex == 5
      ){
        // console.log('prevent forward: anti-money laundering')
        this.preventNextButton = true
      }else{
        this.preventNextButton = false
      }
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    askForNext(toSubmit = false) {

      //make sure the user has clicked on the CA Privacy Notice, if it exists
      const link = document.querySelector('a[href="https://hq.quility.com/api/public/document/179846/view/quility-ca-employee-contractors-privacy-notice-2023-01-19"]')
      if( link
        && ! this.ca_privacy_notice_clicked
        && link.parentElement.parentElement.parentElement.parentElement.parentElement.getAttribute('style').indexOf('display: none') == -1
      ) {
        this.show_ca_privacy_notice_error = true
        return
      }
      this.show_ca_privacy_notice_error = false

      this.nextToSubmit = toSubmit;

      const errorExist = this.$refs.page.hasErrors()
      if (errorExist) {
        // [STEP-VALIDATION]
        if(process.env.MIX_APP_ENV !== 'production') {
          // Option1: deactivated on production
          //  If there is validation errors, ask to continue with errors
          this.showConfirmProceedModal = true
        } else {
          // Option2: required to pass validation
          //  If there is validation errors, you can't move on
          this.showErrors = true
          this.$root.$emit('check_validation')
          this.setSnackbar({
            status: true,
            text: 'Sorry, we can\'t move you forward now.',
            color: 'warning',
          })
        }
      } else {
        this.next()
      }
    },
    next() {
      console.log("next", !this.nextToSubmit && this.currentStepIndex < this.steps.length - 1)
      if (!this.nextToSubmit && this.currentStepIndex < this.steps.length - 1) this.currentStepIndex = this.currentStepIndex + 1
      else {
        // Check review notes are all fixed before re-submittion
        if (this.countTotalNotes() != this.countTotalNotesFixed()) {

          this.setSnackbar({
            status: true,
            text: 'Sorry, you still have some notes left to fix!',
            color: 'warning',
          })
          this.showConfirmProceedModal = false
        } else {
          console.log("do submit")
          this.submit()
        }
      }
    },
    previous() {
      this.currentStepIndex = this.currentStepIndex > 0 ? this.currentStepIndex - 1 : this.currentStepIndex
      this.preventNextButton = false
      this.setVerifyNpnContinueEditing(false)
    },

    countTotalNotes() {
      return this.notes ? Object.keys(this.notes).length : 0
    },
    countTotalNotesFixed() {
      let countTotalNotesFixed = this.notes ? Object.keys(this.notes).filter(n => this.notes[n].fixed).length : 0
      if(this.countTotalNotes() > 0 && this.countTotalNotes() == countTotalNotesFixed)
        this.showSubmitForReviewDialog = true
      else
        this.showSubmitForReviewDialog = false
      return countTotalNotesFixed
    },

    helloSign() {
      this.bGettingSignRequest = true
      this.getHelloSignView()
        .then(res => {
          if (res && res.data) {
            const { signUrl, clientId } = res.data

            if (signUrl && clientId) {
              const client = new HelloSign()

              client.open(signUrl, {
                clientId,
                skipDomainVerification: (process.env.MIX_HELLOSIGN_SKIP_DOMAIN_VERIFICATION === 'true') ? true : false,
                locale: HelloSign.locales.EN_US,
                redirectTo: '',
                // container: document.getElementById('hellosign-area')
              })

              client.on('finish', () => {
                // console.log('Signing process is finished!')
                client.close()

                // Save local storage flag as for hellosign complete
                // ttl: 30 seconds
                setWithExpiry(HELLOSIGN_SIGNATURE_PROCESS, true, 30 * 1000);

                this.setUser();

                this.askForNext(false)
              })

              client.on('message', ({ type, payload }) => {
                console.log('Message recevied: ', type, payload)
              })
            }
          }
        })
        .finally(() => {
          this.bGettingSignRequest = false
        })
    },

    checkVector() {
      //Vector AND NPN check for principals
      if(!this.vector_acknowledged && this.$store.state.auth.user.type == APPLICATION_TYPES.PRINCIPAL) {
        return this.getVectorResults(this.$store.state.auth.user.id)
          .then(res => {
            this.npn_results = res.data?.npn

            if(res.data?.vector.EntitySearchResult?.Entities && res.data?.vector?.EntitySearchResult?.Entities['EntityReports.EntitySearchResult']?.Matches > 0) {
              console.log("vectors found")
              let vector = res.data?.vector?.EntitySearchResult?.Entities['EntityReports.EntitySearchResult']
              this.vector_matches = vector.Matches
              this.vector_company = vector.Reports['EntityReports.Report'].ReportingCompany
              this.loading = false
              this.showAcknowledgeVectorDialog = true
              return false
            } else {
              if(res.data?.npn?.exists) {
                this.showAcknowledgeVectorDialog = true
                return false
              } else {
                this.showAcknowledgeVectorDialog = false
                return true
              }
            }
          })
          .catch(err => {
            console.log("error", err)
            return true
          })
      } else {
        return true
      }
    },
    async submit() {
      console.log("submit started")
      this.loading = true
      this.showConfirmProceedModal = false

      let vector = await this.checkVector()
      // if(!vector) return

      this.updateApplication({
        id: this.$store.state.auth.user.id,
        status: ONBOARD_STATUS_TYPES.SUBMITTED_AO,
      })
        .then(() => {
          this.getUser()
        })
        .then(() => {
          this.goToPath(`/app/my_task`)
        })
        .catch((err) => {
          this.setSnackbar({
            status: true,
            text: err.response?.data?.message || 'Failed to submit your application. Please contact support.',
            color: 'warning',
          })
        })
        .finally(() => (this.loading = false))
    },
    goToSection(item) {
      this.showNotesModal = false

      const newStep = this.steps.findIndex(s => s.label === item.page)
      if (newStep === this.currentStepIndex) {
        this.$refs.page.goToSection(`section_${item.sectionId}_${item.sort}`)
      } else if (newStep !== -1) {
        this.showNotesModal = false
        this.currentStepIndex = newStep

        this.goToSectionRefKey = `section_${item.sectionId}_${item.sort}`
      }
    },
    resolveNote(item, fixed) {
      let g = this
      this.resolveApplicationReviewNote({ appId: item.id, fixed })
        .then(() => {
          item.fixed = fixed
          g.setSnackbar({
            status: true,
            text: 'Resolving note success!',
            color: 'success',
          })
        })
        .catch(() =>
          g.setSnackbar({
            status: true,
            text: 'Resolving note failed!',
            color: 'warning',
          }),
        )
    },
    inCurrentStep(item) {
        const itemIndex = this.steps.findIndex(s => s.label === item.page)
        return itemIndex === this.currentStepIndex
    },
    progressBarGoToStep(goToStepIndex){
      this.currentStepIndex = goToStepIndex
    },

    verifyRecruitNpn(staff_override = false) {
      this.license_id_error = '';
      this.state_error = '';
      let form_error = false
      if(this.use_license_number && this.licenseId == '') {
        this.license_id_error = 'License Number is required'
        form_error = true
      }
      if(this.use_license_number && this.state == '') {
        this.state_error = 'State is required'
        form_error = true
      }
      if(form_error)
        return

      this.verifying = true
      this.verifyNpn({ first_name: "", last_name: "", ssn_last_4: this.ssn_last_4, state: this.state, licenseId: this.licenseId, staff_override })
        .then(response => {
          if(response.data?.npn > 0) {
            this.setSnackbar({
              status: true,
              text: 'Your NPN has been verified!',
              color: 'success',
            })
            this.verify_npn = false
            this.npn_is_verified = true
            this.showConfirmLicensingStepModal = false
            this.preventNextButton = false
          } else {
            if(response.data?.exists || response.data?.npn?.exists) {
              this.npn_is_verified = true
              this.show_npn_exists_alert = true
            } else if(response.data?.message == 'No entity found' && !this.use_license_number) {
              this.confirmationText = `<span style="font-size:1.5em;font-weight:bold;" class="error--text">NPN not found. Please check your entry and try again, or try the State License Number verification option.</span>`
              this.show_license_option_button = true
            } else if(response.data?.message == 'No entity found' && this.use_license_number) {
              this.confirmationText = `<span style="font-size:1.5em;font-weight:bold;" class="error--text">NPN not found. Please check your entry and try again.</span>`
            } else if(response.data.indexOf('multiple producers') > 0) {
              this.confirmationText = `<span style="font-size:1.5em;font-weight:bold;" class="error--text">NPN couldn't be matched via last four of SSN. Let's try verifying with your state license number.</span>`
              this.use_license_number = true
            } else {
              this.confirmationText = `<span style="font-size:1.5em;font-weight:bold;" class="error--text">${response.data.message}</span>`
            }
            this.setSnackbar({
              status: true,
              text: 'Your NPN could not be verified.',
              color: 'warning',
            })
          }
        })
        .catch((err) => {
          this.setSnackbar({
            status: true,
            text: 'An error occurred while verifying the NPN!',
            color: 'warning',
          })
          console.log("some error", err)
        }).finally(() => {
          this.verifying = false
        })
    },
    handleDocumentClick(event) {
      const target = event.target
      if (target.matches('a[href="https://hq.quility.com/api/public/document/179846/view/quility-ca-employee-contractors-privacy-notice-2023-01-19"]')) {
        // the clicked element is the link of interest
        this.ca_privacy_notice_clicked = true
        this.show_ca_privacy_notice_error = false
      }
    },
    getUserMeta(key) {
      const metaArray = this.$store.state.auth.user.meta;
      const metaItem = metaArray.find(item => item.key === key);
      return metaItem ? metaItem.value : null;
    },
  },
}
</script>

<style scope>
.v-otp-input.theme--dark input {
  color: #000 !important;
  font-size: 36px;
}
</style>
