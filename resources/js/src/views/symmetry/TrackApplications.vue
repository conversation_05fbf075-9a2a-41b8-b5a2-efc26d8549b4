<template>
  <v-container class="d-flex flex-column text-center pa-8" fluid>
    <v-row class="mb-2">
      <v-col class="text-left">
        <text-input label="Search Entries" v-model="search.text" :default="search.text" :key="search_text_key" />
        <v-btn x-small text class="mt-2" @click="resetSearch()">Reset Search</v-btn>
      </v-col>
      <v-col class="text-left" cols="4">
        <select-box label="Onboarding Status" :items="itemsApplicationStatus" v-model="search.status" :default="search.status" />
      </v-col>
      <v-col v-if="groups.length > 0" class="text-left" cols="4">
        <select-box label="Groups" :items="groups" v-model="search.group_id" :default="search.group_id" itemText="group_name" itemValue="id" />
      </v-col>
      
    </v-row>
    <v-data-table
      :headers="headers"
      :items="filtered_items"
      :items-per-page="10"
      :loading="loading"
      :options.sync="options"
    >
        <template v-slot:top>
          <v-row class="mb-6">
            <v-col class="pt-6">
              <v-switch :disabled="loading" color="primary" v-model="use_baseshop" :label="level_label"></v-switch>
            </v-col>
            <v-col class="pt-6">
              <v-select 
                label="Date Field"
                v-model="search.date_field"
                dense
                :items="filter_date_options">
              </v-select>
            </v-col>
            <v-col>
              <v-menu
                ref="start_date_menu"
                v-model="start_date_menu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="search.start_date"
                    label="Start Date"
                    prepend-icon="mdi-calendar"
                    v-bind="attrs"
                    v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="search.start_date"
                  no-title
                  scrollable
                  @input="start_date_menu = false"
                >
                </v-date-picker>
              </v-menu>
            </v-col>
            <v-col>
              <v-menu
                ref="end_date_menu"
                v-model="end_date_menu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="search.end_date"
                    label="End Date"
                    prepend-icon="mdi-calendar"
                    v-bind="attrs"
                    v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="search.end_date"
                  no-title
                  scrollable
                  @input="end_date_menu = false"
                >
                </v-date-picker>
              </v-menu>
            </v-col>
            <v-col class="text-right pt-6">
              <v-btn color="grey" text @click="downloadApps" :loading="downloading">Download</v-btn>
            </v-col>
          </v-row>
        </template>
      <template v-slot:[`item.agent`]="{ item }">
        <a class="pr-2 text--darken-3 text--secondary" @click="goToAction(item, true)">
          {{ item.agent }}
        </a>
      </template>
      <template v-slot:[`item.status`]="{ item }">
        <span v-if="item.status && item.status.indexOf('Error') > -1">
          <span v-if="!hasUserRole(['SalesRep', 'AgencyOwner'])" class="error--text font-weight-black">
            <v-icon color="error" class="">mdi-alert</v-icon>
            {{ item.status }}
          </span>
          <span v-else>
            Approved
          </span>
        </span>
        <span v-else>{{ item.status }}</span>
        <h5 class="text-h5 info--text" v-if="activeReviewer(item.id)" v-bind:key="tickReviewStatus">
          {{ activeReviewer(item.id) }} is reviewing...
        </h5>
      </template>
      <template v-slot:[`item.applied_on`]="{ item }">
        {{ narrateDate(item.applied_on) }}
      </template>
      <template v-slot:[`item.created_at`]="{ item }">
        {{ narrateDate(item.created_at, false) }}
      </template>
      <template v-slot:[`item.updated_at`]="{ item }">
        {{ narrateDate(item.updated_at, false) }}
      </template>
      <template v-slot:[`item.completed_on`]="{ item }">
        {{ narrateDate(item.completed_on) }}
      </template>
      <template v-slot:[`item.homeoffice_submission_on`]="{ item }">
        {{ narrateDate(item.homeoffice_submission_on) }}
      </template>
      <template v-slot:[`item.actions`]="{ item }">
          <v-btn
            v-show="hasUserRole('SuperAdmin') || (hasUserRole('AgencyOwner') && item.status_slug == onboard_status_types.SUBMITTED_AO && !item.approved_by_ao)"
            @click="goToAction(item)"
            :color="item.last_revision_requested_on ? 'error' : 'primary'"
            small
          >
            Review
          </v-btn>

          <v-btn v-if="showReminderBtn(item)" small
            @click="sendReminderEmail(item)"
            :loading="sendingReminder(item.id)"
          >Send Reminder</v-btn>
      </template>
      <template v-slot:no-data>
        <h4>No applications</h4>
      </template>
    </v-data-table>
    <p class="text-h5 mt-10 pt-10">
      Please note that applications are saved online for 60 days. Pending applications older than 60 days will need to
      be resubmitted.
    </p>
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import { narrateDate } from '@/utils/helper'
import { ONBOARD_STATUS_TYPES, USER_ROLES_TYPES } from '@/utils/const'
import TextInput from '@/components/base/TextInput'
import SelectBox from '@/components/base/SelectBox'
import LazyLoading from '@/mixins/LazyLoading'

export default {
  components: {
    // DataTable,
    TextInput,
    SelectBox,
  },
  mixins: [LazyLoading],
  data() {
    return {
      headers: [
        { text: '', value:'license_status' },
        { text: 'NEW AGENT', value: 'agent', class: 'app-table-col' },
        { text: 'RECRUITER', value: 'recruiter', class: 'app-table-col' },
        {
          text: 'DIRECT UPLINE',
          value: 'direct_upline',
          class: 'app-table-col',
        },
        { text: 'AGENCY OWNER', value: 'agency_owner', class: 'app-table-col'},
        { text: 'SUBMITTED DATE', value: 'applied_on', class: 'app-table-col' },
        { text: 'DATE CREATED', value: 'created_at', class: 'app-table-col'},
        { text: 'DATE UPDATED', value: 'updated_at', class: 'app-table-col'},
        { text: 'DATE SUBMITTED TO HOME OFFICE', value: 'homeoffice_submission_on', class: 'app-table-col' },
        { text: 'ONBOARDING STATUS', value: 'status', class: 'app-table-col' },
        {
          text: 'ACTIONS',
          value: 'actions',
          class: 'app-table-col',
          cellClass: 'text-no-wrap',
          sortable: false
        },
      ],
      items: [],
      filtered_items: [],
      total: 0,
      loading: false,

      itemsApplicationStatus: [],

      options: {}, // option for pagination
      search: {
        // option for search
        text: '',
        status: '',
        start_date: '',
        end_date: '',
        date_field: 'updated_at',
        group_id: '',
      },

      //onboarding status types
      onboard_status_types: ONBOARD_STATUS_TYPES,
      use_baseshop: false,
      downloading: false,
      search_text_key: 'search_text_key',
      sending_reminder: {},
      reminders_sent: [],
      start_date_menu: false,
      start_date: "",
      end_date_menu: false,
      end_date: "",

      filter_date_options: [
        { text: "Date Updated", value: "updated_at" },
        { text: "Date Created", value: "created_at" },
        { text: "Date Submitted to AO", value: "applied_on" },
        { text: "Date Submitted to HO", value: "homeoffice_submission_on"}
      ],

      groups: [],
      loading_groups: false,
    }
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
      activeReviewer: 'review/activeReviewer',
      tickReviewStatus: 'review/tick',
    }),

    level_label() {
        return this.use_baseshop ? 'Baseshop' : 'Total Agency'
    },
  },
  watch: {
    options: {
      handler() {
         window.localStorage.setItem("track_applications_options", JSON.stringify(this.options))
        this.newLazyLoadingRequest(this.filterItems)
      },
      deep: true,
    },
    search: {
      handler() {
        this.newLazyLoadingRequest(this.filterItems)
      },
      deep: true,
    },
    use_baseshop: {
      handler() {
        this.newLazyLoadingRequest(this.getDataFromApi)
      },
      deep: true,
    },
  },
  mounted() {
    this.getApplicationStatusOptions()
    this.getDataFromApi()
    // this.getUserInvitesGroups()
    this.getInvitesGroups()
    this.options = JSON.parse(window.localStorage.getItem('track_applications_options')) ?? {itemsPerPage: -1}
    let search = window.localStorage.getItem("track_applications_search")
    if(search) {
      let { text, status, group_id } = JSON.parse(search)
      this.search = { text, status, group_id }
      this.search_text_key = Math.random()
    }
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getApplications: 'application/getApplications',
      getStatusOptions: 'application/getStatusOptions',
      sendReminder: 'application/sendReminder',
      getUserInvitesGroups: 'user_invite/getUserInvitesGroups',
    }),
    narrateDate,
    resetSearch() {
      window.localStorage.setItem("track_applications_search", JSON.stringify({ text: '', status: '' }))
      this.search = { text: '', status: '', start_date: '', end_date: '', group_id: '' }
      this.options = {}
      this.search_text_key = Math.random()
    },
    filterItems () {
      window.localStorage.setItem("track_applications_search", JSON.stringify(this.search))
      this.filtered_items = []
      // let filtered_items = []
      this.filtered_items = this.items.filter(item => {
        let found_text = true
        let found_status = true
        let found_start_date = true
        let found_end_date = true
        let found_group_id = true

        if(this.search.text != '') {
          found_text = false
          if(item.agent.toLowerCase().indexOf(this.search.text.toLowerCase()) > -1)
            found_text = true
          else if (item.agency_owner?.toLowerCase().indexOf(this.search.text.toLowerCase()) > -1)
            found_text = true
          else if (item.recruiter?.toLowerCase().indexOf(this.search.text.toLowerCase()) > -1)
            found_text = true
          else if (item.direct_upline?.toLowerCase().indexOf(this.search.text.toLowerCase()) > -1)
            found_text = true
        }
        if(this.search.status != ""){
          found_status = false
          if(this.search.status == item.status_slug)
            found_status = true
          else if(this.search.status == 'ao-approved' && item.approved_by_ao)
            found_status = true
          else if(this.search.status == 'in-progress' && item.status.indexOf('Application:') > -1)
            found_status = true
          else if(this.search.status == 'stale' && item.application_age > 30 && item.status.indexOf('Application:') > -1)
            found_status = true
        }

        if(this.search.start_date) {
          found_start_date = false
          if(this.search.start_date <= item[this.search.date_field])
            found_start_date = true
        }

        if(this.search.end_date) {
          found_end_date = false
          if(this.search.end_date >= item[this.search.date_field])
            found_end_date = true
        }

        if(this.search.group_id) {
          found_group_id = false
          if(item.invitation.group_id == this.search.group_id)
            found_group_id = true
        }
        return found_text && found_status && found_start_date && found_end_date && found_group_id
      })
    },
    getInvitesGroups() {
      this.loading_groups = true
      this.getUserInvitesGroups()
        .then(res => {
          if(!res.data || !res.data['groups'])
            throw 'invalid invitation data'
          this.groups = res.data.groups
        })
        .catch(err => {
          console.log("group invitations error:", err)
        })
        .finally(() => {
          this.loading_groups = false
        })
    },
    goToPath(path) {
      this.$router.push({ path })
    },
    goTo(route) {
      this.$router.push({ name: route })
    },
    goToAction(item, view = false) {
      if (view) this.goToPath('/app/application/track/' + item.id)
      else if (this.canReview(item)) this.goToPath('/app/application/review/' + item.id)
      else {
        this.setSnackbar({
          status: true,
          text: 'Sorry, you cannot review this application now.',
          color: 'warning',
        })
      }
    },
    canReview(item) {
      return (
        this.hasUserPermission('review applications') &&
        ((this.hasUserRole(USER_ROLES_TYPES.AGENCY_OWNER) && item.status_slug === ONBOARD_STATUS_TYPES.SUBMITTED_AO) ||
         (this.hasUserRole([USER_ROLES_TYPES.SUPER_ADMIN, USER_ROLES_TYPES.STAFF]) && item.approved_by_ao)
        ) &&
        !this.activeReviewer(item.id)
      )
    },
    getDataFromApi() {
      this.loading = true

      let options = {sortBy:[]}
      let search = {}
      options.itemsPerPage = -1

      this.getApplications(
        new URLSearchParams({
          ...options,
          ...search,
          'use_baseshop': this.use_baseshop
        }).toString(),
      )
        .then(res => {
          if (res && res.data) {
            this.items = res.data.items
            this.total = res.data.total
            this.filterItems()
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    getApplicationStatusOptions() {
      this.getStatusOptions().then(response => {
        this.itemsApplicationStatus = response.data
      })
    },
    downloadApps() {
      this.downloadCsv(this.filtered_items)
      return

      // this.downloading = true
      // let options = {...this.options}
      // options.itemsPerPage = -1
      // this.getApplications(
      //   new URLSearchParams({
      //     ...options,
      //     ...this.search,
      //     'use_baseshop': this.use_baseshop
      //   }).toString(),
      // )
      //   .then(res => {
      //     if (res && res.data) {
      //       this.downloadCsv(res.data.items)
      //     }
      //   })
      //   .finally(() => {
      //     this.downloading = false
      //   })
    },
    downloadCsv: function(data) {
        const lines = []
        
        let headers = this.the_headers()
        let phone_header = { text: 'PHONE', value: 'phone' }
        headers.splice(2, 0, phone_header)
        

        //array of data table fields for csv header row
        const fields = headers.map(header => {
            return header.text != '' ? header.text : "ID"
        })
        //build the string and add to lines array
        lines.push(`"`+fields.join(`","`)+`"`)
        //loop through carrier records and build the csv text line
        data.map(application => {
            //array of carrier field values based on fields defined by data table
            let values = headers.map(header => {
                switch (header.value) {
                  case 'applied_on':
                  case 'homeoffice_submission_on':
                    return narrateDate(application[header.value])
                  case 'is_fully_licensed':
                  case 'has_passed_exam':
                    return application[header.value] == true ? 'Yes' : 'No'
                  default:
                    return application[header.value]
                }
            })
            //build the string and add to lines array
            lines.push(`"`+values.join(`","`)+`"`)
        })

        //build all rows of csv by joining lines array
        let txt = lines.join("\n")

        //generate the download
        var element = document.createElement('a')
        element.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(txt))
        element.setAttribute('download', "Applications.csv")
        element.style.display = 'none'
        document.body.appendChild(element)
        element.click()
        document.body.removeChild(element)

    },
    the_headers: function() {
        return [{
                text: 'Name',
                value: 'agent',
                align: "left"
            }, {
                text: 'Email',
                value: 'email',
                align: "left"
            }, {
                text: 'Upline',
                value: 'direct_upline',
                align: "left"
            }, {
                text: 'Agency Owner',
                value: 'agency_owner',
                align: "left"
            }, {
                text: 'Date Submitted',
                value: 'applied_on',
                align: "left"
            }, {
                text: 'Submitted to Home Office',
                value: 'homeoffice_submission_on',
                align: "left"
            }, {
                text: 'Onboarding Status',
                value: 'status',
                align: "left"
            }, {
                text: 'Fully Licensed',
                value: 'is_fully_licensed',
                align: "left"
            }, {
                text: 'Passed Exam',
                value: 'has_passed_exam',
                align: "left"
            },
        ]
    },
    sendReminderEmail: function(user) {
      this.$set(this.sending_reminder, user.id, true)
      
      let g = this
      this.sendReminder(user.id)
      .then(response => {
        let data = response.data
        if(data.error) {
          this.setSnackbar({
            status: true,
            text: data.error,
            color: 'error',
          })
          
        } else {
          this.setSnackbar({
            status: true,
            text: 'Reminder sent.',
            color: 'success',
          })
          g.reminders_sent.push(user.id)
        }
        g.$set(this.sending_reminder, user.id, false)
      })
    },
    sendingReminder: function(user_id) {
      return this.sending_reminder[user_id] ?? false
    },
    showReminderBtn: function(user) {
      let show = user.reminders != 2 
        && !this.reminders_sent.includes(user.id) 
        && this.hasUserRole('AgencyOwner') 
        && user.application_age > 30 
        && (user.last_reminder_sent === '' || user.last_reminder_sent > 7)
        && user.status.indexOf('Application:') > -1

      return show
    }
  }
}
</script>

<style lang="sass" scope>
.app-table-col
  font-weight: 700 !important
  font-size: 1rem !important
</style>
