<template>
  <v-container class="d-flex flex-column pa-8">
    <v-row no-gutters>
      <v-col class="col-12 mb-4">
        <v-btn
          color="secondary"
          rounded
          small
          class="ma-1"
          :disabled="currentPage == p"
          v-for="p in pages"
          v-bind:key="p"
          @click="theFields(p)"
        >
          Page {{ p }}
        </v-btn>
      </v-col>
    </v-row>
    <div v-for="page in fields" v-bind:key="page.id">
      <h2>Page {{ currentPage }}</h2>
      <hr class="mt-4 mb-4" />
      <v-btn color="secondary" rounded small class="ma-1" @click="saveTheFields(page)"> Save </v-btn>
      <hr class="mt-4 mb-4" />
      <v-row no-gutters>
        <v-col class="col-6">
          <v-text-field label="Page Label" outlined v-model="page.label"></v-text-field>
        </v-col>
      </v-row>
      <div v-for="form_section in page.form_sections" v-bind:key="form_section.id">
        <h3 class="mt-4 mb-4">Page Section</h3>
        <v-row no-gutters>
          <v-col class="col-6">
            <v-text-field label="Page Section Label" outlined v-model="form_section.label"></v-text-field>
          </v-col>
        </v-row>
        <h4 class="mt-4 mb-4">Section Fields</h4>
        <div v-for="form_field in form_section.form_fields" v-bind:key="form_field.id">
          <v-row no-gutters>
            <v-col class="col-6">
              <v-text-field label="Field Label" outlined v-model="form_field.label"></v-text-field>
              <v-checkbox label="Personal Identifiable Information (PII)" v-model="form_field.pii"></v-checkbox>
              <v-checkbox label="Required" v-model="form_field.is_required"></v-checkbox>
            </v-col>
          </v-row>
          <div v-if="form_field.form_options && form_field.form_options.length">
            <h4>Field Options</h4>
            <div v-for="form_option in form_field.form_options" v-bind:key="form_option.id">
              <v-row no-gutters>
                <v-col class="col-12 col-md-6">
                  <v-row>
                    <v-col class="col-12 col-md-6">
                      <v-text-field label="Option Label" outlined v-model="form_option.label"></v-text-field>
                    </v-col>
                    <v-col class="col-12 col-md-6">
                      <v-text-field label="Option Value" outlined v-model="form_option.value"></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </div>
          </div>
          <hr class="mb-4" />
        </div>
      </div>
      <v-btn color="secondary" rounded small class="ma-1" @click="saveTheFields(page)"> Save </v-btn>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  data() {
    return {
      fields: [],
      currentPage: 1,
      pages: 1,
    }
  },
  mounted() {
    this.theFields()
  },
  methods: {
    ...mapActions({
      setSnackbar: 'snackbar/set',
      getFields: 'form_field_editor/getFields',
      saveFields: 'form_field_editor/saveFields',
    }),
    theFields(page) {
      if (!page) {
        page = this.currentPage
      }
      this.fields = []
      this.currentPage = page
      this.getFields(page).then(response => {
        this.fields = response.data.fields
        this.pages = response.data.pages
      })
    },
    async saveTheFields(page) {
      this.setSnackbar({ status: false })

      await this.saveFields(page)
        .then(() => {
          this.setSnackbar({
            status: true,
            text: 'Fields Updated',
            color: 'success',
          })
        })
        .catch(error => {
          console.log(error)
          this.setSnackbar({
            status: true,
            text: 'Errors',
            color: 'rgba(255, 0, 0, 0.8)',
          })
        })
    },
  },
}
</script>
