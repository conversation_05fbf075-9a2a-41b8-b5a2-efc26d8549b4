<template>
  <v-container class="login-container">
    <v-tabs v-model="tabs" grow class="login-container__tabs">
      <v-tab><span class="d-none d-sm-flex">New&nbsp;</span>Applicant</v-tab>
      <v-tab v-if="!isDemoSite">Agent<span class="d-none d-sm-flex">&nbsp;Login</span></v-tab>

      <v-tab-item class="pt-12 pb-8 px-8" style="min-height:400px;">
        <v-form ref="form" @submit.prevent="validate">
          <div class="line">
            <v-text-field
              :label="$i18n.t('auth.email')"
              prepend-icon="mdi-account"
              v-model="form.email"
              required
              :error-messages="errorMessages.email"
            />
          </div>

          <div class="line">
            <v-text-field
              :label="$i18n.t('auth.password')"
              prepend-icon="mdi-lock"
              type="password"
              v-model="form.password"
              required
            />
          </div>
          <div class="line">
            <v-checkbox v-model="form.remember" :label="$i18n.t('auth.remember')" />
          </div>
          <div class="line">
            <v-btn :loading="loading" color="primary" type="submit" block large depressed>
              {{ $i18n.t('auth.sign_in') }}
            </v-btn>
          </div>
        </v-form>

        <div class="links">
          <router-link class="link" :to="{ name: 'forgot_password' }">
            {{ $i18n.t('auth.forgot_password') }}
          </router-link>
          <router-link class="link" :to="{ name: 'portal' }"> ← {{ $i18n.t('auth.go_to_portal') }} </router-link>
        </div>

      </v-tab-item>

      <v-tab-item style="min-height:400px;" class="py-8 px-8">
        <v-sheet class="text-center pa-16 mt-4" outlined>
          <p><strong>Are you an agent?</strong><br />Log in using your HQ credentials.</p>
          <!-- <a href="/auth0/login" class="link mt-12 mb-1 text-h4 text-capitalize"> -->
            <v-btn class="text-capitalize mt-8" type="submit" href="/auth0/login" color="primary" block large depressed>
              Login with HQ
            </v-btn>
          <!-- </a> -->
        </v-sheet>
      </v-tab-item>

    </v-tabs>
    
  </v-container>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
import TextInput from '@/components/base/TextInput'
import CheckBox from '@/components/base/CheckBox'

export default {
  components: {
    TextInput,
    CheckBox,
  },
  data() {
    return {
      form: {
        email: null,
        password: null,
        remember: false,
      },
      errorMessages: {},
      loading: false,
      tabs: localStorage.login_tab ? parseInt(localStorage.login_tab) : 0
    }
  },
  created() {
    this.isLoggedIn()
  },
  computed: {
    ...mapGetters({
      hasUserRole: 'auth/hasUserRole',
      hasUserPermission: 'auth/hasUserPermission',
    }),
    isDemoSite() {
      return window.location.host == 'demo.quilityonboarding.com'
    }
  },
  methods: {
    ...mapActions({
      login: 'auth/login',
      getUser: 'auth/getUser',
    }),
    async validate() {
      if (this.$refs.form.validate()) {
        this.loading = true

        await this.login(this.form)
          .then(() => {
            this.getUser().then(() => {
              this.$router.push({ name: 'index' })
            })
          })
          .catch(
            ({
              response: {
                data: { errors, message },
              },
              message: messageHttp,
            }) => {
              if (errors) {
                this.errorMessages = errors
                return
              }

              this.errorMessages = { email: message || messageHttp || '' }
            },
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
    isLoggedIn() {
      this.getUser().then(() => {
        this.$router.push({ name: 'index' })
      })
    },
  },
  watch: {
    'tabs': function(newV, oldV) {
      localStorage.login_tab = newV
      this.$emit('changeLoginTab', newV)
    }
  }
}
</script>

<style lang="scss">
.login-container {
  &__tabs {
    border: 1px solid #46c3b22a;

    .v-tabs-slider-wrapper {
      top: 0;
    }

    .v-tab {
      &::before {
        opacity: 0.15;
        background: var(--v-primary-base) !important;
        // background: rgb(76, 141, 132);
      }
      &:hover::before {
        opacity: 0.15 !important;
      }

      &--active {
        &::before {
          opacity: 0.0;
        }
        &:hover {
          &:before { opacity: 0.01 !important; }
        }
      }
    }
  }
}
</style>

<style lang="sass" scoped>
.login-container
  padding-left: 50px
  padding-right: 50px

  .line
    display: flex
    flex-direction: row

  .links
    display: flex
    flex-direction: column
    text-align: center
    align-items: center
    margin-top: 30px

    .link
      width: fit-content
      text-decoration: none
      margin: 3px
      font-size: 12px
      color: #444444
</style>
