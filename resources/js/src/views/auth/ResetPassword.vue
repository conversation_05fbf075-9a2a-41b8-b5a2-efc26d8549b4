<template>
  <v-container class="reset-password-container">
    <v-form ref="form" @submit.prevent="validate">
      <div class="line" v-if="messageError">
        <p class="message-error">
          {{ messageError }}
        </p>
      </div>
      <div class="line" v-if="messageSuccess">
        <p class="message-success">
          {{ messageSuccess }}
        </p>
      </div>
      <div class="line">
        <text-input
          :label="$i18n.t('auth.new_password')"
          prepend-icon="mdi-lock"
          password
          v-model="form.password"
          :errors="formErrors.password"
          required
        ></text-input>
      </div>
      <div class="line">
        <text-input
          :label="$i18n.t('auth.password_confirmation')"
          prepend-icon="mdi-lock"
          password
          v-model="form.password_confirmation"
          required
        ></text-input>
      </div>

      <div class="line">
        <v-btn :loading="loading" color="primary" type="submit" rounded>
          {{ $i18n.t('auth.reset') }}
        </v-btn>
      </div>
    </v-form>
    <div class="links">
      <router-link class="link" :to="{ name: 'login' }">
        {{ $i18n.t('auth.sign_in') }}
      </router-link>
      <router-link class="link" :to="{ name: 'portal' }"> ← {{ $i18n.t('auth.go_to_portal') }} </router-link>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
import TextInput from '@/components/base/TextInput'

export default {
  components: {
    TextInput,
  },
  data() {
    return {
      form: {
        password: null,
        password_confirmation: null,
      },
      messageSuccess: '',
      messageError: '',
      formErrors: {},
      loading: false,
    }
  },
  created() {
    const { token, email } = this.$route.query
    if (!token || !email) this.$router.push({ name: 'login' })
  },
  methods: {
    ...mapActions('auth', ['resetPassword']),
    async validate() {
      this.messageSuccess = ''
      this.messageError = ''

      if (!this.form.password || !this.form.password_confirmation) {
        this.messageError = 'Please input password'
      } else {
        this.loading = true
        const { token, email } = this.$route.query

        await this.resetPassword({
          ...this.form,
          token,
          email,
        })
          .then(({ data: { success, message } }) => {
            if (success) this.messageSuccess = 'Password changed successfully. Now you can go to login page to sign in.'
            else this.messageError = message || 'Failed to reset password. Please try again.'
          })
          .catch(
            ({
              response: {
                data: { errors, message },
              },
              message: messageHttp,
            }) => {
              console.log(errors)
              if (errors) this.formErrors = errors
              this.messageError = message || messageHttp || ''
            },
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
  },
}
</script>

<style lang="sass" scoped>
.reset-password-container
  padding-left: 50px
  padding-right: 50px
  .line
    display: block
    text-align: center
    margin-bottom: 10px

    .message-success
      max-width: 480px
      margin-left: auto
      margin-right: auto
      font-size: 14px
      margin-bottom: 30px
      color: green
      font-weight: 500

    .message-error
      max-width: 480px
      margin-left: auto
      margin-right: auto
      font-size: 14px
      margin-bottom: 30px
      color: red
      font-weight: 500

  .links
    display: flex
    flex-direction: column
    text-align: center
    align-items: center
    margin-top: 50px

    .link
      width: fit-content
      text-decoration: none
      margin: 3px
      font-size: 12px
      color: #444444
</style>
