<template>
  <v-container class="register-container">
    <v-form ref="form" @submit.prevent="validate">
      <div class="line">
        <p class="message">
          You got an invitation from your Agency Owner! Please type in your information below to register.
        </p>
      </div>

      <v-alert type="info" v-if="user && user.upline_agent">
        ATTENTION >> Is this your Direct Upline: <strong>{{ user.upline_agent.AgentName }}</strong> and Agency Owner: <strong>{{ user.agency_owner ? user.agency_owner.AgentName : user.upline_agent.AgentName }}</strong>?  
        If this information is incorrect you have multiple applications.  
        Each application invite is unique.  If you cannot locate the other 
        invite, please reach out to your chosen upline to resolve before moving forward.
      </v-alert>

      <div class="line">
        <template>
          <v-flex>
          <span class="text-h5" v-html="$i18n.t('auth.full_name')"></span>
          <v-text-field
            outlined
            v-model="form.name"
            required
            :error-messages="errorMessages.name"
            hide-details="auto"
            autocomplete="on"
          />
        </v-flex>
        </template>
      </div>

      <div class="line">
        <template>
          <v-flex>
          <span class="text-h5" v-html="$i18n.t('auth.email')"></span>
          <v-text-field
            outlined
            v-model="form.email"
            required
            :error-messages="errorMessages.email"
            hide-details="auto"
            autocomplete="on"
          />
        </v-flex>
        </template>
      </div>

      <div class="line">
        <text-input
          :label="$i18n.t('auth.password')"
          prepend-icon="mdi-lock"
          password
          v-model="form.password"
          required
          :errors="errorMessages.password"
        ></text-input>
      </div>

      <div class="line">
        <text-input
          :label="$i18n.t('auth.password_confirmation')"
          prepend-icon="mdi-lock"
          password
          v-model="form.password_confirmation"
          required
        ></text-input>
      </div>

      <div class="line">
        <v-btn :loading="loading" color="primary" type="submit" block large>
          {{ $i18n.t('auth.register') }}
        </v-btn>
      </div>
    </v-form>
    <div class="links">
      <router-link class="link" :to="{ name: 'login' }">
        {{ $i18n.t('auth.sign_in') }}
      </router-link>
      <router-link class="link" :to="{ name: 'portal' }"> ← {{ $i18n.t('auth.go_to_portal') }} </router-link>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
import TextInput from '@/components/base/TextInput'

export default {
  components: {
    TextInput,
  },
  data() {
    return {
      form: {
        name: null,
        email: null,
        password: null,
        password_confirmation: null,
      },
      errorMessages: {},
      loading: false,
      user: null
    }
  },
  created() {
    this.code = this.$route.query.code ?? null
    this.group_code = this.$route.query.group_code ?? null
    if (!this.code && !this.group_code) this.$router.push({ name: 'login' })
    this.getUserByCode();
  },
  mounted() {
  },
  methods: {
    ...mapActions({
      register: 'auth/register',
      getUser: 'auth/getUser',
      getInvitation: 'auth/getInvitation',
    }),
    async validate() {
      if (this.$refs.form.validate()) {
        this.loading = true;

        await this.register({
          ...this.form,
          code: this.code,
          group_code: this.group_code,
          // type: this.user.type,
        })
          .then((response) => {
            this.getUser().then(() => {
              this.$router.push({ name: 'index' })
            })
          })
          .catch(
            ({
              response: {
                data: { errors, message },
              },
              message: messageHttp,
            }) => {
              if (errors) {
                this.errorMessages = errors
                return
              }

              this.errorMessages = { name: message || messageHttp || '' }
            },
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
    async getUserByCode(){
      if(this.code) {
        await this.getInvitation({
          'code': this.code
        })
        .then(res => {
            if (res && res.data) {
              this.form.name = res.data.name ? res.data.name : ''
              this.form.email = res.data.email ? res.data.email : ''
              this.user = res.data
            }
          })
      }
    },
  },
}
</script>

<style lang="sass" scoped>
.register-container
  padding-left: 50px
  padding-right: 50px
  .line
    display: flex
    flex-direction: row
    margin-bottom: 10px
    justify-content: center

    .message
      max-width: 480px
      margin-left: auto
      margin-right: auto
      font-size: 14px
      color: #72777c
      margin-bottom: 30px
      text-align: center

  .links
    display: flex
    flex-direction: column
    text-align: center
    align-items: center
    margin-top: 50px

    .link
      width: fit-content
      text-decoration: none
      margin: 3px
      font-size: 12px
      color: #444444

    .optional-field-input-label
      color: #888
      font-size: .75rem
      letter-spacing: .025em
      text-transform: uppercase
</style>

