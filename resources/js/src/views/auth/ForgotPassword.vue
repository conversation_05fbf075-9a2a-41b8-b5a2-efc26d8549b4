<template>
  <v-container class="forgot-container">
    <v-form ref="form" @submit.prevent="validate">
      <div class="line">
        <p class="message">
          Please enter your email address. You will receive an email message with instructions on how to reset your
          password.
        </p>
      </div>
      <div class="line" v-if="!!successMessage">
        <p class="message-success">
          {{ successMessage }}
        </p>
      </div>
      <div class="line">
        <text-input
          :label="$i18n.t('auth.email')"
          prepend-icon="mdi-account"
          v-model="form.email"
          required
          :errors="errorMessages.email"
        ></text-input>
      </div>

      <div class="line">
        <v-btn :loading="loading" color="primary" type="submit" rounded>
          {{ $i18n.t('auth.get_new_password') }}
        </v-btn>
      </div>
    </v-form>
    <div class="links">
      <router-link class="link" :to="{ name: 'login' }">
        {{ $i18n.t('auth.sign_in') }}
      </router-link>
      <router-link class="link" :to="{ name: 'portal' }"> ← {{ $i18n.t('auth.go_to_portal') }} </router-link>
    </div>
  </v-container>
</template>

<script>
import { mapActions } from 'vuex'
import TextInput from '@/components/base/TextInput'

export default {
  components: {
    TextInput,
  },
  data() {
    return {
      form: {
        email: null,
      },
      successMessage: '',
      errorMessages: {},
      loading: false,
    }
  },
  methods: {
    ...mapActions('auth', ['forgotPassword']),
    async validate() {
      if (this.$refs.form.validate()) {
        this.loading = true
        this.successMessage = ''
        this.errorMessages = {}

        await this.forgotPassword(this.form)
          .then(({ data: { message } }) => {
            this.successMessage = message
          })
          .catch(
            ({
              response: {
                data: { message },
              },
              message: messageHttp,
            }) => {
              this.errorMessages = {
                email: message || messageHttp || 'An error occured',
              }
            },
          )
          .finally(() => {
            this.loading = false
          })
      }
    },
  },
}
</script>

<style lang="sass" scoped>
.forgot-container
  padding-left: 50px
  padding-right: 50px
  .line
    display: block
    text-align: center
    margin-bottom: 10px

    .message
      max-width: 480px
      margin-left: auto
      margin-right: auto
      font-size: 14px
      color: #72777c
      margin-bottom: 30px

    .message-success
      max-width: 480px
      margin-left: auto
      margin-right: auto
      font-size: 14px
      margin-bottom: 30px
      color: green
      font-weight: 500

  .links
    display: flex
    flex-direction: column
    text-align: center
    align-items: center
    margin-top: 50px

    .link
      width: fit-content
      text-decoration: none
      margin: 3px
      font-size: 12px
      color: #444444
</style>
