import Vue from 'vue'
import Vuetify from 'vuetify/lib'
import en from 'vuetify/es5/locale/en'
import fr from 'vuetify/es5/locale/fr'
import de from 'vuetify/es5/locale/de'
import '@fortawesome/fontawesome-free/css/all.css' // Ensure you are using css-loader

Vue.use(Vuetify)

export default new Vuetify({
  lang: {
    locales: { en, fr, de },
    current: process.env.VUE_APP_I18N_LOCALE || navigator.language.substr(0, 2),
  },
  theme: {
    options: {
      customProperties: true,
    },
    themes: {
      light: {
        primary: process.env.MIX_APP_PRIMARY_COLOR_LIGHT || '#46C3B2',
      },
      dark: {
        primary: process.env.MIX_APP_SECONDARY_COLOR_DARK || '#005851',
      },
    },
  },
  icons: {  
    iconfont: 'fa',
  },
})
