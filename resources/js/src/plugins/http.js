import axios from 'axios'
// import https from "https";
import store from '@/store'
// import router from "@/router";

const baseURL = process.env.MIX_API_BASE_URL || ''

const base = axios.create({
  baseURL: baseURL,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
  withCredentials: true,
  // httpsAgent: new https.Agent({
  //   rejectUnauthorized: false,
  // }),
})

base.interceptors.response.use(
  response => response,
  error => {
    const statusCode = error.response.status

    const { url } = error.config

    if (statusCode === 401 || statusCode === 403 || statusCode === 419) {
      if (url !== '/api/user') store.dispatch('auth/getUser')

      // REVISIT: when failing to get user, we defitely take some action
      // else router.push({ name: "login" });
    }

    return Promise.reject(error)
  },
)

export default base
