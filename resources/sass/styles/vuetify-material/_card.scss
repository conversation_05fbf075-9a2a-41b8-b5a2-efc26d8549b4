.v-card {
  border-radius: 6px;

  &.v-card--material {
    margin-top: 30px;
    margin-bottom: 15px;
  }
  .card-title {
    font-size: 18px;
  }
  .v-card--material__heading {
    top: -30px;

    .subtitle-1 {
      color: hsla(0,0%,100%,.8);
    }
    .display-2 {
      font-size: 18px !important;
    }
  }
  .caption {
    font-size: 12px !important;
    letter-spacing: 0 !important;
  }
  .v-card__actions {
    padding-top: 15px;
    .display-2 {
      font-size: 18px !important;
    }
  }
  .v-divider {
    border-color: #eee;
  }
  .ct-label {
    font-size: 14px;
  }
}
.v-card--material-chart .v-card--material__heading .ct-label {
  font-weight: 300;
}

.v-btn--icon.v-size--default .v-icon,
.v-btn--fab.v-size--default .v-icon {
  font-size: 18px;
}
.v-card--material .v-image {
  .v-image__image {
    border-radius: 6px;
  }
}
.v-card__title {
  font-size: 18px;
  padding-top: 7px;
  padding-bottom: 2px;
}
.theme--light {
  .v-card > .v-card__text {
    color: #333;
  }
  .card-title {
    color: #3c4858;
  }
}
.theme--dark {
  .card-title {
    color: #fff;
  }
}
.v-timeline-item .v-card {
  margin-top: 0;
}
.v-card--wizard {
  .v-tabs-bar {
    height: 42px;
  }
  .v-card__actions {
    .v-btn {
      margin-right: 0 !important;
    }
  }
  .v-tabs .v-tab--active:hover::before,
  .theme--light.v-tabs .v-tab--active::before {
    opacity: 0;
  }
  .v-tabs .v-tab:hover::before {
    opacity: 0;
  }
}
.v-card--plan {
  .body-2 {
    font-weight: 500;
    letter-spacing: 0 !important;
    margin-top: 10px;
    margin-bottom: 8px;
  }
  .display-2 {
    margin-top: 30px;
  }
  .v-card__text {
    color: #999;
    margin-bottom: 16px;
  }
  .v-btn {
    margin-right: 0 !important;
  }
  .v-avatar {
    margin-top: 10px;
  }
}
.v-card--testimony {
  .v-card__text {
    color: #999 !important;
  }
  .display-2 {
    font-size: 18px !important;
  }
  .body-2 {
    font-weight: 500;
    font-size: 12px !important;
  }
  .v-avatar {
    left: calc(50% - 50px);
  }
}
.ct-square:before {
  float: none;
}