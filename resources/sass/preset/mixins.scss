@import '../styles/variables';

// ? Original Vuetify Mixin: states
@mixin app-states($material) {
  &::before {
    opacity: 1;
    background-color: inherit;
  }

  &:hover::before {
    background-color: rgba(94, 86, 105, map-deep-get($material, 'states', 'hover'));
    // opacity: 1 !important;
  }

  &:focus::before {
    background-color: rgba(94, 86, 105, map-deep-get($material, 'states', 'focus'));
    // opacity: 1 !important;
  }

  &--active {
    @include app-active-states($material);
  }
}

// ? Original Vuetify Mixin: active-states
@mixin app-active-states($material) {
  &:hover::before,
  &::before {
    background-color: rgba(94, 86, 105, map-deep-get($material, 'states', 'activated'));
    // opacity: 1 !important;
  }

  &:focus::before {
    background-color: rgba(94, 86, 105, map-deep-get($material, 'states', 'pressed'));
    // opacity: 1 !important;
  }
}

// ? Inspired Vuetify Mixin: theme
@mixin theme--child($class) {
  .v-application.theme--light .#{$class} {
    @content ($material-light);
  }
  .v-application.theme--dark .#{$class} {
    @content ($material-dark);
  }
}

// ? Original Vuetify Mixin: elevation
@mixin app-elevation($class, $z, $important: false) {
  @include theme--child($class) using ($material) {
    // ——— Completely Customized Shadows ——————— //

    @if $z == 1 {
      box-shadow: 0 1px 3px 0 rgba(map-get($material, 'shadow-color'), 0.2),
        0 2px 1px -1px rgba(map-get($material, 'shadow-color'), 0.12),
        0 1px 1px 0 rgba(map-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 2 {
      box-shadow: 0 1px 3px 0 rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 3px 1px -2px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0 2px 2px 0 rgba(map-deep-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 3 {
      box-shadow: 0 4px 8px -4px rgba(map-deep-get($material, 'shadow-color'), 0.42) if($important, !important, null);
    } @else if $z == 4 {
      box-shadow: 0 6px 18px -8px rgba(map-deep-get($material, 'shadow-color'), 0.56) if($important, !important, null);
    } @else if $z == 6 {
      box-shadow: 0 2px 10px 0 rgba(map-deep-get($material, 'shadow-color'), 0.1) if($important, !important, null);
    } @else if $z == 8 {
      box-shadow: 0 4px 14px 0 rgba(map-deep-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 9 {
      box-shadow: 0 5px 6px -3px rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 3px 16px 2px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0 9px 12px 1px rgba(map-deep-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 12 {
      box-shadow: 0 7px 8px -4px rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 5px 22px 4px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0 12px 17px 2px rgba(map-deep-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 16 {
      box-shadow: 0 8px 10px -5px rgba(map-deep-get($material, 'shadow-color'), 0.2),
        0 6px 30px 5px rgba(map-deep-get($material, 'shadow-color'), 0.12),
        0 16px 24px 2px rgba(map-deep-get($material, 'shadow-color'), 0.14) if($important, !important, null);
    } @else if $z == 24 {
      box-shadow: 0 18px 42px -6px rgba(map-deep-get($material, 'shadow-color'), 0.18) if($important, !important, null);
    }

    // ——— Vuetify Shadows w/ Color update ——————— //
    // TODO: Next update - Sync our color changes with shadows we didn't overridden

    // ——— Else for 0 elevation or any arbitrary value ——————— //
    @else {
      box-shadow: map-get($shadow-key-umbra, $z), map-get($shadow-key-penumbra, $z),
        map-get($shadow-key-ambient, $z) if($important, !important, null);
    }
  }
}

// ——— Generate Light/Dark style ——————— //
// ? Inspired from Vuetify's  `theme` mixin

@mixin theme-diff($component, $lightThemePropertyValue, $darkThemePropertyValue) {
  .theme--light.#{$component} {
    @content ($lightThemePropertyValue);
  }

  .theme--dark.#{$component} {
    @content ($darkThemePropertyValue);
  }
}

// Ability to create style based on theme colors
@mixin themeable() {
  @each $color, $value in $theme-colors {
    @content ($color, $value);
  }
}
