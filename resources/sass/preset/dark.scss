@import './mixins';

// ? This includes dark theme related style
.v-application {
  &.theme--dark {
    @at-root {
      .v-alert {
        &.v-sheet.theme--dark {
          &:not([class*='--text']) {
            color: #fff !important;
            svg {
              color: #fff !important;
            }
          }
        }
      }
    }

    // ————————————————————————————————————
    //* ——— Data table
    // ————————————————————————————————————
    .v-data-table {
      th {
        font-weight: 600;
        background-color: #3b355a !important;
      }
    }
  }
}

// ————————————————————————————————————
//* ——— Perfect Scrollbar
// ————————————————————————————————————

.v-application.theme--dark {
  .ps__rail-y,
  .ps__rail-x {
    background-color: transparent !important;
  }

  .ps__thumb-y {
    background-color: map-deep-get($material-dark, 'cards');
  }
}
