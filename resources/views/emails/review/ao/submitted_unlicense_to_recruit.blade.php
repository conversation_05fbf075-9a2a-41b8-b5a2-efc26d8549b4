@component('mail::message')


Hi {{ $recruit->name }},

Thanks for submitting your application! You indicated that you aren't licensed, so you still have another step to take. Depending on how you answered determines your next step. Please see the possible responses and corresponding necessary actions below:

- Passed State Exam: Once you have received your state license information, login to the <a href="{{ config('app.url') }}">Onboarding Portal</a>, and update your application with your state license information.
- Enrolled in a Pre-Licensing Course: Once you have completed pre-licensing and passed your state exam, login to the <a href="{{ config('app.url') }}">Onboarding Portal</a>, and submit proof you have passed your state exam or your state license information.
- Not Enrolled in a Pre-Licensing Course: Once you have enrolled in a pre-licensing course, please login to the <a href="{{ config('app.url') }}">Onboarding Portal</a>, and submit proof you have enrolled.

Once you've submitted the required information, we'll be able to continue with the contracting process.

Thank You,<br>

@if($recruiter)
{{ $recruiter->name }}<br>
<a href="mailto:{{ $recruiter->email }}">{{ $recruiter->email }}</a><br>
@endif

Sent via Onboarding Portal
@endcomponent
