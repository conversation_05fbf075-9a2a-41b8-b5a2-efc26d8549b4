@component('mail::message')
# Welcome to Advanced Markets, {{ $name }}!

We're excited to welcome you to the team!

Please take a moment to review the details of our partnership with Advisors Excel below. You are receiving this email because of the path and carrier selections made by your Agency. 

Kindly note that Advisors Excel operates through a different SureLC system. Completing the
necessary certifications will initiate your contracting process

**Advisors Excel Partnership: Why choose to work with them, and what do they offer?**

Advisors Excel is a strategic partnership that offers specialized proprietary annuity products, comprehensive training for individuals seeking knowledge and solutions, and back-office support for agents selling annuity products. This collaboration equips them with the tools needed to effectively navigate and serve clients in the annuity market.

**How to get contracted with Advisors Excel:**  

**Step 1 - Complete the Certification course in Quility U**  
At the end of level 3 certification, complete the Form in the chapter titled “How to become contracted”. It can take 3-5 business days for the Advisors Excel team to receive and process your request. Once processed you can expect to receive an email from the licensing department at Advisors Excel.

**Step 2 - Set up your Advisors Excel Agent Portal**  
Once you are contracted with one carrier you will receive instructions to set up your personal AE Portal account. Create username and password. You will need the AE Portal to access all the support and materials needed to become successful with Advisors Excel

**Step 3 - Download the contracting operations form**  
Download the Advisors Excel Contact and Operations form in HQ for important information on
<a href="https://hq.quility.com/page/annuity">https://hq.quility.com/page/annuity</a>

Helpful Tips:
When contacting Advisors Excel, always ask for Team 3 (or Team “Polite”) and inform that you work with Quility. Advisors Excel has a specialized support team for our agents.

If you have any immediate questions, please don't hesitate to reach out to your upline or agency owner.

Best regards,<br>
{{ config('app.name') }}
@endcomponent 