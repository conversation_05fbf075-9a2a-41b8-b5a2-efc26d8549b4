<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>Socket Test</title>

        <!-- Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">

        <style>
            body {
                font-family: 'Nunito', sans-serif;
            }
        </style>
    </head>
    <body class="antialiased">
        <h1>Page List</h1>
        <script type='text/javascript'>
            /* <![CDATA[ */
                var sys = {!! $output !!};
            /* ]]> */
        </script>
        <script type='text/javascript'>
            (function() {
                var websocket;
                var heartbeat;
                console.log(sys);
                startSocket();
                function startSocket() {
                    websocket = new WebSocket(sys.url);
                    websocket.onopen = function (evt) { onOpen(evt) };
                    websocket.onclose = function (evt) { onClose(evt) };
                    websocket.onmessage = function (evt) { onMessage(evt) };
                    websocket.onerror = function (evt) { onError(evt) };
                }
                function doSend(message) {
                    if (typeof websocket != 'undefined') {
                        if (websocket.readyState == 1) {
                            websocket.send(message);
                        }
                    }
                }
                function startKeepAlive() {
                    heartbeat = setInterval(function(){
                        doSend(JSON.stringify({event: 'heartbeat', data: '00000000-0000-0000-0000-000000000000'}));
                    }, 10000);
                }
                function onOpen(evt) {
                    doSend(JSON.stringify({event: 'login', data: sys.ident}));
                    startKeepAlive();
                }
                function onClose(evt) { 
                    console.log("socket disconnected.");
                    clearInterval(heartbeat);
                }
                function onMessage(evt) {
                    const obj = JSON.parse(evt.data);
                    console.log(obj);
                    if (obj.action == "loggedin") {
                        // successful login event
                    }
                    if (obj.action == "pagelist") {
                        // list of all locked pages
                    }
                    if (obj.action == "pagelock") {
                        // new single page locked, update row
                    }
                    if (obj.action == "pageunlock") {
                        // single page has been unlocked
                    }
                }
                function onError(evt) {
                    console.log("socket errored.");
                    clearInterval(heartbeat);
                }
            })();
        </script>
    </body>
</html>
