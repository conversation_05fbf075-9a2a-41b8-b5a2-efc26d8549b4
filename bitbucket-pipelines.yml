# This is a sample build configuration for Other.
# Check our guides at https://confluence.atlassian.com/x/5Q4SMw for more examples.
# Only use spaces to indent your .yml configuration.
# -----
# You can specify a custom docker image from Docker Hub as your build environment.
image: atlassian/default-image:latest
#image: cakekeith/rprimage:deploy1

pipelines:
#  custom:
#    manual-dev:
#      - step:
#          script:
            #- cd ~/.ssh/
            #- cat /opt/atlassian/pipelines/agent/data/id_rsa #this will show you the pem file saved to BitBucket
            #- ls /opt/atlassian/pipelines/agent/data/ #this will list the whole directory just an fyi
            #- eval `ssh-agent -s`
            #- ls -la /opt/atlassian/pipelines/agent/data/id_rsa
            #- ssh-add /opt/atlassian/pipelines/agent/data/id_rsa
            #- ssh-add -l
            #- ssh -t <EMAIL> "dev.quility.com/deploy.sh"
  branches:
    develop:
      - step:
          script:
            - curl https://forge.laravel.com/servers/551832/sites/1624344/deploy/http?token=CaraOtGGgZ00GdUv5zOXcwNIvCqfkEqmQntmEYSG
    staging:
      - step:
          script:
            - curl https://forge.laravel.com/servers/551832/sites/1727477/deploy/http?token=zuBK7TE7nESTn03O0s2X6HgHMUQzwKxaayq6o92F
#    master:
#      - step:
#          script:
#            - eval `ssh-agent -s`
#    
