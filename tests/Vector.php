<?php

$wsdl = "https://apidev.debit-check.com/EntityReports.svc?wsdl";
// $wsdl = "https://api.debit-check.com/EntityReports.svc?wsdl";

$options = [
    'trace' => 1, 
    'exceptions' => 1,
    'cache_wsdl' => WSDL_CACHE_NONE
];

$client = new SoapClient($wsdl, $options);

$params = [
    'request' => [
        'Entities' => [
            'EntityReports.EntitySearchCriteria' => [
                'EntityID' => '123456789',
                'EntityIDType' => 'SSN',
                'ReferenceID' => 'ref01',
                'State' => 'AR'
            ]
        ],
        'Password' => 'HeNmuWLOvJJU-knjb-pMlrub-91vC2MvazClaGzY',
        'ReferenceID' => 'inquiry83432',
        'Username' => 'quilitywsdev'
    ]
];

try {
    $response = $client->__soapCall('EntitySearch', [$params]);
    echo "Response:\n";
    print_r($response);
} catch (SoapFault $fault) {
    echo "Error: {$fault->getMessage()}";
}

?>