<?php

namespace Tests\Feature;

// use Illuminate\Foundation\Testing\RefreshDatabase;
// use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class VectorOneControllerTest extends TestCase
{
    /**
     * Test the /vector-one/ssn route.
     *
     * @return void
     */
    public function testSearchSSN()
    {
        // Assuming you want to send some data with the POST request
        $postData = [
            'ssn' => '555555555',
        ];

        // Send a POST request to the route
        $response = $this->post('/vector-one/ssn', $postData);

        // Assert the status code
        $response->assertStatus(200);

        // Optionally, assert the response structure or content
        // $response->assertJson(['key' => 'expected value']);
    }
}