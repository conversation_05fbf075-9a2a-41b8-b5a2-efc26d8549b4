<?php

namespace Tests\Unit;

use Illuminate\Support\Facades\Mail;
use Tests\TestCase;
use App\Models\User;
use App\Mail\Review\HO\SubmittedPrincipalApplicationMail;

class SubmittedPrincipalApplicationMailTest extends TestCase
{
    /** 
     * @test 
     */
    public function it_contains_the_correct_data_and_subject()
    {
        // Fake the mailer
        Mail::fake();

        // Create a principal user
        $principalUser = User::factory()->create([
            'type' => 'principal',
            'email' => '<EMAIL>',
        ]);

        // Create a new instance of the mailable
        $mail = new SubmittedPrincipalApplicationMail($principalUser);

        // Build the email to check its content before sending
        $builtMail = $mail->build();

        // Assert the correct subject
        $this->assertEquals('New Principal Application Submitted', $builtMail->subject);

        // Assert the correct data is present
        $viewData = $builtMail->viewData;
        $this->assertEquals($principalUser->id, $viewData['recruit']->id);
        $this->assertEquals($principalUser->invitedBy?->id, $viewData['recruiter']->id ?? null);

        // Send the email and assert it was sent
        Mail::to($principalUser->email)->send($mail);

        Mail::assertSent(SubmittedPrincipalApplicationMail::class, function ($mail) use ($principalUser) {
            return $mail->hasTo($principalUser->email);
        });
    }
}
