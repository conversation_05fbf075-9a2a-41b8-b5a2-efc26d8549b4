<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\SureLcService;
use Illuminate\Support\Facades\Http;

class SureLcServiceTest extends TestCase
{
    protected $sureLcService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sureLcService = new SureLcService();
    }

    /** @test */
    public function it_can_lookup_producer_by_npn_successfully()
    {
        // Mock the successful response
        Http::fake([
            'surelc.surancebay.com/*' => Http::response([
                'npn' => '17536023',
                'firstName' => 'John',
                'lastName' => 'Doe',
            ], 200)
        ]);

        // Call the service method
        $response = $this->sureLcService->lookupProducerByNPN('17536023');

        // Assert the response is as expected
        $this->assertEquals('John', $response['firstName']);
        $this->assertEquals('Doe', $response['lastName']);
    }

    /** @test */
    public function it_can_get_producer_branch_code_by_npn_successfully()
    {
        // Mock the successful response with branchCode
        Http::fake([
            'surelc.surancebay.com/*' => Http::response([
                'npn' => '123456789',
                'branchCode' => 'BC123',
            ], 200)
        ]);

        // Call the service method
        $response = $this->sureLcService->getProducerBranchCodeByNPN('123456789');

        // Assert the branchCode is returned correctly
        $this->assertArrayHasKey('branchCode', $response);
        $this->assertEquals('BC123', $response['branchCode']);
    }

}
